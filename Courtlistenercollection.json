{"info": {"_postman_id": "f5d8e3a0-a1b2-4c3d-8e4f-a1b2c3d4e5f6", "name": "CourtListener API v4", "description": "Collection for interacting with the CourtListener REST API v4.\n\n**Setup Required:**\n1. Create a Postman Environment (e.g., \"CourtListener Env\").\n2. Add an environment variable named `API_TOKEN` to it, setting its value to your personal CourtListener API token.\n3. Select this environment in Postman before running requests.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "YOUR_EXPORTER_ID"}, "item": [{"name": "Setup & Exploration (OPTIONS)", "description": "Send OPTIONS requests to understand endpoint capabilities (filters, ordering, etc.)", "item": [{"name": "OPTIONS /search/", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{BASE_URL}}/search/", "host": ["{{BASE_URL}}"], "path": ["search", ""]}, "description": "Check available filters, sorting, and parameters for the Search API."}, "response": []}, {"name": "OPTIONS /opinions/", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{BASE_URL}}/opinions/", "host": ["{{BASE_URL}}"], "path": ["opinions", ""]}, "description": "Check available filters, sorting, and fields for the Opinions API."}, "response": []}, {"name": "OPTIONS /clusters/", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{BASE_URL}}/clusters/", "host": ["{{BASE_URL}}"], "path": ["clusters", ""]}, "description": "Check available filters, sorting, and fields for the Clusters API."}, "response": []}, {"name": "OPTIONS /dockets/", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{BASE_URL}}/dockets/", "host": ["{{BASE_URL}}"], "path": ["dockets", ""]}, "description": "Check available filters, sorting, and fields for the Dockets API."}, "response": []}, {"name": "OPTIONS /courts/", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{BASE_URL}}/courts/", "host": ["{{BASE_URL}}"], "path": ["courts", ""]}, "description": "Check available filters, sorting, and fields for the Courts API."}, "response": []}]}, {"name": "Search API (Find Cases)", "description": "Use the Search API (`type=o` for opinions) to find relevant cases.", "item": [{"name": "Personal Injury Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/search/?type=o&q=personal%20injury%20OR%20negligence%20OR%20wrongful%20death&order_by=-score", "host": ["{{BASE_URL}}"], "path": ["search", ""], "query": [{"key": "type", "value": "o", "description": "Search Opinion Clusters"}, {"key": "q", "value": "personal injury OR negligence OR wrongful death", "description": "Keywords for Personal Injury. Refine as needed."}, {"key": "order_by", "value": "-score", "description": "Sort by relevance score (desc). Try '-citeCount' if supported/desired."}]}, "description": "Finds opinion clusters related to personal injury keywords. Check results for `cluster_id` or `opinion` IDs and `citeCount`."}, "response": []}, {"name": "Family Law Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/search/?type=o&q=divorce%20OR%20custody%20OR%20child%20support%20OR%20marital%20property&order_by=-score", "host": ["{{BASE_URL}}"], "path": ["search", ""], "query": [{"key": "type", "value": "o"}, {"key": "q", "value": "divorce OR custody OR child support OR marital property", "description": "Keywords for Family Law. Refine as needed."}, {"key": "order_by", "value": "-score", "description": "Sort by relevance score (desc). Try '-citeCount' if supported/desired."}]}, "description": "Finds opinion clusters related to family law keywords."}, "response": []}, {"name": "Criminal Defense Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/search/?type=o&q=criminal%20defense%20OR%20fourth%20amendment%20OR%20search%20seizure%20OR%20due%20process&order_by=-score", "host": ["{{BASE_URL}}"], "path": ["search", ""], "query": [{"key": "type", "value": "o"}, {"key": "q", "value": "criminal defense OR fourth amendment OR search seizure OR due process", "description": "Keywords for Criminal Defense. Refine as needed."}, {"key": "order_by", "value": "-score", "description": "Sort by relevance score (desc). Try '-citeCount' if supported/desired."}]}, "description": "Finds opinion clusters related to criminal defense keywords."}, "response": []}]}, {"name": "Case Law API (Get Details)", "description": "Retrieve full details (including text) using IDs found via search.", "item": [{"name": "Get Opinion by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/opinions/{{OPINION_ID}}/", "host": ["{{BASE_URL}}"], "path": ["opinions", "{{OPINION_ID}}", ""]}, "description": "Retrieves the full details of a specific opinion.\n\n**Action Required:** Replace `{{OPINION_ID}}` in the URL path with an actual ID obtained from a search result (often found within the cluster details or nested in the search result itself)."}, "response": []}, {"name": "Get Cluster by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/clusters/{{CLUSTER_ID}}/", "host": ["{{BASE_URL}}"], "path": ["clusters", "{{CLUSTER_ID}}", ""]}, "description": "Retrieves the details of a specific opinion cluster (which groups related opinions like dissents/concurrences).\n\n**Action Required:** Replace `{{CLUSTER_ID}}` in the URL path with an actual ID obtained from a search result (`type=o`)."}, "response": []}]}], "auth": {"type": "apikey", "apikey": [{"key": "value", "value": "Token {{API_TOKEN}}", "type": "string"}, {"key": "key", "value": "Authorization", "type": "string"}, {"key": "in", "value": "header", "type": "string"}]}, "variable": [{"key": "BASE_URL", "value": "https://www.courtlistener.com/api/rest/v4", "type": "string", "description": "Base URL for the CourtListener API v4"}, {"key": "OPINION_ID", "value": "REPLACE_WITH_OPINION_ID", "type": "string", "description": "Placeholder for an Opinion ID obtained from search results."}, {"key": "CLUSTER_ID", "value": "REPLACE_WITH_CLUSTER_ID", "type": "string", "description": "Placeholder for a Cluster ID obtained from search results."}]}
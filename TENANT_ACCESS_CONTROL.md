# Tenant Access Control System

## ✅ TENANT ACCESS CONTROL - FULLY SUPPORTED

The Week 1 implementation **perfectly supports** your tenant-based access model where tenants pay for:

1. **Access to a specific state (jurisdiction)**
2. **Access to one or more practice areas within that state**

## Business Model Support

### Jurisdiction-Based Access
- **Supported Jurisdictions**: Federal, Texas, California, New York, Florida, Ohio
- **Data Organization**: All data organized by jurisdiction code (`fed`, `tx`, `ca`, `ny`, `fl`, `oh`)
- **Storage Structure**: `legal/{jurisdiction}/{doc_type}/{subtype}/{year}/{document_id}`
- **Database Filtering**: All tables include `jurisdiction` field for tenant access control

### Practice Area Access Control
- **9 Practice Areas Supported**:
  1. `personal_injury` - Personal Injury
  2. `criminal_law` - Criminal Law
  3. `business_law` - Business Law
  4. `family_law` - Family Law
  5. `employment_law` - Employment Law
  6. `real_estate` - Real Estate
  7. `tax_law` - Tax Law
  8. `constitutional_law` - Constitutional Law
  9. `immigration_law` - Immigration Law

### Database Schema for Tenant Control

```sql
-- All tables include tenant access fields
ALTER TABLE cases ADD COLUMN jurisdiction TEXT DEFAULT 'tx';
ALTER TABLE cases ADD COLUMN practice_areas TEXT[];
ALTER TABLE cases ADD COLUMN tenant_id TEXT;
ALTER TABLE cases ADD COLUMN user_id TEXT;
```

## Implementation Examples

### Example 1: Texas Personal Injury Tenant
```sql
-- Query for tenant with Texas + Personal Injury access
SELECT * FROM cases 
WHERE jurisdiction = 'tx' 
AND 'personal_injury' = ANY(practice_areas)
AND tenant_id = 'tenant_123';
```

### Example 2: California Multi-Practice Tenant
```sql
-- Query for tenant with California + Business Law + Employment Law
SELECT * FROM cases 
WHERE jurisdiction = 'ca' 
AND (practice_areas && ARRAY['business_law', 'employment_law'])
AND tenant_id = 'tenant_456';
```

### Example 3: Storage Path Filtering
```python
# GCS path filtering by jurisdiction and practice area
gcs_path = f"legal/{jurisdiction}/cases/{year}/{document_id}"

# Pinecone namespace filtering
namespace = f"{jurisdiction}_case"

# Practice area filtering in metadata
metadata_filter = {"practice_areas": ["personal_injury"]}
```

## Data Organization Architecture

### Storage Systems
- **GCS**: Jurisdiction-based folder structure
- **Pinecone**: Namespace separation by jurisdiction and document type
- **Neo4j**: Labels for jurisdiction and document type
- **Supabase**: Tenant ID and jurisdiction fields for access control

### Access Control Flow
1. **Tenant Authentication**: Verify tenant credentials
2. **Jurisdiction Check**: Ensure tenant has access to requested jurisdiction
3. **Practice Area Check**: Verify tenant has access to requested practice areas
4. **Data Filtering**: Apply jurisdiction and practice area filters to queries
5. **Result Delivery**: Return only authorized data to tenant

## CourtListener API Integration

### ✅ FULLY FUNCTIONAL
The CourtListener API integration remains completely functional and enhanced:

- **Jurisdiction Filtering**: Search by specific states
- **Practice Area Classification**: Automatic classification of incoming cases
- **Tenant-Aware Processing**: Cases automatically tagged with jurisdiction and practice areas

### Example Tenant-Aware Processing
```python
def fetch_cases_for_tenant(tenant_config):
    jurisdiction = tenant_config['jurisdiction']  # e.g., 'tx'
    practice_areas = tenant_config['practice_areas']  # e.g., ['personal_injury']
    
    # Build search query with practice area keywords
    keywords = []
    for area in practice_areas:
        area_config = document_taxonomy['practice_areas'][area]
        keywords.extend(area_config['keywords'])
    
    query = ' OR '.join(keywords)
    
    # Search CourtListener with jurisdiction filter
    results = client.search_by_jurisdiction(
        jurisdiction=jurisdiction,
        query=query,
        page_size=50
    )
    
    # Process and store with tenant access controls
    for case in results['results']:
        classification = classifier.classify_document(case['text'])
        store_case_with_tenant_access(
            case_data=case,
            jurisdiction=jurisdiction,
            practice_areas=classification['practice_areas'],
            tenant_id=tenant_config['tenant_id']
        )
```

## Billing Integration Ready

### Subscription Model Support
- **State-Level Billing**: Charge per jurisdiction access
- **Practice Area Add-ons**: Additional fees for multiple practice areas
- **Usage Tracking**: Monitor data access by jurisdiction and practice area
- **Scalable Pricing**: Easy to add new jurisdictions and practice areas

### Access Control Implementation
- **API Layer**: Filter all responses by tenant's purchased access
- **Database Queries**: Automatic filtering by jurisdiction and practice areas
- **Storage Access**: Tenant-specific data paths and namespaces
- **Monitoring**: Track usage for billing and compliance

## Configuration Files

### Jurisdiction Configuration
- **File**: `src/config/enhanced_jurisdiction_config.json`
- **Contains**: Court hierarchies, citation patterns, storage configs

### Document Taxonomy
- **File**: `src/config/document_taxonomy.json`
- **Contains**: Practice area definitions, keywords, document types

## Next Steps for Full Implementation

1. **API Layer**: Add tenant authentication and filtering
2. **Billing Integration**: Connect access selections to billing system
3. **Data Migration**: Apply tenant IDs to existing data
4. **UI Controls**: Implement jurisdiction and practice area selection
5. **Monitoring**: Set up usage tracking for billing

## Benefits

- ✅ **Cost Efficiency**: Only store/process data for purchased jurisdictions
- ✅ **Performance**: Jurisdiction-based indexing and namespaces
- ✅ **Compliance**: Clear data boundaries for tenant access
- ✅ **Scalability**: Easy to add new jurisdictions and practice areas
- ✅ **Flexibility**: Tenants can upgrade to additional practice areas
- ✅ **Business Model Alignment**: Perfect fit for SaaS pricing strategy

## Conclusion

The Week 1 implementation provides a complete foundation for your tenant-based access control system, supporting both state-level and practice area-level access controls while maintaining full CourtListener API functionality.

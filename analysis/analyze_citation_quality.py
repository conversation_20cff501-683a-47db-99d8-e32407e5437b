#!/usr/bin/env python3
"""
Analyze the quality of citations from LLM extraction
"""

import json
import os
from collections import Counter

# Files to analyze 
LLM_FILE = 'citation_analysis_20250408_200454.json'

def load_data(file_path):
    """Load JSON data from file"""
    with open(file_path, 'r') as f:
        return json.load(f)

def analyze_citation_quality(data):
    """Analyze the quality metrics of citations"""
    # Initialize counters
    confidence_levels = Counter()
    extraction_methods = Counter()
    citation_types = Counter()
    context_length = []
    citation_length = []
    
    # Track citations with special format indicators
    structured_citations = 0  # Citations matching standard legal citation patterns
    malformed_citations = 0   # Citations with potential formatting issues
    
    # Sample citations by confidence level
    high_conf_samples = []
    medium_conf_samples = []
    low_conf_samples = []
    
    # Track citation patterns
    special_patterns = {
        "chapter": 0,
        "section": 0,
        "article": 0,
        "v.": 0,        # Case citations often contain "v."
        "§": 0,         # Section symbol
        "et seq": 0,    # "and following" citations
        "et al": 0      # "and others" citations
    }
    
    # Citation context analysis
    total_citations = 0
    citations_with_context = 0
    
    # Process all documents and their citations
    for doc in data:
        citations = doc.get('citations', [])
        
        # Some files might have a different structure
        if isinstance(citations, dict) and 'found' in citations:
            # This format doesn't have detailed citation information
            continue
            
        if not isinstance(citations, list):
            continue
            
        for cit in citations:
            total_citations += 1
            
            # Record confidence level
            confidence = cit.get('confidence', 'unknown')
            confidence_levels[confidence] += 1
            
            # Record extraction method
            method = cit.get('extraction_method', 'unknown')
            extraction_methods[method] += 1
            
            # Record citation type
            cit_type = cit.get('type', 'unknown')
            citation_types[cit_type] += 1
            
            # Analyze citation text
            text = cit.get('text', '')
            citation_length.append(len(text))
            
            # Check for special patterns
            for pattern, _ in special_patterns.items():
                if pattern.lower() in text.lower():
                    special_patterns[pattern] += 1
            
            # Analyze context if available
            context = cit.get('context', '')
            if context:
                citations_with_context += 1
                context_length.append(len(context))
            
            # Categorize format quality
            if any(p in text.lower() for p in ["chapter", "section", "§", "v."]):
                structured_citations += 1
            elif len(text) < 5 or text.count(" ") < 1:
                malformed_citations += 1
                
            # Collect samples by confidence
            sample = {
                'text': text, 
                'type': cit_type,
                'context': context[:50] + "..." if len(context) > 50 else context
            }
            
            if confidence == 'high' and len(high_conf_samples) < 3:
                high_conf_samples.append(sample)
            elif confidence == 'medium' and len(medium_conf_samples) < 3:
                medium_conf_samples.append(sample)
            elif confidence == 'low' and len(low_conf_samples) < 3:
                low_conf_samples.append(sample)
    
    # Calculate averages
    avg_citation_length = sum(citation_length) / len(citation_length) if citation_length else 0
    avg_context_length = sum(context_length) / len(context_length) if context_length else 0
    
    # Calculate quality metrics
    structured_pct = (structured_citations / total_citations * 100) if total_citations > 0 else 0
    malformed_pct = (malformed_citations / total_citations * 100) if total_citations > 0 else 0
    context_pct = (citations_with_context / total_citations * 100) if total_citations > 0 else 0
    
    return {
        'total_citations': total_citations,
        'confidence_levels': confidence_levels,
        'extraction_methods': extraction_methods,
        'citation_types': citation_types,
        'structured_citations': structured_citations,
        'structured_pct': structured_pct,
        'malformed_citations': malformed_citations,
        'malformed_pct': malformed_pct,
        'citations_with_context': citations_with_context,
        'context_pct': context_pct,
        'avg_citation_length': avg_citation_length,
        'avg_context_length': avg_context_length,
        'special_patterns': special_patterns,
        'high_conf_samples': high_conf_samples,
        'medium_conf_samples': medium_conf_samples,
        'low_conf_samples': low_conf_samples
    }

def print_quality_report(analysis):
    """Print a detailed report on citation quality"""
    print("\n=== CITATION QUALITY ANALYSIS ===")
    print(f"Total citations analyzed: {analysis['total_citations']}")
    
    print("\n--- CONFIDENCE DISTRIBUTION ---")
    total = sum(analysis['confidence_levels'].values())
    for level, count in sorted(analysis['confidence_levels'].items(), 
                               key=lambda x: (0 if x[0] == 'high' else 
                                             (1 if x[0] == 'medium' else 
                                              (2 if x[0] == 'low' else 3)))):
        pct = (count / total * 100) if total > 0 else 0
        print(f"  {level}: {count} ({pct:.1f}%)")
    
    print("\n--- EXTRACTION METHODS ---")
    for method, count in analysis['extraction_methods'].most_common():
        print(f"  {method}: {count}")
    
    print("\n--- CITATION TYPES ---")
    for cit_type, count in analysis['citation_types'].most_common(10):
        print(f"  {cit_type}: {count}")
    
    print("\n--- FORMAT QUALITY ---")
    print(f"  Structured citations: {analysis['structured_citations']} ({analysis['structured_pct']:.1f}%)")
    print(f"  Potentially malformed: {analysis['malformed_citations']} ({analysis['malformed_pct']:.1f}%)")
    
    print("\n--- CONTEXT QUALITY ---")
    print(f"  Citations with context: {analysis['citations_with_context']} ({analysis['context_pct']:.1f}%)")
    print(f"  Average citation length: {analysis['avg_citation_length']:.1f} characters")
    print(f"  Average context length: {analysis['avg_context_length']:.1f} characters")
    
    print("\n--- CITATION PATTERNS ---")
    for pattern, count in sorted(analysis['special_patterns'].items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            pattern_pct = (count / analysis['total_citations'] * 100) if analysis['total_citations'] > 0 else 0
            print(f"  {pattern}: {count} ({pattern_pct:.1f}%)")
    
    print("\n--- SAMPLE CITATIONS BY CONFIDENCE ---")
    print("High confidence examples:")
    for i, sample in enumerate(analysis['high_conf_samples'], 1):
        print(f"  {i}. {sample['text']} (type: {sample['type']})")
        if sample['context']:
            print(f"     Context: {sample['context']}")
    
    print("\nMedium confidence examples:")
    for i, sample in enumerate(analysis['medium_conf_samples'], 1):
        print(f"  {i}. {sample['text']} (type: {sample['type']})")
        if sample['context']:
            print(f"     Context: {sample['context']}")
    
    print("\nLow confidence examples:")
    for i, sample in enumerate(analysis['low_conf_samples'], 1):
        print(f"  {i}. {sample['text']} (type: {sample['type']})")
        if sample['context']:
            print(f"     Context: {sample['context']}")

def main():
    """Main function to analyze citation quality"""
    try:
        # Load LLM enhanced citation data
        llm_data = load_data(LLM_FILE)
        
        # Analyze citation quality
        quality_analysis = analyze_citation_quality(llm_data)
        
        # Print quality report
        print_quality_report(quality_analysis)
        
    except Exception as e:
        print(f"Error analyzing citation quality: {str(e)}")

if __name__ == "__main__":
    main()

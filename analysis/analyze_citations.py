#!/usr/bin/env python3
"""
Citation Analysis Script for Texas Civil Practice and Remedies Code
Analyzes existing document files for citations and builds a knowledge graph
"""

import os
import glob
import json
import uuid
import time
from datetime import datetime
from dotenv import load_dotenv

# Import our enhanced modules
from document_graph import LegalDocumentGraph, setup_document_in_graph

# Load environment variables
load_dotenv()

# Configuration
PDF_FOLDER_PATH = os.getenv("PDF_FOLDER_PATH_SUCCESS", "/Users/<USER>/Documents/Texas/CP/Success")
USE_LLM = True  # Set to True to enable LLM-based extraction and schema evolution
BATCH_SIZE = 5  # Process this many documents before pausing to avoid rate limits
PAUSE_SECONDS = 60  # Seconds to pause between batches
DEFAULT_DOC_LIMIT = 20  # Process only this many documents by default to avoid rate limits

def extract_metadata_from_filename(filename):
    """Extract CPRC chapter and section information from filename"""
    
    # Handle different filename formats
    parts = filename.replace('.txt', '').replace('.pdf', '').split('_')
    
    # Default metadata
    metadata = {
        "doc_type": "law",
        "jurisdiction": "Texas",
        "statute_title": "Civil Practice and Remedies Code"
    }
    
    # Try to extract chapter and section
    if len(parts) > 1:
        # Format like "CPRC_33_001" or "chapter_33_section_001"
        try:
            metadata["statute_chapter"] = parts[1].lstrip("0")
            if len(parts) > 2:
                metadata["statute_section"] = parts[2].lstrip("0")
                metadata["document_title"] = f"CPRC Chapter {metadata['statute_chapter']} Section {metadata['statute_section']}"
            else:
                metadata["document_title"] = f"CPRC Chapter {metadata['statute_chapter']}"
        except (IndexError, ValueError):
            metadata["document_title"] = f"CPRC {filename}"
    else:
        metadata["document_title"] = f"CPRC {filename}"
        
    return metadata

def find_document_files():
    """Find all document files to process"""
    
    # Look for PDFs in the success folder
    pdf_files = glob.glob(os.path.join(PDF_FOLDER_PATH, "*.pdf"))
    if pdf_files:
        print(f"Found {len(pdf_files)} PDF files in {PDF_FOLDER_PATH}")
        return pdf_files
    
    # If no files found
    raise FileNotFoundError(f"No PDF files found in {PDF_FOLDER_PATH}")

def read_document_content(file_path):
    """Read document content from file"""
    
    try:
        if file_path.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        elif file_path.endswith('.pdf'):
            # For PDFs, we would need to extract text
            # This is simplified - you might want to use your existing PDF extraction code
            from PyPDF2 import PdfReader
            reader = PdfReader(file_path)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
            return text
        else:
            print(f"Unsupported file format: {file_path}")
            return ""
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return ""

def analyze_documents(file_paths, limit=None, batch_size=5):
    """Process documents and extract citations"""
    
    # Initialize Neo4j graph
    graph = LegalDocumentGraph(use_llm=USE_LLM)
    
    try:
        # Process documents
        total_files = len(file_paths) if limit is None else min(limit, len(file_paths))
        print(f"Processing {total_files} documents...")
        
        results = []
        doc_count = 0
        
        # Process in batches to avoid overwhelming the database
        for i, file_path in enumerate(file_paths):
            if limit is not None and i >= limit:
                break
                
            # For batch processing
            if i > 0 and i % batch_size == 0:
                print(f"Processed {i}/{total_files} documents")
                if USE_LLM:
                    # Pause to avoid API rate limits
                    pause_seconds = 60
                    print(f"Pausing for {pause_seconds} seconds to avoid API rate limits...")
                    time.sleep(pause_seconds)
            
            # Generate unique document ID
            filename = os.path.basename(file_path)
            document_id = f"cprc-{str(uuid.uuid4())[:8]}"
            
            # Extract metadata from filename
            metadata = extract_metadata_from_filename(filename)
            
            # Read content
            content = read_document_content(file_path)
            if not content:
                print(f"Skipping {file_path} - no content extracted")
                continue
            
            print(f"Processing {i+1}/{total_files}: {metadata.get('document_title', filename)}")
            
            # Process document through our enhanced system
            result = setup_document_in_graph(
                document_id=document_id, 
                pdf_path=file_path, 
                content=content, 
                metadata=metadata,
                use_llm=USE_LLM,
                suggest_relationships=(i % 5 == 0)  # Only suggest relationships for every 5th document to save API calls
            )
            
            # Add filename to result for reference
            result["filename"] = filename
            result["file_path"] = file_path
            results.append(result)
            
            doc_count += 1
            
            # Print summary
            print(f"  - Citations found: {result['citations']['found']}")
            print(f"  - Citations resolved: {result['citations']['resolved']}")
        
        # Print overall statistics
        print(f"\nProcessed {doc_count} documents")
        total_citations = sum(r['citations']['found'] for r in results)
        resolved_citations = sum(r['citations']['resolved'] for r in results)
        print(f"Total citations found: {total_citations}")
        print(f"Total citations resolved: {resolved_citations}")
        print(f"Resolution rate: {resolved_citations/total_citations*100:.2f}% (if citations found > 0)")
        
        # Save results to file
        results_file = f"citation_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Results saved to {results_file}")
        
        return results
    
    finally:
        # Always close graph connection
        graph.close()

def analyze_graph_relationships():
    """Analyze relationships in the Neo4j graph"""
    
    graph = LegalDocumentGraph(use_llm=False)  # Don't need LLM for this analysis
    
    try:
        print("\nAnalyzing graph relationships...")
        
        # Get basic statistics
        with graph.driver.session() as session:
            # Document count by type
            doc_count = session.run("""
            MATCH (d:Document)
            RETURN d.doc_type as type, count(d) as count
            ORDER BY count DESC
            """)
            
            print("\nDocument count by type:")
            for record in doc_count:
                print(f"  {record['type']}: {record['count']}")
            
            # Citation count by type
            citation_count = session.run("""
            MATCH (c:Citation)
            RETURN c.type as type, count(c) as count
            ORDER BY count DESC
            """)
            
            print("\nCitation count by type:")
            for record in citation_count:
                print(f"  {record['type']}: {record['count']}")
            
            # Documents with most citations
            most_citations = session.run("""
            MATCH (d:Document)-[r:CITES]->()
            RETURN d.document_id as id, d.title as title, count(r) as citation_count
            ORDER BY citation_count DESC
            LIMIT 5
            """)
            
            print("\nDocuments with most citations:")
            for record in most_citations:
                print(f"  {record['title']}: {record['citation_count']} citations")
            
            # Most cited documents
            most_cited = session.run("""
            MATCH ()-[r:CITES]->(c:Citation)-[res:RESOLVES_TO]->(d:Document)
            RETURN d.document_id as id, d.title as title, count(r) as cited_count
            ORDER BY cited_count DESC
            LIMIT 5
            """)
            
            print("\nMost cited documents:")
            for record in most_cited:
                print(f"  {record['title']}: cited {record['cited_count']} times")
            
            # Content-based relationships (if any)
            content_relationships = session.run("""
            MATCH (d1:Document)-[r]->(d2:Document)
            WHERE r.auto_generated = true
            RETURN type(r) as relationship_type, count(r) as count
            ORDER BY count DESC
            """)
            
            print("\nContent-based relationships:")
            content_rels = list(content_relationships)
            if content_rels:
                for record in content_rels:
                    print(f"  {record['relationship_type']}: {record['count']}")
            else:
                print("  No content-based relationships found")
            
        return True
    
    finally:
        graph.close()

def main():
    """Main function"""
    
    print("=== Texas CPRC Citation Analysis ===")
    print(f"Using LLM: {USE_LLM}")
    
    # Find document files
    file_paths = find_document_files()
    if not file_paths:
        print("No documents found. Please check your file paths.")
        return
    
    # Process limit - adjust as needed
    limit = int(input(f"Found {len(file_paths)} documents. How many to process? (0 for all, Enter for default {DEFAULT_DOC_LIMIT}): ") or str(DEFAULT_DOC_LIMIT))
    if limit == 0:
        limit = None
    
    # Analyze documents
    results = analyze_documents(file_paths, limit)
    
    # Analyze relationships
    analyze_graph_relationships()
    
    print("\nAnalysis complete!")

if __name__ == "__main__":
    main()

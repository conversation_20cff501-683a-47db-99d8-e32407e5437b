#!/usr/bin/env python3
"""
Compare citation analysis results between regex-only and LLM-enhanced extraction
"""

import json
import os
from collections import Counter

# Files to compare
REGEX_FILE = 'citation_analysis_20250408_162303.json'
LLM_FILE = 'citation_analysis_20250408_200454.json'

def load_data(file_path):
    """Load JSON data from file"""
    with open(file_path, 'r') as f:
        return json.load(f)

def analyze_citations(data):
    """Analyze citation data from a document set"""
    doc_count = len(data)
    
    # Citation counts
    citation_counts = []
    citation_types = Counter()
    extraction_methods = Counter()
    confidence_levels = Counter()
    
    # Document tracking
    docs_with_citations = 0
    avg_citations_per_doc = 0
    
    # Extract actual citations
    for doc in data:
        # Some files use nested 'citations' objects with 'found' key, others use direct lists
        citations = doc.get('citations', [])
        if isinstance(citations, dict) and 'found' in citations:
            # This is the newer format with a count
            citation_count = citations.get('found', 0)
            # No details for further analysis in this format
            citation_counts.append(citation_count)
        elif isinstance(citations, list):
            # This is the older format with full citation details
            citation_count = len(citations)
            citation_counts.append(citation_count)
            
            # Analyze citation types and methods
            for cit in citations:
                cit_type = cit.get('type', 'unknown')
                citation_types[cit_type] += 1
                
                method = cit.get('extraction_method', 'unknown')
                extraction_methods[method] += 1
                
                confidence = cit.get('confidence', 'unknown')
                confidence_levels[confidence] += 1
        
        if citation_count > 0:
            docs_with_citations += 1
    
    # Calculate averages
    total_citations = sum(citation_counts)
    if doc_count > 0:
        avg_citations_per_doc = total_citations / doc_count
    
    # File names for documents with most citations
    docs_with_most = []
    if isinstance(data[0].get('citations', []), list):  # Only if we have detailed citation data
        docs_by_citation_count = sorted([(doc.get('filename', 'unknown'), len(doc.get('citations', []))) 
                                        for doc in data], key=lambda x: x[1], reverse=True)
        docs_with_most = docs_by_citation_count[:5]
    
    return {
        'doc_count': doc_count,
        'total_citations': total_citations,
        'docs_with_citations': docs_with_citations,
        'docs_with_citations_percent': (docs_with_citations / doc_count * 100) if doc_count > 0 else 0,
        'avg_citations_per_doc': avg_citations_per_doc,
        'citation_types': citation_types,
        'extraction_methods': extraction_methods,
        'confidence_levels': confidence_levels,
        'docs_with_most': docs_with_most
    }

def print_analysis(title, analysis):
    """Print citation analysis in a readable format"""
    print(f"\n=== {title} ===")
    print(f"Documents analyzed: {analysis['doc_count']}")
    print(f"Total citations found: {analysis['total_citations']}")
    print(f"Documents with citations: {analysis['docs_with_citations']} ({analysis['docs_with_citations_percent']:.1f}%)")
    print(f"Average citations per document: {analysis['avg_citations_per_doc']:.2f}")
    
    # Print citation types
    if analysis['citation_types']:
        print("\nTop citation types:")
        for cit_type, count in analysis['citation_types'].most_common(10):
            print(f"  {cit_type}: {count}")
    
    # Print extraction methods
    if analysis['extraction_methods']:
        print("\nExtraction methods:")
        for method, count in analysis['extraction_methods'].most_common():
            print(f"  {method}: {count}")
    
    # Print confidence levels
    if analysis['confidence_levels']:
        print("\nConfidence levels:")
        for level, count in analysis['confidence_levels'].most_common():
            print(f"  {level}: {count}")
    
    # Print docs with most citations
    if analysis['docs_with_most']:
        print("\nDocuments with most citations:")
        for filename, count in analysis['docs_with_most']:
            print(f"  {filename}: {count}")

def compare_analyses(regex_analysis, llm_analysis):
    """Compare the two analyses and print differences"""
    print("\n=== COMPARISON ===")
    
    # Compare document counts
    regex_doc_count = regex_analysis['doc_count']
    llm_doc_count = llm_analysis['doc_count']
    print(f"Document count: Regex={regex_doc_count}, LLM={llm_doc_count}")
    
    # Calculate citation improvement
    regex_avg = regex_analysis['avg_citations_per_doc']
    llm_avg = llm_analysis['avg_citations_per_doc']
    if regex_avg > 0:
        improvement_pct = ((llm_avg - regex_avg) / regex_avg) * 100
        print(f"Average citations per document: Regex={regex_avg:.2f}, LLM={llm_avg:.2f}")
        print(f"Citation improvement: {improvement_pct:.1f}%")
    
    # Compare citation types
    regex_types = set(regex_analysis['citation_types'].keys())
    llm_types = set(llm_analysis['citation_types'].keys())
    
    unique_to_llm = llm_types - regex_types
    if unique_to_llm:
        print("\nUnique citation types found by LLM:")
        for cit_type in unique_to_llm:
            print(f"  {cit_type}: {llm_analysis['citation_types'][cit_type]}")
    
    # Compare confidence levels if available
    if llm_analysis['confidence_levels']:
        print("\nLLM confidence distribution:")
        total = sum(llm_analysis['confidence_levels'].values())
        for level, count in llm_analysis['confidence_levels'].most_common():
            pct = (count / total) * 100 if total > 0 else 0
            print(f"  {level}: {count} ({pct:.1f}%)")

def main():
    """Main function to compare citation analyses"""
    try:
        # Load data
        regex_data = load_data(REGEX_FILE)
        llm_data = load_data(LLM_FILE)
        
        # Analyze each dataset
        regex_analysis = analyze_citations(regex_data)
        llm_analysis = analyze_citations(llm_data)
        
        # Print individual analyses
        print_analysis("REGEX-ONLY EXTRACTION", regex_analysis)
        print_analysis("LLM-ENHANCED EXTRACTION", llm_analysis)
        
        # Compare the analyses
        compare_analyses(regex_analysis, llm_analysis)
        
    except Exception as e:
        print(f"Error comparing citation files: {str(e)}")

if __name__ == "__main__":
    main()

import os
import pandas as pd
from supabase import create_client
from dotenv import load_dotenv
from pinecone import Pinecone

# Load environment variables
load_dotenv()

# Environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")
VOYAGE_API_KEY = os.getenv("VOYAGE_API_KEY")

# Embedding model configuration
EMBEDDING_MODEL = "voyage-3-large"  # Current embedding model
EMBEDDING_DIMENSIONS = 1024  # Voyage-3-large has 1024 dimensions

# Initialize Supabase client
supabase_client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Initialize Pinecone client
pc = Pinecone(api_key=PINECONE_API_KEY)

# Check if the index exists
index_list = pc.list_indexes()
if PINECONE_INDEX_NAME not in index_list.names():
    raise RuntimeError(f"[Error] Pinecone index '{PINECONE_INDEX_NAME}' does not exist.")

# Access the Pinecone index
index = pc.Index(PINECONE_INDEX_NAME)


def fetch_document_summary():
    """
    Fetch the processed documents from Supabase and corresponding metadata from Pinecone,
    including the enhanced citation metadata.
    """
    # Fetch all documents from the 'documents' table
    docs_response = supabase_client.table("documents").select("*").execute()
    if not docs_response.data:
        raise RuntimeError(f"[Error] Failed to fetch documents: {docs_response.error}")

    documents = docs_response.data
    summary_data = []

    # Process each document
    for doc in documents:
        document_id = doc["document_id"]
        title = doc["title"]
        namespace = doc["namespace"]
        gcs_path = doc.get("gcs_path", "N/A")

        # Fetch corresponding chunks with enhanced metadata
        chunks_response = supabase_client.table("chunks").select(
            "pinecone_id", "document_name", "document_title", "page_numbers", "chunk_index",
            "jurisdiction", "prev_chunk_id", "next_chunk_id", "doc_type", "statute_section",
            "case_citation", "case_date", "case_parties"
        ).eq("document_id", document_id).execute()
        
        if not chunks_response.data:
            print(f"[Warning] No chunks found for document '{title}'")
            continue

        chunks = chunks_response.data
        
        # Analyze page coverage
        all_pages = []
        for chunk in chunks:
            if chunk.get("page_numbers"):
                all_pages.extend(chunk["page_numbers"])
        
        unique_pages = sorted(set(all_pages)) if all_pages else []
        page_coverage = len(unique_pages)
        page_range = f"{min(unique_pages)}-{max(unique_pages)}" if unique_pages else "N/A"

        # Fetch vector count from Pinecone
        try:
            index_stats = index.describe_index_stats(namespace=namespace)
            vector_count = len(chunks)  # Supabase already tracks chunks per document   
        except Exception as e:
            print(f"[Error] Failed to fetch vector count for '{title}': {e}")
            vector_count = "N/A"

        # Calculate average chunk length (if available)
        avg_chunk_length = "N/A"
        try:
            chunk_texts = supabase_client.table("chunks").select("content").eq("document_id", document_id).execute()
            if chunk_texts.data:
                total_length = sum(len(chunk.get("content", "")) for chunk in chunk_texts.data)
                avg_chunk_length = round(total_length / len(chunk_texts.data))
        except Exception as e:
            print(f"[Error] Failed to calculate average chunk length: {e}")

        # Check for navigation links between chunks
        chunks_with_prev = sum(1 for chunk in chunks if chunk.get("prev_chunk_id") is not None)
        chunks_with_next = sum(1 for chunk in chunks if chunk.get("next_chunk_id") is not None)
        navigation_coverage = f"{chunks_with_prev}/{len(chunks)} prev, {chunks_with_next}/{len(chunks)} next"
        
        # Check jurisdiction
        jurisdiction = chunks[0].get("jurisdiction", "N/A") if chunks else "N/A"
        
        # Check document type and type-specific fields
        doc_type = chunks[0].get("doc_type", "N/A") if chunks else "N/A"
        type_specific_fields = "N/A"
        
        if doc_type == "law":
            statute_sections = set(chunk.get("statute_section") for chunk in chunks if chunk.get("statute_section"))
            type_specific_fields = f"Statute sections: {len(statute_sections)}"
        elif doc_type == "precedent_case":
            case_citation = chunks[0].get("case_citation", "N/A") if chunks else "N/A"
            case_date = chunks[0].get("case_date", "N/A") if chunks else "N/A"
            type_specific_fields = f"Citation: {case_citation}, Date: {case_date}"
        
        # Append to summary data with enhanced metadata
        summary_data.append({
            "Document Title": title,
            "Document Filename": chunks[0].get("document_name", "N/A") if chunks else "N/A",
            "Namespace": namespace,
            "Jurisdiction": jurisdiction,
            "Doc Type": doc_type,
            "Type-Specific": type_specific_fields,
            "Number of Chunks": len(chunks),
            "Number of Vectors": vector_count,
            "Navigation": navigation_coverage,
            "Pages Covered": page_coverage,
            "Page Range": page_range,
            "Avg Chunk Length": avg_chunk_length,
            "Storage Path": gcs_path
        })

    return summary_data


def generate_summary_table(summary_data, output_file="summary.csv"):
    """
    Generate a comprehensive summary table from the fetched data, including enhanced metadata,
    and save it as a CSV file.
    """
    if not summary_data:
        print("[Info] No data available to generate a summary table.")
        return

    # Create a Pandas DataFrame
    df = pd.DataFrame(summary_data)
    
    # Calculate summary statistics
    total_docs = len(df)
    total_chunks = df["Number of Chunks"].sum()
    total_vectors = sum([x for x in df["Number of Vectors"] if x != "N/A"])
    
    # Format the DataFrame for display
    if "Avg Chunk Length" in df.columns:
        df["Avg Chunk Length"] = df["Avg Chunk Length"].apply(lambda x: f"{x:,} chars" if x != "N/A" else "N/A")

    # Print the enhanced summary table
    print("\n======================================================")
    print("             TEXAS LAWS PROCESSING SUMMARY         ")
    print("======================================================")
    print(f"Embedding Model: {EMBEDDING_MODEL} ({EMBEDDING_DIMENSIONS} dimensions)")
    print(f"Total Documents: {total_docs}")
    print(f"Total Chunks: {total_chunks:,}")
    print(f"Total Vectors: {total_vectors:,}")
    print("------------------------------------------------------")
    
    # Display the DataFrame with better formatting
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)
    print(df[["Document Title", "Document Filename", "Jurisdiction", "Doc Type", "Number of Chunks", "Navigation", "Pages Covered"]])
    
    # Save the full DataFrame to a CSV file
    df.to_csv(output_file, index=False)
    print(f"\n[Success] Detailed summary table saved to '{output_file}'.")
    
    # Generate additional targeted reports
    try:
        # Save a document-chunks mapping
        docs_chunks_file = output_file.replace(".csv", "_chunks.csv")
        df[["Document Title", "Document Filename", "Number of Chunks", "Pages Covered"]].to_csv(docs_chunks_file, index=False)
        print(f"[Success] Document-chunks mapping saved to '{docs_chunks_file}'.")
        
        # Save a page coverage report
        coverage_file = output_file.replace(".csv", "_coverage.csv")
        df[["Document Title", "Page Range", "Pages Covered"]].to_csv(coverage_file, index=False)
        print(f"[Success] Page coverage report saved to '{coverage_file}'.")
        
        # Save an embedding system report
        embedding_info = {
            "Embedding Model": [EMBEDDING_MODEL],
            "Vector Dimensions": [EMBEDDING_DIMENSIONS],
            "Total Vectors": [total_vectors],
            "Vector Database": ["Pinecone"],
            "Index Name": [PINECONE_INDEX_NAME],
            "Average Chunks Per Document": [round(total_chunks/total_docs) if total_docs > 0 else 0]
        }
        embedding_file = output_file.replace(".csv", "_embeddings.csv")
        pd.DataFrame(embedding_info).to_csv(embedding_file, index=False)
        print(f"[Success] Embedding system report saved to '{embedding_file}'.")
    except Exception as e:
        print(f"[Warning] Could not generate additional reports: {e}")


if __name__ == "__main__":
    try:
        # Fetch data and generate the summary table
        summary_data = fetch_document_summary()
        generate_summary_table(summary_data)
    except Exception as e:
        print(f"[Error] {e}")

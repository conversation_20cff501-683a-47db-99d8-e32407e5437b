"""
Document Graph Explorer - Flask-based web application 
that visualizes legal document relationships from Neo4j
for internal document processing and analysis
"""

import os
import json
from flask import Flask, render_template, request, jsonify
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Neo4j credentials
NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

app = Flask(__name__)
driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

@app.route('/')
def index():
    """Render the main dashboard page"""
    return render_template('index.html')

@app.route('/api/jurisdictions')
def get_jurisdictions():
    """Get all available jurisdictions"""
    with driver.session() as session:
        result = session.run("""
        MATCH (j:Jurisdiction)
        RETURN j.name AS name, j.code AS code
        ORDER BY j.name
        """)
        
        jurisdictions = [dict(record) for record in result]
        return jsonify(jurisdictions)

@app.route('/api/document_types')
def get_document_types():
    """Get all document types"""
    with driver.session() as session:
        result = session.run("""
        MATCH (dt:DocumentType)
        RETURN dt.name AS name, dt.description AS description
        ORDER BY dt.name
        """)
        
        doc_types = [dict(record) for record in result]
        return jsonify(doc_types)

@app.route('/api/documents')
def get_documents():
    """Get documents with filtering"""
    jurisdiction = request.args.get('jurisdiction')
    doc_type = request.args.get('doc_type')
    
    query = """
    MATCH (d:Document)
    WHERE 1=1
    """
    
    params = {}
    
    if jurisdiction:
        query += " AND d.jurisdiction = $jurisdiction"
        params['jurisdiction'] = jurisdiction
        
    if doc_type:
        query += " AND d.doc_type = $doc_type"
        params['doc_type'] = doc_type
        
    query += """
    RETURN d.document_id AS id, 
           d.title AS title,
           d.doc_type AS type,
           d.jurisdiction AS jurisdiction,
           d.created_at AS created_at
    ORDER BY d.created_at DESC
    LIMIT 100
    """
    
    with driver.session() as session:
        result = session.run(query, **params)
        documents = [dict(record) for record in result]
        return jsonify(documents)

@app.route('/api/document/<document_id>')
def get_document(document_id):
    """Get detailed information about a document"""
    with driver.session() as session:
        result = session.run("""
        MATCH (d:Document {document_id: $document_id})
        RETURN d
        """, document_id=document_id)
        
        record = result.single()
        if not record:
            return jsonify({"error": "Document not found"}), 404
            
        document = dict(record["d"])
        
        # Get outgoing citations
        result = session.run("""
        MATCH (d:Document {document_id: $document_id})-[:CITES]->(c:Citation)
        OPTIONAL MATCH (c)-[:RESOLVES_TO]->(cited:Document)
        RETURN c.text AS citation_text, 
               c.type AS citation_type,
               cited.document_id AS cited_document_id,
               cited.title AS cited_document_title
        """, document_id=document_id)
        
        outgoing_citations = [dict(record) for record in result]
        
        # Get incoming citations
        result = session.run("""
        MATCH (d:Document {document_id: $document_id})<-[:RESOLVES_TO]-(c:Citation)<-[:CITES]-(citing:Document)
        RETURN citing.document_id AS citing_document_id,
               citing.title AS citing_document_title,
               c.text AS citation_text
        """, document_id=document_id)
        
        incoming_citations = [dict(record) for record in result]
        
        return jsonify({
            "document": document,
            "outgoing_citations": outgoing_citations,
            "incoming_citations": incoming_citations
        })

@app.route('/api/graph/<document_id>')
def get_graph(document_id):
    """Get document network as a graph for visualization"""
    depth = request.args.get('depth', default=2, type=int)
    
    with driver.session() as session:
        result = session.run("""
        MATCH path = (d:Document {document_id: $document_id})-[*1..%d]-(related)
        RETURN path
        LIMIT 100
        """ % min(depth, 3), document_id=document_id)
        
        # Format results for D3.js visualization
        nodes = {}
        links = []
        
        for record in result:
            path = record["path"]
            
            # Process all nodes in the path
            path_nodes = path.nodes
            for node in path_nodes:
                if node.id not in nodes:
                    label = list(node.labels)[0]
                    title = node.get("title", "") or node.get("text", "")
                    
                    nodes[node.id] = {
                        "id": node.id,
                        "label": label,
                        "title": title,
                        "type": node.get("doc_type") or label
                    }
            
            # Process all relationships in the path
            path_rels = path.relationships
            for rel in path_rels:
                links.append({
                    "source": rel.start_node.id,
                    "target": rel.end_node.id,
                    "type": rel.type
                })
        
        return jsonify({
            "nodes": list(nodes.values()),
            "links": links
        })

@app.route('/api/stats')
def get_stats():
    """Get overall statistics about the document graph"""
    with driver.session() as session:
        # Get document counts by jurisdiction and type
        result = session.run("""
        MATCH (d:Document)
        RETURN d.jurisdiction AS jurisdiction, 
               d.doc_type AS doc_type,
               count(*) AS count
        ORDER BY count DESC
        """)
        
        doc_counts = [dict(record) for record in result]
        
        # Get citation statistics
        result = session.run("""
        MATCH (d:Document)-[r:CITES]->(c:Citation)
        RETURN count(r) AS total_citations,
               count(DISTINCT d) AS citing_documents,
               count(DISTINCT c) AS unique_citations,
               1.0 * count(r) / count(DISTINCT d) AS avg_citations_per_doc
        """)
        
        citation_stats = dict(result.single())
        
        # Get top cited documents
        result = session.run("""
        MATCH (d:Document)<-[:RESOLVES_TO]-(c:Citation)<-[:CITES]-(citing:Document)
        RETURN d.document_id AS document_id,
               d.title AS title,
               d.doc_type AS doc_type,
               d.jurisdiction AS jurisdiction,
               count(DISTINCT citing) AS cited_by_count
        ORDER BY cited_by_count DESC
        LIMIT 10
        """)
        
        top_cited = [dict(record) for record in result]
        
        return jsonify({
            "document_counts": doc_counts,
            "citation_stats": citation_stats,
            "top_cited_documents": top_cited
        })

if __name__ == '__main__':
    app.run(debug=True, port=5000)

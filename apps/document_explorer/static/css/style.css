/* Document Explorer Styles */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

.content-section {
    margin-bottom: 2rem;
}

/* Graph visualization styles */
#graph-container {
    background-color: #f8f9fa;
    position: relative;
}

.node {
    stroke: #fff;
    stroke-width: 1.5px;
}

.link {
    stroke-opacity: 0.6;
}

/* Legend styles */
.legend-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 5px;
    vertical-align: middle;
}

.legend-line {
    display: inline-block;
    width: 20px;
    height: 3px;
    margin-right: 5px;
    vertical-align: middle;
}

/* Card shadows and hover effects */
.card {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Table styles */
.table th {
    background-color: #f1f1f1;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Document detail modal styles */
.document-metadata {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.citation-item {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #4e79a7;
}

.citation-context {
    font-size: 0.9rem;
    font-style: italic;
    color: #666;
    margin-top: 0.5rem;
}

/* Stats display styles */
.stat-card {
    text-align: center;
    padding: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* Role-based styling */
.role-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.role-partner {
    background-color: #4e79a7;
    color: white;
}

.role-attorney {
    background-color: #f28e2c;
    color: white;
}

.role-paralegal {
    background-color: #e15759;
    color: white;
}

.role-staff {
    background-color: #76b7b2;
    color: white;
}

.role-client {
    background-color: #59a14f;
    color: white;
}

/**
 * Legal Document Explorer - Main Application
 * Handles document visualization, navigation, and data management
 */

// Main application controller
class DocumentExplorer {
    constructor() {
        this.currentSection = 'dashboard';
        this.documents = [];
        this.jurisdictions = [];
        this.documentTypes = [];
        this.currentDocument = null;
        this.graphSimulation = null;
        
        // Initialize components
        this.initializeNavigation();
        this.initializeEventListeners();
        this.loadInitialData();
    }
    
    /**
     * Initialize navigation between sections
     */
    initializeNavigation() {
        // Set up navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetSection = e.target.getAttribute('href').substring(1);
                this.navigateToSection(targetSection);
            });
        });
        
        // Check for hash in URL
        const hash = window.location.hash.substring(1);
        if (hash) {
            this.navigateToSection(hash);
        } else {
            this.navigateToSection('dashboard');
        }
    }
    
    /**
     * Navigate to a specific section
     */
    navigateToSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('d-none');
        });
        
        // Show the target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.remove('d-none');
            this.currentSection = sectionName;
            
            // Update active navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionName}`) {
                    link.classList.add('active');
                }
            });
            
            // Update URL hash
            window.location.hash = sectionName;
            
            // Load section-specific data
            this.loadSectionData(sectionName);
        }
    }
    
    /**
     * Initialize event listeners
     */
    initializeEventListeners() {
        // Document filter change handlers
        const jurisdictionFilter = document.getElementById('jurisdiction-filter');
        const docTypeFilter = document.getElementById('doc-type-filter');
        const searchDocuments = document.getElementById('search-documents');
        
        if (jurisdictionFilter) {
            jurisdictionFilter.addEventListener('change', () => {
                this.filterDocuments();
            });
        }
        
        if (docTypeFilter) {
            docTypeFilter.addEventListener('change', () => {
                this.filterDocuments();
            });
        }
        
        if (searchDocuments) {
            searchDocuments.addEventListener('input', () => {
                this.filterDocuments();
            });
        }
        
        // Graph visualization controls
        const visualizationDocument = document.getElementById('visualization-document');
        const visualizationDepth = document.getElementById('visualization-depth');
        
        if (visualizationDocument) {
            visualizationDocument.addEventListener('change', () => {
                const documentId = visualizationDocument.value;
                if (documentId) {
                    const depth = visualizationDepth.value;
                    this.loadGraphData(documentId, depth);
                } else {
                    this.clearGraph();
                }
            });
        }
        
        if (visualizationDepth) {
            visualizationDepth.addEventListener('change', () => {
                const documentId = visualizationDocument.value;
                if (documentId) {
                    const depth = visualizationDepth.value;
                    this.loadGraphData(documentId, depth);
                }
            });
        }
        
        // Document detail modal
        const detailModal = document.getElementById('document-detail-modal');
        if (detailModal) {
            detailModal.addEventListener('hidden.bs.modal', () => {
                this.currentDocument = null;
            });
            
            const viewGraphButton = document.getElementById('view-document-graph');
            if (viewGraphButton) {
                viewGraphButton.addEventListener('click', () => {
                    if (this.currentDocument) {
                        // Hide modal
                        const modal = bootstrap.Modal.getInstance(detailModal);
                        modal.hide();
                        
                        // Navigate to graph view
                        this.navigateToSection('visualization');
                        
                        // Select document in dropdown
                        const visualizationDocument = document.getElementById('visualization-document');
                        if (visualizationDocument) {
                            visualizationDocument.value = this.currentDocument.document_id;
                            // Trigger change event
                            const event = new Event('change');
                            visualizationDocument.dispatchEvent(event);
                        }
                    }
                });
            }
        }
    }
    
    /**
     * Load initial data
     */
    loadInitialData() {
        // Load jurisdictions
        this.loadJurisdictions();
        
        // Load document types
        this.loadDocumentTypes();
        
        // Load dashboard data
        this.loadSectionData('dashboard');
    }
    
    /**
     * Load section-specific data
     */
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'documents':
                this.loadDocuments();
                break;
            case 'visualization':
                this.loadDocumentsForVisualization();
                break;
            case 'stats':
                this.loadStatistics();
                break;
        }
    }
    
    /**
     * Load jurisdictions for filters
     */
    loadJurisdictions() {
        fetch('/api/jurisdictions')
            .then(response => response.json())
            .then(data => {
                this.jurisdictions = data;
                
                // Populate jurisdiction filter
                const jurisdictionFilter = document.getElementById('jurisdiction-filter');
                if (jurisdictionFilter) {
                    // Clear existing options except the first one
                    while (jurisdictionFilter.options.length > 1) {
                        jurisdictionFilter.remove(1);
                    }
                    
                    // Add jurisdiction options
                    data.forEach(jurisdiction => {
                        const option = document.createElement('option');
                        option.value = jurisdiction.name;
                        option.textContent = jurisdiction.name;
                        jurisdictionFilter.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading jurisdictions:', error);
            });
    }
    
    /**
     * Load document types for filters
     */
    loadDocumentTypes() {
        fetch('/api/document_types')
            .then(response => response.json())
            .then(data => {
                this.documentTypes = data;
                
                // Populate document type filter
                const docTypeFilter = document.getElementById('doc-type-filter');
                if (docTypeFilter) {
                    // Clear existing options except the first one
                    while (docTypeFilter.options.length > 1) {
                        docTypeFilter.remove(1);
                    }
                    
                    // Add document type options
                    data.forEach(docType => {
                        const option = document.createElement('option');
                        option.value = docType.name;
                        option.textContent = docType.description || docType.name;
                        docTypeFilter.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading document types:', error);
            });
    }

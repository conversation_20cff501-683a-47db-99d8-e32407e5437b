{"harvesting": {"enabled": true, "default_schedule": "0 2 * * *", "max_concurrent_jobs": 5, "retry_attempts": 3, "retry_delay_seconds": 300, "batch_size": 50, "rate_limit_delay": 0.5}, "jurisdictions": {"tx": {"name": "Texas", "enabled": true, "priority": "high", "schedule": "0 1 * * *", "courts": ["tex", "texapp1st", "texapp2nd", "texapp3rd", "texapp4th", "texapp5th", "texapp6th", "texapp7th", "texapp8th", "texapp9th", "texapp10th", "texapp11th", "texapp12th", "texapp13th", "texapp14th"], "practice_areas": ["personal_injury", "criminal_law", "business_law", "family_law", "employment_law", "real_estate", "constitutional_law", "tax_law"], "search_queries": {"personal_injury": ["negligence", "tort liability", "personal injury", "medical malpractice", "product liability", "premises liability", "motor vehicle accident", "wrongful death"], "criminal_law": ["criminal procedure", "constitutional rights", "search and seizure", "due process"], "tax_law": ["state tax", "property tax", "sales tax", "tax assessment", "tax appeal"]}, "date_range": {"start_date": "2020-01-01", "incremental_days": 30}, "max_cases_per_run": 100}, "oh": {"name": "Ohio", "enabled": true, "priority": "medium", "schedule": "0 3 * * *", "courts": ["ohio", "ohioapp1st", "ohioapp2nd", "ohioapp3rd", "ohioapp4th", "ohioapp5th", "ohioapp6th", "ohioapp7th", "ohioapp8th", "ohioapp9th", "ohioapp10th", "ohioapp11th", "ohioapp12th"], "practice_areas": ["personal_injury", "business_law", "real_estate", "criminal_law", "employment_law", "family_law", "constitutional_law", "tax_law"], "search_queries": {"personal_injury": ["negligence", "tort liability", "personal injury", "medical malpractice", "product liability", "premises liability", "motor vehicle accident", "wrongful death"], "criminal_law": ["criminal procedure", "constitutional rights", "search and seizure", "due process"], "business_law": ["contract", "commercial law", "business dispute", "corporate law"], "family_law": ["divorce", "custody", "marriage", "family law", "domestic relations"], "constitutional_law": ["constitutional", "amendment", "due process", "equal protection", "state constitution"], "tax_law": ["state tax", "property tax", "sales tax", "tax assessment", "tax appeal"]}, "date_range": {"start_date": "2020-01-01", "incremental_days": 30}, "max_cases_per_run": 50}, "ny": {"name": "New York", "enabled": true, "priority": "high", "schedule": "0 2 * * *", "courts": ["ny", "nyapp1st", "nyapp2nd", "nyapp3rd", "nyapp4th", "nyappterm", "nysupct"], "practice_areas": ["personal_injury", "criminal_law", "business_law", "real_estate", "employment_law", "family_law", "constitutional_law", "tax_law"], "search_queries": {"personal_injury": ["negligence", "tort liability", "personal injury", "medical malpractice", "product liability", "premises liability", "motor vehicle accident", "wrongful death"], "criminal_law": ["criminal procedure", "constitutional rights", "search and seizure", "due process"], "business_law": ["contract", "commercial law", "business dispute", "corporate law"], "constitutional_law": ["constitutional", "amendment", "due process", "equal protection", "state constitution", "civil rights"], "tax_law": ["state tax", "property tax", "sales tax", "tax assessment", "tax appeal", "income tax"]}, "date_range": {"start_date": "2020-01-01", "incremental_days": 30}, "max_cases_per_run": 75}, "fl": {"name": "Florida", "enabled": true, "priority": "high", "schedule": "0 5 * * *", "courts": ["fla", "flaapp1st", "flaapp2nd", "flaapp3rd", "flaapp4th", "flaapp5th"], "practice_areas": ["personal_injury", "criminal_law", "business_law", "real_estate", "employment_law", "family_law", "constitutional_law", "tax_law"], "search_queries": {"personal_injury": ["negligence", "tort liability", "personal injury", "medical malpractice", "product liability", "premises liability", "motor vehicle accident", "wrongful death"], "criminal_law": ["criminal procedure", "constitutional rights", "search and seizure", "due process"], "business_law": ["contract", "commercial law", "business dispute", "corporate law"], "constitutional_law": ["constitutional", "amendment", "due process", "equal protection", "state constitution", "civil rights"], "tax_law": ["state tax", "property tax", "sales tax", "tax assessment", "tax appeal"]}, "date_range": {"start_date": "2020-01-01", "incremental_days": 30}, "max_cases_per_run": 75}, "fed": {"name": "Federal", "enabled": true, "priority": "medium", "schedule": "0 6 * * 0", "courts": ["scotus", "ca1", "ca2", "ca3", "ca4", "ca5", "ca6", "ca7", "ca8", "ca9", "ca10", "ca11", "cadc", "cafc"], "practice_areas": ["constitutional_law", "immigration_law", "tax_law", "personal_injury", "criminal_law", "business_law", "employment_law"], "search_queries": {"constitutional_law": ["constitutional", "amendment", "due process", "equal protection", "first amendment", "fourth amendment"], "immigration_law": ["immigration", "deportation", "asylum", "visa", "naturalization"], "tax_law": ["tax", "irs", "revenue", "taxation", "tax court"], "personal_injury": ["negligence", "tort liability", "personal injury", "product liability"], "criminal_law": ["criminal procedure", "constitutional rights", "search and seizure", "due process"], "business_law": ["federal contract", "interstate commerce", "securities", "antitrust", "bankruptcy", "federal trade"], "employment_law": ["federal employment", "discrimination", "civil rights", "labor law", "EEOC", "workplace rights"]}, "date_range": {"start_date": "2020-01-01", "incremental_days": 7}, "max_cases_per_run": 50}}, "quality_thresholds": {"min_opinion_length": 100, "min_citation_count": 1, "required_fields": ["case_name", "court", "date_filed"]}, "storage": {"gcs_bucket": "legal-documents", "pinecone_namespace_prefix": "harvested", "neo4j_batch_size": 100}, "monitoring": {"log_level": "INFO", "metrics_enabled": true, "alert_on_failure_rate": 0.3, "alert_email": null}}
{"jurisdictions": {"Texas": {"code": "tx", "full_name": "Texas", "document_types": {"law": {"patterns": ["statute", "section", "§", "\\bchapter\\b", "\\btitle\\b", "\\bact\\b", "civil practice", "code"], "metadata_fields": ["statute_title", "statute_chapter", "statute_section"], "citation_format": "Tex. [Title] § [Section]"}, "precedent_case": {"patterns": ["case.*no\\.", "plaintiff", "defendant", "appellant", "v\\.", "vs\\.", "court of appeals", "supreme court"], "metadata_fields": ["case_number", "case_parties", "court", "case_date", "case_citation"], "citation_format": "[Party] v. [Party], [Reporter] [Page] ([Court] [Year])"}}}, "California": {"code": "ca", "full_name": "California", "document_types": {"law": {"patterns": ["statute", "section", "§", "\\bchapter\\b", "\\btitle\\b", "\\bact\\b", "california code"], "metadata_fields": ["statute_title", "statute_chapter", "statute_section"], "citation_format": "Cal. [Code] § [Section]"}, "precedent_case": {"patterns": ["case.*no\\.", "plaintiff", "defendant", "appellant", "v\\.", "vs\\.", "california court", "cal\\."], "metadata_fields": ["case_number", "case_parties", "court", "case_date", "case_citation"], "citation_format": "[Party] v. [Party], [Reporter] [Page] ([Court] [Year])"}}}, "New York": {"code": "ny", "full_name": "New York", "document_types": {"law": {"patterns": ["statute", "section", "§", "\\bchapter\\b", "\\btitle\\b", "\\bact\\b", "new york code"], "metadata_fields": ["statute_title", "statute_chapter", "statute_section"], "citation_format": "N.Y. [Code] § [Section]"}, "precedent_case": {"patterns": ["case.*no\\.", "plaintiff", "defendant", "appellant", "v\\.", "vs\\.", "new york court", "n\\.y\\."], "metadata_fields": ["case_number", "case_parties", "court", "case_date", "case_citation"], "citation_format": "[Party] v. [Party], [Reporter] [Page] ([Court] [Year])"}}}, "Federal": {"code": "fed", "full_name": "Federal", "document_types": {"law": {"patterns": ["u\\.s\\.c", "united states code", "§", "\\bchapter\\b", "\\btitle\\b", "\\bact\\b", "public law"], "metadata_fields": ["statute_title", "statute_chapter", "statute_section"], "citation_format": "[Title] U.S.C. § [Section]"}, "precedent_case": {"patterns": ["case.*no\\.", "plaintiff", "defendant", "appellant", "v\\.", "vs\\.", "supreme court", "circuit court", "f\\.\\d+d", "u\\.s\\."], "metadata_fields": ["case_number", "case_parties", "court", "case_date", "case_citation"], "citation_format": "[Party] v. [Party], [Reporter] [Page] ([Court] [Year])"}}}}, "document_types": {"law": {"description": "Statutory law, including codes, statutes, and legislative acts", "embedding_model": "voyage-3-large", "chunk_size": 1000, "chunk_overlap": 150, "required_metadata": ["jurisdiction", "statute_title", "statute_section"]}, "precedent_case": {"description": "Court cases establishing legal precedent", "embedding_model": "voyage-3-large", "chunk_size": 1500, "chunk_overlap": 200, "required_metadata": ["jurisdiction", "court", "case_parties", "case_citation"]}, "regulation": {"description": "Administrative regulations or rules issued by government agencies", "embedding_model": "voyage-3-large", "chunk_size": 1000, "chunk_overlap": 150, "required_metadata": ["jurisdiction", "issuing_agency", "regulation_number"]}, "administrative_ruling": {"description": "Agency decisions or interpretations of regulations", "embedding_model": "voyage-3-large", "chunk_size": 1200, "chunk_overlap": 150, "required_metadata": ["jurisdiction", "issuing_agency", "ruling_number", "ruling_date"]}}, "embedding_models": {"voyage-3-large": {"dimensions": 1024, "api_key_env": "VOYAGE_API_KEY", "model_name": "voyage-3-large"}, "openai-text-embedding-3-large": {"dimensions": 3072, "api_key_env": "OPENAI_API_KEY", "model_name": "text-embedding-3-large"}}, "quality_thresholds": {"min_success_rate": 90.0, "min_metadata_completeness": 80.0, "max_empty_fields_percentage": 10.0, "min_citation_extraction_rate": 2.0}, "neo4j_config": {"document_labels": {"law": ["Document", "Law"], "precedent_case": ["Document", "Case"], "regulation": ["Document", "Regulation"], "administrative_ruling": ["Document", "<PERSON>uling"]}, "relationship_types": {"cites": "CITES", "amends": "AMENDS", "overturns": "OVERTURNS", "implements": "IMPLEMENTS", "interprets": "INTERPRETS"}}}
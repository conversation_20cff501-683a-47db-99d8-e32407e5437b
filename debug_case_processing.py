#!/usr/bin/env python
"""
Debug Case Processing Script

This script provides a focused way to debug the entire case processing pipeline
for a single Court Listener case, with detailed output for each step.
"""

import os
import sys
import json
import logging
import argparse
import traceback
from typing import Dict, Optional
from datetime import datetime
from dotenv import load_dotenv

# Define APIError for compatibility
try:
    from postgrest.exceptions import APIError
except ImportError:
    # Define a generic APIError if the specific one isn't available
    class APIError(Exception):
        def __init__(self, message="API Error", code=None, details=None):
            self.message = message
            self.code = code
            self.details = details
            super().__init__(self.message)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('case_processing_debug.log')
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import processing modules
from src.processing.providers.court_listener_api import (
    get_cluster_by_id, 
    get_opinions_by_cluster,
    transform_cluster_for_processing
)
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.neo4j_helper import flatten_for_neo4j
from src.processing.storage.gcs_helper import store_case_document, store_case_json


def setup_connectors():
    """Initialize and return all storage connectors"""
    # Initialize Supabase connector for database operations
    supabase = SupabaseConnector()
    logger.info("Initialized Supabase connector")
    
    # Initialize Neo4j connector for graph operations
    try:
        from src.processing.storage.neo4j_connector import Neo4jConnector
        neo4j = Neo4jConnector()
        logger.info("Initialized Neo4j connector")
    except Exception as e:
        logger.error(f"Failed to initialize Neo4j connector: {str(e)}")
        neo4j = None
    
    # Initialize Pinecone connector for vector embeddings
    try:
        from src.processing.storage.pinecone_connector import PineconeConnector
        pinecone = PineconeConnector()
        logger.info("Initialized Pinecone connector")
    except Exception as e:
        logger.error(f"Failed to initialize Pinecone connector: {str(e)}")
        pinecone = None
    
    return {
        "supabase": supabase,
        "neo4j": neo4j,
        "pinecone": pinecone
    }


def fetch_case_data(case_id: str) -> Dict:
    """Fetch case data from Court Listener API"""
    logger.info(f"Fetching case data for ID: {case_id}")
    
    try:
        # Fetch the cluster data
        cluster_data = get_cluster_by_id(case_id)
        if not cluster_data:
            logger.error(f"Failed to fetch cluster data for case {case_id}")
            return None
            
        logger.info(f"Successfully fetched cluster data for case {case_id}")
        
        # Save raw data for debugging
        with open(f"case_{case_id}_raw.json", 'w') as f:
            json.dump(cluster_data, f, indent=2)
        logger.info(f"Saved raw case data to case_{case_id}_raw.json")
        
        # Transform the data for processing
        transformed_data = transform_cluster_for_processing(cluster_data)
        
        # Save transformed data for debugging
        with open(f"case_{case_id}_transformed.json", 'w') as f:
            json.dump(transformed_data, f, indent=2)
        logger.info(f"Saved transformed case data to case_{case_id}_transformed.json")
        
        # Also fetch opinions
        opinions_data = get_opinions_by_cluster(case_id)
        if opinions_data and "results" in opinions_data:
            logger.info(f"Found {len(opinions_data['results'])} opinions for case {case_id}")
            
            # Attach opinions to transformed data
            transformed_data["opinions"] = opinions_data["results"]
            
            # Save opinions data for debugging
            with open(f"case_{case_id}_opinions.json", 'w') as f:
                json.dump(opinions_data, f, indent=2)
            logger.info(f"Saved opinions data to case_{case_id}_opinions.json")
        else:
            logger.warning(f"No opinions found for case {case_id}")
            transformed_data["opinions"] = []
        
        # Attach raw data
        transformed_data["raw_data"] = cluster_data
        
        return transformed_data
    except Exception as e:
        logger.error(f"Error fetching case data: {str(e)}", exc_info=True)
        return None


def process_supabase(connectors: Dict, case_data: Dict, jurisdiction: str) -> bool:
    """Process and store case data in Supabase"""
    logger.info(f"Processing case {case_data['id']} for Supabase storage")
    
    try:
        supabase = connectors["supabase"]
        
        # Format case data for Supabase
        case_insert_data = {
            'id': case_data['id'],
            'case_name': case_data.get('case_name', ''),
            'case_name_full': case_data.get('case_name_full', ''),
            'court_id': case_data.get('court', {}).get('id') or case_data.get('court_id', ''),
            'jurisdiction': jurisdiction,
            'date_filed': case_data.get('date_filed', ''),
            'citation_count': case_data.get('citation_count', 0),
            'precedential': case_data.get('precedential_status', 'Published') == 'Published',
            'docket_id': case_data.get('docket', {}).get('id') or case_data.get('docket_id', ''),
            'source': 'court_listener',
            'source_id': f"cl_{case_data['id']}",
            'status': 'processing',
            'cluster_id': case_data['id'],
            'metadata_quality': 'Basic',
            'document_quality': 'Standard',
            'source_url': case_data.get('resource_uri') or case_data.get('absolute_url', '')
        }
        
        # Generate a batch ID if needed for tracking
        batch_id = f"debug_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Check if record already exists
        existing_case = None
        if hasattr(supabase, "get_case"):
            existing_case = supabase.get_case(case_data['id'])
        else:
            # Try using the table query interface
            response = supabase.client.table("cases").select("*").eq("id", case_data['id']).execute()
            if response.data and len(response.data) > 0:
                existing_case = response.data[0]
                
        # Handle insert or update based on existence
        if existing_case:
            logger.info(f"Case {case_data['id']} already exists in Supabase, updating metadata")
            # Update the case status to processing
            update_data = {
                'status': 'processing',  # Using the correct column name from schema
                'updated_at': datetime.utcnow().isoformat()  # Using the correct column name from schema
            }
            
            update_success = False
            try:
                if hasattr(supabase, "update_case_metadata"):
                    update_success = supabase.update_case_metadata(case_data['id'], update_data)
                    if update_success:
                        logger.info(f"Updated case status using update_case_metadata: processing")
                else:
                    # Try direct table interface
                    response = supabase.client.table("cases") \
                        .update(update_data) \
                        .eq("id", case_data['id']) \
                        .execute()
                    update_success = response.data and len(response.data) > 0
                    if update_success:
                        logger.info(f"Updated case status using direct table access: processing")
            except Exception as e:
                logger.warning(f"Failed to update case status: {str(e)}")
                
            return update_success
        else:
            # Store in Supabase (new record)
            if hasattr(supabase, "store_case"):
                stored_case = supabase.store_case(case_insert_data, batch_id)
                success = stored_case is not None
            else:
                # Try direct table interface
                response = supabase.client.table("cases").insert(case_insert_data).execute()
                success = response.data and len(response.data) > 0
                
            if success:
                logger.info(f"Successfully stored case {case_data['id']} in Supabase")
                return True
            else:
                logger.error(f"Failed to store case {case_data['id']} in Supabase")
                return False
    except Exception as e:
        logger.error(f"Error in Supabase processing: {str(e)}", exc_info=True)
        return False


def process_neo4j(connectors: Dict, case_data: Dict, jurisdiction: str) -> bool:
    """Process and store case data in Neo4j"""
    logger.info(f"Processing case {case_data['id']} for Neo4j storage")
    
    try:
        neo4j = connectors["neo4j"]
        if not neo4j:
            logger.error("Neo4j connector not available")
            return False
        
        # Prepare data for Neo4j
        neo4j_id = f"cl_{case_data['id']}"
        neo4j_case_data = {
            'id': neo4j_id,
            'original_id': case_data['id'],
            'source_id': neo4j_id,
            'name': case_data.get('case_name', ''),
            'case_name': case_data.get('case_name', ''),
            'court_id': case_data.get('court', {}).get('id') or case_data.get('court_id', ''),
            'jurisdiction': jurisdiction,
            'date_filed': case_data.get('date_filed', ''),
            'doc_type': 'case'
        }
        
        # Extract year from date_filed if available
        if "date_filed" in neo4j_case_data and neo4j_case_data["date_filed"]:
            try:
                if isinstance(neo4j_case_data["date_filed"], str):
                    neo4j_case_data["year"] = int(neo4j_case_data["date_filed"].split("-")[0])
            except Exception:
                # Set current year as fallback
                neo4j_case_data["year"] = datetime.now().year
        
        # Flatten data for Neo4j compatibility
        flattened_data = flatten_for_neo4j(neo4j_case_data)
        
        # Store in Neo4j
        result = neo4j.create_case(flattened_data)
        if result:
            logger.info(f"Successfully stored case {case_data['id']} in Neo4j")
            return True
        else:
            logger.error(f"Failed to store case {case_data['id']} in Neo4j")
            return False
    except Exception as e:
        logger.error(f"Error in Neo4j processing: {str(e)}", exc_info=True)
        return False


def process_gcs(connectors: Dict, case_data: Dict, jurisdiction: str) -> Dict:
    """Process and store case text and metadata in GCS"""
    logger.info(f"Processing case {case_data['id']} for GCS storage")
    
    try:
        supabase = connectors["supabase"]
        
        # Extract case text
        case_text = case_data.get("case_text", "")
        
        # If no text is available, create a minimal representation
        if not case_text:
            case_text = f"Case: {case_data.get('case_name', '')}\n"
            case_text += f"Court: {case_data.get('court_id', '')}\n"
            case_text += f"Date Filed: {case_data.get('date_filed', '')}\n"
            case_text += f"Jurisdiction: {jurisdiction}\n\n"
            
            # Add opinion data if available
            if "opinions" in case_data and case_data["opinions"]:
                for i, opinion in enumerate(case_data["opinions"]):
                    case_text += f"--- OPINION {i+1} ---\n"
                    case_text += f"Author: {opinion.get('author', 'Unknown')}\n"
                    case_text += f"Type: {opinion.get('type', 'Unknown')}\n"
                    
                    if opinion.get('plain_text'):
                        case_text += f"Text: {opinion.get('plain_text')[:500]}...\n\n"
                    else:
                        case_text += "No text available for this opinion.\n\n"
        
        # Extract year for path organization
        year = None
        date_filed = case_data.get('date_filed')
        if date_filed:
            try:
                if isinstance(date_filed, str):
                    year = date_filed.split("-")[0]
            except Exception:
                pass
        
        # Store text document
        gcs_text_path = store_case_document(
            case_id=case_data['id'],
            content=case_text,
            jurisdiction=jurisdiction,
            doc_type="full_text",
            year=year
        )
        
        # Store metadata document
        gcs_metadata_path = store_case_json(
            case_id=case_data['id'],
            data=case_data["raw_data"],
            jurisdiction=jurisdiction,
            doc_type="metadata",
            year=year
        )
        
        logger.info(f"Stored case text at {gcs_text_path}")
        logger.info(f"Stored case metadata at {gcs_metadata_path}")
        
        # Update Supabase with GCS paths
        gcs_bucket = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        gcs_data = {
            "gcs_path": gcs_text_path,  # This matches the column name in the schema
            "source_url": f"gs://{gcs_bucket}/{gcs_text_path}",  # Using an existing field for the URL
            "status": "stored"  # Use status field which exists in the schema
        }
        
        # Update case record with GCS paths using the appropriate method
        try:
            # Use a more generic approach to update Supabase data
            update_succeeded = False
            
            if hasattr(supabase, "update_case_metadata"):
                try:
                    update_succeeded = supabase.update_case_metadata(case_data['id'], gcs_data)
                except Exception as upd_err:
                    logger.warning(f"update_case_metadata failed: {str(upd_err)}")
                    
            # Fallback to other methods if the first one failed
            if not update_succeeded and hasattr(supabase, "update_record"):
                try:
                    supabase.update_record("cases", {"id": case_data['id']}, gcs_data)
                    update_succeeded = True
                except Exception as upd_err:
                    logger.warning(f"update_record failed: {str(upd_err)}")
            
            # Try direct table interface as a last resort
            if not update_succeeded:
                try:
                    # Try with table method
                    if hasattr(supabase, "table"):
                        response = supabase.table("cases").update(gcs_data).eq("id", case_data['id']).execute()
                        update_succeeded = True
                    # Or try with client.table if available
                    elif hasattr(supabase, "client") and hasattr(supabase.client, "table"):
                        response = supabase.client.table("cases").update(gcs_data).eq("id", case_data['id']).execute()
                        update_succeeded = True
                except Exception as upd_err:
                    logger.warning(f"Direct table update failed: {str(upd_err)}")
                    
            if update_succeeded:
                logger.info(f"Updated Supabase record with GCS paths")
            else:
                logger.warning("Could not update Supabase with GCS paths using any available method")
                
        except Exception as e:
            logger.error(f"Failed to update Supabase with GCS paths: {str(e)}")
            # Continue processing even if the update fails
        
        return gcs_data
    except Exception as e:
        logger.error(f"Error in GCS processing: {str(e)}", exc_info=True)
        return {}


def process_pinecone(connectors: Dict, case_data: Dict, jurisdiction: str, gcs_data: Dict) -> bool:
    """Process and store case embedding in Pinecone"""
    logger.info(f"Processing case {case_data['id']} for Pinecone storage")
    
    try:
        pinecone = connectors["pinecone"]
        if not pinecone:
            logger.error("Pinecone connector not available")
            return False
        
        # Prepare the embedding data
        pinecone_id = f"cl_{case_data['id']}"
        
        # Get the text to embed
        case_text = case_data.get("case_text", "")
        if not case_text:
            case_text = f"Case: {case_data.get('case_name', '')}"
        
        # Extract year for metadata
        year = None
        date_filed = case_data.get('date_filed')
        if date_filed:
            try:
                if isinstance(date_filed, str):
                    year = date_filed.split("-")[0]
            except Exception:
                pass
        
        # Create metadata for the embedding
        metadata = {
            # Case identification information
            "case_id": pinecone_id,
            "original_id": str(case_data['id']),
            "source_id": pinecone_id,
            "case_name": case_data.get('case_name', ''),
            
            # Jurisdiction and court information
            "jurisdiction": jurisdiction,
            "court_id": case_data.get('court', {}).get('id') or case_data.get('court_id', ''),
            
            # Temporal information
            "date_filed": str(date_filed) if date_filed else "",
            "year": str(year) if year else "",
            "processing_time": datetime.utcnow().isoformat(),
            
            # Document classification
            "document_type": "case_summary",
            "precedential": case_data.get('precedential_status', 'Published') == 'Published',
            "citation_count": int(case_data.get('citation_count', 0)),
            
            # Storage locations for cross-referencing
            "text_path": gcs_data.get("text_path", ""),
            "metadata_path": gcs_data.get("metadata_path", ""),
            
            # For user-specific access control
            "role_access": ["partner", "attorney", "paralegal", "staff"]
        }
        
        # Create a simple dummy vector embedding for testing
        # In a real implementation, you would use a model to generate embeddings
        # This is just a placeholder for testing the storage pipeline
        vector = [0.1] * 1024  # 1024-dimensional vector filled with 0.1
        
        # Create embedding ID
        embedding_id = f"{pinecone_id}_summary"
        
        # Store in Pinecone using store_embedding method (not index_document)
        result = pinecone.store_embedding(
            vector=vector,
            id=embedding_id,
            metadata=metadata,
            jurisdiction=jurisdiction,
            doc_type="case"
        )
        
        # Update Supabase with embedding ID
        # Use the correct method for updating case records in Supabase
        try:
            # Using pinecone_id column which exists in the schema
            update_data = {
                "pinecone_id": embedding_id,
                "status": "completed"
            }
            
            # Try multiple methods to update Supabase
            update_succeeded = False
            
            if hasattr(connectors["supabase"], "update_case_metadata"):
                try:
                    update_succeeded = connectors["supabase"].update_case_metadata(case_data['id'], update_data)
                    if update_succeeded:
                        logger.info(f"Updated Pinecone ID using update_case_metadata: {embedding_id}")
                except Exception as e1:
                    logger.warning(f"update_case_metadata failed for Pinecone ID: {str(e1)}")
                    
            # Try update_case as fallback
            if not update_succeeded and hasattr(connectors["supabase"], "update_case"):
                try:
                    connectors["supabase"].update_case(case_data['id'], update_data)
                    update_succeeded = True
                    logger.info(f"Updated Pinecone ID using update_case: {embedding_id}")
                except Exception as e2:
                    logger.warning(f"update_case failed for Pinecone ID: {str(e2)}")
                    
            # Try update_record as fallback
            if not update_succeeded and hasattr(connectors["supabase"], "update_record"):
                try:
                    connectors["supabase"].update_record("cases", {"id": case_data['id']}, update_data)
                    update_succeeded = True
                    logger.info(f"Updated Pinecone ID using update_record: {embedding_id}")
                except Exception as e3:
                    logger.warning(f"update_record failed for Pinecone ID: {str(e3)}")
                    
            # Try direct table approach as last resort
            if not update_succeeded:
                try:
                    if hasattr(connectors["supabase"], "table"):
                        response = connectors["supabase"].table("cases").update(update_data).eq("id", case_data['id']).execute()
                        update_succeeded = bool(response.data)
                        logger.info(f"Updated Pinecone ID using direct table access: {embedding_id}")
                    elif hasattr(connectors["supabase"], "client") and hasattr(connectors["supabase"].client, "table"):
                        response = connectors["supabase"].client.table("cases").update(update_data).eq("id", case_data['id']).execute()
                        update_succeeded = bool(response.data)
                        logger.info(f"Updated Pinecone ID using client table access: {embedding_id}")
                except Exception as e4:
                    logger.warning(f"Direct table update failed for Pinecone ID: {str(e4)}")
                    
            if not update_succeeded:
                logger.warning("Could not update Supabase with Pinecone ID using any available method")
        except Exception as e:
            logger.warning(f"Could not update Supabase with embedding ID: {str(e)}")
        
        logger.info(f"Successfully stored embedding for case {case_data['id']} in Pinecone")
        return True
    except Exception as e:
        logger.error(f"Error in Pinecone processing: {str(e)}", exc_info=True)
        return False


def process_case(case_id: str, jurisdiction: str = "tx"):
    """Process a single case through all storage systems"""
    logger.info(f"Starting debug processing for case {case_id}")
    
    # Initialize connectors
    connectors = setup_connectors()
    
    # Fetch case data
    case_data = fetch_case_data(case_id)
    if not case_data:
        logger.error(f"Failed to fetch data for case {case_id}, aborting.")
        return
    
    # Process each storage system
    supabase_success = process_supabase(connectors, case_data, jurisdiction)
    neo4j_success = process_neo4j(connectors, case_data, jurisdiction)
    gcs_data = process_gcs(connectors, case_data, jurisdiction)
    pinecone_success = process_pinecone(connectors, case_data, jurisdiction, gcs_data)
    
    # Print summary
    logger.info("=" * 80)
    logger.info(f"CASE PROCESSING SUMMARY: {case_id}")
    logger.info("=" * 80)
    logger.info(f"Supabase: {'SUCCESS' if supabase_success else 'FAILURE'}")
    logger.info(f"Neo4j: {'SUCCESS' if neo4j_success else 'FAILURE'}")
    logger.info(f"GCS: {'SUCCESS' if gcs_data else 'FAILURE'}")
    logger.info(f"Pinecone: {'SUCCESS' if pinecone_success else 'FAILURE'}")
    logger.info("=" * 80)
    
    # Update the status in Supabase based on overall result
    overall_success = supabase_success and (neo4j_success or True) and bool(gcs_data) and (pinecone_success or True)
    status = "completed" if overall_success else "partial"
    
    # Update Supabase with final status
    if supabase_success:
        try:
            supabase = connectors["supabase"]
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow().isoformat()
            }
            update_succeeded = False
            
            # Try each update method in sequence with proper error handling
            if hasattr(supabase, "update_case_metadata"):
                try:
                    update_succeeded = supabase.update_case_metadata(case_id, update_data)
                    if update_succeeded:
                        logger.info(f"Updated case status using update_case_metadata: {status}")
                except Exception as e1:
                    logger.warning(f"update_case_metadata failed: {str(e1)}")
            
            # Try update_case as fallback
            if not update_succeeded and hasattr(supabase, "update_case"):
                try:
                    supabase.update_case(case_id, update_data)
                    update_succeeded = True
                    logger.info(f"Updated case status using update_case: {status}")
                except Exception as e2:
                    logger.warning(f"update_case failed: {str(e2)}")
            
            # Try update_record as fallback
            if not update_succeeded and hasattr(supabase, "update_record"):
                try:
                    supabase.update_record("cases", {"id": case_id}, update_data)
                    update_succeeded = True
                    logger.info(f"Updated case status using update_record: {status}")
                except Exception as e3:
                    logger.warning(f"update_record failed: {str(e3)}")
            
            # Try direct table approach as last resort
            if not update_succeeded:
                try:
                    if hasattr(supabase, "table"):
                        supabase.table("cases").update(update_data).eq("id", case_id).execute()
                        update_succeeded = True
                        logger.info(f"Updated case status using direct table access: {status}")
                    elif hasattr(supabase, "client") and hasattr(supabase.client, "table"):
                        supabase.client.table("cases").update(update_data).eq("id", case_id).execute()
                        update_succeeded = True
                        logger.info(f"Updated case status using client table access: {status}")
                except Exception as e4:
                    logger.warning(f"Direct table update failed: {str(e4)}")
            
            if not update_succeeded:
                logger.warning(f"Could not update final case status in Supabase to {status}")
                
        except Exception as e:
            logger.error(f"Failed to update case status in Supabase: {str(e)}")
    
    logger.info(f"Case processing {'succeeded' if overall_success else 'partially succeeded'}")
    return overall_success


def main():
    parser = argparse.ArgumentParser(description="Debug processing for a single Court Listener case")
    parser.add_argument("case_id", help="Court Listener case ID to process")
    parser.add_argument("--jurisdiction", default="tx", help="Jurisdiction (default: tx)")
    
    args = parser.parse_args()
    
    success = process_case(args.case_id, args.jurisdiction)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
Diagnostic script to test Court Listener API v4 search and compare response format.
"""

import os
import json
import requests
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """Test Court Listener API v4 search endpoint."""
    api_key = os.getenv("COURTLISTENER_API_KEY")
    if not api_key:
        logger.error("COURTLISTENER_API_KEY not found in environment.")
        return False
    
    # Base URL for Court Listener API v4
    base_url = "https://www.courtlistener.com/api/rest/v4"
    
    # Headers
    headers = {
        "Authorization": f"Token {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test search for Texas cases
    logger.info("Testing v4 search API for Texas cases...")
    search_params = {
        "q": "court_id:tex",
        "type": "o",  # Opinions
        "page_size": 5,
        "ordering": "-citeCount",  # Highest cited first
        "stat_precedential": "on",  # Published opinions
    }
    
    try:
        search_url = f"{base_url}/search/"
        logger.info(f"Making request to {search_url} with params: {search_params}")
        response = requests.get(search_url, headers=headers, params=search_params)
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Search successful! Count: {data.get('count', 0)}")
            
            # Save the response to a file for examination
            with open("v4_search_response.json", "w") as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved response to v4_search_response.json")
            
            # Check for results and display some information
            results = data.get("results", [])
            if results:
                logger.info(f"Found {len(results)} results")
                for i, result in enumerate(results[:3]):  # Show first 3 only
                    logger.info(f"Result {i+1}:")
                    logger.info(f"  ID: {result.get('id')}")
                    logger.info(f"  Case Name: {result.get('caseName')}")
                    logger.info(f"  Court: {result.get('court')}")
                    logger.info(f"  Date Filed: {result.get('dateFiled')}")
                    # Check for cluster ID or similar reference
                    cluster_id = result.get('cluster', {}).get('id') if isinstance(result.get('cluster'), dict) else result.get('cluster')
                    logger.info(f"  Cluster ID: {cluster_id}")
            else:
                logger.warning("No results found!")
                
            return True
        else:
            logger.error(f"Search failed with status code {response.status_code}")
            logger.error(f"Response: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Error testing Court Listener API: {str(e)}", exc_info=True)
        return False
        
if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python
"""
Debug Script for Court Listener API

This script tests the Court Listener API endpoints directly to help diagnose
integration issues and understand the API response structure.
"""

import os
import json
import logging
import argparse
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path=dotenv_path)
print(f"Loaded .env file: {load_dotenv(dotenv_path=dotenv_path)}")

# Check for API key
api_key = os.getenv("COURTLISTENER_API_KEY")
if not api_key:
    logger.error("Missing COURTLISTENER_API_KEY environment variable")
    exit(1)
else:
    logger.info(f"Using API key: {api_key[:5]}...{api_key[-5:]}")

class CourtListenerDebugger:
    """Debug tool for testing Court Listener API endpoints directly."""
    
    def __init__(self):
        """Initialize the debugger with the API key."""
        self.api_key = api_key
        self.base_url = "https://www.courtlistener.com/api/rest/v4"
        self.headers = {
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json"
        }
        logger.info("Initialized Court Listener API debugger")
    
    def get_cluster_v4(self, cluster_id: str) -> dict:
        """
        Test fetching a specific cluster using the v4 API.
        
        Args:
            cluster_id: ID of the cluster to fetch
            
        Returns:
            Cluster data
        """
        url = f"{self.base_url}/clusters/{cluster_id}/"
        logger.info(f"Testing v4 API endpoint: {url}")
        
        try:
            response = requests.get(url, headers=self.headers)
            logger.info(f"Response status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Successfully retrieved cluster data with {len(data)} fields")
                return data
            else:
                logger.error(f"Error response: {response.text}")
                return {}
        except Exception as e:
            logger.error(f"Exception while fetching cluster: {str(e)}", exc_info=True)
            return {}
    
    def search_v4(self, query: str, court_id: str = None, count: int = 5) -> dict:
        """
        Test the v4 search API endpoint.
        
        Args:
            query: Search query
            court_id: Optional court ID to filter by
            count: Number of results to return
            
        Returns:
            Search results
        """
        url = f"{self.base_url}/search/"
        
        # Build the query for v4 API
        query_params = {
            "q": query,
            "type": "o",  # Search for opinions
            "page_size": count,
            "ordering": "-citeCount"
        }
        
        # Add court filter if provided
        if court_id:
            # Use field-specific search format
            query_params["q"] = f"{query} AND court_id:{court_id}"
        
        logger.info(f"Testing v4 search API with params: {query_params}")
        
        try:
            response = requests.get(url, headers=self.headers, params=query_params)
            logger.info(f"Response status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Successfully retrieved {len(data.get('results', []))} search results")
                return data
            else:
                logger.error(f"Error response: {response.text}")
                return {}
        except Exception as e:
            logger.error(f"Exception while searching: {str(e)}", exc_info=True)
            return {}
    
    def get_opinions_by_cluster(self, cluster_id: str) -> dict:
        """
        Test fetching opinions associated with a cluster.
        
        Args:
            cluster_id: ID of the cluster
            
        Returns:
            Opinions data
        """
        url = f"{self.base_url}/opinions/"
        
        params = {
            "cluster_id": cluster_id,
            "page_size": 20
        }
        
        logger.info(f"Testing opinions API with params: {params}")
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            logger.info(f"Response status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Successfully retrieved {len(data.get('results', []))} opinions")
                return data
            else:
                logger.error(f"Error response: {response.text}")
                return {}
        except Exception as e:
            logger.error(f"Exception while fetching opinions: {str(e)}", exc_info=True)
            return {}
    
    def save_response_to_file(self, data: dict, filename: str) -> None:
        """
        Save API response data to a JSON file.
        
        Args:
            data: Response data to save
            filename: Output filename
        """
        if not data:
            logger.warning(f"No data to save to {filename}")
            return
        
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved response data to {filename}")
        except Exception as e:
            logger.error(f"Failed to save response to {filename}: {str(e)}")

def main():
    """Main entry point for the debug script."""
    parser = argparse.ArgumentParser(description="Debug Court Listener API integration")
    parser.add_argument("--cluster-id", help="Test fetching a specific cluster ID")
    parser.add_argument("--search", help="Test search API with the given query")
    parser.add_argument("--court", help="Court ID to filter search by")
    parser.add_argument("--save", action="store_true", help="Save response data to JSON files")
    
    args = parser.parse_args()
    
    debugger = CourtListenerDebugger()
    
    # Test specific cluster fetch
    if args.cluster_id:
        logger.info(f"Testing cluster fetch for ID: {args.cluster_id}")
        cluster_data = debugger.get_cluster_v4(args.cluster_id)
        
        if cluster_data:
            logger.info(f"Cluster keys: {list(cluster_data.keys())}")
            
            # Show some useful fields
            if "case_name" in cluster_data:
                logger.info(f"Case name: {cluster_data['case_name']}")
            if "date_filed" in cluster_data:
                logger.info(f"Date filed: {cluster_data['date_filed']}")
            
            if args.save:
                debugger.save_response_to_file(cluster_data, "clusters_api_response.json")
            
            # Also test getting opinions for this cluster
            opinions_data = debugger.get_opinions_by_cluster(args.cluster_id)
            if opinions_data and opinions_data.get("results"):
                logger.info(f"Found {len(opinions_data['results'])} opinions for cluster {args.cluster_id}")
                if args.save:
                    debugger.save_response_to_file(opinions_data, "opinions_api_response.json")
    
    # Test search API
    if args.search:
        logger.info(f"Testing search API with query: {args.search}")
        search_data = debugger.search_v4(args.search, args.court)
        
        if search_data and search_data.get("results"):
            logger.info(f"Search returned {len(search_data['results'])} results")
            
            # Show first few results
            for i, result in enumerate(search_data["results"][:3]):
                logger.info(f"Result {i+1}: {result.get('caseName', 'Unknown')} ({result.get('court_id', 'Unknown')})")
            
            if args.save:
                debugger.save_response_to_file(search_data, "search_api_response.json")
    
    # If no specific test was requested, run a simple test
    if not (args.cluster_id or args.search):
        logger.info("No specific test requested, running a search test for 'negligence' in Texas courts")
        search_data = debugger.search_v4("negligence", "tex")
        
        if search_data and search_data.get("results"):
            logger.info(f"Search returned {len(search_data['results'])} results")
            
            # Get the first result's cluster ID
            if search_data["results"]:
                first_result = search_data["results"][0]
                cluster_id = first_result.get("id")
                
                if cluster_id:
                    logger.info(f"Testing cluster fetch for first result ID: {cluster_id}")
                    cluster_data = debugger.get_cluster_v4(cluster_id)
                    
                    if cluster_data:
                        logger.info(f"Successfully fetched cluster {cluster_id}: {cluster_data.get('case_name', 'Unknown')}")
                        
                        # Also test getting opinions
                        opinions_data = debugger.get_opinions_by_cluster(cluster_id)
                        if opinions_data and opinions_data.get("results"):
                            logger.info(f"Found {len(opinions_data['results'])} opinions for cluster {cluster_id}")
            
            if args.save:
                debugger.save_response_to_file(search_data, "search_api_response.json")

if __name__ == "__main__":
    main()

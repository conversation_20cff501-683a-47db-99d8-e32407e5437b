#!/usr/bin/env python
"""
Debug script for GCS (Google Cloud Storage) data storage issues
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('gcs_debug.log')
    ]
)
logger = logging.getLogger(__name__)

# Import the GCS connectors
try:
    # Try to import the real connector first
    from src.processing.storage.gcs_connector import GCSConnector
    use_mock = False
except Exception as e:
    # If that fails, use the mock connector
    logger.warning(f"Failed to import real GCS connector: {str(e)}. Using mock connector instead.")
    from src.processing.storage.gcs_connector_mock import GCSConnector
    use_mock = True


def test_gcs_connection():
    """Test basic GCS connection and configuration"""
    try:
        # Get bucket name from environment variables
        bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        logger.info(f"Testing GCS connection to bucket: {bucket_name}")
        
        # Initialize the GCS connector
        gcs = GCSConnector(bucket_name=bucket_name)
        
        # Test writing and reading from GCS with a test file
        test_path = "test/connection_test.txt"
        test_content = f"GCS connection test at {datetime.now().isoformat()}"
        
        # Store test content
        logger.info(f"Testing GCS by writing to {test_path}")
        result = gcs.store_text(test_path, test_content)
        
        if result:
            # Try to read it back
            logger.info(f"Testing GCS by reading from {test_path}")
            retrieved = gcs.get_text(test_path)
            
            if retrieved == test_content:
                logger.info("Successfully wrote and read from GCS bucket")
                return True
            else:
                logger.error("Content mismatch when reading from GCS")
                return False
        else:
            logger.error("Failed to write test content to GCS")
            return False
    except Exception as e:
        logger.error(f"Failed to connect to GCS: {str(e)}", exc_info=True)
        return False


def test_store_text(case_id, text_content):
    """Test storing text in GCS"""
    try:
        # Get bucket name from environment variables
        bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        
        # Initialize the GCS connector
        gcs = GCSConnector(bucket_name=bucket_name)
        
        # Define file path with consistent format using cl_ prefix
        gcs_path = f"cases/cl_{case_id}/full_text.txt"
        
        # Log details
        logger.info(f"Storing text ({len(text_content)} chars) for case {case_id} at path: {gcs_path}")
        
        # Store the text
        result = gcs.store_text(gcs_path, text_content)
        
        if result:
            logger.info(f"Successfully stored text in GCS at {gcs_path}")
            
            # Verify the text was stored by retrieving it
            retrieved_text = gcs.get_text(gcs_path)
            if retrieved_text:
                logger.info(f"Successfully retrieved text from GCS ({len(retrieved_text)} chars)")
                
                # Check that the retrieved text matches what we stored
                if retrieved_text == text_content:
                    logger.info("Retrieved text matches stored text ✅")
                else:
                    logger.warning("Retrieved text differs from stored text ❌")
            else:
                logger.error(f"Failed to retrieve text from {gcs_path}")
        else:
            logger.error(f"Failed to store text in GCS at {gcs_path}")
        
        return result
    except Exception as e:
        logger.error(f"Error in test_store_text: {str(e)}", exc_info=True)
        return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Debug GCS storage issues")
    parser.add_argument("--test-connection", action="store_true", help="Test GCS connection")
    parser.add_argument("--case-id", help="Court Listener case ID to test")
    parser.add_argument("--case-file", help="JSON file containing case data")
    
    args = parser.parse_args()
    
    if args.test_connection:
        success = test_gcs_connection()
        logger.info(f"GCS connection test {'succeeded' if success else 'failed'}")
    
    if args.case_id and args.case_file:
        # Load case data from file
        try:
            with open(args.case_file, 'r') as f:
                case_data = json.load(f)
            
            # Extract text for storage
            text_content = case_data.get('case_text', '')
            
            # Check if we have opinions to enhance the text
            if 'raw_data' in case_data and 'sub_opinions' in case_data['raw_data']:
                # In a real scenario, we would fetch these opinions
                # For now, we'll use metadata to create placeholder text
                opinion_urls = case_data['raw_data'].get('sub_opinions', [])
                for i, opinion_url in enumerate(opinion_urls):
                    text_content += f"\n\n--- OPINION {i+1} ---\n"
                    text_content += f"Opinion URL: {opinion_url}\n"
            
            # Add metadata to ensure we have meaningful content
            if not text_content or len(text_content) < 100:
                # Add richer metadata as text
                text_content = f"Case: {case_data.get('case_name', '')}\n"
                text_content += f"Full Name: {case_data.get('case_name_full', '')}\n"
                text_content += f"Date Filed: {case_data.get('date_filed', '')}\n"
                text_content += f"Court: {case_data.get('court_id', '')}\n"
                text_content += f"Jurisdiction: {case_data.get('jurisdiction', '')}\n"
                text_content += f"Precedential Status: {case_data.get('precedential_status', '')}\n"
                text_content += f"Source: {case_data.get('source', '')}\n"
                
                # Add position if available
                if 'raw_data' in case_data and 'posture' in case_data['raw_data']:
                    text_content += f"Posture: {case_data['raw_data']['posture']}\n"
                
                # Add judges if available
                if 'raw_data' in case_data and 'judges' in case_data['raw_data']:
                    text_content += f"Judges: {case_data['raw_data']['judges']}\n"
                
                # Add citations if available
                if 'raw_data' in case_data and 'citations' in case_data['raw_data']:
                    text_content += "\nCitations:\n"
                    for citation in case_data['raw_data']['citations']:
                        if isinstance(citation, dict):
                            cite_str = f"{citation.get('volume')} {citation.get('reporter')} {citation.get('page')}"
                            text_content += f"- {cite_str}\n"
            
            # Test storing the text
            success = test_store_text(args.case_id, text_content)
            logger.info(f"GCS text storage test {'succeeded' if success else 'failed'}")
        except Exception as e:
            logger.error(f"Error processing case file: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
Debug script to test importing the CaseLawProcessor class.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """Test importing the CaseLawProcessor class."""
    logger.debug("Attempting to import CaseLawProcessor...")
    try:
        # Add this debug print to see all imported modules
        logger.debug("Modules before import: %s", list(sys.modules.keys()))
        
        # Import just the class - this is the minimal test
        from src.processing.case_law_processor import CaseLawProcessor
        
        logger.debug("Successfully imported CaseLawProcessor")
        return True
    except Exception as e:
        logger.error("Failed to import CaseLawProcessor: %s", str(e), exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

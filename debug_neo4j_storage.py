#!/usr/bin/env python
"""
Debug script for Neo4j storage issues with Court Listener data
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('neo4j_debug.log')
    ]
)
logger = logging.getLogger(__name__)

# Import Neo4jConnector
from src.processing.storage.neo4j_connector import Neo4jConnector


def test_neo4j_connection():
    """Test basic Neo4j connection and schema setup"""
    try:
        neo4j = Neo4jConnector()
        logger.info("Successfully connected to Neo4j")
        
        # Close the connection when done
        neo4j.close()
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Neo4j: {str(e)}", exc_info=True)
        return False


def test_create_case(case_id, case_data):
    """Test creating a case node in Neo4j"""
    try:
        neo4j = Neo4jConnector()
        
        # First, try to get the case if it exists
        exists = neo4j.check_case_exists(case_id)
        if exists:
            logger.info(f"Case with ID {case_id} already exists in Neo4j")
            neo4j.close()
            return True
        
        # Add required fields for Neo4j
        if "year" not in case_data and "date_filed" in case_data:
            try:
                # Extract year from date_filed
                if isinstance(case_data["date_filed"], str):
                    case_data["year"] = int(case_data["date_filed"].split("-")[0])
                else:
                    case_data["year"] = datetime.now().year
            except Exception:
                case_data["year"] = datetime.now().year
                
        if "doc_type" not in case_data:
            case_data["doc_type"] = "case"
            
        if "name" not in case_data and "case_name" in case_data:
            case_data["name"] = case_data["case_name"]
        
        # Ensure we have a jurisdiction
        if "jurisdiction" not in case_data:
            case_data["jurisdiction"] = "tx"  # Default to Texas
            
        # Debugging - print the data we're sending
        logger.info(f"Creating case with data: {json.dumps(case_data, default=str)[:500]}...")
        
        # Create the case
        result = neo4j.create_case(case_data)
        
        # Check if the case was created successfully
        if result:
            logger.info(f"Successfully created case node for {case_id}")
        else:
            logger.error(f"Failed to create case node for {case_id}")
        
        # Close the connection when done
        neo4j.close()
        return result
    except Exception as e:
        logger.error(f"Error in test_create_case: {str(e)}", exc_info=True)
        return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Debug Neo4j storage issues")
    parser.add_argument("--test-connection", action="store_true", help="Test Neo4j connection")
    parser.add_argument("--case-id", help="Court Listener case ID to test")
    parser.add_argument("--case-file", help="JSON file containing case data")
    
    args = parser.parse_args()
    
    if args.test_connection:
        success = test_neo4j_connection()
        logger.info(f"Neo4j connection test {'succeeded' if success else 'failed'}")
    
    if args.case_id and args.case_file:
        # Load case data from file
        try:
            with open(args.case_file, 'r') as f:
                case_data = json.load(f)
            
            # Use consistent ID format with cl_ prefix
            neo4j_id = f"cl_{args.case_id}"
            case_data["id"] = neo4j_id
            
            # Test creating the case
            success = test_create_case(neo4j_id, case_data)
            logger.info(f"Neo4j case creation test {'succeeded' if success else 'failed'}")
        except Exception as e:
            logger.error(f"Error processing case file: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()

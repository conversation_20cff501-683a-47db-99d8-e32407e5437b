#!/usr/bin/env python
"""
Debug script for Neo4j storage issues with Court Listener data - Version 2
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('neo4j_debug.log')
    ]
)
logger = logging.getLogger(__name__)

# Import Neo4jConnector
from src.processing.storage.neo4j_connector import Neo4jConnector


def test_neo4j_connection():
    """Test basic Neo4j connection and schema setup"""
    try:
        neo4j = Neo4jConnector()
        logger.info("Successfully connected to Neo4j")
        
        # Close the connection when done
        neo4j.close()
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Neo4j: {str(e)}", exc_info=True)
        return False


def flatten_case_data(case_data):
    """
    Flatten case data for Neo4j storage, removing nested objects.
    Neo4j only accepts primitive types and arrays of primitive types.
    """
    flattened = {}
    
    # Copy only the primitive values and exclude nested objects
    for key, value in case_data.items():
        # Skip the raw_data field completely
        if key == "raw_data":
            continue
            
        # Include only primitive types or arrays of primitive types
        if value is None or isinstance(value, (str, int, float, bool)):
            flattened[key] = value
        elif isinstance(value, list) and all(isinstance(item, (str, int, float, bool)) for item in value):
            flattened[key] = value
    
    # Add some serialized citation info if present
    if "raw_data" in case_data and "citations" in case_data["raw_data"]:
        citations = case_data["raw_data"]["citations"]
        if citations and isinstance(citations, list):
            citation_strings = []
            for citation in citations:
                if isinstance(citation, dict):
                    try:
                        # Format as "volume reporter page"
                        cite_str = f"{citation.get('volume')} {citation.get('reporter')} {citation.get('page')}"
                        citation_strings.append(cite_str)
                    except Exception as e:
                        logger.warning(f"Could not format citation: {str(e)}")
            
            if citation_strings:
                flattened["citation_strings"] = citation_strings
    
    return flattened


def test_create_case(case_id, case_data):
    """Test creating a case node in Neo4j"""
    try:
        neo4j = Neo4jConnector()
        
        # First, try to get the case if it exists
        exists = neo4j.check_case_exists(case_id)
        if exists:
            logger.info(f"Case with ID {case_id} already exists in Neo4j")
            neo4j.close()
            return True
        
        # Flatten the data for Neo4j compatibility
        neo4j_case_data = flatten_case_data(case_data)
        
        # Add required fields for Neo4j
        if "year" not in neo4j_case_data and "date_filed" in neo4j_case_data:
            try:
                # Extract year from date_filed
                if isinstance(neo4j_case_data["date_filed"], str):
                    neo4j_case_data["year"] = int(neo4j_case_data["date_filed"].split("-")[0])
                else:
                    neo4j_case_data["year"] = datetime.now().year
            except Exception:
                neo4j_case_data["year"] = datetime.now().year
                
        if "doc_type" not in neo4j_case_data:
            neo4j_case_data["doc_type"] = "case"
            
        if "name" not in neo4j_case_data and "case_name" in neo4j_case_data:
            neo4j_case_data["name"] = neo4j_case_data["case_name"]
        
        # Ensure we have a jurisdiction
        if "jurisdiction" not in neo4j_case_data:
            neo4j_case_data["jurisdiction"] = "tx"  # Default to Texas
        
        # Make sure id field is correctly set
        neo4j_case_data["id"] = case_id
        
        # Debugging - print the data we're sending
        logger.info(f"Creating case with Neo4j-compatible data: {json.dumps(neo4j_case_data, default=str)}")
        
        # Create the case
        result = neo4j.create_case(neo4j_case_data)
        
        # Check if the case was created successfully
        if result:
            logger.info(f"Successfully created case node for {case_id}")
        else:
            logger.error(f"Failed to create case node for {case_id}")
        
        # Close the connection when done
        neo4j.close()
        return result
    except Exception as e:
        logger.error(f"Error in test_create_case: {str(e)}", exc_info=True)
        return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Debug Neo4j storage issues")
    parser.add_argument("--test-connection", action="store_true", help="Test Neo4j connection")
    parser.add_argument("--case-id", help="Court Listener case ID to test")
    parser.add_argument("--case-file", help="JSON file containing case data")
    
    args = parser.parse_args()
    
    if args.test_connection:
        success = test_neo4j_connection()
        logger.info(f"Neo4j connection test {'succeeded' if success else 'failed'}")
    
    if args.case_id and args.case_file:
        # Load case data from file
        try:
            with open(args.case_file, 'r') as f:
                case_data = json.load(f)
            
            # Use consistent ID format with cl_ prefix
            neo4j_id = f"cl_{args.case_id}"
            
            # Test creating the case
            success = test_create_case(neo4j_id, case_data)
            logger.info(f"Neo4j case creation test {'succeeded' if success else 'failed'}")
        except Exception as e:
            logger.error(f"Error processing case file: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()

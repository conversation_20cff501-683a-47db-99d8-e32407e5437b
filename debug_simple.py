#!/usr/bin/env python
"""
Debug script to test importing the simplified CaseLawProcessor class.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """Test importing the simplified CaseLawProcessor class."""
    logger.debug("Attempting to import simplified CaseLawProcessor...")
    try:
        # Import the simplified processor
        from src.processing.case_law_processor_simple import CaseLawProcessor
        
        logger.debug("Successfully imported simplified CaseLawProcessor")
        
        # Test instantiation
        processor = CaseLawProcessor()
        logger.debug("Successfully instantiated CaseLawProcessor")
        
        # Test getting court IDs
        court_ids = processor.get_court_ids_for_jurisdiction("tx")
        logger.debug(f"Court IDs for TX: {court_ids}")
        
        return True
    except Exception as e:
        logger.error("Failed to import/use simplified CaseLawProcessor: %s", str(e), exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

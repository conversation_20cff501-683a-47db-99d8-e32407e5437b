#!/usr/bin/env python
"""
Debug Script for Storage Pipeline

This script tests each component of the storage pipeline independently, 
helping to diagnose issues with any specific storage system.
"""

import os
import sys
import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Optional
from dotenv import load_dotenv
from bs4 import BeautifulSoup

# Load environment variables right at the start
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path=dotenv_path)
print(f"DEBUG: load_dotenv from '{dotenv_path}' returned: {load_dotenv(dotenv_path)}")

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('storage_pipeline_debug.log')
    ]
)
logger = logging.getLogger(__name__)

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.dirname(__file__))

# Import storage connectors directly to test independently
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.gcs_connector import GCSConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.neo4j_connector import Neo4jConnector
from src.processing.providers.court_listener import CourtListenerConnector
from src.processing.providers.court_listener_api import get_cluster_by_id

class StoragePipelineDebugger:
    """Debug tool for testing storage pipeline components individually."""

    def __init__(self):
        """Initialize all storage components for testing."""
        logger.info("Initializing storage pipeline debugger...")
        
        # Initialize connectors
        self.supabase = SupabaseConnector()
        logger.info("Supabase connector initialized")
        
        self.gcs = GCSConnector()
        logger.info("GCS connector initialized")
        
        self.pinecone = PineconeConnector()
        logger.info("Pinecone connector initialized")
        
        self.neo4j = Neo4jConnector()
        logger.info("Neo4j connector initialized")
        
        self.court_listener = CourtListenerConnector()
        logger.info("Court Listener connector initialized")
        
        # Create a batch ID for testing
        self.batch_id = str(uuid.uuid4())
        logger.info(f"Created test batch ID: {self.batch_id}")

    def fetch_test_case(self, cluster_id: str) -> Optional[Dict]:
        """Fetch a test case from Court Listener API."""
        try:
            logger.info(f"Fetching test case with cluster ID: {cluster_id}")
            # Use the dedicated helper function instead of a method on CourtListenerConnector
            case_data = get_cluster_by_id(cluster_id)
            if case_data:
                logger.info(f"Successfully fetched case data for cluster {cluster_id}")
                return case_data
            else:
                logger.error(f"No data returned for cluster {cluster_id}")
                return None
        except Exception as e:
            logger.error(f"Error fetching case {cluster_id}: {str(e)}", exc_info=True)
            return None

    def test_supabase_storage(self, case_data: Dict) -> bool:
        """Test storing case data in Supabase."""
        try:
            cluster_id = case_data.get('id')
            if not cluster_id:
                logger.error("Missing cluster ID in case data")
                return False
                
            logger.info(f"Testing Supabase storage for cluster {cluster_id}")
            
            # Format case data for Supabase
            case_insert_data = {
                'id': cluster_id,
                'case_name': case_data.get('case_name', ''),
                'case_name_full': case_data.get('case_name_full', ''),
                'court_id': case_data.get('court', {}).get('id', ''),
                'jurisdiction': case_data.get('court', {}).get('jurisdiction', 'unknown'),
                'date_filed': case_data.get('date_filed', ''),
                'citation_count': case_data.get('citation_count', 0),
                'precedential': case_data.get('precedential_status', 'Published') == 'Published',
                'docket_id': case_data.get('docket', {}).get('id'),
                'source': 'court_listener',
                'source_id': f"cl_{cluster_id}",
                'status': 'processing',
                'cluster_id': cluster_id,
                'metadata_quality': 'Basic',
                'document_quality': 'Standard',
                'source_url': case_data.get('resource_uri') or case_data.get('absolute_url', '')
            }
            
            # Store in Supabase
            stored_case = self.supabase.store_case(case_insert_data, self.batch_id)
            if stored_case:
                logger.info(f"Successfully stored case {cluster_id} in Supabase")
                return True
            else:
                logger.error(f"Failed to store case {cluster_id} in Supabase")
                return False
        except Exception as e:
            logger.error(f"Error in Supabase storage test: {str(e)}", exc_info=True)
            return False

    def test_gcs_storage(self, case_data: Dict) -> bool:
        """Test storing case text in GCS."""
        try:
            cluster_id = case_data.get('id')
            if not cluster_id:
                logger.error("Missing cluster ID in case data")
                return False
                
            logger.info(f"Testing GCS storage for cluster {cluster_id}")
            
            # Extract text from case data
            case_text = case_data.get("caseSummary", "") or case_data.get("case_text", "")
            
            if not case_text and case_data.get("html"):
                # Extract text from HTML if available
                soup = BeautifulSoup(case_data["html"], "html.parser")
                case_text = soup.get_text(" ", strip=True)
            
            if not case_text:
                # If we still don't have text, use the case name and date as minimal content
                case_name = case_data.get('case_name', '')
                date_filed = case_data.get('date_filed', '')
                court = case_data.get('court', {}).get('id', '')
                jurisdiction = case_data.get('court', {}).get('jurisdiction', 'unknown')
                case_text = f"Case: {case_name}\nDate: {date_filed}\nCourt: {court}\nJurisdiction: {jurisdiction}"
            
            # Store text in GCS
            gcs_path = f"cases/{cluster_id}/full_text.txt"
            self.gcs.store_text(gcs_path, case_text)
            
            # Verify storage by reading it back
            stored_text = self.gcs.get_text(gcs_path)
            if stored_text:
                logger.info(f"Successfully stored and retrieved text for case {cluster_id} in GCS")
                
                # Update Supabase with GCS path
                self.supabase.update_case(cluster_id, {"gcs_path": gcs_path}, self.batch_id)
                logger.info(f"Updated Supabase record with GCS path: {gcs_path}")
                return True
            else:
                logger.error(f"Failed to verify GCS storage for case {cluster_id}")
                return False
        except Exception as e:
            logger.error(f"Error in GCS storage test: {str(e)}", exc_info=True)
            return False

    def test_pinecone_storage(self, case_data: Dict) -> bool:
        """Test storing case embedding in Pinecone."""
        try:
            cluster_id = case_data.get('id')
            if not cluster_id:
                logger.error("Missing cluster ID in case data")
                return False
                
            logger.info(f"Testing Pinecone storage for cluster {cluster_id}")
            
            # Get text for embedding (use same approach as GCS storage)
            case_text = case_data.get("caseSummary", "") or case_data.get("case_text", "")
            
            if not case_text and case_data.get("html"):
                soup = BeautifulSoup(case_data["html"], "html.parser")
                case_text = soup.get_text(" ", strip=True)
            
            if not case_text:
                case_name = case_data.get('case_name', '')
                date_filed = case_data.get('date_filed', '')
                court = case_data.get('court', {}).get('id', '')
                jurisdiction = case_data.get('court', {}).get('jurisdiction', 'unknown')
                case_text = f"Case: {case_name}\nDate: {date_filed}\nCourt: {court}\nJurisdiction: {jurisdiction}"
            
            # Create embedding data
            embedding_data = {
                "case_id": cluster_id,
                "text": case_text[:5000],  # Limit text size for embedding
                "metadata": {
                    "case_id": cluster_id,
                    "source_id": f"cl_{cluster_id}",
                    "case_name": case_data.get('case_name', ''),
                    "jurisdiction": case_data.get('court', {}).get('jurisdiction', 'unknown'),
                    "court_id": case_data.get('court', {}).get('id', ''),
                    "date_filed": case_data.get('date_filed', ''),
                    "document_type": "case_summary"
                }
            }
            
            # Store in Pinecone
            embedding_id = f"{cluster_id}_summary"
            self.pinecone.index_document(embedding_id, embedding_data)
            
            # Verify by querying
            results = self.pinecone.query(
                query_text="",  # Dummy query
                top_k=1,
                filter={"case_id": cluster_id}
            )
            
            if results and len(results) > 0:
                logger.info(f"Successfully stored and queried embedding for case {cluster_id} in Pinecone")
                return True
            else:
                logger.error(f"Failed to verify Pinecone storage for case {cluster_id}")
                return False
        except Exception as e:
            logger.error(f"Error in Pinecone storage test: {str(e)}", exc_info=True)
            return False

    def test_neo4j_storage(self, case_data: Dict) -> bool:
        """Test storing case node in Neo4j."""
        try:
            cluster_id = case_data.get('id')
            if not cluster_id:
                logger.error("Missing cluster ID in case data")
                return False
                
            logger.info(f"Testing Neo4j storage for cluster {cluster_id}")
            
            # Format case data for Neo4j
            neo4j_case_data = {
                'id': cluster_id,
                'source_id': f"cl_{cluster_id}",
                'case_name': case_data.get('case_name', ''),
                'court_id': case_data.get('court', {}).get('id', ''),
                'jurisdiction': case_data.get('court', {}).get('jurisdiction', 'unknown'),
                'date_filed': case_data.get('date_filed', ''),
                'citation_count': case_data.get('citation_count', 0),
                'precedential': case_data.get('precedential_status', 'Published') == 'Published'
            }
            
            # Create case node in Neo4j
            self.neo4j.create_case(neo4j_case_data)
            
            # Verify by checking existence
            exists = self.neo4j.check_case_exists(cluster_id)
            if exists:
                logger.info(f"Successfully created and verified Neo4j node for case {cluster_id}")
                return True
            else:
                # Try alternative ID format
                exists = self.neo4j.check_case_exists(f"cl_{cluster_id}")
                if exists:
                    logger.info(f"Successfully created Neo4j node for case {cluster_id} (verified with cl_ prefix)")
                    return True
                else:
                    logger.error(f"Failed to verify Neo4j storage for case {cluster_id}")
                    return False
        except Exception as e:
            logger.error(f"Error in Neo4j storage test: {str(e)}", exc_info=True)
            return False

    def run_full_test(self, cluster_id: str) -> None:
        """Run full storage pipeline test for a single case."""
        logger.info(f"Starting full storage pipeline test for cluster {cluster_id}")
        
        # Step 1: Fetch case data
        case_data = self.fetch_test_case(cluster_id)
        if not case_data:
            logger.error(f"Failed to fetch case data for cluster {cluster_id}. Aborting test.")
            return
        
        # Step 2: Test Supabase storage
        supabase_success = self.test_supabase_storage(case_data)
        logger.info(f"Supabase storage test result: {'SUCCESS' if supabase_success else 'FAILURE'}")
        
        # Step 3: Test GCS storage
        gcs_success = self.test_gcs_storage(case_data)
        logger.info(f"GCS storage test result: {'SUCCESS' if gcs_success else 'FAILURE'}")
        
        # Step 4: Test Pinecone storage
        pinecone_success = self.test_pinecone_storage(case_data)
        logger.info(f"Pinecone storage test result: {'SUCCESS' if pinecone_success else 'FAILURE'}")
        
        # Step 5: Test Neo4j storage
        neo4j_success = self.test_neo4j_storage(case_data)
        logger.info(f"Neo4j storage test result: {'SUCCESS' if neo4j_success else 'FAILURE'}")
        
        # Overall result
        overall_success = all([supabase_success, gcs_success, pinecone_success, neo4j_success])
        
        # Print summary
        logger.info("=" * 80)
        logger.info("STORAGE PIPELINE TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Test case: {cluster_id}")
        logger.info(f"Supabase: {'SUCCESS' if supabase_success else 'FAILURE'}")
        logger.info(f"GCS: {'SUCCESS' if gcs_success else 'FAILURE'}")
        logger.info(f"Pinecone: {'SUCCESS' if pinecone_success else 'FAILURE'}")
        logger.info(f"Neo4j: {'SUCCESS' if neo4j_success else 'FAILURE'}")
        logger.info("=" * 80)
        logger.info(f"Overall test result: {'SUCCESS' if overall_success else 'FAILURE'}")
        logger.info("=" * 80)


def main():
    """Main entry point for the script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the storage pipeline components")
    parser.add_argument("--cluster-id", required=True, help="Court Listener cluster ID to test with")
    
    args = parser.parse_args()
    
    debugger = StoragePipelineDebugger()
    debugger.run_full_test(args.cluster_id)


if __name__ == "__main__":
    main()

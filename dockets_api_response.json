{"name": "Docket List", "description": "", "renders": ["application/json", "text/html", "application/xml"], "parses": ["application/json", "application/x-www-form-urlencoded", "multipart/form-data", "application/xml"], "actions": {"POST": {"resource_uri": {"type": "field", "required": false, "read_only": true, "label": "Resource uri"}, "id": {"type": "field", "required": false, "read_only": true, "label": "Id"}, "court": {"type": "field", "required": true, "read_only": false, "label": "Court"}, "court_id": {"type": "field", "required": false, "read_only": true, "label": "Court id"}, "original_court_info": {"type": "nested object", "required": true, "read_only": false, "label": "Original court info", "children": {"resource_uri": {"type": "field", "required": false, "read_only": true, "label": "Resource uri"}, "id": {"type": "field", "required": false, "read_only": true, "label": "Id"}, "date_created": {"type": "datetime", "required": false, "read_only": true, "label": "Date created", "help_text": "The moment when the item was created."}, "date_modified": {"type": "datetime", "required": false, "read_only": true, "label": "Date modified", "help_text": "The last moment when the item was modified. A value in year 1750 indicates the value is unknown"}, "docket_number": {"type": "string", "required": false, "read_only": false, "label": "Docket number", "help_text": "The docket number in the lower court."}, "assigned_to_str": {"type": "string", "required": false, "read_only": false, "label": "Assigned to str", "help_text": "The judge that the case was assigned to, as a string."}, "ordering_judge_str": {"type": "string", "required": false, "read_only": false, "label": "Ordering judge str", "help_text": "The judge that issued the final order in the case, as a string."}, "court_reporter": {"type": "string", "required": false, "read_only": false, "label": "Court reporter", "help_text": "The court reporter responsible for the case."}, "date_disposed": {"type": "date", "required": false, "read_only": false, "label": "Date disposed", "help_text": "The date the case was disposed at the lower court."}, "date_filed": {"type": "date", "required": false, "read_only": false, "label": "Date filed", "help_text": "The date the case was filed in the lower court."}, "date_judgment": {"type": "date", "required": false, "read_only": false, "label": "Date judgment", "help_text": "The date of the order or judgment in the lower court."}, "date_judgment_eod": {"type": "date", "required": false, "read_only": false, "label": "Date judgment eod", "help_text": "The date the judgment was Entered On the Docket at the lower court."}, "date_filed_noa": {"type": "date", "required": false, "read_only": false, "label": "Date filed noa", "help_text": "The date the notice of appeal was filed for the case."}, "date_received_coa": {"type": "date", "required": false, "read_only": false, "label": "Date received coa", "help_text": "The date the case was received at the court of appeals."}, "assigned_to": {"type": "field", "required": false, "read_only": false, "label": "Assigned to", "help_text": "The judge the case was assigned to."}, "ordering_judge": {"type": "field", "required": false, "read_only": false, "label": "Ordering judge", "help_text": "The judge that issued the final order in the case."}}}, "idb_data": {"type": "nested object", "required": true, "read_only": false, "label": "Idb data", "children": {"resource_uri": {"type": "field", "required": false, "read_only": true, "label": "Resource uri"}, "date_created": {"type": "datetime", "required": false, "read_only": true, "label": "Date created", "help_text": "The moment when the item was created."}, "date_modified": {"type": "datetime", "required": false, "read_only": true, "label": "Date modified", "help_text": "The last moment when the item was modified. A value in year 1750 indicates the value is unknown"}, "dataset_source": {"type": "choice", "required": true, "read_only": false, "label": "Dataset source", "help_text": "IDB has several source datafiles. This field helps keep track of where a row came from originally.", "choices": [{"value": 1, "display_name": "Civil cases filed and terminated from SY 1970 through SY 1987"}, {"value": 2, "display_name": "Civil cases filed, terminated, and pending from SY 1988 to present (2017)"}, {"value": 8, "display_name": "Civil cases filed, terminated, and pending from SY 1988 to present (2020)"}, {"value": 9, "display_name": "Civil cases filed, terminated, and pending from SY 1988 to present (September 2021)"}, {"value": 10, "display_name": "Civil cases filed, terminated, and pending from SY 1988 to present (March 2022)"}, {"value": 3, "display_name": "Criminal defendants filed and terminated from SY 1970 through FY 1995"}, {"value": 4, "display_name": "Criminal defendants filed, terminated, and pending from FY 1996 to present (2017)"}, {"value": 5, "display_name": "Appellate cases filed and terminated from SY 1971 through FY 2007"}, {"value": 6, "display_name": "Appellate cases filed, terminated, and pending from FY 2008 to present (2017)"}, {"value": 7, "display_name": "Bankruptcy cases filed, terminated, and pending from FY 2008 to present (2017)"}]}, "office": {"type": "string", "required": false, "read_only": false, "label": "Office", "help_text": "The code that designates the office within the district where the case is filed. Must conform with format established in Volume XI, Guide to Judiciary Policies and Procedures, Appendix A. See: https://free.law/idb-facts/", "max_length": 3}, "docket_number": {"type": "string", "required": false, "read_only": false, "label": "Docket number", "help_text": "The number assigned by the Clerks' office; consists of 2 digit Docket Year (usually calendar year in which the case was filed) and 5 digit sequence number.", "max_length": 7}, "origin": {"type": "choice", "required": false, "read_only": false, "label": "Origin", "help_text": "A single digit code describing the manner in which the case was filed in the district.", "choices": [{"value": 1, "display_name": "Original Proceeding"}, {"value": 2, "display_name": "Removed  (began in the state court, removed to the district court)"}, {"value": 3, "display_name": "Remanded for further action (removal from court of appeals)"}, {"value": 4, "display_name": "Reinstated/reopened (previously opened and closed, reopened for additional action)"}, {"value": 5, "display_name": "Transferred from another district(pursuant to 28 USC 1404)"}, {"value": 6, "display_name": "Multi district litigation (cases transferred to this district by an order entered by Judicial Panel on Multi District Litigation pursuant to 28 USC 1407)"}, {"value": 7, "display_name": "Appeal to a district judge of a magistrate judge's decision"}, {"value": 8, "display_name": "Second reopen"}, {"value": 9, "display_name": "Third reopen"}, {"value": 10, "display_name": "Fourth reopen"}, {"value": 11, "display_name": "Fifth reopen"}, {"value": 12, "display_name": "Sixth reopen"}, {"value": 13, "display_name": "Multi district litigation originating in the district (valid beginning July 1, 2016)"}]}, "date_filed": {"type": "date", "required": false, "read_only": false, "label": "Date filed", "help_text": "The date on which the case was filed in the district."}, "jurisdiction": {"type": "choice", "required": false, "read_only": false, "label": "Juris<PERSON>", "help_text": "The code which provides the basis for the U.S. district court jurisdiction in the case. This code is used in conjunction with appropriate nature of suit code.", "choices": [{"value": 1, "display_name": "Government plaintiff"}, {"value": 2, "display_name": "Government defendant"}, {"value": 3, "display_name": "Federal question"}, {"value": 4, "display_name": "Diversity of citizenship"}, {"value": 5, "display_name": "Local question"}]}, "nature_of_suit": {"type": "choice", "required": false, "read_only": false, "label": "Nature of suit", "help_text": "A three digit statistical code representing the nature of suit of the action filed.", "choices": [{"value": 110, "display_name": "110 Insurance"}, {"value": 120, "display_name": "120 Marine contract actions"}, {"value": 130, "display_name": "130 Miller act"}, {"value": 140, "display_name": "140 Negotiable instruments"}, {"value": 150, "display_name": "150 Overpayments & enforcement of judgments"}, {"value": 151, "display_name": "151 Overpayments under the medicare act"}, {"value": 152, "display_name": "152 Recovery of defaulted student loans"}, {"value": 153, "display_name": "153 Recovery of overpayments of vet benefits"}, {"value": 160, "display_name": "160 Stockholder's suits"}, {"value": 190, "display_name": "190 Other contract actions"}, {"value": 195, "display_name": "195 Contract product liability"}, {"value": 196, "display_name": "196 Contract franchise"}, {"value": 210, "display_name": "210 Land condemnation"}, {"value": 220, "display_name": "220 Foreclosure"}, {"value": 230, "display_name": "230 Rent, lease, ejectment"}, {"value": 240, "display_name": "240 Torts to land"}, {"value": 245, "display_name": "245 Tort product liability"}, {"value": 290, "display_name": "290 Other real property actions"}, {"value": 310, "display_name": "310 Airplane personal injury"}, {"value": 315, "display_name": "315 Airplane product liability"}, {"value": 320, "display_name": "320 Assault, libel, and slander"}, {"value": 330, "display_name": "330 Federal employers' liability"}, {"value": 340, "display_name": "340 Marine personal injury"}, {"value": 345, "display_name": "345 Marine - Product liability"}, {"value": 350, "display_name": "350 Motor vehicle personal injury"}, {"value": 355, "display_name": "355 Motor vehicle product liability"}, {"value": 360, "display_name": "360 Other personal liability"}, {"value": 362, "display_name": "362 Medical malpractice"}, {"value": 365, "display_name": "365 Personal injury - Product liability"}, {"value": 367, "display_name": "367 Health care / pharm"}, {"value": 368, "display_name": "368 Asbestos personal injury - Prod. <PERSON>b."}, {"value": 370, "display_name": "370 Other fraud"}, {"value": 371, "display_name": "371 Truth in lending"}, {"value": 375, "display_name": "375 False Claims Act"}, {"value": 380, "display_name": "380 Other personal property damage"}, {"value": 385, "display_name": "385 Property damage - Product liability"}, {"value": 400, "display_name": "400 State re-appointment"}, {"value": 410, "display_name": "410 Antitrust"}, {"value": 422, "display_name": "422 Bankruptcy appeals rule 28 USC 158"}, {"value": 423, "display_name": "423 Bankruptcy withdrawal 28 USC 157"}, {"value": 430, "display_name": "430 Banks and banking"}, {"value": 440, "display_name": "440 Civil rights other"}, {"value": 441, "display_name": "441 Civil rights voting"}, {"value": 442, "display_name": "442 Civil rights jobs"}, {"value": 443, "display_name": "443 Civil rights accomodations"}, {"value": 444, "display_name": "444 Civil rights welfare"}, {"value": 445, "display_name": "445 Civil rights ADA employment"}, {"value": 446, "display_name": "446 Civil rights ADA other"}, {"value": 448, "display_name": "448 Education"}, {"value": 450, "display_name": "450 Interstate commerce"}, {"value": 460, "display_name": "460 Deportation"}, {"value": 462, "display_name": "462 Naturalization, petition for hearing of denial"}, {"value": 463, "display_name": "463 Habeas corpus - alien detainee"}, {"value": 465, "display_name": "465 Other immigration actions"}, {"value": 470, "display_name": "470 Civil (RICO)"}, {"value": 480, "display_name": "480 Consumer credit"}, {"value": 490, "display_name": "490 Cable/Satellite TV"}, {"value": 510, "display_name": "510 Prisoner petitions - vacate sentence"}, {"value": 530, "display_name": "530 Prisoner petitions - habeas corpus"}, {"value": 535, "display_name": "535 Habeas corpus: Death penalty"}, {"value": 540, "display_name": "540 Prisoner petitions - man<PERSON><PERSON> and other"}, {"value": 550, "display_name": "550 Prisoner - civil rights"}, {"value": 555, "display_name": "555 Prisoner - prison condition"}, {"value": 560, "display_name": "560 Civil detainee"}, {"value": 610, "display_name": "610 Agricultural acts"}, {"value": 620, "display_name": "620 Food and drug acts"}, {"value": 625, "display_name": "625 Drug related seizure of property"}, {"value": 630, "display_name": "630 Liquor laws"}, {"value": 640, "display_name": "640 Railroad and trucks"}, {"value": 650, "display_name": "650 Airline regulations"}, {"value": 660, "display_name": "660 Occupational safety/health"}, {"value": 690, "display_name": "690 Other forfeiture and penalty suits"}, {"value": 710, "display_name": "710 Fair Labor Standards Act"}, {"value": 720, "display_name": "720 Labor/Management Relations Act"}, {"value": 730, "display_name": "730 Labor/Management report & disclosure"}, {"value": 740, "display_name": "740 Railway Labor Act"}, {"value": 751, "display_name": "751 Family and Medical Leave Act"}, {"value": 790, "display_name": "790 Other labor litigation"}, {"value": 791, "display_name": "791 Employee Retirement Income Security Act"}, {"value": 810, "display_name": "810 Selective service"}, {"value": 820, "display_name": "820 Copyright"}, {"value": 830, "display_name": "830 Patent"}, {"value": 835, "display_name": "835 Patent Abbreviated New Drug Application (ANDA)"}, {"value": 840, "display_name": "840 Trademark"}, {"value": 850, "display_name": "850 Securities, Commodities, Exchange"}, {"value": 860, "display_name": "860 Social security"}, {"value": 861, "display_name": "861 HIA (1395 FF) / Medicare"}, {"value": 862, "display_name": "862 Black lung"}, {"value": 863, "display_name": "863 D.I.W.C. / D.I.W.W."}, {"value": 864, "display_name": "864 S.S.I.D."}, {"value": 865, "display_name": "865 R.S.I."}, {"value": 870, "display_name": "870 Tax suits"}, {"value": 871, "display_name": "871 IRS 3rd party suits 26 USC 7609"}, {"value": 875, "display_name": "875 Customer challenge 12 USC 3410"}, {"value": 890, "display_name": "890 Other statutory actions"}, {"value": 891, "display_name": "891 Agricultural acts"}, {"value": 892, "display_name": "892 Economic Stabilization Act"}, {"value": 893, "display_name": "893 Environmental matters"}, {"value": 894, "display_name": "894 Energy Allocation Act"}, {"value": 895, "display_name": "895 Freedom of Information Act of 1974"}, {"value": 896, "display_name": "896 Arbitration"}, {"value": 899, "display_name": "899 Administrative procedure act / review or appeal of agency decision"}, {"value": 900, "display_name": "900 Appeal of fee - equal access to justice"}, {"value": 910, "display_name": "910 Domestic relations"}, {"value": 920, "display_name": "920 Insanity"}, {"value": 930, "display_name": "930 Probate"}, {"value": 940, "display_name": "940 Substitute trustee"}, {"value": 950, "display_name": "950 Constitutionality of state statutes"}, {"value": 990, "display_name": "990 Other"}, {"value": 992, "display_name": "992 Local jurisdictional appeal"}, {"value": 999, "display_name": "999 Miscellaneous"}]}, "title": {"type": "string", "required": false, "read_only": false, "label": "Title", "help_text": "No description provided by FJC."}, "section": {"type": "string", "required": false, "read_only": false, "label": "Section", "help_text": "No description provided by FJC.", "max_length": 200}, "subsection": {"type": "string", "required": false, "read_only": false, "label": "Subsection", "help_text": "No description provided by FJC.", "max_length": 200}, "diversity_of_residence": {"type": "integer", "required": false, "read_only": false, "label": "Diversity of residence", "help_text": "Involves diversity of citizenship for the plaintiff and defendant. First position is the citizenship of the plaintiff, second position is the citizenship of the defendant. Only used when jurisdiction is 4", "min_value": -32768, "max_value": 32767}, "class_action": {"type": "boolean", "required": false, "read_only": false, "label": "Class action", "help_text": "Involves an allegation by the plaintiff that the complaint meets the prerequisites of a \"Class Action\" as provided in Rule 23 - F.R.CV.P. "}, "monetary_demand": {"type": "integer", "required": false, "read_only": false, "label": "Monetary demand", "help_text": "The monetary amount sought by plaintiff (in thousands). Amounts less than $500 appear as 1, and amounts over $10k appear as 9999. See notes in codebook.", "min_value": -2147483648, "max_value": 2147483647}, "county_of_residence": {"type": "integer", "required": false, "read_only": false, "label": "County of residence", "help_text": "The code for the county of residence of the first listed plaintiff (see notes in codebook). Appears to use FIPS code.", "min_value": -2147483648, "max_value": 2147483647}, "arbitration_at_filing": {"type": "choice", "required": false, "read_only": false, "label": "Arbitration at filing", "help_text": "This field is used only by the courts  participating in the Formal Arbitration Program.  It is not used for any other purpose.", "choices": [{"value": "M", "display_name": "Mandatory"}, {"value": "V", "display_name": "Voluntary"}, {"value": "E", "display_name": "Exempt"}, {"value": "Y", "display_name": "Yes, but type unknown"}]}, "arbitration_at_termination": {"type": "choice", "required": false, "read_only": false, "label": "Arbitration at termination", "help_text": "Termination arbitration code.", "choices": [{"value": "M", "display_name": "Mandatory"}, {"value": "V", "display_name": "Voluntary"}, {"value": "E", "display_name": "Exempt"}, {"value": "Y", "display_name": "Yes, but type unknown"}]}, "multidistrict_litigation_docket_number": {"type": "string", "required": false, "read_only": false, "label": "Multidistrict litigation docket number", "help_text": "A 4 digit multi district litigation docket number."}, "plaintiff": {"type": "string", "required": false, "read_only": false, "label": "Plaintiff", "help_text": "First listed plaintiff. This field appears to be cut off at 30 characters"}, "defendant": {"type": "string", "required": false, "read_only": false, "label": "Defendant", "help_text": "First listed defendant. This field appears to be cut off at 30 characters."}, "date_transfer": {"type": "date", "required": false, "read_only": false, "label": "Date transfer", "help_text": "The date when the papers were received in the receiving district for a transferred  case."}, "transfer_office": {"type": "string", "required": false, "read_only": false, "label": "Transfer office", "help_text": "The office number of the district losing the case.", "max_length": 3}, "transfer_docket_number": {"type": "string", "required": false, "read_only": false, "label": "Transfer docket number", "help_text": "The docket number of the case in the losing district"}, "transfer_origin": {"type": "string", "required": false, "read_only": false, "label": "Transfer origin", "help_text": "The origin number of the case in the losing district"}, "date_terminated": {"type": "date", "required": false, "read_only": false, "label": "Date terminated", "help_text": "The date the district court received the final judgment or the order disposing of the case."}, "termination_class_action_status": {"type": "choice", "required": false, "read_only": false, "label": "Termination class action status", "help_text": "A code that indicates a case involving allegations of class action.", "choices": [{"value": 2, "display_name": "Denied"}, {"value": 3, "display_name": "Granted"}]}, "procedural_progress": {"type": "choice", "required": false, "read_only": false, "label": "Procedural progress", "help_text": "The point to which the case had progressed when it was disposed of. See notes in codebook.", "choices": [{"value": 1, "display_name": "No court action (before issue joined)"}, {"value": 2, "display_name": "Order entered"}, {"value": 11, "display_name": "Hearing held"}, {"value": 12, "display_name": "Order decided"}, {"value": 3, "display_name": "No court action (after issue joined)"}, {"value": 4, "display_name": "Judgment on motion"}, {"value": 5, "display_name": "Pretrial conference held"}, {"value": 6, "display_name": "During court trial"}, {"value": 7, "display_name": "During jury trial"}, {"value": 8, "display_name": "After court trial"}, {"value": 9, "display_name": "After jury trial"}, {"value": 10, "display_name": "Other"}, {"value": 13, "display_name": "Request for trial de novo after arbitration"}]}, "disposition": {"type": "choice", "required": false, "read_only": false, "label": "Disposition", "help_text": "The manner in which the case was disposed of.", "choices": [{"value": 0, "display_name": "Transfer to another district"}, {"value": 1, "display_name": "Remanded to state court"}, {"value": 10, "display_name": "Multi-district litigation transfer"}, {"value": 11, "display_name": "Remanded to U.S. agency"}, {"value": 2, "display_name": "Want of prosecution"}, {"value": 3, "display_name": "Lack of jurisdiction"}, {"value": 12, "display_name": "Voluntarily dismissed"}, {"value": 13, "display_name": "Settled"}, {"value": 14, "display_name": "Other"}, {"value": 4, "display_name": "<PERSON><PERSON><PERSON>"}, {"value": 5, "display_name": "Consent"}, {"value": 6, "display_name": "Motion before trial"}, {"value": 7, "display_name": "Jury verdict"}, {"value": 8, "display_name": "Directed verdict"}, {"value": 9, "display_name": "Court trial"}, {"value": 15, "display_name": "Award of arbitrator"}, {"value": 16, "display_name": "Stayed pending bankruptcy"}, {"value": 17, "display_name": "Other"}, {"value": 18, "display_name": "Statistical closing"}, {"value": 19, "display_name": "Appeal affirmed (magistrate judge)"}, {"value": 20, "display_name": "Appeal denied (magistrate judge"}]}, "nature_of_judgement": {"type": "choice", "required": false, "read_only": false, "label": "Nature of judgement", "help_text": "Cases disposed of by an entry of a final judgment.", "choices": [{"value": 0, "display_name": "No monetary award"}, {"value": 1, "display_name": "Monetary award only"}, {"value": 2, "display_name": "Monetary award and other"}, {"value": 3, "display_name": "Injunction"}, {"value": 4, "display_name": "Forfeiture/foreclosure/condemnation, etc."}, {"value": 5, "display_name": "Costs only"}, {"value": 6, "display_name": "Costs and attorney fees"}]}, "amount_received": {"type": "integer", "required": false, "read_only": false, "label": "Amount received", "help_text": "Dollar amount received (in thousands) when appropriate. Field not used uniformally; see codebook.", "min_value": -2147483648, "max_value": 2147483647}, "judgment": {"type": "choice", "required": false, "read_only": false, "label": "Judgment", "help_text": "Which party the cases was disposed in favor of.", "choices": [{"value": 1, "display_name": "Plaintiff"}, {"value": 2, "display_name": "Defendant"}, {"value": 3, "display_name": "Both plaintiff and defendant"}, {"value": 4, "display_name": "Unknown"}]}, "pro_se": {"type": "choice", "required": false, "read_only": false, "label": "Pro se", "help_text": "Which parties filed pro se? (See codebook for more details.)", "choices": [{"value": 0, "display_name": "No pro se plaintiffs or defendants"}, {"value": 1, "display_name": "Pro se plaintiffs, but no pro se defendants"}, {"value": 2, "display_name": "Pro se defendants, but no pro se plaintiffs"}, {"value": 3, "display_name": "Both pro se plaintiffs & defendants"}]}, "year_of_tape": {"type": "integer", "required": false, "read_only": false, "label": "Year of tape", "help_text": "Statistical year label on data files obtained from the Administrative Office of the United States Courts.  2099 on pending case records.", "min_value": -2147483648, "max_value": 2147483647}, "nature_of_offense": {"type": "choice", "required": false, "read_only": false, "label": "Nature of offense", "help_text": "The four digit D2 offense code associated with the filing title/secion 1. These codes were created in FY2005 to replace the AO offense codes.", "choices": [{"value": "0100", "display_name": "Murder, First Degree"}, {"value": "0101", "display_name": "Murder, Government Officials"}, {"value": "0200", "display_name": "Murder, Second Degree"}, {"value": "0201", "display_name": "Murder, 2nd Degree, Government Officials"}, {"value": "0300", "display_name": "Manslaughter"}, {"value": "0301", "display_name": "Manslaughter"}, {"value": "0310", "display_name": "Negligent Homicide"}, {"value": "0311", "display_name": "<PERSON><PERSON><PERSON>, Government Officials"}, {"value": "1100", "display_name": "Robbery, Bank"}, {"value": "1200", "display_name": "Robbery, Postal"}, {"value": "1400", "display_name": "<PERSON><PERSON>, Other"}, {"value": "1500", "display_name": "Aggravated or Felonious"}, {"value": "1501", "display_name": "<PERSON><PERSON><PERSON>, on Government Official"}, {"value": "1560", "display_name": "Fair Housing Law"}, {"value": "1600", "display_name": "Assault, Other"}, {"value": "1601", "display_name": "Misdemeanor, on Government Official"}, {"value": "1602", "display_name": "Obstruction of Justice-Interference"}, {"value": "1700", "display_name": "Racketeering, Violent Crime"}, {"value": "1800", "display_name": "Carjacking"}, {"value": "2100", "display_name": "Burglary, Bank"}, {"value": "2200", "display_name": "Burglary, Postal"}, {"value": "2300", "display_name": "Interstate Commerce"}, {"value": "2400", "display_name": "Burglary, Other"}, {"value": "3100", "display_name": "Larceny & Theft, Bank"}, {"value": "3200", "display_name": "Larceny & Theft, Postal"}, {"value": "3300", "display_name": "Interstate Commerce"}, {"value": "3400", "display_name": "Theft of U.S. Property"}, {"value": "3500", "display_name": "Theft within Special Maritime Jurisdiction"}, {"value": "3600", "display_name": "Transportation of Stolen Property"}, {"value": "3700", "display_name": "Larceny & Theft, Felony Other"}, {"value": "3800", "display_name": "Larceny & Theft, Misdemeanor Other"}, {"value": "4100", "display_name": "Bank Embezzlement"}, {"value": "4200", "display_name": "Postal Embezzlement"}, {"value": "4310", "display_name": "Embezzles Public Moneys Or Property"}, {"value": "4320", "display_name": "Lending, Credit, Insurance Institutions"}, {"value": "4330", "display_name": "By Officers Of A Carrier"}, {"value": "4340", "display_name": "World War Veterans Relief"}, {"value": "4350", "display_name": "Embezzlement: Officer or Employee of U.S. Govt."}, {"value": "4390", "display_name": "Embezzlement, Other"}, {"value": "4510", "display_name": "Income Tax, Evade or Defeat"}, {"value": "4520", "display_name": "Income Tax, Other Felony"}, {"value": "4530", "display_name": "Income Tax, Failure to File"}, {"value": "4540", "display_name": "Income Tax, Other Misdemeanor"}, {"value": "4600", "display_name": "Lending, Credit Institutions"}, {"value": "4601", "display_name": "Bank Fraud"}, {"value": "4700", "display_name": "Postal, Interstate Wire, Radio, etc."}, {"value": "4800", "display_name": "Veterans and Allotments"}, {"value": "4900", "display_name": "Bankruptcy"}, {"value": "4910", "display_name": "Marketing Agreements & Commodity Credit"}, {"value": "4920", "display_name": "Securities & Exchange"}, {"value": "4931", "display_name": "<PERSON><PERSON>, Excise Tax, Other"}, {"value": "4932", "display_name": "<PERSON><PERSON>, Wagering Tax, Other"}, {"value": "4933", "display_name": "<PERSON><PERSON>, Other Tax"}, {"value": "4940", "display_name": "Railroad Retirement & Unemployment"}, {"value": "4941", "display_name": "Fraud Food Stamp Program"}, {"value": "4950", "display_name": "Social Security"}, {"value": "4960", "display_name": "False Personation"}, {"value": "4970", "display_name": "Nationality Laws"}, {"value": "4980", "display_name": "Passport Fraud"}, {"value": "4991", "display_name": "False Claims & Statements"}, {"value": "4992", "display_name": "<PERSON><PERSON>, Conspiracy to <PERSON><PERSON><PERSON>, Other"}, {"value": "4993", "display_name": "<PERSON><PERSON>, Consp<PERSON> (General), Other"}, {"value": "4994", "display_name": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Other"}, {"value": "4995", "display_name": "Credit Card Fraud"}, {"value": "4996", "display_name": "<PERSON> Fraud"}, {"value": "4997", "display_name": "Telemarketing Fraud"}, {"value": "4998", "display_name": "Health Care Fraud"}, {"value": "4999", "display_name": "<PERSON><PERSON>, Other"}, {"value": "5100", "display_name": "Transport etc. Stolen Vehicles, Aircraft"}, {"value": "5200", "display_name": "Auto Theft, Other"}, {"value": "5500", "display_name": "Transport, Forged Securities"}, {"value": "5600", "display_name": "Forgery, Postal"}, {"value": "5710", "display_name": "Forgery, Other U. S."}, {"value": "5720", "display_name": "<PERSON><PERSON><PERSON>, Other"}, {"value": "5800", "display_name": "Counterfeiting"}, {"value": "5900", "display_name": "Sexually Explicit Material"}, {"value": "6100", "display_name": "Sexual Abuse of Adult"}, {"value": "6110", "display_name": "Sexual Abuse of Children"}, {"value": "6120", "display_name": "Interstate Domestic Violence"}, {"value": "6121", "display_name": "Viol<PERSON>, Other"}, {"value": "6200", "display_name": "White Slave & Importing Aliens"}, {"value": "6300", "display_name": "Sex Offenses, Other"}, {"value": "6301", "display_name": "Transportation for Illegal Sexual Activity"}, {"value": "6400", "display_name": "Failure to Register"}, {"value": "6500", "display_name": "Narc. Marijuana Tax Act (Terms/Reopens)"}, {"value": "6501", "display_name": "Marijuana-Sell, Distribute, or Dispense"}, {"value": "6502", "display_name": "Marijuana-Importation/Exportation"}, {"value": "6503", "display_name": "Marijuana-Manufacture"}, {"value": "6504", "display_name": "Marijuana-Possession"}, {"value": "6505", "display_name": "Marijuana-Records, Rx's, Fraudulent Rx"}, {"value": "6600", "display_name": "Narc. Border Registration (Terms/Reopens)"}, {"value": "6700", "display_name": "Narcotics, Other (Terms/Reopens)"}, {"value": "6701", "display_name": "Narcotics-Sell, Distribute, or Dispense"}, {"value": "6702", "display_name": "Narcotics-Importation/Exportation"}, {"value": "6703", "display_name": "Narcotics-Manufacture"}, {"value": "6704", "display_name": "Narcotics-Possession"}, {"value": "6705", "display_name": "Narcotics-Records, Rx'S, Fraudulent Rx's"}, {"value": "6706", "display_name": "Narcotics, Other (Terms/Reopens)"}, {"value": "6707", "display_name": "Narcotics, Other (Terms/Reopens)"}, {"value": "6800", "display_name": "Continuing Criminal Enterprise"}, {"value": "6801", "display_name": "Controlled Substance-Sell, Distribute, or Dispense"}, {"value": "6802", "display_name": "Controlled Substance-Importation/Exportation"}, {"value": "6803", "display_name": "Controlled Substance-Manufacture"}, {"value": "6804", "display_name": "Controlled Substance-Possession"}, {"value": "6805", "display_name": "Control Substance-Records, Rx's, Fraudulent Rx's"}, {"value": "6806", "display_name": "Drug Cultivation"}, {"value": "6807", "display_name": "Illicit Drug Profits"}, {"value": "6808", "display_name": "Controlled Substances Aboard Aircraft"}, {"value": "6809", "display_name": "Mail Order Drug Paraphernalia"}, {"value": "6810", "display_name": "Under Influence Alcohol/Drugs"}, {"value": "6900", "display_name": "Polluting Federal Lands-Controlled Substance"}, {"value": "6905", "display_name": "Other Drug Offenses"}, {"value": "6907", "display_name": "Illicit Drug Profits"}, {"value": "6909", "display_name": "Mail Order Drug Paraphernalia"}, {"value": "6911", "display_name": "Other DAPCA Offenses"}, {"value": "7100", "display_name": "B<PERSON>bery"}, {"value": "7130", "display_name": "Conflict of Interest-Mining"}, {"value": "7131", "display_name": "Conflict of Interest-Health/Welfare"}, {"value": "7210", "display_name": "Traffic Offenses, Drunken Driving"}, {"value": "7220", "display_name": "Traffic Offenses, Other"}, {"value": "7310", "display_name": "Escape"}, {"value": "7311", "display_name": "Escape, Jumping Bail"}, {"value": "7312", "display_name": "Escape, Bail Reform Act of 1966"}, {"value": "7313", "display_name": "Escape from Custody"}, {"value": "7314", "display_name": "Criminal Default"}, {"value": "7320", "display_name": "Escape, Aiding or Harboring"}, {"value": "7330", "display_name": "Prison Contraband"}, {"value": "7331", "display_name": "<PERSON><PERSON>, Other"}, {"value": "7400", "display_name": "Extortion, Racketeering, & Threats"}, {"value": "7401", "display_name": "Threat<PERSON> Against The President"}, {"value": "7410", "display_name": "Racketeering, Arson"}, {"value": "7420", "display_name": "Racketeering, Bribery"}, {"value": "7430", "display_name": "Racketeering, Extortion"}, {"value": "7440", "display_name": "Racketeering, Gambling"}, {"value": "7450", "display_name": "Racketeering, Liquor"}, {"value": "7460", "display_name": "Racketeering, Narcotics"}, {"value": "7470", "display_name": "Racketeering, Prostitution"}, {"value": "7471", "display_name": "Racketeering, Murder"}, {"value": "7472", "display_name": "Racketeering, Kidnap"}, {"value": "7473", "display_name": "Racketeering, <PERSON><PERSON>"}, {"value": "7474", "display_name": "Conspiracy, Murder, Kidnap"}, {"value": "7475", "display_name": "Attempt, Conspire/Maim, Assault"}, {"value": "7477", "display_name": "Monetary Laundering"}, {"value": "7478", "display_name": "Murder, First Degree"}, {"value": "7480", "display_name": "Racketeering"}, {"value": "7481", "display_name": "Racketeering, <PERSON><PERSON>"}, {"value": "7482", "display_name": "Racketeering, Threats"}, {"value": "7490", "display_name": "Racketeering, Extortion Credit Transaction"}, {"value": "7500", "display_name": "Gambling & Lottery"}, {"value": "7520", "display_name": "Gambling & Lottery, Travel/Racketeering"}, {"value": "7530", "display_name": "Gambling & Lottery, Transmit Wager Info."}, {"value": "7600", "display_name": "Kidnapping (18:1201,1202)"}, {"value": "7601", "display_name": "Kidnapping, Govt Officials"}, {"value": "7610", "display_name": "Kidnapping (18:13)"}, {"value": "7611", "display_name": "Kidnap, Hostage"}, {"value": "7700", "display_name": "Perjury"}, {"value": "7800", "display_name": "Firearms & Weapons"}, {"value": "7820", "display_name": "Firearms, Unlawful Possession"}, {"value": "7830", "display_name": "Firearms"}, {"value": "7831", "display_name": "Furtherance of Violence"}, {"value": "7910", "display_name": "Arson"}, {"value": "7920", "display_name": "Abortion"}, {"value": "7930", "display_name": "<PERSON><PERSON><PERSON>"}, {"value": "7940", "display_name": "Malicious Destruction of Property"}, {"value": "7941", "display_name": "Other, Property"}, {"value": "7950", "display_name": "Disorderly Conduct"}, {"value": "7961", "display_name": "Travel to Incite to Riot"}, {"value": "7962", "display_name": "Civil Disorder"}, {"value": "7990", "display_name": "Misc. General <PERSON>, Other"}, {"value": "7991", "display_name": "Juvenile Delinquency"}, {"value": "8100", "display_name": "Failure to Pay Child Support"}, {"value": "8200", "display_name": "False Claims and Services, Government"}, {"value": "8201", "display_name": "Identification Documents and Information Fraud"}, {"value": "8500", "display_name": "Mail Fraud"}, {"value": "8600", "display_name": "Wire, Radio, or Television Fraud"}, {"value": "8710", "display_name": "Immigration Laws, Illegal Entry"}, {"value": "8720", "display_name": "Immigration Laws, Illegal Re-Entry"}, {"value": "8730", "display_name": "Immigration Laws, Other"}, {"value": "8731", "display_name": "Fraud And Misuse of Visa/Permits"}, {"value": "8740", "display_name": "Immigration Laws, Illegal Entry"}, {"value": "8750", "display_name": "Immigration Laws, Fraudulent Citizenship"}, {"value": "8900", "display_name": "<PERSON><PERSON><PERSON><PERSON>, Internal Revenue"}, {"value": "8901", "display_name": "<PERSON><PERSON>, Other Tax"}, {"value": "9001", "display_name": "Hazardous Waste-Treatment/Disposal/Store"}, {"value": "9110", "display_name": "Agriculture Acts"}, {"value": "9115", "display_name": "Agriculture Acts"}, {"value": "9120", "display_name": "Agriculture, Federal Seed Act"}, {"value": "9130", "display_name": "Game Conservation Acts"}, {"value": "9140", "display_name": "Agriculture, Insecticide Act"}, {"value": "9150", "display_name": "National Park/Recreation Violations"}, {"value": "9160", "display_name": "Agriculture, Packers & Stockyard Act"}, {"value": "9170", "display_name": "Agriculture, Plant Quarantine"}, {"value": "9180", "display_name": "Agriculture, Handling Animals, Research"}, {"value": "9200", "display_name": "Antitrust Violations"}, {"value": "9300", "display_name": "Fair Labor Standards Act"}, {"value": "9400", "display_name": "Food & Drug Act"}, {"value": "9500", "display_name": "Migratory Bird Laws"}, {"value": "9600", "display_name": "Motor Carrier Act"}, {"value": "9710", "display_name": "National Defense, Selective Service Acts"}, {"value": "9720", "display_name": "National Defense, Illegal Use of Uniform"}, {"value": "9730", "display_name": "National Defense, Defense Production Act"}, {"value": "9731", "display_name": "Economic Stabilization Act of 1970-Price"}, {"value": "9732", "display_name": "Economic Stabilization Act of 1970-Rents"}, {"value": "9733", "display_name": "Economic Stabilization Act of 1970-Wages"}, {"value": "9740", "display_name": "Alien Registration"}, {"value": "9741", "display_name": "Energy Facility"}, {"value": "9751", "display_name": "<PERSON><PERSON><PERSON>"}, {"value": "9752", "display_name": "Espionage"}, {"value": "9753", "display_name": "Sabotage"}, {"value": "9754", "display_name": "Sedition"}, {"value": "9755", "display_name": "Smith Act"}, {"value": "9760", "display_name": "<PERSON><PERSON><PERSON>w, Restricted Areas"}, {"value": "9770", "display_name": "Exportation of War Materials"}, {"value": "9771", "display_name": "Anti-Apartheid Program"}, {"value": "9780", "display_name": "Trading with the Enemy Act"}, {"value": "9790", "display_name": "National Defense, Other"}, {"value": "9791", "display_name": "Subversive Activities Control Act"}, {"value": "9792", "display_name": "Defense Contractors"}, {"value": "9793", "display_name": "Armed Forces"}, {"value": "9810", "display_name": "Obscene Mail"}, {"value": "9820", "display_name": "Obscene Matter in Interstate Commerce"}, {"value": "9901", "display_name": "Civil Rights"}, {"value": "9902", "display_name": "Election Law Violators"}, {"value": "9903", "display_name": "Federal Statues-Public Officer/Employees"}, {"value": "9904", "display_name": "Federal Statute-U.S. Emblems/Insignias"}, {"value": "9905", "display_name": "Federal Statutes-Foreign Relations"}, {"value": "9906", "display_name": "Federal Statutes-Bank and Banking"}, {"value": "9907", "display_name": "Federal Statutes-Money and Finance"}, {"value": "9908", "display_name": "Federal Statutes-Public Health & Welfare"}, {"value": "9909", "display_name": "Federal Statute-Census"}, {"value": "9910", "display_name": "Communication Acts (Including Wire Tapping)"}, {"value": "9911", "display_name": "Wire Interception"}, {"value": "9912", "display_name": "Federal Statutes-Copyright Laws"}, {"value": "9914", "display_name": "Federal Statutes-Coast Guard"}, {"value": "9915", "display_name": "Federal Statutes-Commerce And Trade"}, {"value": "9916", "display_name": "Federal Statutes-Consumer Credit Protection"}, {"value": "9917", "display_name": "Federal Statutes-Consumer Product Safety"}, {"value": "9918", "display_name": "Federal Statues-Toxic Substance Control"}, {"value": "9919", "display_name": "Federal Statutes-Title 5"}, {"value": "9920", "display_name": "Federal Statutes-Conservation Acts"}, {"value": "9921", "display_name": "Contempt"}, {"value": "9922", "display_name": "Con<PERSON><PERSON>, Congressional"}, {"value": "9923", "display_name": "Forfeiture - Criminal or Drug Related"}, {"value": "9926", "display_name": "Federal Statutes-Extort/Oppress under Law"}, {"value": "9928", "display_name": "Federal Statutes-Removal from State Court"}, {"value": "9929", "display_name": "Federal Statutes-Labor Laws"}, {"value": "9930", "display_name": "Federal Statutes-Minerals & Land Mining"}, {"value": "9931", "display_name": "Customs Laws (Except Narcotics & Liquor)"}, {"value": "9932", "display_name": "Customs Laws - Import Injurious Animals"}, {"value": "9935", "display_name": "Patents and Trademarks"}, {"value": "9936", "display_name": "Patriotic Societies And Observances"}, {"value": "9938", "display_name": "Veterans Benefits"}, {"value": "9940", "display_name": "Social Security"}, {"value": "9941", "display_name": "Connally Act/Hot Oil Act"}, {"value": "9942", "display_name": "Transport Convict-Made Goods Interstate"}, {"value": "9943", "display_name": "Railroad & Transportation Acts"}, {"value": "9944", "display_name": "Destruction of Property, Interstate Commerce"}, {"value": "9947", "display_name": "Telephones Telegraphs & Radios"}, {"value": "9949", "display_name": "Federal Statute-Transportation"}, {"value": "9950", "display_name": "War and National Defense, Other"}, {"value": "9951", "display_name": "Transportation of Strikebreakers"}, {"value": "9952", "display_name": "Taft Hartley Act"}, {"value": "9953", "display_name": "Eight Hour Day on Public Works"}, {"value": "9954", "display_name": "Peonage"}, {"value": "9956", "display_name": "Federal Statute, Phw"}, {"value": "9957", "display_name": "Terrorist Activity"}, {"value": "9960", "display_name": "Liquor (Except Internal Revenue)"}, {"value": "9971", "display_name": "Maritime & Shipping Laws"}, {"value": "9972", "display_name": "Stowaways"}, {"value": "9973", "display_name": "Federal Boat Safety Act of 1971"}, {"value": "9974", "display_name": "Federal Water Pollution Control Act"}, {"value": "9981", "display_name": "Postal, Non Mailable Material"}, {"value": "9982", "display_name": "Postal, Injury to Property"}, {"value": "9983", "display_name": "Postal, Obstructing the Mail"}, {"value": "9984", "display_name": "Postal, Violations By Postal Employees"}, {"value": "9989", "display_name": "Postal, Other"}, {"value": "9990", "display_name": "National Park/Recreation Violations"}, {"value": "9991", "display_name": "Destroying Federal Property"}, {"value": "9992", "display_name": "Intimidation of Witnesses, Jurors, etc."}, {"value": "9993", "display_name": "Aircraft Regulations"}, {"value": "9994", "display_name": "Explosives (Except on Vessels)"}, {"value": "9995", "display_name": "Gold Acts"}, {"value": "9996", "display_name": "Train Wrecking"}, {"value": "9999", "display_name": "Federal Statutes, Other"}]}, "version": {"type": "integer", "required": false, "read_only": false, "label": "Version", "help_text": "This field was created in FY 2012. It increments with each update received to a defendant record.", "min_value": -2147483648, "max_value": 2147483647}, "circuit": {"type": "field", "required": false, "read_only": false, "label": "Circuit", "help_text": "Circuit in which the case was filed."}, "district": {"type": "field", "required": false, "read_only": false, "label": "District", "help_text": "District court in which the case was filed."}}}, "clusters": {"type": "field", "required": true, "read_only": false, "label": "Clusters"}, "audio_files": {"type": "field", "required": true, "read_only": false, "label": "Audio files"}, "assigned_to": {"type": "field", "required": true, "read_only": false, "label": "Assigned to"}, "referred_to": {"type": "field", "required": true, "read_only": false, "label": "Referred to"}, "absolute_url": {"type": "string", "required": false, "read_only": true, "label": "Absolute url"}, "date_created": {"type": "datetime", "required": false, "read_only": true, "label": "Date created", "help_text": "The moment when the item was created."}, "date_modified": {"type": "datetime", "required": false, "read_only": true, "label": "Date modified", "help_text": "The last moment when the item was modified. A value in year 1750 indicates the value is unknown"}, "source": {"type": "choice", "required": true, "read_only": false, "label": "Source", "help_text": "contains the source of the Docket.", "choices": [{"value": 0, "display_name": "<PERSON><PERSON><PERSON>"}, {"value": 1, "display_name": "RECAP"}, {"value": 2, "display_name": "<PERSON><PERSON><PERSON>"}, {"value": 3, "display_name": "RECAP and Scraper"}, {"value": 4, "display_name": "Columbia"}, {"value": 6, "display_name": "Columbia and Scraper"}, {"value": 5, "display_name": "Columbia and RECAP"}, {"value": 7, "display_name": "Columbia, RECAP, and Scraper"}, {"value": 8, "display_name": "Integrated Database"}, {"value": 9, "display_name": "RECAP and IDB"}, {"value": 10, "display_name": "Scraper and IDB"}, {"value": 11, "display_name": "RECAP, Scraper, and IDB"}, {"value": 12, "display_name": "Columbia and IDB"}, {"value": 13, "display_name": "Columbia, RECAP, and IDB"}, {"value": 14, "display_name": "Columbia, Scraper, and IDB"}, {"value": 15, "display_name": "Columbia, RECAP, Scraper, and IDB"}, {"value": 16, "display_name": "Harvard"}, {"value": 17, "display_name": "Harvard and RECAP"}, {"value": 18, "display_name": "Sc<PERSON>er and Harvard"}, {"value": 19, "display_name": "RECAP, Scraper and Harvard"}, {"value": 20, "display_name": "Harvard and Columbia"}, {"value": 21, "display_name": "Columbia, RECAP, and Harvard"}, {"value": 22, "display_name": "Columbia, Scraper, and Harvard"}, {"value": 23, "display_name": "Columbia, RECAP, Scraper, and Harvard"}, {"value": 24, "display_name": "IDB and Harvard"}, {"value": 25, "display_name": "RECAP, IDB and Harvard"}, {"value": 26, "display_name": "Scraper, IDB and Harvard"}, {"value": 27, "display_name": "RECAP, Scraper, IDB and Harvard"}, {"value": 28, "display_name": "Columbia, IDB, and Harvard"}, {"value": 29, "display_name": "Columbia, Recap, IDB, and Harvard"}, {"value": 30, "display_name": "Columbia, Scraper, IDB, and Harvard"}, {"value": 31, "display_name": "Columbia, Recap, Scraper, IDB, and Harvard"}, {"value": 32, "display_name": "Direct court input"}, {"value": 33, "display_name": "RECAP and Direct court input"}, {"value": 34, "display_name": "Scraper and Direct court input"}, {"value": 35, "display_name": "RECAP, Scraper, and Direct court input"}, {"value": 36, "display_name": "Columbia and Direct court input"}, {"value": 37, "display_name": "RECAP, Columbia, and Direct court input"}, {"value": 38, "display_name": "Scraper, Columbia, and Direct court input"}, {"value": 39, "display_name": "RECAP, Scraper, Columbia, and Direct court input"}, {"value": 40, "display_name": "IDB and Direct court input"}, {"value": 41, "display_name": "RECAP, IDB, and Direct court input"}, {"value": 42, "display_name": "Scraper, IDB, and Direct court input"}, {"value": 43, "display_name": "RECAP, Scraper, IDB, and Direct court input"}, {"value": 44, "display_name": "Columbia, IDB, and Direct court input"}, {"value": 45, "display_name": "RECAP, Columbia, IDB, and Direct court input"}, {"value": 46, "display_name": "Scraper, Columbia, IDB, and Direct court input"}, {"value": 47, "display_name": "RECAP, Scraper, Columbia, IDB, and Direct court input"}, {"value": 48, "display_name": "Direct court input and Harvard"}, {"value": 49, "display_name": "RECAP, Harvard, and Direct court input"}, {"value": 50, "display_name": "<PERSON><PERSON>er, Harvard, and Direct court input"}, {"value": 51, "display_name": "RECAP, Scraper, Harvard, and Direct court input"}, {"value": 52, "display_name": "Columbia, Harvard, and Direct court input"}, {"value": 53, "display_name": "RECAP, Columbia, Harvard, and Direct court input"}, {"value": 54, "display_name": "<PERSON><PERSON>er, Columbia, Harvard, and Direct court input"}, {"value": 55, "display_name": "RECAP, Scraper, Columbia, Harvard, and Direct court input"}, {"value": 56, "display_name": "IDB, Harvard, and Direct court input"}, {"value": 57, "display_name": "RECAP, IDB, Harvard, and Direct court input"}, {"value": 58, "display_name": "Scraper, IDB, Harvard, and Direct court input"}, {"value": 59, "display_name": "RECAP, Scraper, IDB, Harvard, and Direct court input"}, {"value": 60, "display_name": "Columbia, IDB, Harvard, and Direct court input"}, {"value": 61, "display_name": "RECAP, Columbia, IDB, Harvard, and Direct court input"}, {"value": 62, "display_name": "Scraper, Columbia, IDB, Harvard, and Direct court input"}, {"value": 63, "display_name": "RECAP, Scraper, Columbia, IDB, Harvard, and Direct court input"}, {"value": 64, "display_name": "2020 anonymous database"}, {"value": 72, "display_name": "IDB and 2020 anonymous database"}, {"value": 66, "display_name": "2020 anonymous database and Scraper"}, {"value": 67, "display_name": "RECAP, Scraper, and 2020 anonymous database"}, {"value": 68, "display_name": "Columbia and 2020 anonymous database"}, {"value": 69, "display_name": "RECAP, Columbia, and 2020 anonymous database"}, {"value": 70, "display_name": "<PERSON><PERSON><PERSON>, Columbia, and 2020 anonymous database"}, {"value": 71, "display_name": "RECAP, Scraper, Columbia, and 2020 anonymous database"}, {"value": 73, "display_name": "RECAP, IDB, and 2020 anonymous database"}, {"value": 74, "display_name": "Scraper, IDB, and 2020 anonymous database"}, {"value": 75, "display_name": "RECAP, Scraper, IDB, and 2020 anonymous database"}, {"value": 76, "display_name": "Columbia, IDB, and 2020 anonymous database"}, {"value": 77, "display_name": "RECAP, Columbia, IDB, and 2020 anonymous database"}, {"value": 78, "display_name": "Scraper, Columbia, IDB, and 2020 anonymous database"}, {"value": 79, "display_name": "RECAP, Scraper, Columbia, IDB, and 2020 anonymous database"}, {"value": 80, "display_name": "2020 anonymous database and Harvard"}, {"value": 82, "display_name": "2020 anonymous database, Scraper, and Harvard"}, {"value": 81, "display_name": "RECAP, Harvard, and 2020 anonymous database"}, {"value": 83, "display_name": "RECAP, Scraper, Harvard, and 2020 anonymous database"}, {"value": 84, "display_name": "Columbia, Harvard, and 2020 anonymous database"}, {"value": 85, "display_name": "RECAP, Columbia, Harvard, and 2020 anonymous database"}, {"value": 86, "display_name": "<PERSON><PERSON><PERSON>, Columbia, Harvard, and 2020 anonymous database"}, {"value": 87, "display_name": "RECAP, Scraper, Columbia, Harvard, and 2020 anonymous database"}, {"value": 88, "display_name": "IDB, Harvard, and 2020 anonymous database"}, {"value": 89, "display_name": "RECAP, IDB, Harvard, and 2020 anonymous database"}, {"value": 90, "display_name": "<PERSON><PERSON>er, IDB, Harvard, and 2020 anonymous database"}, {"value": 91, "display_name": "RECAP, Scraper, IDB, Harvard, and 2020 anonymous database"}, {"value": 92, "display_name": "Columbia, IDB, Harvard, and 2020 anonymous database"}, {"value": 93, "display_name": "RECAP, Columbia, IDB, Harvard, and 2020 anonymous database"}, {"value": 94, "display_name": "<PERSON><PERSON><PERSON>, Columbia, IDB, Harvard, and 2020 anonymous database"}, {"value": 95, "display_name": "RECAP, Scraper, Columbia, IDB, Harvard, and 2020 anonymous database"}, {"value": 96, "display_name": "Direct court input and 2020 anonymous database"}, {"value": 97, "display_name": "RECAP, Direct court input, and 2020 anonymous database"}, {"value": 98, "display_name": "Scraper, Direct court input, and 2020 anonymous database"}, {"value": 99, "display_name": "RECAP, Scraper, Direct court input, and 2020 anonymous database"}, {"value": 100, "display_name": "Columbia, Direct court input, and 2020 anonymous database"}, {"value": 101, "display_name": "RECAP, Columbia, Direct court input, and 2020 anonymous database"}, {"value": 102, "display_name": "<PERSON><PERSON>er, Columbia, Direct court input, and 2020 anonymous database"}, {"value": 103, "display_name": "RECAP, Scraper, Columbia, Direct court input, and 2020 anonymous database"}, {"value": 104, "display_name": "IDB, Direct court input, and 2020 anonymous database"}, {"value": 105, "display_name": "RECAP, IDB, Direct court input, and 2020 anonymous database"}, {"value": 106, "display_name": "Scraper, IDB, Direct court input, and 2020 anonymous database"}, {"value": 107, "display_name": "RECAP, Scraper, IDB, Direct court input, and 2020 anonymous database"}, {"value": 108, "display_name": "Columbia, IDB, Direct court input, and 2020 anonymous database"}, {"value": 109, "display_name": "RECAP, Columbia, IDB, Direct court input, and 2020 anonymous database"}, {"value": 110, "display_name": "Scraper, Columbia, IDB, Direct court input, and 2020 anonymous database"}, {"value": 111, "display_name": "RECAP, Scraper, Columbia, IDB, Direct court input, and 2020 anonymous database"}, {"value": 112, "display_name": "Harvard, Direct court input, and 2020 anonymous database"}, {"value": 113, "display_name": "RECAP, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 114, "display_name": "<PERSON><PERSON><PERSON>, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 115, "display_name": "RECAP, Scraper, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 116, "display_name": "Columbia, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 117, "display_name": "RECAP, Columbia, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 118, "display_name": "<PERSON><PERSON><PERSON>, Columbia, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 119, "display_name": "RECAP, Scraper, Columbia, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 120, "display_name": "IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 121, "display_name": "RECAP, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 122, "display_name": "Scraper, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 123, "display_name": "RECAP, Scraper, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 124, "display_name": "Columbia, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 125, "display_name": "RECAP, Columbia, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 126, "display_name": "<PERSON><PERSON>er, Columbia, IDB, Harvard, Direct court input, and 2020 anonymous database"}, {"value": 127, "display_name": "RECAP, Scraper, Columbia, IDB, Harvard, Direct court input, and 2020 anonymous database"}]}, "appeal_from_str": {"type": "string", "required": false, "read_only": false, "label": "Appeal from str", "help_text": "In appellate cases, this is the lower court or administrative body where this case was originally heard. This field is frequently blank due to it not being populated historically. This field may have values when the appeal_from field does not. That can happen if we are unable to normalize the value in this field."}, "assigned_to_str": {"type": "string", "required": false, "read_only": false, "label": "Assigned to str", "help_text": "The judge that the case was assigned to, as a string."}, "referred_to_str": {"type": "string", "required": false, "read_only": false, "label": "Referred to str", "help_text": "The judge that the case was referred to, as a string."}, "panel_str": {"type": "string", "required": false, "read_only": false, "label": "Panel str", "help_text": "The initials of the judges on the panel that heard this case. This field is similar to the 'judges' field on the cluster, but contains initials instead of full judge names, and applies to the case on the whole instead of only to a specific decision."}, "date_last_index": {"type": "datetime", "required": false, "read_only": false, "label": "Date last index", "help_text": "The last moment that the item was indexed in Solr."}, "date_cert_granted": {"type": "date", "required": false, "read_only": false, "label": "Date cert granted", "help_text": "date cert was granted for this case, if applicable"}, "date_cert_denied": {"type": "date", "required": false, "read_only": false, "label": "Date cert denied", "help_text": "the date cert was denied for this case, if applicable"}, "date_argued": {"type": "date", "required": false, "read_only": false, "label": "Date argued", "help_text": "the date the case was argued"}, "date_reargued": {"type": "date", "required": false, "read_only": false, "label": "Date reargued", "help_text": "the date the case was reargued"}, "date_reargument_denied": {"type": "date", "required": false, "read_only": false, "label": "Date reargument denied", "help_text": "the date the reargument was denied"}, "date_filed": {"type": "date", "required": false, "read_only": false, "label": "Date filed", "help_text": "The date the case was filed."}, "date_terminated": {"type": "date", "required": false, "read_only": false, "label": "Date terminated", "help_text": "The date the case was terminated."}, "date_last_filing": {"type": "date", "required": false, "read_only": false, "label": "Date last filing", "help_text": "The date the case was last updated in the docket, as shown in PACER's Docket History report or iquery page."}, "case_name_short": {"type": "string", "required": false, "read_only": false, "label": "Case name short", "help_text": "The abridged name of the case, often a single word, e.g. 'Marsh'"}, "case_name": {"type": "string", "required": false, "read_only": false, "label": "Case name", "help_text": "The standard name of the case"}, "case_name_full": {"type": "string", "required": false, "read_only": false, "label": "Case name full", "help_text": "The full name of the case"}, "slug": {"type": "slug", "required": false, "read_only": false, "label": "Slug", "help_text": "URL that the document should map to (the slug)", "max_length": 75}, "docket_number": {"type": "string", "required": false, "read_only": false, "label": "Docket number", "help_text": "The docket numbers of a case, can be consolidated and quite long. In some instances they are too long to be indexed by postgres and we store the full docket in the correction field on the Opinion Cluster."}, "docket_number_core": {"type": "string", "required": false, "read_only": false, "label": "Docket number core", "help_text": "For federal district court dockets, this is the most distilled docket number available. In this field, the docket number is stripped down to only the year and serial digits, eliminating the office at the beginning, letters in the middle, and the judge at the end. Thus, a docket number like 2:07-cv-34911-MJL becomes simply 0734911. This is the format that is provided by the IDB and is useful for de-duplication types of activities which otherwise get messy. We use a char field here to preserve leading zeros.", "max_length": 20}, "federal_dn_office_code": {"type": "string", "required": false, "read_only": false, "label": "Federal dn office code", "help_text": "A one digit statistical code (either alphabetic or numeric) of the office within the federal district. In this example, 2:07-cv-34911-MJL, the 2 preceding the : is the office code.", "max_length": 3}, "federal_dn_case_type": {"type": "string", "required": false, "read_only": false, "label": "Federal dn case type", "help_text": "Case type, e.g., civil (cv), magistrate (mj), criminal (cr), petty offense (po), and miscellaneous (mc). These codes can be upper case or lower case, and may vary in number of characters.", "max_length": 6}, "federal_dn_judge_initials_assigned": {"type": "string", "required": false, "read_only": false, "label": "Federal dn judge initials assigned", "help_text": "A typically three-letter upper cased abbreviation of the judge's initials. In the example 2:07-cv-34911-MJL, MJL is the judge's initials. Judge initials change if a new judge takes over a case.", "max_length": 5}, "federal_dn_judge_initials_referred": {"type": "string", "required": false, "read_only": false, "label": "Federal dn judge initials referred", "help_text": "A typically three-letter upper cased abbreviation of the judge's initials. In the example 2:07-cv-34911-MJL-GOG, GOG is the magistrate judge initials.", "max_length": 5}, "federal_defendant_number": {"type": "integer", "required": false, "read_only": false, "label": "Federal defendant number", "help_text": "A unique number assigned to each defendant in a case, typically found in pacer criminal cases as a -1, -2 after the judge initials. Example: 1:14-cr-10363-RGS-1.", "min_value": -32768, "max_value": 32767}, "pacer_case_id": {"type": "string", "required": false, "read_only": false, "label": "Pacer case id", "help_text": "The case ID provided by PACER.", "max_length": 100}, "cause": {"type": "string", "required": false, "read_only": false, "label": "Cause", "help_text": "The cause for the case.", "max_length": 2000}, "nature_of_suit": {"type": "string", "required": false, "read_only": false, "label": "Nature of suit", "help_text": "The nature of suit code from PACER.", "max_length": 1000}, "jury_demand": {"type": "string", "required": false, "read_only": false, "label": "Jury demand", "help_text": "The compensation demand.", "max_length": 500}, "jurisdiction_type": {"type": "string", "required": false, "read_only": false, "label": "Jurisdiction type", "help_text": "Stands for jurisdiction in RECAP XML docket. For example, 'Diversity', 'U.S. Government Defendant'.", "max_length": 100}, "appellate_fee_status": {"type": "string", "required": false, "read_only": false, "label": "Appellate fee status", "help_text": "The status of the fee in the appellate court. Can be used as a hint as to whether the government is the appellant (in which case the fee is waived)."}, "appellate_case_type_information": {"type": "string", "required": false, "read_only": false, "label": "Appellate case type information", "help_text": "Information about a case from the appellate docket in PACER. For example, 'civil, private, bankruptcy'."}, "mdl_status": {"type": "string", "required": false, "read_only": false, "label": "Mdl status", "help_text": "The MDL status of a case before the Judicial Panel for Multidistrict Litigation", "max_length": 100}, "filepath_ia": {"type": "string", "required": false, "read_only": false, "label": "Filepath ia", "help_text": "Path to the Docket XML page in The Internet Archive", "max_length": 1000}, "filepath_ia_json": {"type": "string", "required": false, "read_only": false, "label": "Filepath ia json", "help_text": "Path to the docket JSON page in the Internet Archive", "max_length": 1000}, "ia_upload_failure_count": {"type": "integer", "required": false, "read_only": false, "label": "Ia upload failure count", "help_text": "Number of times the upload to the Internet Archive failed.", "min_value": -32768, "max_value": 32767}, "ia_needs_upload": {"type": "boolean", "required": false, "read_only": false, "label": "Ia needs upload", "help_text": "Does this item need to be uploaded to the Internet Archive? I.e., has it changed? This field is important because it keeps track of the status of all the related objects to the docket. For example, if a related docket entry changes, we need to upload the item to IA, but we can't easily check that."}, "ia_date_first_change": {"type": "datetime", "required": false, "read_only": false, "label": "Ia date first change", "help_text": "The moment when this item first changed and was marked as needing an upload. Used for determining when to upload an item."}, "date_blocked": {"type": "date", "required": false, "read_only": false, "label": "Date blocked", "help_text": "The date that this opinion was blocked from indexing by search engines"}, "blocked": {"type": "boolean", "required": false, "read_only": false, "label": "Blocked", "help_text": "Whether a document should be blocked from indexing by search engines"}, "appeal_from": {"type": "field", "required": false, "read_only": false, "label": "Appeal from", "help_text": "In appellate cases, this is the lower court or administrative body where this case was originally heard. This field is frequently blank due to it not being populated historically or due to our inability to normalize the value in appeal_from_str."}, "parent_docket": {"type": "field", "required": false, "read_only": false, "label": "Parent docket", "help_text": "In criminal cases (and some magistrate) PACER creates a parent docket and one or more child dockets. Child dockets contain docket information for each individual defendant while parent dockets are a superset of all docket entries."}, "tags": {"type": "field", "required": false, "read_only": false, "label": "Tags", "help_text": "The tags associated with the docket."}, "panel": {"type": "field", "required": false, "read_only": false, "label": "Panel", "help_text": "The empaneled judges for the case. Currently an unused field but planned to be used in conjunction with the panel_str field."}}}, "filters": {"id": {"type": "NumberRangeFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range"]}, "date_modified": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day", "hour", "minute", "second"]}, "date_created": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day", "hour", "minute", "second"]}, "date_filed": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day"]}, "date_terminated": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day"]}, "date_last_filing": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day"]}, "docket_number": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_types": ["exact"]}, "docket_number_core": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_types": ["exact", "startswith"]}, "nature_of_suit": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_types": ["exact", "iexact", "startswith", "istartswith", "endswith", "iendswith", "contains", "icontains"]}, "pacer_case_id": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_types": ["exact"]}, "source": {"type": "NumberInFilter", "lookup_types": ["exact", "in"]}, "date_blocked": {"type": "NumberFilter", "lookup_types": ["exact", "gte", "gt", "lte", "lt", "range", "year", "month", "day"]}, "blocked": {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_types": ["exact"]}, "court": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Courts'"}, "clusters": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Opinion Clusters'"}, "docket_entries": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Docket Entries'"}, "audio_files": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Audio Files'"}, "assigned_to": {"type": "RelatedFilter", "lookup_types": "See available filters for 'People'"}, "referred_to": {"type": "RelatedFilter", "lookup_types": "See available filters for 'People'"}, "parties": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Parties'"}, "tags": {"type": "RelatedFilter", "lookup_types": "See available filters for 'Tags'"}}, "ordering": ["id", "date_created", "date_modified", "date_blocked", "date_filed", "date_terminated", "date_last_filing"]}
# Frontend Integration Quick Start

## 🚀 Week 4 API Ready for Integration

### 📍 Service URL
```
https://legal-api-stg-[hash]-uc.a.run.app
```
*(Will be provided after deployment)*

### 🔐 Authentication
All endpoints require Supabase JWT token:
```javascript
headers: {
  'Authorization': `Bearer ${supabaseJwtToken}`
}
```

## 📋 Available Endpoints

### 1. Search API
```http
GET /v0/search?q={query}&jurisdiction={jurisdiction}&limit={limit}
```

**Example**:
```javascript
const response = await fetch(`${SERVICE_URL}/v0/search?q=medical%20malpractice&jurisdiction=tx&limit=10`, {
  headers: { 'Authorization': `Bear<PERSON> ${token}` }
});
```

**Response**:
```json
{
  "results": [
    {
      "id": "C-2023-TX-123",
      "title": "<PERSON> v. Jones Medical Malpractice",
      "type": "case",
      "jurisdiction": "tx",
      "authority_score": 0.87,
      "relevance_score": 0.92,
      "snippet": "This case involves...",
      "metadata": { "court": "Texas Supreme Court" }
    }
  ],
  "total": 156,
  "query_time_ms": 245,
  "cached": false
}
```

### 2. Recommendations API
```http
GET /v0/recommend/{document_id}?limit={limit}
```

**Example**:
```javascript
const response = await fetch(`${SERVICE_URL}/v0/recommend/C-2023-TX-123?limit=5`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

**Response**:
```json
{
  "document_id": "C-2023-TX-123",
  "recommendations": [
    {
      "id": "C-2019-TX-456",
      "title": "Johnson v. Memorial Hospital",
      "type": "case",
      "relationship": "cites",
      "strength": 0.85,
      "reason": "Cited by this document",
      "score_explainer": "Authority score: 0.72, cited 12 times. This document is directly referenced...",
      "authority_score": 0.72
    }
  ],
  "total": 5
}
```

### 3. Graph API (React-Flow Ready)
```http
GET /v0/graph?id={document_id}&depth={1-3}&direction={both|in|out}&max_nodes={limit}
```

**Example**:
```javascript
const response = await fetch(`${SERVICE_URL}/v0/graph?id=C-2023-TX-123&depth=2&max_nodes=50`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

**Response** (React-Flow Compatible):
```json
{
  "nodes": [
    {
      "id": "C-2023-TX-123",
      "label": "Smith v. Jones",
      "type": "case",
      "authority": 0.87,
      "data": {
        "jurisdiction": "tx",
        "court": "Texas Supreme Court",
        "date": "2023-03-15"
      }
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "C-2023-TX-123",
      "target": "C-2019-TX-456",
      "type": "cites",
      "data": {
        "weight": 34,
        "relationship_type": "supportive"
      }
    }
  ],
  "metadata": {
    "returned_nodes": 25,
    "total_nodes": 156,
    "truncated": false,
    "query_time_ms": 156
  }
}
```

## ⚡ React-Flow Integration

### Direct Usage
```jsx
import ReactFlow from 'reactflow';

function LegalNetworkGraph({ documentId }) {
  const [graphData, setGraphData] = useState({ nodes: [], edges: [] });

  useEffect(() => {
    fetch(`${SERVICE_URL}/v0/graph?id=${documentId}&depth=2`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(res => res.json())
    .then(data => {
      setGraphData({
        nodes: data.nodes,
        edges: data.edges
      });
    });
  }, [documentId]);

  return (
    <ReactFlow
      nodes={graphData.nodes}
      edges={graphData.edges}
      fitView
    />
  );
}
```

### Sample Data for Development
```javascript
// Use this endpoint for development without auth
const sampleData = await fetch(`${SERVICE_URL}/v0/graph/sample`);
```

## 🔍 LangGraph Tool Integration

### Search Tool
```python
def search_legal_documents(query: str, jurisdiction: str = None) -> dict:
    """Search legal documents with hybrid semantic + keyword search."""
    url = f"{SERVICE_URL}/v0/search"
    params = {"q": query}
    if jurisdiction:
        params["jurisdiction"] = jurisdiction
    
    response = requests.get(url, params=params, headers=auth_headers)
    return response.json()
```

### Recommendation Tool
```python
def get_document_recommendations(document_id: str, limit: int = 5) -> dict:
    """Get related document recommendations."""
    url = f"{SERVICE_URL}/v0/recommend/{document_id}"
    params = {"limit": limit}
    
    response = requests.get(url, params=params, headers=auth_headers)
    return response.json()
```

### Graph Analysis Tool
```python
def get_document_network(document_id: str, depth: int = 2) -> dict:
    """Get citation network for document analysis."""
    url = f"{SERVICE_URL}/v0/graph"
    params = {"id": document_id, "depth": depth, "max_nodes": 100}
    
    response = requests.get(url, params=params, headers=auth_headers)
    return response.json()
```

## 📊 Performance Headers

### Cache Status
```javascript
// Check if response was cached
const cacheStatus = response.headers.get('X-Cache'); // 'HIT' or 'MISS'
```

### Rate Limiting
```javascript
// Handle rate limits gracefully
if (response.status === 429) {
  const retryAfter = response.headers.get('Retry-After'); // seconds
  // Wait and retry
}
```

## 🚨 Error Handling

### Common Response Codes
- `200` - Success
- `401` - Invalid/expired JWT token
- `403` - Insufficient permissions
- `429` - Rate limit exceeded (includes Retry-After header)
- `500` - Server error

### Example Error Handler
```javascript
async function apiCall(url, options) {
  const response = await fetch(url, options);
  
  if (response.status === 401) {
    // Refresh JWT token
    await refreshToken();
    return apiCall(url, options);
  }
  
  if (response.status === 429) {
    const retryAfter = response.headers.get('Retry-After');
    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
    return apiCall(url, options);
  }
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }
  
  return response.json();
}
```

## 🎯 Quick Test

### No-Auth Test
```bash
curl https://your-service-url/health
curl https://your-service-url/v0/graph/sample
```

### With Auth Test
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "https://your-service-url/v0/search?q=test"
```

## 📞 Support

- **API Documentation**: `/docs` endpoint (Swagger UI)
- **Health Check**: `/health` endpoint
- **Sample Data**: `/v0/graph/sample` endpoint

**Ready to integrate! 🚀**

# Week 4 Production Deployment Guide

## 🎯 Overview

This guide walks through deploying the Week 4 implementation to Google Cloud Run for production staging and testing.

## 📋 Prerequisites

### 1. Required Tools
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Install Docker
# Follow instructions at: https://docs.docker.com/get-docker/

# Verify installations
gcloud --version
docker --version
```

### 2. GCP Setup
```bash
# Authenticate with Google Cloud
gcloud auth login

# Set your project ID
export PROJECT_ID=your-actual-gcp-project-id
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### 3. Environment Variables
```bash
# Required
export PROJECT_ID=your-actual-gcp-project-id

# Optional (defaults provided)
export TAG=week4
export REGION=us-central1
export SERVICE_NAME=legal-api-stg
```

## 🚀 Deployment Steps

### Option A: Automated Deployment (Recommended)

```bash
# Navigate to repository root
cd /path/to/texas-laws-personalinjury

# Set your project ID
export PROJECT_ID=your-actual-gcp-project-id

# Run the deployment script
./scripts/deploy_week4.sh
```

The script will:
- ✅ Check all prerequisites
- ✅ Build the Docker image
- ✅ Push to Google Container Registry
- ✅ Deploy to Cloud Run
- ✅ Run smoke tests
- ✅ Provide next steps

### Option B: Manual Deployment

#### 1. Build & Push Docker Image
```bash
# Set variables
export TAG=week4
export PROJECT_ID=your-actual-gcp-project-id

# Build the image
docker build -t gcr.io/$PROJECT_ID/legal-api:$TAG .

# Configure Docker for GCR
gcloud auth configure-docker

# Push the image
docker push gcr.io/$PROJECT_ID/legal-api:$TAG
```

#### 2. Deploy to Cloud Run
```bash
gcloud run deploy legal-api-stg \
  --image gcr.io/$PROJECT_ID/legal-api:$TAG \
  --region us-central1 \
  --memory 1Gi \
  --max-instances 3 \
  --allow-unauthenticated \
  --set-env-vars "CACHE_BACKEND=memory" \
  --set-env-vars "SUPABASE_JWKS_URL=https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys" \
  --set-env-vars "LOG_LEVEL=INFO" \
  --platform managed \
  --port 8000 \
  --timeout 300 \
  --concurrency 80
```

## 🧪 Testing the Deployment

### 1. Get Service URL
```bash
SERVICE_URL=$(gcloud run services describe legal-api-stg --region us-central1 \
             --format='value(status.url)')
echo "Service URL: $SERVICE_URL"
```

### 2. Basic Health Checks
```bash
# Test health endpoint
curl "$SERVICE_URL/health"

# Test API root
curl "$SERVICE_URL/"

# Test sample graph (no auth required)
curl "$SERVICE_URL/v0/graph/sample"
```

### 3. Authenticated Endpoint Testing

You'll need a valid Supabase JWT token. Get one from your Supabase dashboard or authentication flow.

```bash
# Set your JWT token
export JWT_TOKEN="your-supabase-jwt-token"

# Test search endpoint
curl -H "Authorization: Bearer $JWT_TOKEN" \
     "$SERVICE_URL/v0/search?q=medical%20malpractice&jurisdiction=tx" -i

# Expected: HTTP 200 + X-Cache: MISS header

# Test recommendation endpoint
curl -H "Authorization: Bearer $JWT_TOKEN" \
     "$SERVICE_URL/v0/recommend/C-2023-TX-123" -i

# Test graph endpoint
curl -H "Authorization: Bearer $JWT_TOKEN" \
     "$SERVICE_URL/v0/graph?id=C-2023-TX-123&depth=2&direction=both" -i
```

### 4. Performance Testing
```bash
# Test response times
time curl -H "Authorization: Bearer $JWT_TOKEN" \
          "$SERVICE_URL/v0/search?q=test" -s > /dev/null

# Test cache headers
curl -H "Authorization: Bearer $JWT_TOKEN" \
     "$SERVICE_URL/v0/search?q=test" -I | grep X-Cache
```

## 📊 Monitoring & Logs

### View Logs
```bash
# Real-time logs
gcloud run services logs tail legal-api-stg --region us-central1

# Recent logs
gcloud run services logs read legal-api-stg --region us-central1 --limit 50
```

### Monitor Performance
```bash
# Service details
gcloud run services describe legal-api-stg --region us-central1

# Traffic allocation
gcloud run services list --filter="metadata.name:legal-api-stg"
```

## 🔧 Configuration

### Current Environment Variables
- `CACHE_BACKEND=memory` (in-process cache, no Redis VPC yet)
- `SUPABASE_JWKS_URL=https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys`
- `LOG_LEVEL=INFO`

### Resource Limits
- **Memory**: 1 GiB
- **CPU**: 1 vCPU (default)
- **Max Instances**: 3
- **Concurrency**: 80 requests per instance
- **Timeout**: 300 seconds

## 🎯 Frontend/LangGraph Integration

### Service Information
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe legal-api-stg --region us-central1 \
             --format='value(status.url)')
```

### API Endpoints
- **Search**: `GET $SERVICE_URL/v0/search?q={query}&jurisdiction={jurisdiction}`
- **Recommendations**: `GET $SERVICE_URL/v0/recommend/{document_id}`
- **Graph**: `GET $SERVICE_URL/v0/graph?id={document_id}&depth={1-3}&direction={both|in|out}`

### Authentication
All endpoints require `Authorization: Bearer {supabase_jwt_token}` header.

### Sample Integration
```python
import requests

SERVICE_URL = "your-cloud-run-url"
JWT_TOKEN = "your-supabase-jwt"

headers = {"Authorization": f"Bearer {JWT_TOKEN}"}

# Search for documents
response = requests.get(
    f"{SERVICE_URL}/v0/search",
    params={"q": "medical malpractice", "jurisdiction": "tx"},
    headers=headers
)

# Get recommendations
response = requests.get(
    f"{SERVICE_URL}/v0/recommend/C-2023-TX-123",
    headers=headers
)

# Get graph data for React-Flow
response = requests.get(
    f"{SERVICE_URL}/v0/graph",
    params={"id": "C-2023-TX-123", "depth": 2},
    headers=headers
)
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Authentication Errors
```bash
# Check JWKS URL is accessible
curl https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys

# Verify JWT token format
echo $JWT_TOKEN | cut -d'.' -f1 | base64 -d
```

#### 2. Memory Issues
```bash
# Increase memory if needed
gcloud run services update legal-api-stg \
  --memory 2Gi \
  --region us-central1
```

#### 3. Cold Start Issues
```bash
# Set minimum instances to reduce cold starts
gcloud run services update legal-api-stg \
  --min-instances 1 \
  --region us-central1
```

### Debug Commands
```bash
# Check service status
gcloud run services describe legal-api-stg --region us-central1

# View recent errors
gcloud run services logs read legal-api-stg --region us-central1 \
  --filter="severity>=ERROR" --limit 20

# Test local Docker image
docker run -p 8000:8000 \
  -e CACHE_BACKEND=memory \
  -e SUPABASE_JWKS_URL=https://anwefmklplkjxkmzpnva.supabase.co/auth/v1/keys \
  gcr.io/$PROJECT_ID/legal-api:week4
```

## 🎉 Success Criteria

- ✅ Service deploys without errors
- ✅ Health check returns 200
- ✅ Sample endpoints work without authentication
- ✅ Authenticated endpoints work with valid JWT
- ✅ Response times < 1 second for typical queries
- ✅ Cache headers present in responses
- ✅ Logs show no critical errors

## 📞 Support

If you encounter issues:
1. Check the logs: `gcloud run services logs tail legal-api-stg --region us-central1`
2. Verify environment variables are set correctly
3. Test with the sample endpoints first
4. Ensure JWT tokens are valid and not expired

**The Week 4 API is now ready for production testing and frontend integration!** 🚀

# Legal Database API v1 Documentation

## Overview

The Legal Database API v1 provides enhanced search capabilities with advanced filtering, role-based access control, and production-grade rate limiting. This version builds upon v0 with significant improvements in functionality and security.

## Base URL

```
https://your-api-domain.com/v1
```

## Authentication

All v1 endpoints require JWT authentication via Supabase Auth.

```http
Authorization: Bearer <supabase_jwt_token>
```

## Rate Limits

- **Per User**: 100 requests/minute
- **Per Tenant**: 1000 requests/minute

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Unix timestamp when limit resets
- `Retry-After`: Seconds to wait when rate limited (429 responses only)

## Enhanced Search Endpoint

### `GET /v1/search`

Advanced search with comprehensive filtering capabilities.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `q` | string | Yes | Search query (1-500 characters) |
| `jurisdiction` | string | No | Filter by jurisdiction (tx, ny, fl, oh, fed) |
| `practice_areas` | array[string] | No | Filter by practice areas |
| `authority_min` | float | No | Minimum authority score (0.0-1.0) |
| `date_start` | string | No | Start date (ISO format: YYYY-MM-DD) |
| `date_end` | string | No | End date (ISO format: YYYY-MM-DD) |
| `sort_by` | string | No | Sort order: `relevance` (default), `authority`, `date` |
| `limit` | integer | No | Results per page (1-100, default: 10) |
| `offset` | integer | No | Pagination offset (default: 0) |

#### Supported Practice Areas

- `personal_injury`
- `medical_malpractice`
- `product_liability`
- `premises_liability`
- `motor_vehicle`
- `wrongful_death`
- `criminal_defense`
- `family_law`
- `business_law`

#### Example Request

```http
GET /v1/search?q=medical%20malpractice&jurisdiction=tx&practice_areas=personal_injury&practice_areas=medical_malpractice&authority_min=0.5&date_start=2020-01-01&date_end=2023-12-31&sort_by=authority&limit=20&offset=0
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Response Format

```json
{
  "results": [
    {
      "id": "C-2023-TX-123",
      "title": "Smith v. Memorial Hospital",
      "type": "case",
      "jurisdiction": "tx",
      "authority_score": 0.87,
      "relevance_score": 0.92,
      "snippet": "The court held that medical malpractice requires...",
      "metadata": {
        "court": "Texas Supreme Court",
        "date": "2023-03-15",
        "practice_areas": ["personal_injury", "medical_malpractice"],
        "citation": "123 S.W.3d 456"
      }
    }
  ],
  "total": 15,
  "total_hits": 156,
  "next_offset": 20,
  "query_time_ms": 245,
  "query": "medical malpractice",
  "filters_applied": {
    "jurisdiction": "tx",
    "practice_areas": ["personal_injury", "medical_malpractice"],
    "authority_min": 0.5,
    "date_range": {
      "start": "2020-01-01",
      "end": "2023-12-31"
    },
    "sort_by": "authority"
  },
  "cached": false
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `results` | array | Array of search results |
| `total` | integer | Number of results in current page |
| `total_hits` | integer | Total matching documents (before pagination) |
| `next_offset` | integer\|null | Next pagination offset (null if no more results) |
| `query_time_ms` | integer | Query execution time in milliseconds |
| `query` | string | Original search query |
| `filters_applied` | object | Applied filters and parameters |
| `cached` | boolean | Whether result was served from cache |

#### Search Result Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique document identifier |
| `title` | string | Document title |
| `type` | string | Document type (case, statute) |
| `jurisdiction` | string | Jurisdiction code |
| `authority_score` | float | Authority/importance score (0.0-1.0) |
| `relevance_score` | float | Relevance to search query (0.0-1.0) |
| `snippet` | string | Highlighted text excerpt |
| `metadata` | object | Additional document metadata |

## Role-Based Access Control (RBAC)

### Role Hierarchy

| Role | Level | Description |
|------|-------|-------------|
| `client` | 1 | Basic access to own cases only |
| `staff` | 2 | Read-only access within tenant |
| `paralegal` | 3 | Limited access within assigned jurisdictions |
| `attorney` | 4 | Full access within assigned jurisdictions |
| `partner` | 5 | Full access across all jurisdictions |

### Jurisdiction Access

Users can be restricted to specific jurisdictions via the `jurisdictions` claim in their JWT token:

```json
{
  "sub": "user123",
  "role": "attorney",
  "app_metadata": {
    "jurisdictions": ["tx", "ny"],
    "tenant_id": "law-firm-1"
  }
}
```

If a user requests data from a jurisdiction they don't have access to, they'll receive a `403 Forbidden` response.

## Error Responses

### 400 Bad Request
```json
{
  "error": "validation_error",
  "message": "Invalid parameter value",
  "details": {
    "field": "authority_min",
    "issue": "Value must be between 0.0 and 1.0"
  }
}
```

### 401 Unauthorized
```json
{
  "error": "authentication_required",
  "message": "Valid JWT token required"
}
```

### 403 Forbidden
```json
{
  "error": "access_denied",
  "message": "Access denied to jurisdiction: ca",
  "user_role": "attorney",
  "user_jurisdictions": ["tx", "ny"]
}
```

### 429 Too Many Requests
```json
{
  "error": "rate_limit_exceeded",
  "message": "Rate limit exceeded. Try again later.",
  "remaining_requests": 0,
  "reset_time": 1640995200
}
```

### 500 Internal Server Error
```json
{
  "error": "internal_server_error",
  "message": "An internal error occurred",
  "timestamp": 1640995200
}
```

## Caching

The API implements intelligent caching to improve performance:

- **Search Results**: Cached for 15 minutes
- **Cache Keys**: Include user permissions to ensure security
- **Cache Headers**: `X-Cache: HIT|MISS` indicates cache status

## Performance

### Response Time Targets

- **p95 latency**: < 800ms for complex filtered searches
- **Typical response**: < 300ms for simple searches
- **Cache hit response**: < 50ms

### Optimization Tips

1. **Use specific filters**: More specific queries perform better
2. **Limit result size**: Use appropriate `limit` values
3. **Cache-friendly queries**: Identical queries benefit from caching
4. **Authority filtering**: Use `authority_min` to focus on high-quality results

## Migration from v0

### Key Differences

| Feature | v0 | v1 |
|---------|----|----|
| Practice area filtering | ❌ | ✅ |
| Authority score filtering | ❌ | ✅ |
| Date range filtering | ❌ | ✅ |
| Advanced sorting | ❌ | ✅ |
| Enhanced pagination | ❌ | ✅ |
| RBAC integration | Basic | Full |
| Rate limiting | Basic | Enhanced |

### Backward Compatibility

v0 endpoints remain available and fully functional. You can migrate gradually:

1. Test v1 endpoints in development
2. Update client applications to use v1 features
3. Monitor performance and error rates
4. Complete migration when ready

## Examples

### Basic Search
```bash
curl -X GET "https://api.example.com/v1/search?q=negligence" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### Advanced Filtered Search
```bash
curl -X GET "https://api.example.com/v1/search" \
  -G \
  -d "q=medical malpractice" \
  -d "jurisdiction=tx" \
  -d "practice_areas=personal_injury" \
  -d "practice_areas=medical_malpractice" \
  -d "authority_min=0.7" \
  -d "date_start=2020-01-01" \
  -d "sort_by=authority" \
  -d "limit=50" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### Pagination
```bash
# First page
curl -X GET "https://api.example.com/v1/search?q=contract&limit=10&offset=0" \
  -H "Authorization: Bearer $JWT_TOKEN"

# Second page (using next_offset from previous response)
curl -X GET "https://api.example.com/v1/search?q=contract&limit=10&offset=10" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

## Support

For API support, please contact:
- **Documentation**: [API Documentation Portal]
- **Issues**: [GitHub Issues]
- **Email**: <EMAIL>

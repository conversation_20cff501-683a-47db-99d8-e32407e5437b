# Role-Based Access Control (RBAC) Documentation

## Overview

The Legal Database API implements a comprehensive Role-Based Access Control (RBAC) system to ensure secure and appropriate access to legal documents and data. This system controls both what users can access and what actions they can perform based on their role and jurisdiction assignments.

## Role Hierarchy

The system implements a hierarchical role structure where higher-level roles inherit permissions from lower-level roles:

| Role | Level | Description | Typical Use Case |
|------|-------|-------------|------------------|
| `client` | 1 | Basic access to own cases only | Individual clients accessing their case information |
| `staff` | 2 | Read-only access within tenant | Administrative staff, receptionists |
| `paralegal` | 3 | Limited access within assigned jurisdictions | Paralegals working on specific cases |
| `attorney` | 4 | Full access within assigned jurisdictions | Licensed attorneys |
| `partner` | 5 | Full access across all jurisdictions | Law firm partners, senior attorneys |

### Permission Inheritance

- **Partners** can access everything that attorneys, paralegals, staff, and clients can access
- **Attorneys** can access everything that paralegals, staff, and clients can access
- **Paralegals** can access everything that staff and clients can access
- **Staff** can access everything that clients can access
- **Clients** have the most restricted access

## JWT Token Structure

The RBAC system extracts user information from JWT tokens issued by Supabase Auth. The system looks for role and jurisdiction information in the following order:

### 1. Direct Fields
```json
{
  "sub": "user-uuid-123",
  "email": "<EMAIL>",
  "role": "attorney",
  "tenant_id": "lawfirm-1",
  "jurisdictions": ["tx", "ny"]
}
```

### 2. App Metadata
```json
{
  "sub": "user-uuid-123",
  "email": "<EMAIL>",
  "app_metadata": {
    "role": "attorney",
    "tenant_id": "lawfirm-1",
    "jurisdictions": ["tx", "ny"]
  }
}
```

### 3. User Metadata
```json
{
  "sub": "user-uuid-123",
  "email": "<EMAIL>",
  "user_metadata": {
    "role": "attorney",
    "tenant_id": "lawfirm-1",
    "jurisdictions": ["tx", "ny"]
  }
}
```

## Required JWT Claims

| Claim | Type | Required | Description |
|-------|------|----------|-------------|
| `sub` | string | Yes | Unique user identifier |
| `role` | string | No* | User role (defaults to 'client' if not found) |
| `tenant_id` | string | No | Organization/tenant identifier |
| `jurisdictions` | array[string] | No | Allowed jurisdictions (empty = no restrictions) |

*Role is technically optional but strongly recommended for proper access control.

## Jurisdiction Access Control

### Jurisdiction Codes

The system supports the following jurisdiction codes:

| Code | Jurisdiction | Description |
|------|-------------|-------------|
| `tx` | Texas | Texas state courts and law |
| `ny` | New York | New York state courts and law |
| `fl` | Florida | Florida state courts and law |
| `oh` | Ohio | Ohio state courts and law |
| `ca` | California | California state courts and law |
| `fed` | Federal | Federal courts and law |

### Access Rules

1. **No Jurisdiction Restrictions**: If `jurisdictions` is empty or not provided, user can access all jurisdictions
2. **Specific Jurisdictions**: If `jurisdictions` contains specific codes, user can only access those jurisdictions
3. **Case-Insensitive**: Jurisdiction matching is case-insensitive
4. **Inheritance**: Jurisdiction restrictions apply regardless of role level

### Examples

```json
// Full access to all jurisdictions
{
  "sub": "partner-123",
  "role": "partner",
  "jurisdictions": []
}

// Limited to Texas and New York
{
  "sub": "attorney-456",
  "role": "attorney",
  "jurisdictions": ["tx", "ny"]
}

// Limited to Federal cases only
{
  "sub": "paralegal-789",
  "role": "paralegal",
  "jurisdictions": ["fed"]
}
```

## Tenant-Based Access Control

### Multi-Tenant Architecture

The system supports multi-tenant access where users belong to specific organizations (law firms, legal departments, etc.):

- **Tenant Isolation**: Users can only access data within their tenant
- **Cross-Tenant Restrictions**: No access to other tenants' data
- **Rate Limiting**: Separate rate limits per tenant

### Tenant ID Usage

```json
{
  "sub": "user-123",
  "role": "attorney",
  "tenant_id": "biglaw-firm-1",
  "jurisdictions": ["tx", "ny"]
}
```

## Access Validation Process

The RBAC system validates access through the following steps:

### 1. Token Verification
- Verify JWT signature using Supabase JWKS
- Check token expiration
- Validate audience claim

### 2. Role Extraction
- Extract role from token (with fallback hierarchy)
- Default to 'client' if no valid role found
- Log warnings for missing roles

### 3. Permission Checking
- Compare user role level with required role level
- Check jurisdiction access if specific jurisdiction requested
- Validate tenant membership if applicable

### 4. Access Decision
- Grant access if all checks pass
- Return 403 Forbidden with specific reason if any check fails
- Log all access attempts for audit purposes

## API Integration

### Endpoint Protection

All API endpoints are protected by RBAC middleware that:

1. Extracts user information from JWT tokens
2. Validates access based on role and jurisdiction
3. Logs access attempts for security auditing
4. Returns appropriate error responses for access violations

### Request State

The middleware attaches user information to the request state:

```python
request.state.user_id = "user-123"
request.state.role = "attorney"
request.state.tenant_id = "lawfirm-1"
request.state.jurisdictions = ["tx", "ny"]
```

### Error Responses

#### Insufficient Role
```json
{
  "error": "access_denied",
  "message": "Insufficient permissions. Required role: partner",
  "user_role": "attorney",
  "status_code": 403
}
```

#### Jurisdiction Access Denied
```json
{
  "error": "access_denied",
  "message": "No access to jurisdiction: ca",
  "user_jurisdictions": ["tx", "ny"],
  "status_code": 403
}
```

## Security Logging

The RBAC system logs all access attempts for security monitoring:

### Successful Access
```json
{
  "level": "INFO",
  "message": "Access granted",
  "user_id": "user-123",
  "role": "attorney",
  "tenant_id": "lawfirm-1",
  "endpoint": "/v1/search",
  "status_code": 200,
  "client_ip": "*************",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Access Denied
```json
{
  "level": "WARNING",
  "message": "Access denied",
  "user_id": "user-123",
  "role": "paralegal",
  "tenant_id": "lawfirm-1",
  "endpoint": "/v1/search",
  "status_code": 403,
  "client_ip": "*************",
  "reason": "No access to jurisdiction: ca",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Configuration Examples

### Supabase Auth Setup

Configure user roles and jurisdictions in Supabase:

```sql
-- Create user roles table
CREATE TABLE user_roles (
  user_id UUID REFERENCES auth.users(id),
  role TEXT NOT NULL,
  tenant_id TEXT,
  jurisdictions TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own role" ON user_roles
  FOR SELECT USING (auth.uid() = user_id);
```

### JWT Claims Function

Create a Supabase function to add claims to JWT tokens:

```sql
CREATE OR REPLACE FUNCTION auth.jwt_claims(user_id UUID)
RETURNS JSON AS $$
DECLARE
  user_role RECORD;
BEGIN
  SELECT role, tenant_id, jurisdictions
  INTO user_role
  FROM user_roles
  WHERE user_roles.user_id = jwt_claims.user_id;
  
  RETURN JSON_BUILD_OBJECT(
    'role', COALESCE(user_role.role, 'client'),
    'tenant_id', user_role.tenant_id,
    'jurisdictions', COALESCE(user_role.jurisdictions, ARRAY[]::TEXT[])
  );
END;
$$ LANGUAGE plpgsql;
```

## Best Practices

### 1. Principle of Least Privilege
- Assign the minimum role necessary for user's job function
- Limit jurisdiction access to only what's needed
- Regularly review and update permissions

### 2. Role Assignment Guidelines
- **Clients**: Only access to their own cases
- **Staff**: Read-only access for administrative tasks
- **Paralegals**: Limited write access within specific jurisdictions
- **Attorneys**: Full access within assigned jurisdictions
- **Partners**: Full access across all jurisdictions

### 3. Security Monitoring
- Monitor access logs for unusual patterns
- Set up alerts for repeated access denials
- Regular audit of user roles and permissions

### 4. Token Management
- Use short-lived tokens (1-24 hours)
- Implement proper token refresh mechanisms
- Revoke tokens immediately when users leave organization

## Troubleshooting

### Common Issues

1. **User gets 'client' role instead of expected role**
   - Check JWT token structure
   - Verify role is in correct field (role, app_metadata.role, or user_metadata.role)
   - Ensure role value matches expected values exactly

2. **Jurisdiction access denied unexpectedly**
   - Verify jurisdictions array in JWT token
   - Check for case sensitivity issues
   - Ensure jurisdiction codes are correct

3. **403 errors for valid users**
   - Check role hierarchy and required permissions
   - Verify tenant_id matches
   - Review access logs for specific denial reasons

### Debug Mode

Enable debug logging to troubleshoot RBAC issues:

```bash
export LOG_LEVEL=DEBUG
```

This will log detailed information about:
- JWT token parsing
- Role extraction process
- Permission checking steps
- Access validation results

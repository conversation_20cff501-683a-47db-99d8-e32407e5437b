# Redis Setup Guide for Production Rate Limiting

## Overview

The Legal Database API uses Redis for production-grade rate limiting and caching. This guide covers setting up Redis for both development and production environments.

## Environment Configuration

The system automatically switches between Redis and memory backends based on environment variables:

```bash
# Use Redis for rate limiting and caching
CACHE_BACKEND=redis
RATE_LIMIT_BACKEND=redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Use memory for development (default)
CACHE_BACKEND=memory
RATE_LIMIT_BACKEND=memory
```

## Development Setup

### Option 1: Docker (Recommended)

```bash
# Start Redis container
docker run -d \
  --name redis-dev \
  -p 6379:6379 \
  redis:7-alpine

# Test connection
redis-cli ping
# Should return: PONG
```

### Option 2: Local Installation

#### macOS (using Homebrew)
```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### CentOS/RHEL
```bash
sudo yum install epel-release
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

### Development Configuration

Create a `.env.development` file:

```bash
# Redis Configuration for Development
CACHE_BACKEND=redis
RATE_LIMIT_BACKEND=redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Rate Limiting Configuration
RATE_LIMIT_PER_USER=100
RATE_LIMIT_PER_TENANT=1000
```

## Production Setup

### Google Cloud Memorystore (Recommended for GCP)

#### 1. Create Memorystore Instance

```bash
# Set project and region
export PROJECT_ID=your-gcp-project
export REGION=us-central1

# Create Redis instance
gcloud redis instances create legal-api-redis \
  --size=1 \
  --region=$REGION \
  --redis-version=redis_7_0 \
  --tier=basic \
  --project=$PROJECT_ID
```

#### 2. Get Connection Details

```bash
# Get Redis host IP
gcloud redis instances describe legal-api-redis \
  --region=$REGION \
  --format="value(host)"

# Get Redis port (usually 6379)
gcloud redis instances describe legal-api-redis \
  --region=$REGION \
  --format="value(port)"
```

#### 3. Configure VPC Connector (if using Cloud Run)

```bash
# Create VPC connector for Cloud Run to access Memorystore
gcloud compute networks vpc-access connectors create legal-api-connector \
  --region=$REGION \
  --subnet=default \
  --subnet-project=$PROJECT_ID \
  --min-instances=2 \
  --max-instances=3
```

### AWS ElastiCache

#### 1. Create ElastiCache Cluster

```bash
# Create Redis cluster
aws elasticache create-cache-cluster \
  --cache-cluster-id legal-api-redis \
  --engine redis \
  --cache-node-type cache.t3.micro \
  --num-cache-nodes 1 \
  --port 6379 \
  --security-group-ids sg-xxxxxxxxx \
  --subnet-group-name default
```

#### 2. Get Connection Endpoint

```bash
# Get cluster endpoint
aws elasticache describe-cache-clusters \
  --cache-cluster-id legal-api-redis \
  --show-cache-node-info \
  --query 'CacheClusters[0].CacheNodes[0].Endpoint'
```

### Azure Cache for Redis

#### 1. Create Redis Cache

```bash
# Create resource group
az group create --name legal-api-rg --location eastus

# Create Redis cache
az redis create \
  --resource-group legal-api-rg \
  --name legal-api-redis \
  --location eastus \
  --sku Basic \
  --vm-size c0
```

#### 2. Get Connection Details

```bash
# Get hostname and keys
az redis show \
  --resource-group legal-api-rg \
  --name legal-api-redis \
  --query '[hostName,port]'

az redis list-keys \
  --resource-group legal-api-rg \
  --name legal-api-redis
```

## Production Configuration

### Environment Variables

```bash
# Redis Configuration
CACHE_BACKEND=redis
RATE_LIMIT_BACKEND=redis
REDIS_URL=redis://10.x.x.x:6379  # Internal IP for cloud providers
REDIS_PASSWORD=your_secure_password
REDIS_DB=0

# Rate Limiting Configuration
RATE_LIMIT_PER_USER=100
RATE_LIMIT_PER_TENANT=1000

# Connection Settings
REDIS_SOCKET_CONNECT_TIMEOUT=5
REDIS_SOCKET_TIMEOUT=5
REDIS_MAX_CONNECTIONS=20
```

### Security Configuration

#### 1. Enable Authentication

```bash
# In redis.conf
requirepass your_secure_password

# Or via command line
redis-cli CONFIG SET requirepass "your_secure_password"
```

#### 2. Network Security

```bash
# Bind to specific interfaces only
bind 127.0.0.1 10.x.x.x

# Disable dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d2e7f1"
```

#### 3. TLS/SSL (Production)

```bash
# Enable TLS in redis.conf
port 0
tls-port 6379
tls-cert-file /path/to/redis.crt
tls-key-file /path/to/redis.key
tls-ca-cert-file /path/to/ca.crt
```

Update connection URL for TLS:
```bash
REDIS_URL=rediss://username:password@hostname:6379
```

## Connection Testing

### Test Redis Connection

```python
# test_redis.py
import redis
import os

def test_redis_connection():
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
    password = os.getenv('REDIS_PASSWORD')
    
    try:
        r = redis.Redis.from_url(
            redis_url,
            password=password,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        
        # Test basic operations
        r.ping()
        print("✅ Redis connection successful")
        
        # Test set/get
        r.set('test_key', 'test_value', ex=60)
        value = r.get('test_key')
        print(f"✅ Set/Get test: {value}")
        
        # Test rate limiting pattern
        key = 'test_rate_limit'
        count = r.incr(key)
        r.expire(key, 60)
        print(f"✅ Rate limit test: {count}")
        
        # Cleanup
        r.delete('test_key', key)
        print("✅ All tests passed")
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")

if __name__ == "__main__":
    test_redis_connection()
```

Run the test:
```bash
python test_redis.py
```

### Test API with Redis

```bash
# Start the API with Redis enabled
export CACHE_BACKEND=redis
export RATE_LIMIT_BACKEND=redis
export REDIS_URL=redis://localhost:6379

python -m src.api.main

# Test rate limiting
for i in {1..5}; do
  curl -H "Authorization: Bearer $JWT_TOKEN" \
    http://localhost:8000/v1/search?q=test
  echo "Request $i completed"
done
```

## Monitoring and Maintenance

### Redis Monitoring

#### Key Metrics to Monitor

```bash
# Memory usage
redis-cli INFO memory

# Connection count
redis-cli INFO clients

# Operations per second
redis-cli INFO stats

# Key count
redis-cli INFO keyspace
```

#### Monitoring Commands

```bash
# Real-time monitoring
redis-cli MONITOR

# Slow query log
redis-cli SLOWLOG GET 10

# Client list
redis-cli CLIENT LIST
```

### Performance Tuning

#### Memory Optimization

```bash
# Set memory limit
redis-cli CONFIG SET maxmemory 256mb

# Set eviction policy for cache
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

#### Connection Pooling

```python
# In production, use connection pooling
import redis

pool = redis.ConnectionPool.from_url(
    redis_url,
    max_connections=20,
    retry_on_timeout=True,
    socket_keepalive=True,
    socket_keepalive_options={}
)

redis_client = redis.Redis(connection_pool=pool)
```

## Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
# Check if Redis is running
redis-cli ping

# Check port binding
netstat -tlnp | grep 6379

# Check firewall rules
sudo ufw status
```

#### 2. Authentication Errors
```bash
# Test with password
redis-cli -a your_password ping

# Check auth configuration
redis-cli CONFIG GET requirepass
```

#### 3. Memory Issues
```bash
# Check memory usage
redis-cli INFO memory

# Check for memory leaks
redis-cli MEMORY USAGE key_name
```

#### 4. Performance Issues
```bash
# Check slow queries
redis-cli SLOWLOG GET 10

# Monitor operations
redis-cli --latency

# Check key distribution
redis-cli --scan --pattern "*" | head -20
```

### Fallback Strategy

The API automatically falls back to memory-based backends if Redis is unavailable:

```python
# Automatic fallback in rate_limiter.py
try:
    return RedisRateLimitBackend(redis_url, redis_password)
except Exception as e:
    logger.warning(f"Redis unavailable, using memory backend: {e}")
    return MemoryRateLimitBackend()
```

## Migration Checklist

### Development to Production

- [ ] Redis instance provisioned and accessible
- [ ] VPC/network connectivity configured
- [ ] Authentication and security settings applied
- [ ] Environment variables updated
- [ ] Connection testing completed
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures established

### Switching Backends

```bash
# Current: Memory backend
CACHE_BACKEND=memory
RATE_LIMIT_BACKEND=memory

# Target: Redis backend
CACHE_BACKEND=redis
RATE_LIMIT_BACKEND=redis
REDIS_URL=redis://your-redis-host:6379
REDIS_PASSWORD=your_password
```

Restart the application after changing environment variables.

## Support

For Redis-related issues:

1. **Check logs**: Application logs will show Redis connection errors
2. **Test connectivity**: Use the provided test scripts
3. **Monitor metrics**: Set up Redis monitoring dashboards
4. **Fallback mode**: The system will automatically use memory backends if Redis fails

Remember: The API is designed to gracefully handle Redis unavailability by falling back to memory-based backends, ensuring service continuity.

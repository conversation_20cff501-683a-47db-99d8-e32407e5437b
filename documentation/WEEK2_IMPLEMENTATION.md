# Week 2 Implementation: Data Acquisition and Automated Case Harvesting

This document describes the Week 2 implementation of the legal database population roadmap, focusing on automated case harvesting, enhanced opinion processing, and metadata enhancement capabilities.

## Overview

Week 2 builds upon the foundation improvements from Week 1 and implements:

1. **Automated Case Harvesting**: Scheduled harvesting of cases from Court Listener API
2. **Enhanced Opinion Processing**: Advanced opinion classification and metadata extraction
3. **Case Metadata Enhancement**: Comprehensive metadata extraction and enrichment
4. **Multi-Jurisdictional Support**: Enhanced support for multiple jurisdictions

## New Features

### 1. Automated Harvesting System

**Components**:
- `AutomatedHarvester`: Main orchestrator with scheduling capabilities
- `JurisdictionHarvester`: Jurisdiction-specific harvesting logic
- `HarvestingConfigManager`: Configuration management and validation

**Key Features**:
- Cron-based scheduling for different jurisdictions
- Configurable harvesting parameters per jurisdiction
- Rate limiting and quality filtering
- Concurrent processing with configurable limits
- Comprehensive error handling and retry logic
- Real-time status monitoring and reporting

**Configuration** (`config/harvesting_config.json`):
```json
{
  "harvesting": {
    "enabled": true,
    "default_schedule": "0 2 * * *",
    "max_concurrent_jobs": 3,
    "retry_attempts": 3,
    "batch_size": 50,
    "rate_limit_delay": 0.5
  },
  "jurisdictions": {
    "tx": {
      "name": "Texas",
      "enabled": true,
      "priority": "high",
      "schedule": "0 1 * * *",
      "courts": ["tex", "texapp1st", ...],
      "practice_areas": ["personal_injury", "criminal_law", ...],
      "search_queries": {
        "personal_injury": ["negligence", "tort liability", ...]
      },
      "max_cases_per_run": 100
    }
  }
}
```

### 2. Enhanced Opinion Processing

**Component**: `EnhancedOpinionProcessor`

**Capabilities**:
- **Opinion Type Classification**: Majority, concurring, dissenting, plurality, per curiam
- **Author and Judge Extraction**: Identifies authoring judges and joining judges
- **Citation Extraction**: Extracts legal citations using pattern matching
- **Quality Assessment**: Evaluates opinion quality based on multiple factors
- **Topic Classification**: Identifies key legal topics and subject matter
- **Procedural Analysis**: Classifies procedural posture and outcomes

**Quality Metrics**:
- Word count analysis
- Citation density
- Structural indicators (conclusions, holdings)
- Legal reasoning patterns
- Overall quality scoring (High/Medium/Low/Poor)

### 3. Case Metadata Enhancement

**Component**: `CaseMetadataEnhancer`

**Enhanced Metadata Fields**:
- **Party Information**: Plaintiff/defendant identification, organization detection
- **Judge Information**: Authoring judges, biographical data
- **Case Classification**: Civil, criminal, constitutional, administrative
- **Subject Matter**: Practice area classification using keyword analysis
- **Procedural History**: Timeline of case proceedings
- **Outcome Analysis**: Case disposition and results
- **Completeness Scoring**: Data quality assessment

**Party Detection**:
- Automatic parsing of case names (e.g., "Smith v. Jones Corporation")
- Organization vs. individual classification
- Government entity identification
- Corporate structure recognition

### 4. Enhanced Case Law Processor Integration

**Improvements to `CaseLawProcessor`**:
- Integration with enhanced opinion processor
- Metadata enhancement workflow
- Improved quality metrics calculation
- Enhanced storage with additional metadata fields
- Better error handling and logging

## Installation and Setup

### 1. Install Dependencies

```bash
# Run the Week 2 setup script
python scripts/install_week2_dependencies.py
```

**New Dependencies**:
- `apscheduler>=3.10.0`: For automated scheduling
- `beautifulsoup4>=4.12.0`: For HTML parsing
- `lxml>=4.9.0`: XML/HTML parser

### 2. Configuration

1. **Review Harvesting Configuration**:
   ```bash
   # Edit the harvesting configuration
   nano config/harvesting_config.json
   ```

2. **Environment Variables**:
   Ensure these are set in your `.env` file:
   ```
   COURTLISTENER_API_KEY=your_api_key
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   ```

### 3. Database Schema Updates

Week 2 adds new fields to existing tables:

**Cases Table Enhancements**:
- `case_type`: Type of case (civil, criminal, etc.)
- `subject_matter`: Array of practice areas
- `outcome`: Case outcome
- `precedential_status`: Precedential value
- `judges`: Array of judge names
- `parties`: JSON array of party information
- `procedural_history`: Array of procedural events
- `subsequent_history`: Array of subsequent events

**Opinions Table Enhancements**:
- `opinion_type`: Type of opinion (majority, dissenting, etc.)
- `quality`: Quality assessment
- `procedural_posture`: Procedural context
- `key_topics`: Array of legal topics
- `joining_judges`: Array of joining judge names

## Usage Examples

### 1. Manual Harvesting

```python
from src.harvesting.automated_harvester import get_harvester

# Get harvester instance
harvester = get_harvester()

# Harvest specific jurisdiction
result = harvester.harvest_jurisdiction_now("tx", practice_area="personal_injury")
print(f"Processed {result.total_processed} cases, {result.total_success} successful")

# Harvest all enabled jurisdictions
results = harvester.harvest_all_now()
for jurisdiction, result in results.items():
    print(f"{jurisdiction}: {result.success_rate:.2%} success rate")
```

### 2. Automated Scheduling

```python
from src.harvesting.automated_harvester import start_harvesting, stop_harvesting

# Start automated harvesting
start_harvesting()
print("Automated harvesting started")

# Stop harvesting
stop_harvesting()
print("Automated harvesting stopped")
```

### 3. Enhanced Opinion Processing

```python
from src.processing.enhanced_opinion_processor import EnhancedOpinionProcessor

processor = EnhancedOpinionProcessor()

# Process opinion with enhanced capabilities
result = processor.process_opinion(opinion_data)
print(f"Opinion type: {result['metadata']['opinion_type']}")
print(f"Quality: {result['metadata']['quality']}")
print(f"Citations found: {len(result['citations'])}")
```

### 4. Metadata Enhancement

```python
from src.processing.metadata_enhancer import CaseMetadataEnhancer

enhancer = CaseMetadataEnhancer()

# Enhance case metadata
enhanced = enhancer.enhance_case_metadata(case_data, opinions_data)
print(f"Completeness score: {enhanced.completeness_score:.2f}")
print(f"Case type: {enhanced.case_type}")
print(f"Parties: {[p.name for p in enhanced.parties]}")
```

## Command Line Interface

### Start Harvester

```bash
# Start with default configuration
python scripts/start_harvester.py

# Start with custom log level
python scripts/start_harvester.py --log-level DEBUG

# Validate configuration only
python scripts/start_harvester.py --validate-only

# Check status
python scripts/start_harvester.py --status

# Manual harvest specific jurisdiction
python scripts/start_harvester.py --harvest-now tx

# Manual harvest all jurisdictions
python scripts/start_harvester.py --harvest-all-now
```

## Testing

### Run Week 2 Tests

```bash
# Run all Week 2 tests
python -m pytest tests/test_week2_implementation.py -v

# Run specific test categories
python -m pytest tests/test_week2_implementation.py::TestHarvestingConfig -v
python -m pytest tests/test_week2_implementation.py::TestEnhancedOpinionProcessor -v
python -m pytest tests/test_week2_implementation.py::TestAutomatedHarvester -v
```

### Test Coverage

The test suite covers:
- Configuration loading and validation
- Harvesting logic and error handling
- Opinion processing and classification
- Metadata enhancement and quality assessment
- Automated scheduling functionality

## Monitoring and Logging

### Log Files

- `logs/harvester.log`: Main harvesting log
- Application logs include detailed processing information

### Status Monitoring

```python
from src.harvesting.automated_harvester import get_harvester

harvester = get_harvester()
status = harvester.get_status()

print(f"Running: {status['is_running']}")
print(f"Total harvests: {status['stats']['total_harvests']}")
print(f"Success rate: {status['stats']['success_rate']:.2%}")
```

## Performance Considerations

### Rate Limiting

- Configurable delays between API requests
- Respect Court Listener API rate limits
- Batch processing to optimize throughput

### Concurrent Processing

- Configurable number of concurrent workers
- Thread-safe database operations
- Memory-efficient processing

### Quality Filtering

- Skip low-quality cases based on thresholds
- Prioritize high-citation cases
- Filter by required metadata fields

## Troubleshooting

### Common Issues

1. **Missing Dependencies**:
   ```bash
   python scripts/install_week2_dependencies.py
   ```

2. **Configuration Errors**:
   ```bash
   python scripts/start_harvester.py --validate-only
   ```

3. **API Rate Limiting**:
   - Increase `rate_limit_delay` in configuration
   - Reduce `batch_size` for slower processing

4. **Database Connection Issues**:
   - Verify environment variables
   - Check database connectivity

### Debug Mode

```bash
python scripts/start_harvester.py --log-level DEBUG
```

## Integration with Week 1

Week 2 builds upon Week 1 foundations:

- Uses enhanced jurisdiction configuration from Week 1
- Leverages improved storage organization
- Integrates with document type classification
- Maintains compatibility with existing processing pipeline

## Next Steps: Week 3

Week 2 provides the foundation for:
- **Week 3**: Integration and cross-document relationships
- Enhanced citation network analysis
- Full document retrieval API
- Advanced relationship mapping

The automated harvesting system ensures continuous data acquisition while the enhanced processing capabilities provide richer metadata for future analysis and relationship building.

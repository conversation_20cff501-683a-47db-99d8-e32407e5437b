# Court Listener API Integration

This documentation covers the Court Listener API integration with the Texas Laws Personal Injury project. The integration allows you to search, retrieve, and process case law from the Court Listener database, which contains millions of legal opinions from federal and state courts.

## Table of Contents
- [Overview](#overview)
- [Setup](#setup)
- [API Client](#api-client)
- [Batch Processing Integration](#batch-processing-integration)
- [Command Line Tool](#command-line-tool)
- [Examples](#examples)
- [Advanced Usage](#advanced-usage)
- [Troubleshooting](#troubleshooting)

## Overview

The Court Listener API integration provides the following capabilities:

- Search for cases by citation, text, or jurisdiction
- Retrieve full case details including opinions
- Extract citations from document text and fetch corresponding cases
- Enrich processed documents with related case law
- Cache retrieved data for performance

## Setup

### Prerequisites

1. Court Listener API key (obtain from [CourtListener.com](https://www.courtlistener.com/))
2. Python 3.6+
3. Required packages: `requests`, `python-dotenv`

### Installation

1. Add your Court Listener API key to your `.env` file:

```
COURTLISTENER_API_KEY=your_api_key_here
```

2. Install additional requirements:

```bash
pip install -r requirements-courtlistener.txt
```

## API Client

The API client (`CourtListenerClient`) provides direct access to the Court Listener REST API.

### Basic Usage

```python
from src.api.courtlistener.client import CourtListenerClient

# Initialize client
client = CourtListenerClient()

# Search by citation
cases = client.get_cases_by_citation("410 U.S. 113")  # Roe v. Wade

# Get case details
if cases:
    case = cases[0]
    print(f"Case: {case.name}")
    print(f"Court: {case.court.name}")
    print(f"Date: {case.date_filed}")
    
    # Get opinions
    opinions = client.get_opinions_by_case(case.id)
    if opinions:
        opinion = opinions[0]
        print(f"Opinion by: {opinion.author}")
        print(f"Text: {opinion.text[:200]}...")  # Show first 200 chars
```

### Key Methods

- `search_cases(query, ...)`: Search for cases using various criteria
- `get_cases_by_citation(citation)`: Get cases matching a specific citation
- `get_opinion(opinion_id)`: Get a specific opinion by ID
- `get_opinions_by_case(case_id)`: Get all opinions for a case
- `get_courts(jurisdiction)`: Get list of courts
- `search_by_jurisdiction(jurisdiction, query)`: Search within a jurisdiction
- `get_texas_cases(query)`: Convenience method for Texas cases

## Batch Processing Integration

The Court Listener batch processing integration extends the existing batch processor to enrich documents with case law data.

### Using the Extended Batch Processor

```python
from src.processing.batch_processor_courtlistener import CourtListenerBatchProcessor

# Initialize processor
processor = CourtListenerBatchProcessor()

# Process documents with Court Listener enrichment
processor.process_directory(
    input_dir="/path/to/pdfs",
    jurisdiction="Texas",
    max_workers=4
)

# Find related cases
related_cases = processor.find_related_cases(
    query="slip and fall premises liability",
    jurisdiction="Texas"
)
```

### What Happens During Processing

1. The regular PDF processing occurs first
2. If successful, the Court Listener enrichment phase begins
3. Citations are extracted from the document text
4. The system fetches cases matching those citations
5. If no citations are found, a text search is performed
6. Results are saved to the document's processing directory
7. Document metadata is updated with citation information

## Command Line Tool

The Court Listener CLI provides command-line access to the API.

### Available Commands

- **Citation Search**: Find cases by citation
  ```bash
  python scripts/courtlistener_cli.py citation "410 U.S. 113" --details
  ```

- **Text Search**: Search for cases by text
  ```bash
  python scripts/courtlistener_cli.py search "premises liability" --jurisdiction tex
  ```

- **Opinion Details**: Get opinion text
  ```bash
  python scripts/courtlistener_cli.py opinion 12345 --save
  ```

- **List Courts**: View available courts
  ```bash
  python scripts/courtlistener_cli.py courts --jurisdiction tex
  ```

### Output Formats

All commands support `--format` option with `text` (default) or `json` output:

```bash
python scripts/courtlistener_cli.py citation "410 U.S. 113" --format json
```

## Examples

See the `examples/courtlistener_example.py` script for detailed examples of:

- Citation searching
- Texas case law searching
- Using the batch processor integration

To run the examples:

```bash
python examples/courtlistener_example.py
```

## Advanced Usage

### Caching

Court Listener data is cached to improve performance and reduce API calls:

```python
# The CourtListenerProcessor handles caching automatically
processor = CourtListenerProcessor()

# Cache a case manually
case = client.get_cases_by_citation("410 U.S. 113")[0]
cache_id = processor.cache_case_data(case)

# Load a case from cache
cached_case = processor.load_cached_case(cache_id)
```

### Citation Extraction

Extract citations from text:

```python
from src.api.courtlistener.utils import extract_citation_from_text

text = "The Supreme Court's decision in Roe v. Wade, 410 U.S. 113 (1973), was controversial."
citations = extract_citation_from_text(text)
# Returns: ['410 U.S. 113']
```

### Jurisdiction Handling

Convert jurisdiction names to Court Listener codes:

```python
from src.api.courtlistener.utils import standardize_jurisdiction

code = standardize_jurisdiction("Texas")  # Returns: "tex"
```

## Troubleshooting

### API Key Issues

If you get authentication errors:

1. Check your `.env` file contains a valid `COURTLISTENER_API_KEY`
2. Verify the API key is active on your Court Listener account
3. Check for leading/trailing whitespace in the key

### Rate Limiting

Court Listener has rate limits. The client includes retry logic, but if you're making many requests:

1. Add delays between requests
2. Implement caching for frequently accessed data
3. Consider using the bulk data access if available

### Common Errors

- `CourtListenerAuthenticationError`: Invalid API key
- `CourtListenerRateLimitError`: Rate limit exceeded
- `CourtListenerResourceNotFoundError`: Citation or case not found
- `CourtListenerAPIError`: General API error

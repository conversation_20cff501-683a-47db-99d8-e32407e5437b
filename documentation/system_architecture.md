# Multi-Jurisdiction Legal Document Processing System

## System Architecture Documentation

### Overview

This system processes legal documents from multiple jurisdictions, extracting metadata, classifying documents, identifying citations, and creating a knowledge graph to visualize relationships between legal documents. The architecture combines vector search, relational database, and graph database technologies to provide a comprehensive legal document analysis solution.

## Document Processing Pipeline

![Document Processing Pipeline](pipeline_diagram.png)

### 1. Document Ingestion & Classification

Documents enter the system through the batch processing module:

- **Input**: PDF legal documents organized by jurisdiction folders
- **Process**:
  - `batch_processor.py` coordinates the processing workflow
  - `document_classifier.py` employs a two-stage classification process:
    - Rule-based pattern matching for initial classification
    - Gemini 2.5 Pro AI model for advanced classification and verification
  - Documents are tagged with metadata:
    - Jurisdiction (e.g., Federal, Texas, California)
    - Document type (e.g., statute, case law, regulation)
    - Document-specific metadata (e.g., court, date, section numbers)
- **Configuration**: `jurisdiction_config.json` defines patterns and rules for each jurisdiction

### 2. Text Extraction & Chunking

- **Process**:
  - PyMuPDF extracts text content from PDF documents
  - Text is segmented into semantic chunks that preserve legal context
  - Citations are extracted using regex patterns specific to each jurisdiction
  - Citations are normalized to standard formats
- **Output**: 
  - Clean text chunks with preserved semantic meaning
  - Structured citation data with source context

### 3. Vector Database (Pinecone)

- **Purpose**: Enable semantic search and retrieval across legal documents
- **Process**:
  - Text chunks are embedded using embedding models
  - Vectors are stored in Pinecone with relevant metadata:
    - Document ID
    - Chunk position
    - Jurisdiction and document type
    - Citation information
- **Benefits**:
  - Semantic similarity search across legal corpus
  - Fast retrieval of relevant document segments
  - Facilitates AI-powered legal research

### 4. Relational Database (Supabase)

- **Purpose**: Store structured document metadata and processing status
- **Tables**:
  - Documents: Core metadata about each document
  - Processing_Status: Tracks document processing workflow
  - Citations: Structured citation data
  - Jurisdictions: Jurisdiction-specific information
- **Benefits**:
  - Efficient filtering and querying of document metadata
  - Structured storage of processing status and audit logs
  - Relational integrity between documents and citations

### 5. Knowledge Graph (Neo4j)

- **Purpose**: Model and visualize relationships between legal documents
- **Implementation**: `document_graph.py` creates and manages the graph database
- **Structure**:
  - **Nodes**: Legal documents with properties:
    - Document ID
    - Title
    - Jurisdiction
    - Document type
    - Other metadata
  - **Relationships**: Citations between documents
    - CITES: Document A cites Document B
    - CITED_BY: Document B is cited by Document A
    - Citation context and type
- **Setup**: `setup_neo4j.py` initializes the database schema with appropriate constraints and indexes

### 6. Document Explorer Interface

- **Purpose**: Visualize and navigate the document relationship network
- **Features**:
  - Interactive graph visualization using D3.js
  - Document filtering by jurisdiction and type
  - Citation network exploration
  - Document metadata and citation viewing
  - Statistical dashboard showing document distribution and citation patterns
- **Implementation**: Flask-based web application:
  - `app.py`: Backend API endpoints
  - JavaScript frontend for interactive visualization

## Multi-Jurisdiction Support

The system is designed to handle documents from various jurisdictions with their unique formats and citation styles:

- Each jurisdiction has specific configuration in `jurisdiction_config.json`
- Document types vary by jurisdiction (e.g., federal statutes vs. state regulations)
- Citation patterns are jurisdiction-specific and parsed accordingly
- The `multi_jurisdiction_processor.py` coordinates processing across jurisdictions

## Data Flow

1. **Input**: PDF documents organized by jurisdiction
2. **Processing**:
   - Documents are classified, metadata extracted
   - Text is chunked and citations identified
   - Embeddings are generated and stored in Pinecone
   - Metadata is stored in Supabase
   - Document nodes and relationships are created in Neo4j
3. **Output**:
   - Structured knowledge graph in Neo4j
   - Document explorer interface for visualization
   - Vector database for semantic search
   - Relational database for structured queries

## Technology Stack

- **Languages**: Python, JavaScript
- **Databases**:
  - Neo4j (Graph Database)
  - Supabase (PostgreSQL-based Relational Database)
  - Pinecone (Vector Database)
- **AI/ML**:
  - Gemini 2.5 Pro for document classification
  - Embedding models for vector generation
- **Web Framework**: Flask
- **Visualization**: D3.js, Chart.js
- **PDF Processing**: PyMuPDF

## Environment Configuration

The system requires the following environment variables:

```
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-key

# Pinecone Configuration
PINECONE_API_KEY=your-api-key
PINECONE_ENVIRONMENT=your-environment

# LLM API Configuration
GEMINI_API_KEY=your-api-key
```

## Deployment

The system can be deployed in various configurations:

1. **Development**: Local deployment with Docker containers for databases
2. **Testing**: Integrated testing environment with sample documents
3. **Production**: Scaled deployment with appropriate resources for processing volume

## Scaling Considerations

- Batch processing can be parallelized for high-volume document ingestion
- Neo4j can be clustered for larger graph databases
- Pinecone and Supabase provide cloud-based scaling options
- Processing can be distributed across multiple workers for improved throughput

## Maintenance and Monitoring

- Processing logs are maintained for audit purposes
- Batch processing includes checkpoints for recovery from failures
- Quality control scripts validate document processing integrity
- Monitoring dashboards track processing status and system performance

"""
Example usage of the Court Listener API Integration
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.client import Court<PERSON>istenerClient
from src.processing.courtlistener_processor import CourtListenerProcessor
from src.processing.batch_processor_courtlistener import CourtListenerBatchProcessor


def example_citation_search():
    """Example of searching for cases by citation"""
    client = CourtListenerClient()
    print("\n===== SEARCHING BY CITATION =====")
    
    citation = "410 U.S. 113"  # <PERSON> v. <PERSON>
    print(f"Searching for citation: {citation}")
    
    cases = client.get_cases_by_citation(citation)
    print(f"Found {len(cases)} cases")
    
    if cases:
        case = cases[0]
        print(f"\nCase: {case.name}")
        print(f"Court: {case.court.name}")
        print(f"Date: {case.date_filed.strftime('%B %d, %Y') if case.date_filed else 'Unknown'}")
        print(f"Docket: {case.docket_number}")
        
        # Get and display opinion text
        print("\nRetrieving opinion...")
        opinions = client.get_opinions_by_case(case.id)
        
        if opinions:
            opinion = opinions[0]
            print(f"Opinion by: {opinion.author}")
            print(f"Opinion type: {opinion.type}")
            
            # Show a snippet of the opinion text
            if opinion.text:
                text_snippet = opinion.text[:500] + "..." if len(opinion.text) > 500 else opinion.text
                print(f"\nOpinion text snippet:\n{text_snippet}")
            else:
                print("\nNo plain text available for this opinion")


def example_texas_search():
    """Example of searching for Texas cases"""
    processor = CourtListenerProcessor()
    print("\n===== SEARCHING TEXAS CASES =====")
    
    query = "personal injury premises liability"
    print(f"Searching for: '{query}' in Texas")
    
    response = processor.search_texas_cases(query, page_size=5)
    
    # Extract results from the response
    if isinstance(response, dict) and 'results' in response:
        results = response.get('results', [])
        total_count = response.get('count', 0)
    else:
        results = []
        total_count = 0
    
    print(f"Found {total_count} results (showing {min(3, len(results))})")
    
    # Loop through the top 3 results
    for i, result in enumerate(results[:3], 1):
        if not isinstance(result, dict):
            continue
            
        # Extract court info safely
        court_info = result.get('court', {})
        court_name = "Unknown"
        if isinstance(court_info, dict):
            court_name = court_info.get('full_name', 'Unknown')
        
        print(f"\nResult {i}:")
        print(f"  Case: {result.get('case_name', 'Unknown')}")
        print(f"  Court: {court_name}")
        print(f"  Date: {result.get('date_filed', 'Unknown')}")
        print(f"  Status: {result.get('precedential_status', 'Unknown')}")


def example_batch_process():
    """Example of using the CourtListenerBatchProcessor"""
    print("\n===== COURT LISTENER BATCH PROCESSOR =====")
    
    # This is just a demonstration - for actual processing use:
    # processor = CourtListenerBatchProcessor()
    # processor.process_directory("path/to/pdfs", jurisdiction="Texas")
    
    processor = CourtListenerBatchProcessor()
    
    print("Example finding related cases...")
    query = "slip and fall premises liability"
    related_cases = processor.find_related_cases(query, jurisdiction="Texas", max_results=3)
    
    print(f"Found {len(related_cases)} related cases")
    for i, case in enumerate(related_cases, 1):
        print(f"\nCase {i}:")
        print(f"  Name: {case.get('case_name', 'Unknown')}")
        print(f"  Court: {case.get('court', {}).get('full_name', 'Unknown')}")
        print(f"  Date: {case.get('date_filed', 'Unknown')}")
        
    print("\nTo process a PDF directory with Court Listener enrichment:")
    print("processor = CourtListenerBatchProcessor()")
    print("processor.process_directory('/path/to/pdfs', jurisdiction='Texas')")


def run_examples():
    """Run all example functions"""
    print("======================================================")
    print("COURT LISTENER API INTEGRATION EXAMPLES")
    print("======================================================")
    
    try:
        example_citation_search()
        example_texas_search()
        example_batch_process()
        
        print("\n======================================================")
        print("EXAMPLES COMPLETED SUCCESSFULLY")
        print("======================================================")
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        print("\nCheck your COURTLISTENER_API_KEY in .env file")
        print("Make sure you have internet connection")


if __name__ == "__main__":
    run_examples()

#!/usr/bin/env python3
"""
Texas Practice Areas Example Script
Demonstrates retrieving cases for different practice areas at various court levels
with quality validation metrics.
"""

import os
import sys
import json
from datetime import datetime

# Add the project root to the path so we can import our modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.texas import TexasCaseClient, CourtLevel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure output directory
OUTPUT_DIR = os.path.join("processed_documents", "practice_areas")
os.makedirs(OUTPUT_DIR, exist_ok=True)


def save_quality_report(client, practice_area, court_level=None):
    """Save quality validation report for a practice area search
    
    Args:
        client: TexasCaseClient instance
        practice_area: Name of the practice area
        court_level: Court level used in the search, if any
    """
    court_level_str = court_level.name.lower() if court_level else "all"
    filename = f"{practice_area.replace(' ', '_')}_{court_level_str}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_quality.json"
    filepath = os.path.join(OUTPUT_DIR, filename)
    
    report = client.get_validation_report()
    
    with open(filepath, 'w') as f:
        json.dump(report, f, indent=2)
        
    print(f"Quality report saved to: {filepath}")
    
    # Print summary
    quality = report["quality_assessment"]["overall_quality"]
    completeness = report["quality_assessment"]["completeness"]
    court_coverage = report["quality_assessment"]["court_coverage"]
    
    print(f"  Quality Assessment: {quality}")
    print(f"  Completeness: {completeness}")
    print(f"  Court Coverage: {court_coverage}")
    print(f"  Court Levels Found: {', '.join(report['metrics']['court_levels_found'])}")
    
    # Print missing fields information
    if report["quality_assessment"]["most_common_missing_fields"]:
        print("  Most Common Missing Fields:")
        for field, count in report["quality_assessment"]["most_common_missing_fields"]:
            print(f"    {field}: {count} instances")


def search_personal_injury_cases():
    """Search for personal injury cases at different court levels"""
    client = TexasCaseClient()
    print("\n===== PERSONAL INJURY CASES =====")
    
    # Try different court levels
    for court_level in [CourtLevel.ALL, CourtLevel.SUPREME, CourtLevel.APPEALS]:
        print(f"\n--- {court_level.name} COURT LEVEL ---")
        results = client.get_personal_injury_cases(page_size=10, court_level=court_level)
        count = results.get('count', 0) if isinstance(results, dict) else len(results)
        print(f"Found {count} cases")
        
        # Save quality metrics
        save_quality_report(client, "personal_injury", court_level)


def search_premises_liability_cases():
    """Search for premises liability cases"""
    client = TexasCaseClient()
    print("\n===== PREMISES LIABILITY CASES =====")
    
    results = client.get_premises_liability_cases(page_size=10)
    count = results.get('count', 0) if isinstance(results, dict) else len(results)
    print(f"Found {count} cases")
    
    # Save quality metrics
    save_quality_report(client, "premises_liability")


def search_criminal_defense_cases():
    """Search for criminal defense cases"""
    client = TexasCaseClient()
    print("\n===== CRIMINAL DEFENSE CASES =====")
    
    # Try different queries
    subtopics = [None, "drug possession", "DUI"]
    
    for subtopic in subtopics:
        topic_name = subtopic if subtopic else "general"
        print(f"\n--- {topic_name.upper()} CASES ---")
        
        results = client.get_criminal_defense_cases(query=subtopic, page_size=10)
        count = results.get('count', 0) if isinstance(results, dict) else len(results)
        print(f"Found {count} cases")
        
        # Save quality metrics
        save_quality_report(client, f"criminal_{topic_name}")


def search_family_law_cases():
    """Search for family law cases"""
    client = TexasCaseClient()
    print("\n===== FAMILY LAW CASES =====")
    
    # Try different queries
    subtopics = [None, "divorce", "custody", "child support"]
    
    for subtopic in subtopics:
        topic_name = subtopic if subtopic else "general"
        print(f"\n--- {topic_name.upper()} CASES ---")
        
        results = client.get_family_law_cases(query=subtopic, page_size=10)
        count = results.get('count', 0) if isinstance(results, dict) else len(results)
        print(f"Found {count} cases")
        
        # Save quality metrics
        save_quality_report(client, f"family_{topic_name}")


def run_examples():
    """Run all practice area examples"""
    print("=" * 60)
    print("TEXAS PRACTICE AREAS EXAMPLES")
    print("=" * 60)
    
    try:
        search_personal_injury_cases()
        search_premises_liability_cases()
        search_criminal_defense_cases()
        search_family_law_cases()
        
        print("\n" + "=" * 60)
        print("EXAMPLES COMPLETED SUCCESSFULLY")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        print("\nCheck your COURTLISTENER_API_KEY in .env file")
        print("Make sure you have internet connection")


if __name__ == "__main__":
    run_examples()

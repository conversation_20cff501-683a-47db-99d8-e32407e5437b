"""
Texas Case Law Example <PERSON>
Demonstrates how to use the specialized Texas case law client
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.texas import TexasCaseClient


def example_personal_injury_search():
    """Example searching for Texas personal injury cases"""
    client = TexasCaseClient()
    print("\n===== TEXAS PERSONAL INJURY CASES =====")
    
    try:
        results = client.get_personal_injury_cases(page_size=5)
        
        # Handle different response formats
        if isinstance(results, dict) and 'count' in results:
            count = results.get('count', 0)
            result_items = results.get('results', [])
        elif isinstance(results, list):
            count = len(results)
            result_items = results
        else:
            count = 0
            result_items = []
            
        print(f"Found {count} Texas personal injury cases (showing top {min(5, len(result_items))}):")
        
        for i, result in enumerate(result_items[:5], 1):
            if not isinstance(result, dict):
                print(f"\nCase {i}: [Result format error]")
                continue
                
            # Extract court information safely
            court_name = "Unknown"
            court_info = result.get('court', {})
            if isinstance(court_info, dict):
                court_name = court_info.get('full_name', 'Unknown')
            
            print(f"\nCase {i}:")
            print(f"  Name: {result.get('case_name', 'Unknown')}")
            print(f"  Court: {court_name}")
            print(f"  Date: {result.get('date_filed', 'Unknown')}")
            print(f"  Status: {result.get('precedential_status', 'Unknown')}")
        
        # Save results to file
        filename = client.save_search_results(results, "texas_personal_injury")
        print(f"\nResults saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nCheck your API configuration and internet connection.")


def example_premises_liability_search():
    """Example searching for Texas premises liability cases"""
    client = TexasCaseClient()
    print("\n===== TEXAS PREMISES LIABILITY CASES =====")
    
    try:
        results = client.get_premises_liability_cases(page_size=5)
        
        # Handle different response formats
        if isinstance(results, dict) and 'count' in results:
            count = results.get('count', 0)
            result_items = results.get('results', [])
        elif isinstance(results, list):
            count = len(results)
            result_items = results
        else:
            count = 0
            result_items = []
        
        print(f"Found {count} Texas premises liability cases (showing top {min(5, len(result_items))}):")
        
        for i, result in enumerate(result_items[:5], 1):
            if not isinstance(result, dict):
                print(f"\nCase {i}: [Result format error]")
                continue
                
            # Extract court information safely
            court_name = "Unknown"
            court_info = result.get('court', {})
            if isinstance(court_info, dict):
                court_name = court_info.get('full_name', 'Unknown')
            
            print(f"\nCase {i}:")
            print(f"  Name: {result.get('case_name', 'Unknown')}")
            print(f"  Court: {court_name}")
            print(f"  Date: {result.get('date_filed', 'Unknown')}")
            print(f"  Status: {result.get('precedential_status', 'Unknown')}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nCheck your API configuration and internet connection.")


def example_texas_supreme_court_search():
    """Example searching for Texas Supreme Court cases"""
    client = TexasCaseClient()
    print("\n===== TEXAS SUPREME COURT CASES =====")
    
    try:
        query = "negligence duty of care"
        results = client.get_texas_supreme_court_cases(query, page_size=5)
        
        # Handle different response formats
        if isinstance(results, dict) and 'count' in results:
            count = results.get('count', 0)
            result_items = results.get('results', [])
        elif isinstance(results, list):
            count = len(results)
            result_items = results
        else:
            count = 0
            result_items = []
        
        print(f"Found {count} Texas Supreme Court cases on '{query}' (showing top {min(5, len(result_items))}):")
        
        for i, result in enumerate(result_items[:5], 1):
            if not isinstance(result, dict):
                print(f"\nCase {i}: [Result format error]")
                continue
                
            print(f"\nCase {i}:")
            print(f"  Name: {result.get('case_name', 'Unknown')}")
            print(f"  Date: {result.get('date_filed', 'Unknown')}")
            
            # Handle citations safely
            citation_list = result.get('citations', [])
            if citation_list and isinstance(citation_list, list):
                citations = []
                for c in citation_list:
                    if isinstance(c, dict) and all(k in c for k in ['volume', 'reporter', 'page']):
                        citations.append(f"{c.get('volume')} {c.get('reporter')} {c.get('page')}")
                if citations:
                    print(f"  Citation: {', '.join(citations)}")
                    
            print(f"  URL: {result.get('absolute_url', '')}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nCheck your API configuration and internet connection.")


def example_texas_statute_search():
    """Example searching for Texas cases citing specific statutes"""
    client = TexasCaseClient()
    print("\n===== TEXAS STATUTE REFERENCES =====")
    
    try:
        statute = "CPRC 101.021"  # Texas Tort Claims Act
        results = client.get_texas_statutes(statute, page_size=5)
        
        # Handle different response formats
        if isinstance(results, dict) and 'count' in results:
            count = results.get('count', 0)
            result_items = results.get('results', [])
        elif isinstance(results, list):
            count = len(results)
            result_items = results
        else:
            count = 0
            result_items = []
        
        print(f"Found {count} Texas cases citing '{statute}' (showing top {min(5, len(result_items))}):")
        
        for i, result in enumerate(result_items[:5], 1):
            if not isinstance(result, dict):
                print(f"\nCase {i}: [Result format error]")
                continue
                
            # Extract court information safely
            court_name = "Unknown"
            court_info = result.get('court', {})
            if isinstance(court_info, dict):
                court_name = court_info.get('full_name', 'Unknown')
            
            print(f"\nCase {i}:")
            print(f"  Name: {result.get('case_name', 'Unknown')}")
            print(f"  Court: {court_name}")
            print(f"  Date: {result.get('date_filed', 'Unknown')}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nCheck your API configuration and internet connection.")


def example_texas_courts():
    """Example listing Texas courts"""
    client = TexasCaseClient()
    print("\n===== TEXAS COURTS =====")
    
    try:
        courts = client.get_texas_courts()
        
        if not courts:
            print("No specific Texas courts found in the Court Listener API.")
            print("This is expected as we're using text-based search for Texas cases.")
            return
        
        # Group by jurisdiction/type
        court_groups = {}
        for court in courts:
            if not hasattr(court, 'name') or not court.name:
                continue
                
            court_type = "Other"
            
            if "Supreme" in court.name:
                court_type = "Supreme"
            elif "Appeals" in court.name:
                court_type = "Appeals"
            elif "District" in court.name:
                court_type = "District"
            elif "Criminal" in court.name:
                court_type = "Criminal"
            
            if court_type not in court_groups:
                court_groups[court_type] = []
            
            court_groups[court_type].append(court)
        
        # Print grouped results
        print(f"Found {len(courts)} Texas courts:")
        for court_type, court_list in court_groups.items():
            print(f"\n{court_type} Courts ({len(court_list)}):")
            for court in court_list:
                court_id = getattr(court, 'id', 'Unknown')
                print(f"  {court.name} (ID: {court_id})")
                
    except Exception as e:
        print(f"Error: {str(e)}")
        print("\nCheck your API configuration and internet connection.")


def run_examples():
    """Run all example functions"""
    print("======================================================")
    print("TEXAS CASE LAW EXAMPLES")
    print("======================================================")
    
    try:
        example_personal_injury_search()
        example_premises_liability_search()
        example_texas_supreme_court_search()
        example_texas_statute_search()
        example_texas_courts()
        
        print("\n======================================================")
        print("EXAMPLES COMPLETED SUCCESSFULLY")
        print("======================================================")
        
    except Exception as e:
        print(f"\nERROR: {str(e)}")
        print("\nCheck your COURTLISTENER_API_KEY in .env file")
        print("Make sure you have internet connection")


if __name__ == "__main__":
    run_examples()

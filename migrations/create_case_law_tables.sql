-- Create case_processing_batches table
CREATE TABLE IF NOT EXISTS case_processing_batches (
    id UUID PRIMARY KEY,
    source TEXT NOT NULL,
    jurisdiction TEXT NOT NULL,
    query_params JSONB,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    total INTEGER DEFAULT 0,
    success INTEGER DEFAULT 0,
    failure INTEGER DEFAULT 0,
    skipped INTEGER DEFAULT 0,
    status TEXT DEFAULT 'processing',
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create processing_history table
CREATE TABLE IF NOT EXISTS processing_history (
    id UUID PRIMARY KEY,
    case_id TEXT NOT NULL,
    batch_id UUID REFERENCES case_processing_batches(id),
    action TEXT NOT NULL,
    status TEXT NOT NULL,
    details JSONB,
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cases table if it doesn't exist
CREATE TABLE IF NOT EXISTS cases (
    id TEXT PRIMARY KEY,
    case_name TEXT NOT NULL,
    jurisdiction TEXT NOT NULL,
    court TEXT,
    date_filed DATE,
    docket_number TEXT,
    cluster_id TEXT,
    gcs_path TEXT,
    pinecone_id TEXT,
    opinion_count INTEGER DEFAULT 0,
    citation_count INTEGER DEFAULT 0,
    completeness_score FLOAT DEFAULT 0,
    document_quality TEXT,
    metadata_quality TEXT,
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create opinions table if it doesn't exist
CREATE TABLE IF NOT EXISTS opinions (
    id TEXT PRIMARY KEY,
    case_id TEXT REFERENCES cases(id),
    opinion_type TEXT,
    author TEXT,
    gcs_path TEXT,
    pinecone_id TEXT,
    has_text BOOLEAN DEFAULT FALSE,
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create citations table if it doesn't exist
CREATE TABLE IF NOT EXISTS citations (
    id TEXT PRIMARY KEY,
    citing_case_id TEXT REFERENCES cases(id),
    cited_case_id TEXT,
    citation_text TEXT,
    confidence FLOAT,
    neo4j_relationship_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for case_processing_batches
ALTER TABLE case_processing_batches ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Partners can see all batches"
    ON case_processing_batches
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'partner');

CREATE POLICY "Users can see their own batches"
    ON case_processing_batches
    FOR SELECT
    USING (auth.uid()::text = user_id);

CREATE POLICY "Users can see batches for their tenant"
    ON case_processing_batches
    FOR SELECT
    USING (auth.jwt() ->> 'tenant_id' = tenant_id);

-- Create RLS policies for processing_history
ALTER TABLE processing_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Partners can see all history"
    ON processing_history
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'partner');

CREATE POLICY "Users can see their own history"
    ON processing_history
    FOR SELECT
    USING (auth.uid()::text = user_id);

CREATE POLICY "Users can see history for their tenant"
    ON processing_history
    FOR SELECT
    USING (auth.jwt() ->> 'tenant_id' = tenant_id);

-- Create RLS policies for cases
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Partners and attorneys can see all cases"
    ON cases
    FOR SELECT
    USING (auth.jwt() ->> 'role' IN ('partner', 'attorney'));

CREATE POLICY "Paralegals and staff can see cases for their tenant"
    ON cases
    FOR SELECT
    USING (
        auth.jwt() ->> 'role' IN ('paralegal', 'staff') AND
        auth.jwt() ->> 'tenant_id' = tenant_id
    );

CREATE POLICY "Clients can only see their own cases"
    ON cases
    FOR SELECT
    USING (
        auth.jwt() ->> 'role' = 'client' AND
        auth.uid()::text = user_id
    );

-- Create RLS policies for opinions
ALTER TABLE opinions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Opinions inherit case access"
    ON opinions
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = opinions.case_id
        )
    );

-- Create RLS policies for citations
ALTER TABLE citations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Citations inherit case access"
    ON citations
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = citations.citing_case_id
        )
    );

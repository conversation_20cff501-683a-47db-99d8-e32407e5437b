-- Week 2 Schema Updates: Multi-Jurisdictional Automated Harvesting System
-- This migration adds enhanced fields for automated case harvesting and processing

-- =====================================================
-- 1. CASES TABLE ENHANCEMENTS
-- =====================================================

-- Add Week 2 fields to cases table
ALTER TABLE cases 
ADD COLUMN IF NOT EXISTS case_type TEXT,
ADD COLUMN IF NOT EXISTS subject_matter TEXT[],
ADD COLUMN IF NOT EXISTS outcome TEXT,
ADD COLUMN IF NOT EXISTS precedential_status TEXT,
ADD COLUMN IF NOT EXISTS judges TEXT[],
ADD COLUMN IF NOT EXISTS parties JSONB,
ADD COLUMN IF NOT EXISTS procedural_history TEXT[],
ADD COLUMN IF NOT EXISTS subsequent_history TEXT[],
ADD COLUMN IF NOT EXISTS harvesting_metadata JSONB,
ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS processing_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS last_harvested TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS harvest_source TEXT DEFAULT 'courtlistener',
ADD COLUMN IF NOT EXISTS api_id TEXT,
ADD COLUMN IF NOT EXISTS citation_count_verified INTEGER DEFAULT 0;

-- =====================================================
-- 2. OPINIONS TABLE ENHANCEMENTS  
-- =====================================================

-- Add Week 2 fields to opinions table
ALTER TABLE opinions
ADD COLUMN IF NOT EXISTS opinion_type TEXT,
ADD COLUMN IF NOT EXISTS quality FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS procedural_posture TEXT,
ADD COLUMN IF NOT EXISTS key_topics TEXT[],
ADD COLUMN IF NOT EXISTS joining_judges TEXT[],
ADD COLUMN IF NOT EXISTS word_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS processing_metadata JSONB,
ADD COLUMN IF NOT EXISTS enhancement_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS enhanced_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS opinion_classification JSONB,
ADD COLUMN IF NOT EXISTS legal_topics JSONB;

-- =====================================================
-- 3. CITATIONS TABLE ENHANCEMENTS
-- =====================================================

-- Add Week 2 fields to citations table  
ALTER TABLE citations
ADD COLUMN IF NOT EXISTS citation_strength TEXT,
ADD COLUMN IF NOT EXISTS citation_context TEXT,
ADD COLUMN IF NOT EXISTS extraction_confidence FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS validation_status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS validated_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS extraction_metadata JSONB;

-- =====================================================
-- 4. NEW TABLES FOR WEEK 2
-- =====================================================

-- Create harvesting jobs table
CREATE TABLE IF NOT EXISTS harvesting_jobs (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    jurisdiction TEXT NOT NULL,
    practice_area TEXT,
    job_type TEXT NOT NULL, -- 'scheduled', 'manual', 'retry'
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'
    priority INTEGER DEFAULT 5,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_cases_found INTEGER DEFAULT 0,
    total_cases_processed INTEGER DEFAULT 0,
    total_cases_success INTEGER DEFAULT 0,
    total_cases_failed INTEGER DEFAULT 0,
    error_message TEXT,
    configuration JSONB,
    results JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create case processing queue table
CREATE TABLE IF NOT EXISTS case_processing_queue (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    case_id TEXT NOT NULL,
    job_id TEXT REFERENCES harvesting_jobs(id),
    processing_type TEXT NOT NULL, -- 'initial', 'enhancement', 'reprocessing'
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    priority INTEGER DEFAULT 5,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    processing_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create harvesting statistics table
CREATE TABLE IF NOT EXISTS harvesting_statistics (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    jurisdiction TEXT NOT NULL,
    practice_area TEXT,
    date_period DATE NOT NULL, -- Daily statistics
    total_jobs INTEGER DEFAULT 0,
    successful_jobs INTEGER DEFAULT 0,
    failed_jobs INTEGER DEFAULT 0,
    total_cases_found INTEGER DEFAULT 0,
    total_cases_processed INTEGER DEFAULT 0,
    total_cases_success INTEGER DEFAULT 0,
    average_processing_time FLOAT DEFAULT 0.0,
    success_rate FLOAT DEFAULT 0.0,
    quality_score_average FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(jurisdiction, practice_area, date_period)
);

-- Create case quality metrics table
CREATE TABLE IF NOT EXISTS case_quality_metrics (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    case_id TEXT NOT NULL,
    completeness_score FLOAT DEFAULT 0.0,
    citation_quality_score FLOAT DEFAULT 0.0,
    metadata_quality_score FLOAT DEFAULT 0.0,
    content_quality_score FLOAT DEFAULT 0.0,
    overall_quality_score FLOAT DEFAULT 0.0,
    quality_issues TEXT[],
    enhancement_suggestions TEXT[],
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (case_id) REFERENCES cases(id)
);

-- =====================================================
-- 5. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Cases table indexes for Week 2 fields
CREATE INDEX IF NOT EXISTS idx_cases_case_type ON cases(case_type);
CREATE INDEX IF NOT EXISTS idx_cases_outcome ON cases(outcome);
CREATE INDEX IF NOT EXISTS idx_cases_precedential_status ON cases(precedential_status);
CREATE INDEX IF NOT EXISTS idx_cases_processing_status ON cases(processing_status);
CREATE INDEX IF NOT EXISTS idx_cases_last_harvested ON cases(last_harvested);
CREATE INDEX IF NOT EXISTS idx_cases_harvest_source ON cases(harvest_source);
CREATE INDEX IF NOT EXISTS idx_cases_api_id ON cases(api_id);
CREATE INDEX IF NOT EXISTS idx_cases_quality_score ON cases(quality_score);
CREATE INDEX IF NOT EXISTS idx_cases_subject_matter ON cases USING GIN(subject_matter);
CREATE INDEX IF NOT EXISTS idx_cases_judges ON cases USING GIN(judges);

-- Opinions table indexes for Week 2 fields
CREATE INDEX IF NOT EXISTS idx_opinions_opinion_type ON opinions(opinion_type);
CREATE INDEX IF NOT EXISTS idx_opinions_quality ON opinions(quality);
CREATE INDEX IF NOT EXISTS idx_opinions_enhancement_status ON opinions(enhancement_status);
CREATE INDEX IF NOT EXISTS idx_opinions_enhanced_at ON opinions(enhanced_at);
CREATE INDEX IF NOT EXISTS idx_opinions_word_count ON opinions(word_count);
CREATE INDEX IF NOT EXISTS idx_opinions_key_topics ON opinions USING GIN(key_topics);
CREATE INDEX IF NOT EXISTS idx_opinions_joining_judges ON opinions USING GIN(joining_judges);

-- Citations table indexes for Week 2 fields
CREATE INDEX IF NOT EXISTS idx_citations_citation_strength ON citations(citation_strength);
CREATE INDEX IF NOT EXISTS idx_citations_validation_status ON citations(validation_status);
CREATE INDEX IF NOT EXISTS idx_citations_extraction_confidence ON citations(extraction_confidence);
CREATE INDEX IF NOT EXISTS idx_citations_validated_at ON citations(validated_at);

-- New tables indexes
CREATE INDEX IF NOT EXISTS idx_harvesting_jobs_jurisdiction ON harvesting_jobs(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_harvesting_jobs_status ON harvesting_jobs(status);
CREATE INDEX IF NOT EXISTS idx_harvesting_jobs_scheduled_at ON harvesting_jobs(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_harvesting_jobs_practice_area ON harvesting_jobs(practice_area);
CREATE INDEX IF NOT EXISTS idx_harvesting_jobs_priority ON harvesting_jobs(priority);

CREATE INDEX IF NOT EXISTS idx_case_processing_queue_case_id ON case_processing_queue(case_id);
CREATE INDEX IF NOT EXISTS idx_case_processing_queue_job_id ON case_processing_queue(job_id);
CREATE INDEX IF NOT EXISTS idx_case_processing_queue_status ON case_processing_queue(status);
CREATE INDEX IF NOT EXISTS idx_case_processing_queue_priority ON case_processing_queue(priority);

CREATE INDEX IF NOT EXISTS idx_harvesting_statistics_jurisdiction ON harvesting_statistics(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_harvesting_statistics_date_period ON harvesting_statistics(date_period);
CREATE INDEX IF NOT EXISTS idx_harvesting_statistics_practice_area ON harvesting_statistics(practice_area);

CREATE INDEX IF NOT EXISTS idx_case_quality_metrics_case_id ON case_quality_metrics(case_id);
CREATE INDEX IF NOT EXISTS idx_case_quality_metrics_overall_quality ON case_quality_metrics(overall_quality_score);

-- =====================================================
-- 6. CREATE VIEWS FOR WEEK 2 ANALYTICS
-- =====================================================

-- View for harvesting job summary
CREATE OR REPLACE VIEW harvesting_job_summary AS
SELECT 
    hj.jurisdiction,
    hj.practice_area,
    COUNT(*) as total_jobs,
    COUNT(*) FILTER (WHERE hj.status = 'completed') as completed_jobs,
    COUNT(*) FILTER (WHERE hj.status = 'failed') as failed_jobs,
    COUNT(*) FILTER (WHERE hj.status = 'running') as running_jobs,
    SUM(hj.total_cases_processed) as total_cases_processed,
    SUM(hj.total_cases_success) as total_cases_success,
    AVG(EXTRACT(EPOCH FROM (hj.completed_at - hj.started_at))/60) as avg_duration_minutes,
    MAX(hj.completed_at) as last_harvest
FROM harvesting_jobs hj
GROUP BY hj.jurisdiction, hj.practice_area;

-- View for case quality overview
CREATE OR REPLACE VIEW case_quality_overview AS
SELECT 
    c.jurisdiction,
    c.case_type,
    COUNT(*) as total_cases,
    AVG(cqm.overall_quality_score) as avg_quality_score,
    COUNT(*) FILTER (WHERE cqm.overall_quality_score >= 0.8) as high_quality_cases,
    COUNT(*) FILTER (WHERE cqm.overall_quality_score < 0.5) as low_quality_cases,
    AVG(c.citation_count_verified) as avg_citation_count
FROM cases c
LEFT JOIN case_quality_metrics cqm ON c.id = cqm.case_id
GROUP BY c.jurisdiction, c.case_type;

-- View for processing queue status
CREATE OR REPLACE VIEW processing_queue_status AS
SELECT 
    cpq.processing_type,
    cpq.status,
    COUNT(*) as queue_count,
    AVG(cpq.attempts) as avg_attempts,
    MIN(cpq.created_at) as oldest_queued,
    MAX(cpq.last_attempt_at) as last_attempt
FROM case_processing_queue cpq
GROUP BY cpq.processing_type, cpq.status;

-- =====================================================
-- 7. CREATE FUNCTIONS FOR WEEK 2 OPERATIONS
-- =====================================================

-- Function to calculate case quality score
CREATE OR REPLACE FUNCTION calculate_case_quality_score(case_id_param TEXT)
RETURNS FLOAT AS $$
DECLARE
    completeness_score FLOAT := 0.0;
    citation_score FLOAT := 0.0;
    metadata_score FLOAT := 0.0;
    overall_score FLOAT := 0.0;
    case_record RECORD;
BEGIN
    -- Get case data
    SELECT * INTO case_record FROM cases WHERE id = case_id_param;
    
    IF NOT FOUND THEN
        RETURN 0.0;
    END IF;
    
    -- Calculate completeness score (0-1)
    completeness_score := 0.0;
    IF case_record.case_name IS NOT NULL THEN completeness_score := completeness_score + 0.2; END IF;
    IF case_record.court IS NOT NULL THEN completeness_score := completeness_score + 0.2; END IF;
    IF case_record.date_filed IS NOT NULL THEN completeness_score := completeness_score + 0.2; END IF;
    IF case_record.judges IS NOT NULL AND array_length(case_record.judges, 1) > 0 THEN completeness_score := completeness_score + 0.2; END IF;
    IF case_record.parties IS NOT NULL THEN completeness_score := completeness_score + 0.2; END IF;
    
    -- Calculate citation score (0-1)
    citation_score := LEAST(1.0, COALESCE(case_record.citation_count_verified, 0) / 10.0);
    
    -- Calculate metadata score (0-1)
    metadata_score := 0.0;
    IF case_record.case_type IS NOT NULL THEN metadata_score := metadata_score + 0.25; END IF;
    IF case_record.outcome IS NOT NULL THEN metadata_score := metadata_score + 0.25; END IF;
    IF case_record.precedential_status IS NOT NULL THEN metadata_score := metadata_score + 0.25; END IF;
    IF case_record.subject_matter IS NOT NULL AND array_length(case_record.subject_matter, 1) > 0 THEN metadata_score := metadata_score + 0.25; END IF;
    
    -- Calculate overall score (weighted average)
    overall_score := (completeness_score * 0.4) + (citation_score * 0.3) + (metadata_score * 0.3);
    
    -- Insert or update quality metrics
    INSERT INTO case_quality_metrics (case_id, completeness_score, citation_quality_score, metadata_quality_score, overall_quality_score)
    VALUES (case_id_param, completeness_score, citation_score, metadata_score, overall_score)
    ON CONFLICT (case_id) DO UPDATE SET
        completeness_score = EXCLUDED.completeness_score,
        citation_quality_score = EXCLUDED.citation_quality_score,
        metadata_quality_score = EXCLUDED.metadata_quality_score,
        overall_quality_score = EXCLUDED.overall_quality_score,
        calculated_at = NOW();
    
    RETURN overall_score;
END;
$$ LANGUAGE plpgsql;

-- Function to update harvesting statistics
CREATE OR REPLACE FUNCTION update_harvesting_statistics(jurisdiction_param TEXT, practice_area_param TEXT, date_param DATE)
RETURNS VOID AS $$
DECLARE
    stats_record RECORD;
BEGIN
    -- Calculate statistics for the given date
    SELECT 
        COUNT(*) as total_jobs,
        COUNT(*) FILTER (WHERE status = 'completed') as successful_jobs,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_jobs,
        SUM(total_cases_found) as total_cases_found,
        SUM(total_cases_processed) as total_cases_processed,
        SUM(total_cases_success) as total_cases_success,
        AVG(EXTRACT(EPOCH FROM (completed_at - started_at))/60) as avg_processing_time
    INTO stats_record
    FROM harvesting_jobs
    WHERE jurisdiction = jurisdiction_param 
    AND (practice_area_param IS NULL OR practice_area = practice_area_param)
    AND DATE(created_at) = date_param;
    
    -- Calculate success rate
    DECLARE
        success_rate FLOAT := 0.0;
        quality_avg FLOAT := 0.0;
    BEGIN
        IF stats_record.total_cases_processed > 0 THEN
            success_rate := stats_record.total_cases_success::FLOAT / stats_record.total_cases_processed::FLOAT;
        END IF;
        
        -- Get average quality score for cases processed on this date
        SELECT AVG(cqm.overall_quality_score) INTO quality_avg
        FROM case_quality_metrics cqm
        JOIN cases c ON cqm.case_id = c.id
        WHERE c.jurisdiction = jurisdiction_param
        AND (practice_area_param IS NULL OR practice_area_param = ANY(c.subject_matter))
        AND DATE(c.last_harvested) = date_param;
        
        -- Insert or update statistics
        INSERT INTO harvesting_statistics (
            jurisdiction, practice_area, date_period,
            total_jobs, successful_jobs, failed_jobs,
            total_cases_found, total_cases_processed, total_cases_success,
            average_processing_time, success_rate, quality_score_average
        )
        VALUES (
            jurisdiction_param, practice_area_param, date_param,
            stats_record.total_jobs, stats_record.successful_jobs, stats_record.failed_jobs,
            stats_record.total_cases_found, stats_record.total_cases_processed, stats_record.total_cases_success,
            stats_record.avg_processing_time, success_rate, COALESCE(quality_avg, 0.0)
        )
        ON CONFLICT (jurisdiction, practice_area, date_period) DO UPDATE SET
            total_jobs = EXCLUDED.total_jobs,
            successful_jobs = EXCLUDED.successful_jobs,
            failed_jobs = EXCLUDED.failed_jobs,
            total_cases_found = EXCLUDED.total_cases_found,
            total_cases_processed = EXCLUDED.total_cases_processed,
            total_cases_success = EXCLUDED.total_cases_success,
            average_processing_time = EXCLUDED.average_processing_time,
            success_rate = EXCLUDED.success_rate,
            quality_score_average = EXCLUDED.quality_score_average,
            updated_at = NOW();
    END;
END;
$$ LANGUAGE plpgsql;

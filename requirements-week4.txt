# Week 4 Dependencies: Hybrid Search + Stubbed Recommendations
# Core API framework and authentication

# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# Authentication and JWT
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6

# Caching backends
redis>=5.0.0
cachetools>=5.3.0

# Database connectors (existing)
supabase>=2.0.0
pinecone>=3.0.0
neo4j>=5.14.0

# HTTP client for JWKS
requests>=2.31.0

# Network analysis for authority calculation
networkx>=3.2.0

# Monitoring and metrics
prometheus-client>=0.19.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.25.0  # For FastAPI test client

# Logging and configuration
python-dotenv>=1.0.0

# Data processing (existing dependencies)
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Text processing (existing)
spacy>=3.7.0
nltk>=3.8.0

# PDF processing (existing)
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Google Cloud Storage (existing)
google-cloud-storage>=2.10.0

# Additional utilities
python-dateutil>=2.8.0
pytz>=2023.3

#!/usr/bin/env python3
"""
Entry point script for running common operations in the Texas Laws Personal Injury project
"""

import os
import sys
import argparse

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    parser = argparse.ArgumentParser(description="Texas Laws Personal Injury Project")
    parser.add_argument("--process", action="store_true", help="Process documents using batch processor")
    parser.add_argument("--load", action="store_true", help="Load citations into Neo4j")
    parser.add_argument("--validate", action="store_true", help="Validate citations")
    parser.add_argument("--analyze", action="store_true", help="Run citation analysis")
    parser.add_argument("--explorer", action="store_true", help="Launch document explorer web app")
    parser.add_argument("--list_unprocessed", action="store_true", help="List unprocessed documents")
    parser.add_argument("--cleanup_partial", action="store_true", help="Find and fix partially processed documents")
    
    args = parser.parse_args()
    
    if args.process:
        from src.processing.batch_processor import main as batch_processor_main
        batch_processor_main()
    
    elif args.load:
        from src.database.neo4j_citation_loader import Neo4jCitationLoader
        loader = Neo4jCitationLoader()
        loader.process_directory()
    
    elif args.validate:
        from src.validators.citation_validator import CitationValidator
        validator = CitationValidator()
        validator.validate_all_citations()
    
    elif args.analyze:
        from analysis.analyze_citations import main as analyze_main
        analyze_main()
    
    elif args.explorer:
        from apps.document_explorer.app import main as app_main
        app_main()
    
    elif args.list_unprocessed:
        from src.database.neo4j_citation_loader import Neo4jCitationLoader
        loader = Neo4jCitationLoader()
        loader.list_unprocessed_documents()
    
    elif args.cleanup_partial:
        from src.database.neo4j_citation_loader import Neo4jCitationLoader
        loader = Neo4jCitationLoader()
        loader.cleanup_partial_nodes()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()

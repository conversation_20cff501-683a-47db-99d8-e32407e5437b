#!/usr/bin/env python3
"""
Apply Jurisdiction Functions to Supabase

This script applies the SQL functions and tables needed for proper
multi-jurisdictional implementation in Supabase.

Usage:
    python apply_jurisdiction_functions.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add parent directory to path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Apply jurisdiction functions to Supabase."""
    load_dotenv()
    
    # Initialize Supabase connector
    try:
        supabase = SupabaseConnector()
        logger.info("Connected to Supa<PERSON> successfully")
    except Exception as e:
        logger.error(f"Error connecting to Supabase: {str(e)}")
        return 1
    
    # Read SQL script
    script_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)),
        "create_jurisdiction_functions.sql"
    )
    
    try:
        with open(script_path, 'r') as f:
            sql_script = f.read()
        
        logger.info(f"Read SQL script from {script_path}")
    except Exception as e:
        logger.error(f"Error reading SQL script: {str(e)}")
        return 1
    
    # Split the script into individual statements
    # This is a simple approach - for more complex scripts, consider using a proper SQL parser
    sql_statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
    
    # Execute each statement
    success_count = 0
    total_statements = len(sql_statements)
    
    for i, stmt in enumerate(sql_statements, 1):
        try:
            logger.info(f"Executing statement {i}/{total_statements}")
            supabase.execute_sql(stmt)
            success_count += 1
        except Exception as e:
            logger.error(f"Error executing statement {i}: {str(e)}")
            logger.error(f"Statement: {stmt}")
    
    # Print summary
    logger.info(f"Execution complete: {success_count}/{total_statements} statements successful")
    
    if success_count == total_statements:
        logger.info("All jurisdiction functions applied successfully")
        return 0
    else:
        logger.warning(f"Some statements failed: {total_statements - success_count} errors")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash
# Apply SQL migration to Supabase using the REST API

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
MIGRATION_FILE="$PROJECT_DIR/migrations/create_case_law_tables.sql"

# Check if the migration file exists
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "Migration file not found: $MIGRATION_FILE"
    exit 1
fi

# Get Supabase credentials from .env file
if [ -f "$PROJECT_DIR/.env" ]; then
    source "$PROJECT_DIR/.env"
fi

if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
    echo "Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_KEY in your .env file."
    exit 1
fi

echo "Applying migration to Supabase at $SUPABASE_URL"
echo "Using migration file: $MIGRATION_FILE"

# Read the SQL file content
SQL_CONTENT=$(cat "$MIGRATION_FILE")

# Split the SQL file into individual statements
IFS=';' read -ra STATEMENTS <<< "$SQL_CONTENT"

# Execute each SQL statement separately
for stmt in "${STATEMENTS[@]}"; do
    # Skip empty statements
    if [[ -z "${stmt// }" ]]; then
        continue
    fi
    
    echo "Executing SQL statement..."
    echo "$stmt" | head -c 100 # Show first 100 chars of the statement
    echo "..."
    
    # Execute the SQL statement using the Supabase REST API
    response=$(curl -s -X POST "$SUPABASE_URL/rest/v1/rpc/execute_sql" \
        -H "apikey: $SUPABASE_KEY" \
        -H "Authorization: Bearer $SUPABASE_KEY" \
        -H "Content-Type: application/json" \
        -d "{\"sql\": \"$stmt\"}")
    
    # Check if the request was successful
    if [[ "$response" == *"error"* ]]; then
        echo "Error executing SQL statement: $response"
        echo "Continuing with next statement..."
    else
        echo "SQL statement executed successfully."
    fi
done

echo "Migration completed. Some statements may have failed if tables already exist or if the execute_sql function is not available."
echo "Please check your Supabase dashboard to verify the tables were created correctly."

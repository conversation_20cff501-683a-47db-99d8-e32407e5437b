#!/usr/bin/env python3
"""
<PERSON><PERSON>t to apply RLS policy fixes and test access to Supabase tables.
This script directly executes SQL commands to fix RLS policies.
"""

import os
import sys
import logging
from pprint import pprint
from dotenv import load_dotenv
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_supabase_client() -> Client:
    """Get a Supabase client using environment variables."""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables must be set")
    
    return create_client(supabase_url, supabase_key)

def apply_rls_policies(client: Client):
    """Apply the RLS policies directly using SQL."""
    logger.info("Applying RLS policies...")
    
    # Read the SQL file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    sql_file_path = os.path.join(script_dir, "fix_rls_policies.sql")
    
    with open(sql_file_path, 'r') as f:
        sql_content = f.read()
    
    # Split the SQL into individual statements
    sql_statements = sql_content.split(';')
    
    # Execute each statement
    for statement in sql_statements:
        statement = statement.strip()
        if statement:
            try:
                logger.info(f"Executing SQL: {statement}")
                # Use the REST API to execute SQL directly
                response = client.rpc('exec_sql', {'query': statement}).execute()
                logger.info(f"SQL executed successfully: {response}")
            except Exception as e:
                logger.error(f"Error executing SQL: {str(e)}")
    
    logger.info("RLS policies applied successfully")

def test_table_access(client: Client, case_id="test-case-001"):
    """Test access to the opinions and citations tables."""
    logger.info(f"Testing access to tables for case_id: {case_id}")
    
    # Test opinions access
    try:
        logger.info("Testing access to opinions table...")
        opinions_response = client.table("opinions").select("*").eq("case_id", case_id).execute()
        opinions = opinions_response.data if hasattr(opinions_response, 'data') else []
        
        print("\n=== OPINIONS ===")
        if opinions:
            logger.info(f"Found {len(opinions)} opinions")
            for i, opinion in enumerate(opinions, 1):
                print(f"\nOpinion #{i}:")
                print(f"  ID: {opinion['id']}")
                print(f"  Type: {opinion['opinion_type']}")
                print(f"  Author: {opinion['author']}")
                print(f"  Word Count: {opinion['word_count']}")
                print(f"  Created: {opinion['created_at']}")
        else:
            logger.info("No opinions found for this case")
            print("No opinions found for this case")
    except Exception as e:
        logger.error(f"Error accessing opinions table: {str(e)}")
    
    # Test citations access
    try:
        logger.info("Testing access to citations table...")
        citations_response = client.table("citations").select("*").eq("citing_case_id", case_id).execute()
        citations = citations_response.data if hasattr(citations_response, 'data') else []
        
        print("\n=== CITATIONS ===")
        if citations:
            logger.info(f"Found {len(citations)} citations")
            for i, citation in enumerate(citations, 1):
                print(f"\nCitation #{i}:")
                print(f"  ID: {citation['id']}")
                print(f"  Cited Case ID: {citation['cited_case_id']}")
                print(f"  Citation Text: {citation['citation_text']}")
                print(f"  Confidence: {citation['confidence']}")
                print(f"  Created: {citation['created_at']}")
        else:
            logger.info("No citations found for this case")
            print("No citations found for this case")
    except Exception as e:
        logger.error(f"Error accessing citations table: {str(e)}")

def main():
    # Load environment variables
    load_dotenv()
    
    # Initialize Supabase client
    client = get_supabase_client()
    
    # Apply RLS policies
    apply_rls_policies(client)
    
    # Test table access
    test_table_access(client)

if __name__ == "__main__":
    main()

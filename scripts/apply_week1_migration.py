#!/usr/bin/env python3
"""
Apply Week 1 Database Migration
Applies the jurisdiction tagging and document type classification schema updates.

This script:
1. Applies the Week 1 schema migration to Supabase
2. Updates existing data with default jurisdiction values
3. Validates the migration results
4. Provides rollback capabilities if needed
"""

import os
import sys
import logging
from typing import Dict, List, Optional
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Week1MigrationManager:
    """Manages the Week 1 database migration process."""
    
    def __init__(self):
        """Initialize the migration manager."""
        self.supabase = SupabaseConnector()
        self.migration_file = os.path.join(os.path.dirname(__file__), '..', 'migrations', 'week1_schema_updates.sql')
        
    def load_migration_sql(self) -> str:
        """Load the migration SQL from file."""
        try:
            with open(self.migration_file, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to load migration file: {e}")
            raise
    
    def check_prerequisites(self) -> bool:
        """Check if prerequisites for migration are met."""
        logger.info("Checking migration prerequisites...")
        
        try:
            # Test database connection
            result = self.supabase.execute_sql("SELECT 1 as test")
            if not result:
                logger.error("Database connection test failed")
                return False
            
            # Check if migration has already been applied
            existing_tables = self.supabase.execute_sql("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('jurisdictions', 'document_types')
            """)
            
            if existing_tables and len(existing_tables) > 0:
                logger.warning("Migration tables already exist. This migration may have been applied before.")
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
            
            logger.info("Prerequisites check passed")
            return True
            
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            return False
    
    def backup_existing_data(self) -> bool:
        """Create backup of existing data before migration."""
        logger.info("Creating backup of existing data...")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Backup cases table
            backup_cases_sql = f"""
                CREATE TABLE IF NOT EXISTS cases_backup_{timestamp} AS 
                SELECT * FROM cases;
            """
            self.supabase.execute_sql(backup_cases_sql)
            
            # Backup opinions table
            backup_opinions_sql = f"""
                CREATE TABLE IF NOT EXISTS opinions_backup_{timestamp} AS 
                SELECT * FROM opinions;
            """
            self.supabase.execute_sql(backup_opinions_sql)
            
            # Backup citations table
            backup_citations_sql = f"""
                CREATE TABLE IF NOT EXISTS citations_backup_{timestamp} AS 
                SELECT * FROM citations;
            """
            self.supabase.execute_sql(backup_citations_sql)
            
            logger.info(f"Backup completed with timestamp: {timestamp}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def apply_migration(self) -> bool:
        """Apply the Week 1 migration."""
        logger.info("Applying Week 1 migration...")
        
        try:
            migration_sql = self.load_migration_sql()
            
            # Split the migration into individual statements
            statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                if statement:
                    logger.info(f"Executing statement {i+1}/{len(statements)}")
                    logger.debug(f"Statement: {statement[:100]}...")
                    
                    try:
                        self.supabase.execute_sql(statement)
                    except Exception as e:
                        logger.error(f"Failed to execute statement {i+1}: {e}")
                        logger.error(f"Statement: {statement}")
                        raise
            
            logger.info("Migration applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def validate_migration(self) -> bool:
        """Validate that the migration was applied correctly."""
        logger.info("Validating migration results...")
        
        try:
            # Check that new tables exist
            tables_check = self.supabase.execute_sql("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('jurisdictions', 'document_types')
                ORDER BY table_name
            """)
            
            expected_tables = ['document_types', 'jurisdictions']
            found_tables = [row['table_name'] for row in tables_check] if tables_check else []
            
            if set(found_tables) != set(expected_tables):
                logger.error(f"Expected tables {expected_tables}, found {found_tables}")
                return False
            
            # Check that new columns exist in existing tables
            cases_columns = self.supabase.execute_sql("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'cases' 
                AND column_name IN ('jurisdiction', 'doc_type', 'doc_subtype', 'hierarchy_path', 'practice_areas')
            """)
            
            expected_columns = ['jurisdiction', 'doc_type', 'doc_subtype', 'hierarchy_path', 'practice_areas']
            found_columns = [row['column_name'] for row in cases_columns] if cases_columns else []
            
            if set(found_columns) != set(expected_columns):
                logger.error(f"Expected columns {expected_columns}, found {found_columns}")
                return False
            
            # Check that jurisdiction data was inserted
            jurisdiction_count = self.supabase.execute_sql("SELECT COUNT(*) as count FROM jurisdictions")
            if not jurisdiction_count or jurisdiction_count[0]['count'] == 0:
                logger.error("No jurisdiction data found")
                return False
            
            # Check that document type data was inserted
            doc_type_count = self.supabase.execute_sql("SELECT COUNT(*) as count FROM document_types")
            if not doc_type_count or doc_type_count[0]['count'] == 0:
                logger.error("No document type data found")
                return False
            
            logger.info("Migration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Migration validation failed: {e}")
            return False
    
    def update_existing_data(self) -> bool:
        """Update existing data with default jurisdiction values."""
        logger.info("Updating existing data with default values...")
        
        try:
            # Count existing records
            cases_count = self.supabase.execute_sql("SELECT COUNT(*) as count FROM cases")
            cases_total = cases_count[0]['count'] if cases_count else 0
            
            opinions_count = self.supabase.execute_sql("SELECT COUNT(*) as count FROM opinions")
            opinions_total = opinions_count[0]['count'] if opinions_count else 0
            
            citations_count = self.supabase.execute_sql("SELECT COUNT(*) as count FROM citations")
            citations_total = citations_count[0]['count'] if citations_count else 0
            
            logger.info(f"Found {cases_total} cases, {opinions_total} opinions, {citations_total} citations")
            
            # Update cases with default jurisdiction if null
            if cases_total > 0:
                self.supabase.execute_sql("""
                    UPDATE cases 
                    SET jurisdiction = 'tx' 
                    WHERE jurisdiction IS NULL
                """)
                
                self.supabase.execute_sql("""
                    UPDATE cases 
                    SET doc_type = 'case' 
                    WHERE doc_type IS NULL
                """)
            
            # Update opinions with default jurisdiction if null
            if opinions_total > 0:
                self.supabase.execute_sql("""
                    UPDATE opinions 
                    SET jurisdiction = 'tx' 
                    WHERE jurisdiction IS NULL
                """)
                
                self.supabase.execute_sql("""
                    UPDATE opinions 
                    SET doc_type = 'case' 
                    WHERE doc_type IS NULL
                """)
            
            # Update citations with default jurisdiction if null
            if citations_total > 0:
                self.supabase.execute_sql("""
                    UPDATE citations 
                    SET jurisdiction = 'tx' 
                    WHERE jurisdiction IS NULL
                """)
            
            logger.info("Existing data updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update existing data: {e}")
            return False
    
    def generate_migration_report(self) -> Dict:
        """Generate a report of the migration results."""
        logger.info("Generating migration report...")
        
        try:
            report = {
                'migration_date': datetime.now().isoformat(),
                'migration_version': 'week1_schema_updates',
                'status': 'completed'
            }
            
            # Get jurisdiction counts
            jurisdictions = self.supabase.execute_sql("SELECT code, name FROM jurisdictions ORDER BY code")
            report['jurisdictions'] = jurisdictions if jurisdictions else []
            
            # Get document type counts
            doc_types = self.supabase.execute_sql("SELECT type_code, name FROM document_types ORDER BY type_code")
            report['document_types'] = doc_types if doc_types else []
            
            # Get data counts by jurisdiction
            jurisdiction_stats = self.supabase.execute_sql("""
                SELECT 
                    jurisdiction,
                    COUNT(*) as case_count
                FROM cases 
                GROUP BY jurisdiction 
                ORDER BY jurisdiction
            """)
            report['jurisdiction_stats'] = jurisdiction_stats if jurisdiction_stats else []
            
            # Get data counts by document type
            doc_type_stats = self.supabase.execute_sql("""
                SELECT 
                    doc_type,
                    COUNT(*) as count
                FROM cases 
                GROUP BY doc_type 
                ORDER BY doc_type
            """)
            report['doc_type_stats'] = doc_type_stats if doc_type_stats else []
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate migration report: {e}")
            return {'status': 'error', 'error': str(e)}

def main():
    """Main migration execution function."""
    logger.info("Starting Week 1 database migration")
    
    migration_manager = Week1MigrationManager()
    
    try:
        # Step 1: Check prerequisites
        if not migration_manager.check_prerequisites():
            logger.error("Prerequisites check failed. Aborting migration.")
            return 1
        
        # Step 2: Create backup
        if not migration_manager.backup_existing_data():
            logger.error("Backup failed. Aborting migration.")
            return 1
        
        # Step 3: Apply migration
        if not migration_manager.apply_migration():
            logger.error("Migration failed. Check logs for details.")
            return 1
        
        # Step 4: Update existing data
        if not migration_manager.update_existing_data():
            logger.error("Failed to update existing data. Migration may be incomplete.")
            return 1
        
        # Step 5: Validate migration
        if not migration_manager.validate_migration():
            logger.error("Migration validation failed. Migration may be incomplete.")
            return 1
        
        # Step 6: Generate report
        report = migration_manager.generate_migration_report()
        logger.info("Migration completed successfully!")
        logger.info(f"Migration report: {report}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Migration failed with error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

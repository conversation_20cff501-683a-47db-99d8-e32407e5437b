#!/usr/bin/env python3
"""
Apply Week 2 Database Migration
Applies schema updates for multi-jurisdictional automated harvesting system.

This script:
1. Applies Week 2 schema migration to Supabase
2. Creates new tables for harvesting jobs and quality metrics
3. Updates existing tables with new fields for enhanced processing
4. Validates the migration results
5. Provides rollback capabilities if needed
"""

import os
import sys
import logging
from typing import Dict, List, Optional
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Week2MigrationManager:
    """Manages the Week 2 database migration process."""
    
    def __init__(self):
        """Initialize the migration manager."""
        self.supabase = SupabaseConnector()
        self.migration_file = os.path.join(os.path.dirname(__file__), '..', 'migrations', 'week2_schema_updates.sql')
        
    def load_migration_sql(self) -> str:
        """Load the migration SQL from file."""
        try:
            with open(self.migration_file, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to load migration file: {e}")
            raise
    
    def check_prerequisites(self) -> bool:
        """Check if prerequisites for migration are met."""
        logger.info("Checking Week 2 migration prerequisites...")
        
        try:
            # Test database connection
            result = self.supabase.execute_sql("SELECT 1 as test")
            if not result:
                logger.error("Database connection test failed")
                return False
            
            # Check if Week 1 migration has been applied
            required_tables = ['jurisdictions', 'cases', 'documents']
            existing_tables = self.supabase.execute_sql("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name IN ('jurisdictions', 'cases', 'documents')
            """)

            if not existing_tables or len(existing_tables) < len(required_tables):
                logger.error("Week 1 migration must be applied first")
                if existing_tables:
                    found_tables = [t['table_name'] for t in existing_tables]
                    logger.error(f"Missing tables: {set(required_tables) - set(found_tables)}")
                else:
                    logger.error(f"Missing tables: {required_tables}")
                return False
            
            # Check if Week 2 migration has already been applied
            week2_tables = self.supabase.execute_sql("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('harvesting_jobs', 'case_processing_queue', 'harvesting_statistics')
            """)
            
            if week2_tables and len(week2_tables) > 0:
                logger.warning("Week 2 migration tables already exist. This migration may have been applied before.")
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
            
            logger.info("Prerequisites check passed")
            return True
            
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            return False
    
    def backup_existing_data(self) -> bool:
        """Create backup of existing data before migration."""
        logger.info("Creating backup of existing data...")
        
        try:
            backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Backup existing tables that will be modified
            tables_to_backup = ['cases', 'opinions', 'citations']
            
            for table in tables_to_backup:
                backup_table = f"{table}_backup_week2_{backup_timestamp}"
                
                # Create backup table
                backup_sql = f"""
                CREATE TABLE {backup_table} AS 
                SELECT * FROM {table};
                """
                
                result = self.supabase.execute_sql(backup_sql)
                if result is None:
                    logger.error(f"Failed to backup table {table}")
                    return False
                
                logger.info(f"Backed up {table} to {backup_table}")
            
            logger.info("Backup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def apply_migration(self) -> bool:
        """Apply the Week 2 migration."""
        logger.info("Applying Week 2 migration...")
        
        try:
            migration_sql = self.load_migration_sql()
            
            # Split the migration into individual statements
            statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
            
            for i, statement in enumerate(statements):
                if statement:
                    logger.info(f"Executing statement {i+1}/{len(statements)}")
                    logger.debug(f"Statement: {statement[:100]}...")
                    
                    try:
                        self.supabase.execute_sql(statement)
                    except Exception as e:
                        logger.error(f"Failed to execute statement {i+1}: {e}")
                        logger.error(f"Statement: {statement}")
                        # Continue with other statements for ALTER TABLE commands
                        if not statement.strip().upper().startswith('ALTER TABLE'):
                            raise
                        else:
                            logger.warning(f"Continuing despite ALTER TABLE error: {e}")
            
            logger.info("Week 2 migration applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def validate_migration(self) -> bool:
        """Validate that the migration was applied correctly."""
        logger.info("Validating Week 2 migration...")
        
        try:
            # Check that new tables exist
            required_tables = [
                'harvesting_jobs',
                'case_processing_queue', 
                'harvesting_statistics',
                'case_quality_metrics'
            ]
            
            for table in required_tables:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = '{table}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.error(f"Required table {table} was not created")
                    return False
                
                logger.info(f"✅ Table {table} exists")
            
            # Check that new columns were added
            column_checks = [
                ('cases', 'case_type'),
                ('cases', 'subject_matter'),
                ('cases', 'harvesting_metadata'),
                ('opinions', 'opinion_type'),
                ('opinions', 'quality'),
                ('citations', 'citation_strength')
            ]
            
            for table, column in column_checks:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table}' 
                    AND column_name = '{column}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.error(f"Required column {table}.{column} was not created")
                    return False
                
                logger.info(f"✅ Column {table}.{column} exists")
            
            # Check that views were created
            required_views = [
                'harvesting_job_summary',
                'case_quality_overview',
                'processing_queue_status'
            ]
            
            for view in required_views:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count
                    FROM information_schema.views 
                    WHERE table_schema = 'public' AND table_name = '{view}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.warning(f"View {view} was not created (may be expected)")
                else:
                    logger.info(f"✅ View {view} exists")
            
            # Check that functions were created
            required_functions = [
                'calculate_case_quality_score',
                'update_harvesting_statistics'
            ]
            
            for function in required_functions:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count
                    FROM information_schema.routines 
                    WHERE routine_schema = 'public' AND routine_name = '{function}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.warning(f"Function {function} was not created (may be expected)")
                else:
                    logger.info(f"✅ Function {function} exists")
            
            logger.info("Migration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Migration validation failed: {e}")
            return False

def main():
    """Main migration function"""
    print("🚀 Week 2 Database Migration")
    print("=" * 50)
    
    migration_manager = Week2MigrationManager()
    
    try:
        # Step 1: Check prerequisites
        if not migration_manager.check_prerequisites():
            logger.error("Prerequisites check failed. Aborting migration.")
            return 1
        
        # Step 2: Create backup
        if not migration_manager.backup_existing_data():
            logger.error("Backup failed. Aborting migration.")
            return 1
        
        # Step 3: Apply migration
        if not migration_manager.apply_migration():
            logger.error("Migration failed. Check logs for details.")
            return 1
        
        # Step 4: Validate migration
        if not migration_manager.validate_migration():
            logger.error("Migration validation failed. Migration may be incomplete.")
            return 1
        
        print("\n" + "=" * 50)
        print("✅ Week 2 migration completed successfully!")
        print("=" * 50)
        print("\nNext steps:")
        print("1. Run: python scripts/apply_week3_migration.py")
        print("2. Test the automated harvesting system")
        print("3. Verify enhanced case processing")
        
        return 0
        
    except Exception as e:
        logger.error(f"Migration failed with unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

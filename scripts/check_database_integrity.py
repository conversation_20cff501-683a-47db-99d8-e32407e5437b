#!/usr/bin/env python
"""
Database Integrity Check Script

This script tests connections to all databases used in the case law processing system
and verifies data integrity across systems without requiring the full processing pipeline.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DatabaseIntegrityChecker:
    """Checks database connections and data integrity across systems."""
    
    def __init__(self, user_role: str = "partner", tenant_id: Optional[str] = None):
        """Initialize the database integrity checker."""
        self.user_role = user_role
        self.tenant_id = tenant_id
        
        # Initialize connectors
        self.supabase = None
        self.gcs = None
        self.pinecone = None
        self.neo4j = None
        
        logger.info(f"Database Integrity Checker initialized")
        logger.info(f"User role: {user_role}")
        logger.info(f"Tenant ID: {tenant_id}")
    
    def initialize_connectors(self):
        """Initialize all database connectors."""
        logger.info("Initializing database connectors...")
        
        # Import connectors here to avoid circular imports
        try:
            from src.processing.storage.supabase_connector import SupabaseConnector
        except ImportError:
            logger.error("Supabase connector module not found")
            SupabaseConnector = None
            
        try:
            from src.processing.storage.gcs_connector import GCSConnector
        except ImportError:
            logger.error("GCS connector module not found")
            GCSConnector = None
            
        try:
            from src.processing.storage.pinecone_connector import PineconeConnector
        except ImportError:
            logger.error("Pinecone connector module not found")
            PineconeConnector = None
            
        try:
            from src.processing.storage.neo4j_connector import Neo4jConnector
        except ImportError:
            logger.error("Neo4j connector module not found")
            Neo4jConnector = None
        
        # Initialize Supabase connector if available
        if SupabaseConnector:
            try:
                logger.info("Initializing Supabase connector...")
                self.supabase = SupabaseConnector()
                logger.info("Supabase connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase connector: {str(e)}")
        else:
            logger.warning("Skipping Supabase connector initialization - module not available")
        
        # Initialize GCS connector if available
        if GCSConnector:
            try:
                logger.info("Initializing GCS connector...")
                bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
                self.gcs = GCSConnector(bucket_name=bucket_name)
                logger.info(f"GCS connector initialized successfully for bucket: {bucket_name}")
            except Exception as e:
                logger.error(f"Failed to initialize GCS connector: {str(e)}")
        else:
            logger.warning("Skipping GCS connector initialization - module not available")
        
        # Initialize Pinecone connector if available
        if PineconeConnector:
            try:
                logger.info("Initializing Pinecone connector...")
                index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
                self.pinecone = PineconeConnector(index_name=index_name)
                logger.info(f"Pinecone connector initialized successfully for index: {index_name}")
            except Exception as e:
                logger.error(f"Failed to initialize Pinecone connector: {str(e)}")
        else:
            logger.warning("Skipping Pinecone connector initialization - module not available")
        
        # Initialize Neo4j connector if available
        if Neo4jConnector:
            try:
                logger.info("Initializing Neo4j connector...")
                self.neo4j = Neo4jConnector()
                logger.info("Neo4j connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Neo4j connector: {str(e)}")
        else:
            logger.warning("Skipping Neo4j connector initialization - module not available")
    
    def check_supabase_connection(self) -> bool:
        """Check connection to Supabase and verify table structure."""
        if not self.supabase:
            logger.error("Supabase connector not initialized")
            return False
        
        logger.info("Checking Supabase connection...")
        
        # List of tables we expect to exist
        expected_tables = [
            "case_processing_batches",
            "processing_history",
            "cases",
            "opinions",
            "citations"
        ]
        
        # Check each table
        tables_exist = {}
        for table in expected_tables:
            try:
                # Try to select a single row from the table
                response = self.supabase.client.table(table).select("*").limit(1).execute()
                tables_exist[table] = True
                logger.info(f"Table '{table}' exists")
            except Exception as e:
                tables_exist[table] = False
                logger.warning(f"Table '{table}' does not exist or is not accessible: {str(e)}")
        
        # Report results
        missing_tables = [table for table, exists in tables_exist.items() if not exists]
        if missing_tables:
            logger.warning(f"Missing tables: {', '.join(missing_tables)}")
            logger.info("You may need to create these tables manually or fix permissions")
        else:
            logger.info("All expected tables exist in Supabase")
        
        return len(missing_tables) == 0
    
    def check_gcs_connection(self) -> bool:
        """Check connection to Google Cloud Storage."""
        if not self.gcs:
            logger.error("GCS connector not initialized")
            return False
        
        logger.info("Checking GCS connection...")
        
        try:
            # List buckets to verify connection
            buckets = list(self.gcs.client.list_buckets())
            bucket_names = [bucket.name for bucket in buckets]
            
            logger.info(f"GCS connection successful")
            logger.info(f"Available buckets: {', '.join(bucket_names)}")
            
            # Check if our bucket exists
            if self.gcs.bucket_name in bucket_names:
                logger.info(f"Target bucket '{self.gcs.bucket_name}' exists")
                
                # List some files in the bucket
                blobs = list(self.gcs.bucket.list_blobs(max_results=5))
                if blobs:
                    logger.info(f"Found {len(blobs)} files in bucket")
                    for blob in blobs:
                        logger.info(f"  - {blob.name}")
                else:
                    logger.info("Bucket is empty")
                
                return True
            else:
                logger.warning(f"Target bucket '{self.gcs.bucket_name}' does not exist")
                return False
                
        except Exception as e:
            logger.error(f"Error checking GCS connection: {str(e)}")
            return False
    
    def check_pinecone_connection(self) -> bool:
        """Check connection to Pinecone vector database."""
        if not self.pinecone:
            logger.error("Pinecone connector not initialized")
            return False
        
        logger.info("Checking Pinecone connection...")
        
        try:
            # Get index stats to verify connection
            stats = self.pinecone.index.describe_index_stats()
            
            logger.info(f"Pinecone connection successful")
            logger.info(f"Index stats: {stats}")
            
            # Check if index has vectors
            total_vectors = stats.get('total_vector_count', 0)
            logger.info(f"Total vectors in index: {total_vectors}")
            
            return True
        except Exception as e:
            logger.error(f"Error checking Pinecone connection: {str(e)}")
            return False
    
    def check_neo4j_connection(self) -> bool:
        """Check connection to Neo4j graph database."""
        if not self.neo4j:
            logger.error("Neo4j connector not initialized")
            return False
        
        logger.info("Checking Neo4j connection...")
        
        try:
            # Run a simple query to verify connection
            result = self.neo4j.run_query("MATCH (n) RETURN count(n) as count")
            node_count = result[0]["count"] if result else 0
            
            logger.info(f"Neo4j connection successful")
            logger.info(f"Database contains {node_count} nodes")
            
            # Check for case nodes
            case_result = self.neo4j.run_query("MATCH (c:Case) RETURN count(c) as count")
            case_count = case_result[0]["count"] if case_result else 0
            logger.info(f"Database contains {case_count} case nodes")
            
            # Check for citation relationships
            citation_result = self.neo4j.run_query(
                "MATCH (c1:Case)-[r:CITES]->(c2:Case) RETURN count(r) as count"
            )
            citation_count = citation_result[0]["count"] if citation_result else 0
            logger.info(f"Database contains {citation_count} citation relationships")
            
            return True
        except Exception as e:
            logger.error(f"Error checking Neo4j connection: {str(e)}")
            return False
    
    def check_cross_database_integrity(self, case_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check data integrity across all databases for a specific case.
        
        Args:
            case_id: Optional case ID to check. If None, will try to find a case that exists in all systems.
            
        Returns:
            Dict with integrity check results
        """
        logger.info("Checking cross-database integrity...")
        
        if not all([self.supabase, self.gcs, self.pinecone, self.neo4j]):
            logger.error("Cannot check cross-database integrity: some connectors not initialized")
            return {"status": "error", "message": "Some database connectors not initialized"}
        
        # If no case ID provided, try to find one that exists in Supabase
        if not case_id:
            try:
                response = self.supabase.client.table("cases").select("id").limit(1).execute()
                if response.data and len(response.data) > 0:
                    case_id = response.data[0]["id"]
                    logger.info(f"Found case ID for integrity check: {case_id}")
                else:
                    logger.warning("No cases found in Supabase")
                    return {"status": "error", "message": "No cases found in Supabase"}
            except Exception as e:
                logger.error(f"Error finding case in Supabase: {str(e)}")
                return {"status": "error", "message": f"Error finding case: {str(e)}"}
        
        logger.info(f"Checking data integrity for case: {case_id}")
        
        # Check case in each system
        results = {
            "case_id": case_id,
            "supabase": {"exists": False, "details": {}},
            "gcs": {"exists": False, "details": {}},
            "pinecone": {"exists": False, "details": {}},
            "neo4j": {"exists": False, "details": {}}
        }
        
        # Check in Supabase
        try:
            case_response = self.supabase.client.table("cases").select("*").eq("id", case_id).execute()
            if case_response.data and len(case_response.data) > 0:
                results["supabase"]["exists"] = True
                results["supabase"]["details"] = case_response.data[0]
                logger.info(f"Case exists in Supabase: {case_id}")
            else:
                logger.warning(f"Case not found in Supabase: {case_id}")
        except Exception as e:
            logger.error(f"Error checking case in Supabase: {str(e)}")
        
        # Check in GCS
        try:
            # Check for case document in GCS
            case_path = f"cases/{case_id}/metadata.json"
            blob = self.gcs.bucket.blob(case_path)
            if blob.exists():
                results["gcs"]["exists"] = True
                results["gcs"]["details"]["path"] = case_path
                logger.info(f"Case exists in GCS: {case_id}")
            else:
                logger.warning(f"Case not found in GCS: {case_id}")
        except Exception as e:
            logger.error(f"Error checking case in GCS: {str(e)}")
        
        # Check in Pinecone
        try:
            # Query Pinecone for vectors related to this case
            query_response = self.pinecone.index.query(
                vector=[0.1] * 768,  # Dummy vector for query
                filter={"case_id": case_id},
                top_k=1,
                include_metadata=True
            )
            if query_response.matches and len(query_response.matches) > 0:
                results["pinecone"]["exists"] = True
                results["pinecone"]["details"]["matches"] = len(query_response.matches)
                logger.info(f"Case exists in Pinecone: {case_id}")
            else:
                logger.warning(f"Case not found in Pinecone: {case_id}")
        except Exception as e:
            logger.error(f"Error checking case in Pinecone: {str(e)}")
        
        # Check in Neo4j
        try:
            # Query Neo4j for the case node
            query = f"MATCH (c:Case {{id: '{case_id}'}}) RETURN c"
            result = self.neo4j.run_query(query)
            if result and len(result) > 0:
                results["neo4j"]["exists"] = True
                results["neo4j"]["details"]["node"] = result[0]["c"]
                logger.info(f"Case exists in Neo4j: {case_id}")
                
                # Check citation relationships
                citation_query = f"""
                MATCH (c:Case {{id: '{case_id}'}})-[r:CITES]->(cited:Case)
                RETURN count(r) as citation_count
                """
                citation_result = self.neo4j.run_query(citation_query)
                if citation_result and len(citation_result) > 0:
                    results["neo4j"]["details"]["citation_count"] = citation_result[0]["citation_count"]
            else:
                logger.warning(f"Case not found in Neo4j: {case_id}")
        except Exception as e:
            logger.error(f"Error checking case in Neo4j: {str(e)}")
        
        # Calculate overall integrity
        systems_with_case = sum(1 for system in ["supabase", "gcs", "pinecone", "neo4j"] 
                               if results[system]["exists"])
        results["integrity_score"] = systems_with_case / 4.0
        results["status"] = "success"
        
        logger.info(f"Integrity check complete for case {case_id}")
        logger.info(f"Integrity score: {results['integrity_score'] * 100:.1f}%")
        
        return results
    
    def run_integrity_check(self, case_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Run a full integrity check across all databases.
        
        Args:
            case_id: Optional case ID to check
            
        Returns:
            Dict with check results
        """
        logger.info("=" * 80)
        logger.info("STARTING DATABASE INTEGRITY CHECK")
        logger.info("=" * 80)
        
        # Initialize all connectors
        self.initialize_connectors()
        
        # Check each database connection
        supabase_ok = self.check_supabase_connection()
        gcs_ok = self.check_gcs_connection()
        pinecone_ok = self.check_pinecone_connection()
        neo4j_ok = self.check_neo4j_connection()
        
        # Summarize connection results
        logger.info("=" * 80)
        logger.info("CONNECTION CHECK RESULTS")
        logger.info("=" * 80)
        logger.info(f"Supabase: {'✅ Connected' if supabase_ok else '❌ Failed'}")
        logger.info(f"GCS: {'✅ Connected' if gcs_ok else '❌ Failed'}")
        logger.info(f"Pinecone: {'✅ Connected' if pinecone_ok else '❌ Failed'}")
        logger.info(f"Neo4j: {'✅ Connected' if neo4j_ok else '❌ Failed'}")
        
        # Check cross-database integrity if all connections are working
        integrity_results = {"status": "skipped", "message": "Skipped due to connection issues"}
        if all([supabase_ok, gcs_ok, pinecone_ok, neo4j_ok]):
            logger.info("=" * 80)
            logger.info("CROSS-DATABASE INTEGRITY CHECK")
            logger.info("=" * 80)
            integrity_results = self.check_cross_database_integrity(case_id)
        
        # Compile final results
        results = {
            "connections": {
                "supabase": supabase_ok,
                "gcs": gcs_ok,
                "pinecone": pinecone_ok,
                "neo4j": neo4j_ok
            },
            "all_connected": all([supabase_ok, gcs_ok, pinecone_ok, neo4j_ok]),
            "integrity_check": integrity_results
        }
        
        logger.info("=" * 80)
        logger.info("INTEGRITY CHECK COMPLETE")
        logger.info("=" * 80)
        
        return results

def main():
    """Run the database integrity check."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Check database integrity across systems")
    parser.add_argument("--role", default="partner", help="User role for access control")
    parser.add_argument("--tenant", help="Tenant ID for multi-tenant isolation")
    parser.add_argument("--case-id", help="Specific case ID to check")
    
    args = parser.parse_args()
    
    # Run the integrity check
    checker = DatabaseIntegrityChecker(
        user_role=args.role,
        tenant_id=args.tenant
    )
    
    results = checker.run_integrity_check(case_id=args.case_id)
    
    # Print summary
    print("\n" + "=" * 80)
    print("DATABASE INTEGRITY CHECK SUMMARY")
    print("=" * 80)
    
    print("\nConnection Status:")
    for db, status in results["connections"].items():
        print(f"  - {db.capitalize()}: {'✅ Connected' if status else '❌ Failed'}")
    
    print("\nIntegrity Check:")
    if results["all_connected"] and results["integrity_check"]["status"] == "success":
        print(f"  - Case ID: {results['integrity_check']['case_id']}")
        print(f"  - Integrity Score: {results['integrity_check']['integrity_score'] * 100:.1f}%")
        
        for system, details in results["integrity_check"].items():
            if system in ["supabase", "gcs", "pinecone", "neo4j"]:
                print(f"  - {system.capitalize()}: {'✅ Found' if details['exists'] else '❌ Missing'}")
    else:
        print(f"  - Status: {results['integrity_check']['status']}")
        if "message" in results["integrity_check"]:
            print(f"  - Message: {results['integrity_check']['message']}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
cleanup_databases.py

This script deletes all existing records from Supabase (documents and chunks tables)
and removes all vectors from Pinecone to prepare for a fresh import.

Usage:
  python cleanup_databases.py --namespace tx [--confirm]

Notes:
- Use the --confirm flag to actually perform the deletion (safety measure)
"""

import os
import argparse
import uuid
from dotenv import load_dotenv
from supabase import create_client
from pinecone import Pinecone

# Load environment variables
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")

def delete_from_supabase(namespace, confirm=False):
    """Delete all records for a given namespace from Supabase documents and chunks tables."""
    if not confirm:
        print(f"[DRY RUN] Would delete records for namespace '{namespace}' from Supabase")
        return
    
    # Initialize Supabase client
    supabase_client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Get document IDs for the namespace
    doc_response = supabase_client.table("documents") \
                               .select("document_id") \
                               .eq("namespace", namespace) \
                               .execute()
    
    if len(doc_response.data) == 0:
        print(f"[Info] No documents found for namespace '{namespace}'")
        return
    
    document_ids = [doc["document_id"] for doc in doc_response.data]
    print(f"[Info] Found {len(document_ids)} documents to delete")
    
    # Delete chunks first (foreign key constraint)
    chunks_deleted = 0
    for doc_id in document_ids:
        chunk_response = supabase_client.table("chunks") \
                                     .delete() \
                                     .eq("document_id", doc_id) \
                                     .execute()
        chunks_deleted += len(chunk_response.data)
    
    print(f"[Info] Deleted {chunks_deleted} chunks from Supabase")
    
    # Delete documents
    docs_response = supabase_client.table("documents") \
                                .delete() \
                                .eq("namespace", namespace) \
                                .execute()
    
    print(f"[Info] Deleted {len(docs_response.data)} documents from Supabase")

def delete_from_pinecone(namespace, confirm=False):
    """Delete all vectors for a given namespace from Pinecone."""
    if not confirm:
        print(f"[DRY RUN] Would delete vectors for namespace '{namespace}' from Pinecone")
        return
    
    # Initialize Pinecone
    pc = Pinecone(api_key=PINECONE_API_KEY, environment=PINECONE_ENVIRONMENT)
    index = pc.Index(PINECONE_INDEX_NAME)
    
    # Delete all vectors in the namespace
    try:
        stats_before = index.describe_index_stats()
        namespaces_before = stats_before.namespaces
        
        if namespace in namespaces_before:
            count_before = namespaces_before[namespace].vector_count
            print(f"[Info] Found {count_before} vectors in Pinecone namespace '{namespace}'")
            
            # Delete all vectors in the namespace
            index.delete(delete_all=True, namespace=namespace)
            
            # Verify deletion
            stats_after = index.describe_index_stats()
            namespaces_after = stats_after.namespaces
            
            if namespace in namespaces_after:
                count_after = namespaces_after[namespace].vector_count
                print(f"[Info] Deleted {count_before - count_after} vectors from Pinecone")
            else:
                print(f"[Info] Deleted all vectors from Pinecone namespace '{namespace}'")
        else:
            print(f"[Info] No vectors found in Pinecone namespace '{namespace}'")
    except Exception as e:
        print(f"[Error] Failed to delete from Pinecone: {e}")

def main():
    parser = argparse.ArgumentParser(description='Clean up databases by deleting records for a namespace')
    parser.add_argument('--namespace', type=str, required=True, help='Namespace to delete (e.g., tx)')
    parser.add_argument('--confirm', action='store_true', help='Confirm deletion (without this flag, it runs in dry-run mode)')
    args = parser.parse_args()
    
    namespace = args.namespace
    confirm = args.confirm
    
    if not confirm:
        print("Running in DRY RUN mode. Add --confirm to actually delete data.")
        print("WARNING: This will permanently delete all data for the specified namespace!")
    
    # Delete from Supabase
    delete_from_supabase(namespace, confirm)
    
    # Delete from Pinecone
    delete_from_pinecone(namespace, confirm)
    
    if not confirm:
        print("\nDRY RUN complete. No data was deleted.")
        print("Run with --confirm to actually delete the data.")
    else:
        print("\nCleanup complete! All data has been deleted for namespace: " + namespace)

if __name__ == "__main__":
    main()

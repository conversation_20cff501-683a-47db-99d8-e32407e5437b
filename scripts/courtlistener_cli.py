#!/usr/bin/env python3
"""
Court Listener Command Line Tool
Provides command-line access to Court Listener API functions
"""

import os
import sys
import argparse
import json
from datetime import datetime

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.client import CourtListenerClient
from src.api.courtlistener.exceptions import CourtListenerAPIError
from src.api.courtlistener.utils import standardize_jurisdiction


def format_citation_results(cases):
    """Format citation search results for display"""
    results = []
    for i, case in enumerate(cases, 1):
        case_info = {
            "Case": case.name,
            "Court": case.court.name,
            "Date": case.date_filed.strftime("%Y-%m-%d") if case.date_filed else "Unknown",
            "Docket": case.docket_number or "Unknown",
            "Citations": [str(cite) for cite in case.citations],
            "Judges": [judge.name for judge in case.judges],
            "ID": case.id
        }
        results.append(case_info)
    return results


def format_search_results(results):
    """Format search results for display"""
    formatted = []
    for i, result in enumerate(results.get("results", []), 1):
        formatted.append({
            "Case": result.get("case_name", "Unknown"),
            "Court": result.get("court", {}).get("full_name", "Unknown"),
            "Date": result.get("date_filed", "Unknown"),
            "Status": result.get("precedential_status", "Unknown"),
            "ID": result.get("id", "Unknown")
        })
    
    meta = {
        "Total": results.get("count", 0),
        "Page": results.get("page", 1),
        "Pages": results.get("page_count", 1)
    }
    
    return {"results": formatted, "meta": meta}


def search_citation(args):
    """Search for cases by citation"""
    client = CourtListenerClient()
    try:
        cases = client.get_cases_by_citation(args.citation)
        results = format_citation_results(cases)
        
        # Print results based on format
        if args.format == "json":
            print(json.dumps(results, indent=2))
        else:
            print(f"Found {len(results)} cases for citation '{args.citation}':")
            for i, case in enumerate(results, 1):
                print(f"\nCase {i}:")
                print(f"  Name: {case['Case']}")
                print(f"  Court: {case['Court']}")
                print(f"  Date: {case['Date']}")
                print(f"  Docket: {case['Docket']}")
                print(f"  Citations: {', '.join(case['Citations'])}")
                if case['Judges']:
                    print(f"  Judges: {', '.join(case['Judges'])}")
                print(f"  ID: {case['ID']}")
        
        # If details requested and cases found, get first opinion
        if args.details and cases:
            case_id = cases[0].id
            print(f"\nFetching opinion details for case ID {case_id}...")
            
            try:
                opinions = client.get_opinions_by_case(case_id)
                if opinions:
                    print(f"\nFound {len(opinions)} opinions:")
                    for i, opinion in enumerate(opinions, 1):
                        print(f"\nOpinion {i}:")
                        print(f"  Author: {opinion.author or 'Unknown'}")
                        print(f"  Type: {opinion.type or 'Unknown'}")
                        
                        # Show text snippet if available
                        if opinion.text:
                            snippet = opinion.text[:300] + "..." if len(opinion.text) > 300 else opinion.text
                            print(f"\nText snippet:\n{snippet}")
                        else:
                            print("\nNo text content available")
                else:
                    print("No opinions found for this case")
            
            except CourtListenerAPIError as e:
                print(f"Error retrieving opinions: {e}")
                
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def search_text(args):
    """Search for cases by text query"""
    client = CourtListenerClient()
    try:
        # Prepare search parameters
        params = {
            "page": args.page,
            "page_size": args.limit
        }
        
        # Add jurisdiction if provided
        if args.jurisdiction:
            params["jurisdiction"] = standardize_jurisdiction(args.jurisdiction)
        
        # Execute search
        results = client.search_cases(query=args.query, **params)
        formatted = format_search_results(results)
        
        # Print results based on format
        if args.format == "json":
            print(json.dumps(formatted, indent=2))
        else:
            meta = formatted["meta"]
            print(f"Found {meta['Total']} cases matching '{args.query}'")
            print(f"Page {meta['Page']} of {meta['Pages']}")
            
            for i, case in enumerate(formatted["results"], 1):
                print(f"\nResult {i}:")
                print(f"  Case: {case['Case']}")
                print(f"  Court: {case['Court']}")
                print(f"  Date: {case['Date']}")
                print(f"  Status: {case['Status']}")
                print(f"  ID: {case['ID']}")
    
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def get_opinion(args):
    """Get opinion details"""
    client = CourtListenerClient()
    try:
        opinion = client.get_opinion(args.opinion_id)
        
        # Print results based on format
        if args.format == "json":
            print(json.dumps(opinion.raw_data, indent=2))
        else:
            print(f"Opinion ID: {opinion.id}")
            print(f"Case Name: {opinion.case_name}")
            print(f"Author: {opinion.author or 'Unknown'}")
            print(f"Type: {opinion.type or 'Unknown'}")
            print(f"Download URL: {opinion.download_url or 'N/A'}")
            print(f"Created: {opinion.date_created}")
            print(f"Modified: {opinion.date_modified}")
            
            # Show opinion text based on requested format
            if args.output == "html" and opinion.html:
                print("\nHTML Content:")
                print(opinion.html[:1000] + "..." if len(opinion.html) > 1000 else opinion.html)
            elif args.output == "text" and opinion.text:
                print("\nText Content:")
                print(opinion.text[:1000] + "..." if len(opinion.text) > 1000 else opinion.text)
            elif opinion.text:
                print("\nText Content:")
                print(opinion.text[:1000] + "..." if len(opinion.text) > 1000 else opinion.text)
            elif opinion.html:
                print("\nHTML Content:")
                print(opinion.html[:1000] + "..." if len(opinion.html) > 1000 else opinion.html)
            else:
                print("\nNo content available for this opinion")
                
            # Save to file if requested
            if args.save:
                filename = f"opinion_{opinion.id}.txt"
                content = opinion.text or opinion.html or "No content available"
                with open(filename, "w") as f:
                    f.write(content)
                print(f"\nOpinion saved to {filename}")
    
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def list_courts(args):
    """List available courts"""
    client = CourtListenerClient()
    try:
        courts = client.get_courts(jurisdiction=args.jurisdiction)
        
        # Print results based on format
        if args.format == "json":
            court_data = [{"id": c.id, "name": c.name, "jurisdiction": c.jurisdiction} for c in courts]
            print(json.dumps(court_data, indent=2))
        else:
            jurisdiction_filter = f" in {args.jurisdiction}" if args.jurisdiction else ""
            print(f"Found {len(courts)} courts{jurisdiction_filter}:")
            
            # Group by jurisdiction
            by_jurisdiction = {}
            for court in courts:
                jurisdiction = court.jurisdiction
                if jurisdiction not in by_jurisdiction:
                    by_jurisdiction[jurisdiction] = []
                by_jurisdiction[jurisdiction].append(court)
            
            # Print grouped results
            for jurisdiction, courts in by_jurisdiction.items():
                print(f"\n{jurisdiction.upper()} ({len(courts)} courts):")
                for court in courts:
                    print(f"  {court.name} (ID: {court.id})")
    
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def main():
    """Main entry point for the command-line tool"""
    parser = argparse.ArgumentParser(description="Court Listener API Command Line Tool")
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")
    
    # Citation search command
    cite_parser = subparsers.add_parser("citation", help="Search for cases by citation")
    cite_parser.add_argument("citation", help="Citation to search for (e.g., '410 U.S. 113')")
    cite_parser.add_argument("--format", choices=["text", "json"], default="text", help="Output format")
    cite_parser.add_argument("--details", action="store_true", help="Show detailed opinion information")
    
    # Text search command
    text_parser = subparsers.add_parser("search", help="Search for cases by text")
    text_parser.add_argument("query", help="Search query")
    text_parser.add_argument("--jurisdiction", help="Jurisdiction filter (e.g., 'tex' for Texas)")
    text_parser.add_argument("--page", type=int, default=1, help="Page number")
    text_parser.add_argument("--limit", type=int, default=10, help="Results per page")
    text_parser.add_argument("--format", choices=["text", "json"], default="text", help="Output format")
    
    # Opinion command
    opinion_parser = subparsers.add_parser("opinion", help="Get opinion details")
    opinion_parser.add_argument("opinion_id", help="Opinion ID")
    opinion_parser.add_argument("--format", choices=["text", "json"], default="text", help="Output format")
    opinion_parser.add_argument("--output", choices=["text", "html"], default="text", help="Content format")
    opinion_parser.add_argument("--save", action="store_true", help="Save opinion to file")
    
    # Courts command
    courts_parser = subparsers.add_parser("courts", help="List available courts")
    courts_parser.add_argument("--jurisdiction", help="Filter by jurisdiction")
    courts_parser.add_argument("--format", choices=["text", "json"], default="text", help="Output format")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Execute command
    if args.command == "citation":
        search_citation(args)
    elif args.command == "search":
        search_text(args)
    elif args.command == "opinion":
        get_opinion(args)
    elif args.command == "courts":
        list_courts(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()

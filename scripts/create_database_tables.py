#!/usr/bin/env python
"""
Database Table Creation Script

This script creates the necessary tables in Supabase for the case law processor.
It uses the Supabase MCP server to apply migrations directly to the database.
"""

import os
import sys
import logging
import pathlib
import json
import subprocess
import uuid
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def get_supabase_project_id():
    """Get the Supabase project ID from the environment variables or config."""
    # Connect to Supabase to get the URL
    supabase = SupabaseConnector()
    url = supabase.url
    
    # Extract project ID from URL (e.g., https://anwefmklplkjxkmzpnva.supabase.co)
    if url:
        project_id = url.split('//')[1].split('.')[0]
        logger.info(f"Extracted Supabase project ID: {project_id}")
        return project_id
    else:
        logger.error("Could not determine Supabase project ID")
        return None

def create_tables():
    """Create all necessary tables in Supabase using the MCP server."""
    logger.info("Connecting to Supabase...")
    
    # Get the project ID
    project_id = get_supabase_project_id()
    if not project_id:
        logger.error("Failed to get Supabase project ID")
        return False
    
    # Read the SQL migration file
    migration_path = pathlib.Path(__file__).parent.parent / 'migrations' / 'create_case_law_tables.sql'
    logger.info(f"Reading migration file: {migration_path}")
    
    try:
        with open(migration_path, 'r') as f:
            sql = f.read()
        
        # Generate a unique name for this migration
        migration_name = f"create_case_law_tables_{uuid.uuid4().hex[:8]}"
        logger.info(f"Migration name: {migration_name}")
        
        # Execute the SQL using the MCP server
        logger.info("Applying migration to create tables...")
        
        # Create a temporary file with the MCP command
        mcp_command = {
            "name": "mcp2_apply_migration",
            "params": {
                "name": migration_name,
                "project_id": project_id,
                "query": sql
            }
        }
        
        # Write the command to a temporary file
        temp_file = pathlib.Path(__file__).parent / f"temp_mcp_command_{uuid.uuid4().hex[:8]}.json"
        with open(temp_file, 'w') as f:
            json.dump(mcp_command, f)
        
        # Execute the MCP command using the Codeium CLI
        logger.info("Executing MCP command...")
        try:
            # Use subprocess to run the command
            result = subprocess.run(
                ["codeium", "mcp", "execute", str(temp_file)],
                capture_output=True,
                text=True,
                check=True
            )
            logger.info("MCP command executed successfully")
            logger.info(f"Output: {result.stdout}")
            
            # Clean up the temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
            logger.info("All tables created successfully!")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error executing MCP command: {e.stderr}")
            # Clean up the temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)
            return False
    except Exception as e:
        logger.error(f"Error creating tables: {str(e)}")
        return False

def create_tables_individually():
    """Create tables individually using direct SQL statements via the MCP server."""
    logger.info("Creating tables individually...")
    
    # Get the project ID
    project_id = get_supabase_project_id()
    if not project_id:
        logger.error("Failed to get Supabase project ID")
        return False
    
    # Define SQL statements for each table
    tables = {
        "case_processing_batches": """
        CREATE TABLE IF NOT EXISTS case_processing_batches (
            id UUID PRIMARY KEY,
            source TEXT NOT NULL,
            jurisdiction TEXT NOT NULL,
            query_params JSONB,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE,
            total INTEGER DEFAULT 0,
            success INTEGER DEFAULT 0,
            failure INTEGER DEFAULT 0,
            skipped INTEGER DEFAULT 0,
            status TEXT DEFAULT 'processing',
            user_id TEXT,
            user_role TEXT,
            tenant_id TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        "processing_history": """
        CREATE TABLE IF NOT EXISTS processing_history (
            id UUID PRIMARY KEY,
            case_id TEXT NOT NULL,
            batch_id UUID REFERENCES case_processing_batches(id),
            action TEXT NOT NULL,
            status TEXT NOT NULL,
            details JSONB,
            user_id TEXT,
            user_role TEXT,
            tenant_id TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        "cases": """
        CREATE TABLE IF NOT EXISTS cases (
            id TEXT PRIMARY KEY,
            case_name TEXT NOT NULL,
            jurisdiction TEXT NOT NULL,
            court TEXT,
            date_filed DATE,
            docket_number TEXT,
            cluster_id TEXT,
            gcs_path TEXT,
            pinecone_id TEXT,
            opinion_count INTEGER DEFAULT 0,
            citation_count INTEGER DEFAULT 0,
            completeness_score FLOAT DEFAULT 0,
            document_quality TEXT,
            metadata_quality TEXT,
            user_id TEXT,
            user_role TEXT,
            tenant_id TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        "opinions": """
        CREATE TABLE IF NOT EXISTS opinions (
            id TEXT PRIMARY KEY,
            case_id TEXT REFERENCES cases(id),
            opinion_type TEXT,
            author TEXT,
            gcs_path TEXT,
            pinecone_id TEXT,
            has_text BOOLEAN DEFAULT FALSE,
            word_count INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """,
        
        "citations": """
        CREATE TABLE IF NOT EXISTS citations (
            id TEXT PRIMARY KEY,
            citing_case_id TEXT REFERENCES cases(id),
            cited_case_id TEXT,
            citation_text TEXT,
            confidence FLOAT,
            neo4j_relationship_id TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """
    }
    
    success = True
    for table_name, sql in tables.items():
        try:
            # Generate a unique name for this migration
            migration_name = f"create_{table_name}_{uuid.uuid4().hex[:8]}"
            logger.info(f"Creating table: {table_name} with migration: {migration_name}")
            
            # Create a temporary file with the MCP command
            mcp_command = {
                "name": "mcp2_apply_migration",
                "params": {
                    "name": migration_name,
                    "project_id": project_id,
                    "query": sql
                }
            }
            
            # Write the command to a temporary file
            temp_file = pathlib.Path(__file__).parent / f"temp_mcp_command_{uuid.uuid4().hex[:8]}.json"
            with open(temp_file, 'w') as f:
                json.dump(mcp_command, f)
            
            # Execute the MCP command using the Codeium CLI
            result = subprocess.run(
                ["codeium", "mcp", "execute", str(temp_file)],
                capture_output=True,
                text=True,
                check=True
            )
            logger.info(f"Created table: {table_name}")
            
            # Clean up the temporary file
            if os.path.exists(temp_file):
                os.remove(temp_file)
                
        except Exception as e:
            logger.error(f"Error creating table {table_name}: {str(e)}")
            # Clean up the temporary file if it exists
            if 'temp_file' in locals() and os.path.exists(temp_file):
                os.remove(temp_file)
            success = False
    
    return success

if __name__ == "__main__":
    # Try the full migration first
    logger.info("Attempting to create all tables using migration file...")
    success = create_tables()
    
    # If that fails, try creating tables individually
    if not success:
        logger.info("Migration failed, attempting to create tables individually...")
        success = create_tables_individually()
    
    sys.exit(0 if success else 1)

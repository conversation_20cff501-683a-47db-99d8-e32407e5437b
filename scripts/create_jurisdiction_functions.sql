-- Create jurisdiction-related functions for multi-jurisdictional implementation

-- 1. Create jurisdiction_exists function
CREATE OR REPLACE FUNCTION jurisdiction_exists(p_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    v_exists BOOLEAN;
BEGIN
    SELECT EXISTS(SELECT 1 FROM jurisdictions WHERE code = p_code) INTO v_exists;
    RETURN v_exists;
END;
$$ LANGUAGE plpgsql;

-- 2. Create get_jurisdiction_citation_patterns function
CREATE OR REPLACE FUNCTION get_jurisdiction_citation_patterns(p_jurisdiction TEXT)
RETURNS JSONB AS $$
DECLARE
    v_patterns JSONB;
BEGIN
    SELECT jsonb_agg(
        jsonb_build_object(
            'pattern', pattern,
            'description', description,
            'example', example
        )
    )
    FROM citation_patterns
    WHERE jurisdiction = p_jurisdiction
    INTO v_patterns;
    
    RETURN COALESCE(v_patterns, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- 3. <PERSON>reate get_court_with_jurisdiction function
CREATE OR REPLACE FUNCTION get_court_with_jurisdiction(p_court_id TEXT)
RETURNS JSONB AS $$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'court', row_to_json(c.*),
        'jurisdiction', row_to_json(j.*)
    )
    FROM courts c
    JOIN jurisdictions j ON c.jurisdiction = j.code
    WHERE c.id = p_court_id
    INTO v_result;
    
    RETURN COALESCE(v_result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- 4. Create citation_patterns table if it doesn't exist
CREATE TABLE IF NOT EXISTS citation_patterns (
    id SERIAL PRIMARY KEY,
    jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
    pattern TEXT NOT NULL,
    description TEXT,
    example TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add test citation patterns
DELETE FROM citation_patterns;

-- Texas citation patterns
INSERT INTO citation_patterns (jurisdiction, pattern, description, example)
VALUES 
    ('tx', '\d+\s+S\.W\.\d+', 'South Western Reporter', '123 S.W.2d 456'),
    ('tx', '\d+\s+Tex\.\s+\d+', 'Texas Reports', '123 Tex. 456');

-- California citation patterns
INSERT INTO citation_patterns (jurisdiction, pattern, description, example)
VALUES 
    ('ca', '\d+\s+Cal\.\d+', 'California Reports', '123 Cal.4th 456'),
    ('ca', '\d+\s+Cal\.Rptr\.\d+', 'California Reporter', '123 Cal.Rptr.3d 456');

-- New York citation patterns
INSERT INTO citation_patterns (jurisdiction, pattern, description, example)
VALUES 
    ('ny', '\d+\s+N\.Y\.\d+', 'New York Reports', '123 N.Y.3d 456'),
    ('ny', '\d+\s+N\.Y\.S\.\d+', 'New York Supplement', '123 N.Y.S.2d 456');

-- Federal citation patterns
INSERT INTO citation_patterns (jurisdiction, pattern, description, example)
VALUES 
    ('fed', '\d+\s+U\.S\.\s+\d+', 'United States Reports', '123 U.S. 456'),
    ('fed', '\d+\s+F\.\d+', 'Federal Reporter', '123 F.3d 456'),
    ('fed', '\d+\s+F\.\s*Supp\.\s*\d+', 'Federal Supplement', '123 F.Supp.2d 456');

-- 6. Create court_systems table if it doesn't exist
CREATE TABLE IF NOT EXISTS court_systems (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
    hierarchy JSONB,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Add test court systems
DELETE FROM court_systems;

-- Texas court system
INSERT INTO court_systems (id, name, jurisdiction, hierarchy)
VALUES (
    'tx_courts', 
    'Texas Court System', 
    'tx', 
    '{
        "supreme": {
            "name": "Supreme Court of Texas",
            "id": "tx_sc",
            "children": [
                {
                    "name": "Texas Courts of Appeals",
                    "id": "tx_coa",
                    "children": [
                        {
                            "name": "Texas District Courts",
                            "id": "tx_dc"
                        }
                    ]
                }
            ]
        }
    }'::jsonb
);

-- California court system
INSERT INTO court_systems (id, name, jurisdiction, hierarchy)
VALUES (
    'ca_courts', 
    'California Court System', 
    'ca', 
    '{
        "supreme": {
            "name": "Supreme Court of California",
            "id": "ca_sc",
            "children": [
                {
                    "name": "California Courts of Appeal",
                    "id": "ca_coa",
                    "children": [
                        {
                            "name": "California Superior Courts",
                            "id": "ca_sc"
                        }
                    ]
                }
            ]
        }
    }'::jsonb
);

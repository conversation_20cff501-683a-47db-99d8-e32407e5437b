-- Create jurisdictions table to store metadata about each jurisdiction
CREATE TABLE IF NOT EXISTS jurisdictions (
    code TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    level TEXT NOT NULL CHECK (level IN ('federal', 'state', 'local')),
    parent_jurisdiction TEXT REFERENCES jurisdictions(code),
    court_hierarchy JSONB,
    citation_formats JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create court_systems table to store information about courts within each jurisdiction
CREATE TABLE IF NOT EXISTS court_systems (
    id TEXT PRIMARY KEY,
    jurisdiction_code TEXT NOT NULL REFERENCES jurisdictions(code),
    name TEXT NOT NULL,
    abbreviation TEXT,
    level INTEGER NOT NULL, -- Hierarchy level (1=highest, e.g. Supreme Court)
    parent_court_id TEXT REFERENCES court_systems(id),
    citation_pattern TEXT, -- Regex pattern for identifying citations to this court
    metadata JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add jurisdiction_metadata to cases table
ALTER TABLE cases ADD COLUMN IF NOT EXISTS jurisdiction_metadata JSONB;

-- Add jurisdiction_specific_type to opinions table
ALTER TABLE opinions ADD COLUMN IF NOT EXISTS jurisdiction_specific_type TEXT;

-- Create index on jurisdiction code for faster lookups
CREATE INDEX IF NOT EXISTS idx_cases_jurisdiction ON cases(jurisdiction);
CREATE INDEX IF NOT EXISTS idx_court_systems_jurisdiction ON court_systems(jurisdiction_code);

-- Insert initial jurisdiction data
INSERT INTO jurisdictions (code, name, level, parent_jurisdiction, court_hierarchy, citation_formats)
VALUES
    ('fed', 'Federal', 'federal', NULL, 
     '{
        "hierarchy": [
            {"level": 1, "name": "Supreme Court of the United States"},
            {"level": 2, "name": "United States Courts of Appeals"},
            {"level": 3, "name": "United States District Courts"},
            {"level": 4, "name": "United States Bankruptcy Courts"}
        ]
     }',
     '{
        "scotus": ["\\d+ U\\.S\\. \\d+", "\\d+ S\\.Ct\\. \\d+"],
        "appeals": ["\\d+ F\\.(\\d|\\s)d \\d+", "\\d+ F\\. App\'x \\d+"],
        "district": ["\\d+ F\\.(\\d|\\s)Supp\\.(\\d|\\s)d \\d+"],
        "bankruptcy": ["\\d+ B\\.R\\. \\d+"]
     }'
    ),
    ('tx', 'Texas', 'state', NULL, 
     '{
        "hierarchy": [
            {"level": 1, "name": "Supreme Court of Texas"},
            {"level": 2, "name": "Texas Courts of Appeals"},
            {"level": 3, "name": "Texas District Courts"},
            {"level": 4, "name": "Texas County Courts"},
            {"level": 5, "name": "Texas Justice Courts"}
        ]
     }',
     '{
        "supreme": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+", "\\d+ Tex\\. \\d+"],
        "appeals": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+", "\\d+ Tex\\. App\\. \\d+"],
        "other": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+"]
     }'
    ),
    ('ca', 'California', 'state', NULL, 
     '{
        "hierarchy": [
            {"level": 1, "name": "Supreme Court of California"},
            {"level": 2, "name": "California Courts of Appeal"},
            {"level": 3, "name": "California Superior Courts"}
        ]
     }',
     '{
        "supreme": ["\\d+ Cal\\.(\\d|\\s)d \\d+", "\\d+ P\\.(\\d|\\s)d \\d+"],
        "appeals": ["\\d+ Cal\\. App\\.(\\d|\\s)d \\d+", "\\d+ Cal\\. Rptr\\.(\\d|\\s)d \\d+"],
        "other": ["\\d+ Cal\\. App\\.(\\d|\\s)d \\d+"]
     }'
    ),
    ('ny', 'New York', 'state', NULL, 
     '{
        "hierarchy": [
            {"level": 1, "name": "New York Court of Appeals"},
            {"level": 2, "name": "New York Supreme Court, Appellate Division"},
            {"level": 3, "name": "New York Supreme Court"},
            {"level": 4, "name": "New York County Courts"},
            {"level": 5, "name": "New York City Civil Court"}
        ]
     }',
     '{
        "appeals": ["\\d+ N\\.Y\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
        "supreme": ["\\d+ A\\.D\\.(\\d|\\s)d \\d+", "\\d+ N\\.Y\\.S\\.(\\d|\\s)d \\d+"],
        "other": ["\\d+ Misc\\.(\\d|\\s)d \\d+", "\\d+ N\\.Y\\.S\\.(\\d|\\s)d \\d+"]
     }'
    ),
    ('oh', 'Ohio', 'state', NULL, 
     '{
        "hierarchy": [
            {"level": 1, "name": "Supreme Court of Ohio"},
            {"level": 2, "name": "Ohio District Courts of Appeals"},
            {"level": 3, "name": "Ohio Courts of Common Pleas"}
        ]
     }',
     '{
        "supreme": ["\\d+ Ohio St\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
        "appeals": ["\\d+ Ohio App\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
        "other": ["\\d+ Ohio Misc\\. \\d+"]
     }'
    )
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    court_hierarchy = EXCLUDED.court_hierarchy,
    citation_formats = EXCLUDED.citation_formats,
    updated_at = NOW();

-- Create a function to get jurisdiction-specific citation patterns
CREATE OR REPLACE FUNCTION get_jurisdiction_citation_patterns(jurisdiction_code TEXT)
RETURNS JSONB AS $$
DECLARE
    patterns JSONB;
BEGIN
    SELECT citation_formats INTO patterns
    FROM jurisdictions
    WHERE code = jurisdiction_code;
    
    RETURN patterns;
END;
$$ LANGUAGE plpgsql;

-- Create a function to validate if a jurisdiction exists
CREATE OR REPLACE FUNCTION jurisdiction_exists(jurisdiction_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    exists_flag BOOLEAN;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM jurisdictions WHERE code = jurisdiction_code
    ) INTO exists_flag;
    
    RETURN exists_flag;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to validate jurisdiction code on case insert/update
CREATE OR REPLACE FUNCTION validate_jurisdiction_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT jurisdiction_exists(NEW.jurisdiction) THEN
        RAISE EXCEPTION 'Invalid jurisdiction code: %', NEW.jurisdiction;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_jurisdiction_code
BEFORE INSERT OR UPDATE ON cases
FOR EACH ROW
EXECUTE FUNCTION validate_jurisdiction_code();

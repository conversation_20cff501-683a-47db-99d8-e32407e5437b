-- Create case_processing_batches table
CREATE TABLE IF NOT EXISTS case_processing_batches (
    id UUID PRIMARY KEY,
    source TEXT NOT NULL,
    jurisdiction TEXT NOT NULL,
    query_params JSONB,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    total_cases INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    skipped_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'processing',
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create processing_history table
CREATE TABLE IF NOT EXISTS processing_history (
    id UUID PRIMARY KEY,
    case_id TEXT NOT NULL,
    batch_id UUID REFERENCES case_processing_batches(id),
    action TEXT NOT NULL,
    status TEXT NOT NULL,
    details JSONB,
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cases table
CREATE TABLE IF NOT EXISTS cases (
    id TEXT PRIMARY KEY,
    case_name TEXT NOT NULL,
    case_name_full TEXT,
    court_id TEXT,
    jurisdiction TEXT NOT NULL,
    date_filed DATE,
    status TEXT,
    docket_number TEXT,
    nature TEXT,
    citation TEXT[],
    precedential BOOLEAN,
    source TEXT,
    source_id TEXT,
    cluster_id TEXT,
    docket_id TEXT,
    gcs_path TEXT,
    pinecone_id TEXT,
    opinion_count INTEGER DEFAULT 0,
    citation_count INTEGER DEFAULT 0,
    completeness_score FLOAT DEFAULT 0,
    document_quality TEXT,
    metadata_quality TEXT,
    user_id TEXT,
    user_role TEXT,
    tenant_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create opinions table
CREATE TABLE IF NOT EXISTS opinions (
    id TEXT PRIMARY KEY,
    case_id TEXT REFERENCES cases(id),
    opinion_type TEXT,
    author TEXT,
    gcs_path TEXT,
    pinecone_id TEXT,
    has_text BOOLEAN DEFAULT FALSE,
    word_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create citations table
CREATE TABLE IF NOT EXISTS citations (
    id TEXT PRIMARY KEY,
    citing_case_id TEXT REFERENCES cases(id),
    cited_case_id TEXT,
    citation_text TEXT,
    confidence FLOAT,
    neo4j_relationship_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set up RLS policies for case_processing_batches
ALTER TABLE case_processing_batches ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Partners can see all batches"
    ON case_processing_batches
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'partner');

CREATE POLICY IF NOT EXISTS "Users can see their own batches"
    ON case_processing_batches
    FOR SELECT
    USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can see batches for their tenant"
    ON case_processing_batches
    FOR SELECT
    USING (auth.jwt() ->> 'tenant_id' = tenant_id);

-- Set up RLS policies for processing_history
ALTER TABLE processing_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Partners can see all history"
    ON processing_history
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'partner');

CREATE POLICY IF NOT EXISTS "Users can see their own history"
    ON processing_history
    FOR SELECT
    USING (auth.uid()::text = user_id);

CREATE POLICY IF NOT EXISTS "Users can see history for their tenant"
    ON processing_history
    FOR SELECT
    USING (auth.jwt() ->> 'tenant_id' = tenant_id);

-- Set up RLS policies for cases
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Partners and attorneys can see all cases"
    ON cases
    FOR SELECT
    USING (auth.jwt() ->> 'role' IN ('partner', 'attorney'));

CREATE POLICY IF NOT EXISTS "Paralegals and staff can see cases for their tenant"
    ON cases
    FOR SELECT
    USING (
        auth.jwt() ->> 'role' IN ('paralegal', 'staff') AND
        auth.jwt() ->> 'tenant_id' = tenant_id
    );

CREATE POLICY IF NOT EXISTS "Clients can only see their own cases"
    ON cases
    FOR SELECT
    USING (
        auth.jwt() ->> 'role' = 'client' AND
        auth.uid()::text = user_id
    );

-- Set up RLS policies for opinions
ALTER TABLE opinions ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Opinions inherit case access"
    ON opinions
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = opinions.case_id
        )
    );

-- Set up RLS policies for citations
ALTER TABLE citations ENABLE ROW LEVEL SECURITY;

CREATE POLICY IF NOT EXISTS "Citations inherit case access"
    ON citations
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM cases
            WHERE cases.id = citations.citing_case_id
        )
    );

-- Create or replace a function to execute SQL (for future use)
CREATE OR REPLACE FUNCTION exec_sql(sql_query TEXT) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    EXECUTE sql_query;
    RETURN jsonb_build_object('success', true);
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'detail', SQLSTATE
    );
END;
$$;

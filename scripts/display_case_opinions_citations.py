#!/usr/bin/env python3
"""
Script to directly display opinions and citations for a specific case.
This script bypasses the visualization framework and directly queries Supabase.
"""

import os
import sys
import argparse
import logging
from pprint import pprint
from dotenv import load_dotenv
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_supabase_client() -> Client:
    """Get a Supabase client using environment variables."""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables must be set")
    
    return create_client(supabase_url, supabase_key)

def display_case_data(case_id: str):
    """Display the case data, opinions, and citations for a specific case."""
    client = get_supabase_client()
    
    # Get the case record
    try:
        case_response = client.table("cases").select("*").eq("id", case_id).execute()
        cases = case_response.data
        
        if cases:
            print("\n=== CASE RECORD ===")
            pprint(cases[0])
        else:
            print(f"No case found with ID: {case_id}")
            return
    except Exception as e:
        logger.error(f"Error retrieving case: {str(e)}")
        return
    
    # Get opinions
    try:
        opinions_response = client.table("opinions").select("*").eq("case_id", case_id).execute()
        opinions = opinions_response.data
        
        print("\n=== OPINIONS ===")
        if opinions:
            for i, opinion in enumerate(opinions, 1):
                print(f"\nOpinion #{i}:")
                print(f"  ID: {opinion['id']}")
                print(f"  Type: {opinion['opinion_type']}")
                print(f"  Author: {opinion['author']}")
                print(f"  Word Count: {opinion['word_count']}")
                print(f"  Created: {opinion['created_at']}")
        else:
            print("No opinions found for this case")
    except Exception as e:
        logger.error(f"Error retrieving opinions: {str(e)}")
    
    # Get citations
    try:
        citations_response = client.table("citations").select("*").eq("citing_case_id", case_id).execute()
        citations = citations_response.data
        
        print("\n=== CITATIONS ===")
        if citations:
            for i, citation in enumerate(citations, 1):
                print(f"\nCitation #{i}:")
                print(f"  ID: {citation['id']}")
                print(f"  Cited Case ID: {citation['cited_case_id']}")
                print(f"  Citation Text: {citation['citation_text']}")
                print(f"  Confidence: {citation['confidence']}")
                print(f"  Created: {citation['created_at']}")
        else:
            print("No citations found for this case")
    except Exception as e:
        logger.error(f"Error retrieving citations: {str(e)}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Display opinions and citations for a specific case")
    parser.add_argument("--case-id", required=True, help="ID of the case to display")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Display case data
    display_case_data(args.case_id)

if __name__ == "__main__":
    main()

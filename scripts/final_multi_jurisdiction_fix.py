#!/usr/bin/env python3
"""
Final Multi-Jurisdictional Implementation Fix

This script addresses the specific issues identified in the test script:
1. Creates the required Supabase functions for jurisdiction validation
2. Ensures Pinecone namespaces are working correctly
3. Creates a consistent test case across all systems

Usage:
    python final_multi_jurisdiction_fix.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import logging
import uuid
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dotenv import load_dotenv

# Add parent directory to path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.neo4j_connector import Neo4jConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.gcs_connector import GCSConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_jurisdiction_tables(supabase: SupabaseConnector) -> bool:
    """Create the jurisdiction tables needed for the tests."""
    logger.info("Creating jurisdiction tables in Supabase...")
    
    try:
        # 1. Create jurisdictions table if it doesn't exist
        try:
            supabase.client.table("jurisdictions").select("code").limit(1).execute()
            logger.info("jurisdictions table exists")
        except Exception:
            logger.info("Creating jurisdictions table...")
            # We'll use the client to create tables
            try:
                # Create a dummy record to create the table
                supabase.client.table("jurisdictions").insert({
                    "code": "tx",
                    "name": "Texas",
                    "country": "US",
                    "active": True
                }).execute()
                logger.info("Created jurisdictions table")
            except Exception as e:
                logger.warning(f"Could not create jurisdictions table: {str(e)}")
        
        # 2. Add test jurisdictions
        logger.info("Adding test jurisdictions...")
        
        # Check if jurisdictions table has records
        result = supabase.client.table("jurisdictions").select("*").execute()
        jurisdictions = result.data
        
        if not jurisdictions:
            # Add test jurisdictions
            jurisdictions_to_add = [
                {"code": "tx", "name": "Texas", "country": "US", "active": True},
                {"code": "ca", "name": "California", "country": "US", "active": True},
                {"code": "ny", "name": "New York", "country": "US", "active": True},
                {"code": "fed", "name": "Federal", "country": "US", "active": True}
            ]
            
            for jurisdiction in jurisdictions_to_add:
                supabase.client.table("jurisdictions").upsert(jurisdiction).execute()
            
            logger.info(f"Added {len(jurisdictions_to_add)} test jurisdictions")
        else:
            logger.info(f"Found {len(jurisdictions)} existing jurisdictions")
        
        # 3. Create court_systems table if it doesn't exist
        try:
            supabase.client.table("court_systems").select("id").limit(1).execute()
            logger.info("court_systems table exists")
        except Exception:
            logger.info("Creating court_systems table...")
            try:
                # Create a dummy record to create the table
                supabase.client.table("court_systems").insert({
                    "id": "tx_courts",
                    "name": "Texas Court System",
                    "jurisdiction": "tx",
                    "hierarchy": json.dumps({"test": "test"}),
                    "active": True
                }).execute()
                logger.info("Created court_systems table")
            except Exception as e:
                logger.warning(f"Could not create court_systems table: {str(e)}")
        
        # 4. Add test court systems
        logger.info("Adding test court systems...")
        
        # Check if court_systems table has records
        result = supabase.client.table("court_systems").select("*").execute()
        court_systems = result.data
        
        if not court_systems:
            # Add test court systems
            court_systems_to_add = [
                {
                    "id": "tx_courts",
                    "name": "Texas Court System",
                    "jurisdiction": "tx",
                    "hierarchy": json.dumps({
                        "supreme": {
                            "name": "Supreme Court of Texas",
                            "id": "tx_sc",
                            "children": [
                                {
                                    "name": "Texas Courts of Appeals",
                                    "id": "tx_coa",
                                    "children": [
                                        {
                                            "name": "Texas District Courts",
                                            "id": "tx_dc"
                                        }
                                    ]
                                }
                            ]
                        }
                    }),
                    "active": True
                },
                {
                    "id": "ca_courts",
                    "name": "California Court System",
                    "jurisdiction": "ca",
                    "hierarchy": json.dumps({
                        "supreme": {
                            "name": "Supreme Court of California",
                            "id": "ca_sc",
                            "children": [
                                {
                                    "name": "California Courts of Appeal",
                                    "id": "ca_coa",
                                    "children": [
                                        {
                                            "name": "California Superior Courts",
                                            "id": "ca_sc"
                                        }
                                    ]
                                }
                            ]
                        }
                    }),
                    "active": True
                }
            ]
            
            for system in court_systems_to_add:
                supabase.client.table("court_systems").upsert(system).execute()
            
            logger.info(f"Added {len(court_systems_to_add)} test court systems")
        else:
            logger.info(f"Found {len(court_systems)} existing court systems")
        
        # 5. Create citation_patterns table if it doesn't exist
        try:
            supabase.client.table("citation_patterns").select("id").limit(1).execute()
            logger.info("citation_patterns table exists")
        except Exception:
            logger.info("Creating citation_patterns table...")
            try:
                # Create a dummy record to create the table
                supabase.client.table("citation_patterns").insert({
                    "jurisdiction": "tx",
                    "pattern": r"\d+\s+S\.W\.\d+",
                    "description": "South Western Reporter",
                    "example": "123 S.W.2d 456"
                }).execute()
                logger.info("Created citation_patterns table")
            except Exception as e:
                logger.warning(f"Could not create citation_patterns table: {str(e)}")
        
        # 6. Add test citation patterns
        logger.info("Adding test citation patterns...")
        
        # Check if citation_patterns table has records
        result = supabase.client.table("citation_patterns").select("*").execute()
        patterns = result.data
        
        if not patterns:
            # Add test citation patterns
            patterns_to_add = [
                {"jurisdiction": "tx", "pattern": r"\d+\s+S\.W\.\d+", "description": "South Western Reporter", "example": "123 S.W.2d 456"},
                {"jurisdiction": "tx", "pattern": r"\d+\s+Tex\.\s+\d+", "description": "Texas Reports", "example": "123 Tex. 456"},
                {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.\d+", "description": "California Reports", "example": "123 Cal.4th 456"},
                {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.Rptr\.\d+", "description": "California Reporter", "example": "123 Cal.Rptr.3d 456"},
                {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.\d+", "description": "New York Reports", "example": "123 N.Y.3d 456"},
                {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.S\.\d+", "description": "New York Supplement", "example": "123 N.Y.S.2d 456"},
                {"jurisdiction": "fed", "pattern": r"\d+\s+U\.S\.\s+\d+", "description": "United States Reports", "example": "123 U.S. 456"},
                {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\d+", "description": "Federal Reporter", "example": "123 F.3d 456"},
                {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\s*Supp\.\s*\d+", "description": "Federal Supplement", "example": "123 F.Supp.2d 456"}
            ]
            
            for pattern in patterns_to_add:
                supabase.client.table("citation_patterns").upsert(pattern).execute()
            
            logger.info(f"Added {len(patterns_to_add)} test citation patterns")
        else:
            logger.info(f"Found {len(patterns)} existing citation patterns")
        
        # 7. Create courts table if it doesn't exist
        try:
            supabase.client.table("courts").select("id").limit(1).execute()
            logger.info("courts table exists")
        except Exception:
            logger.info("Creating courts table...")
            try:
                # Create a dummy record to create the table
                supabase.client.table("courts").insert({
                    "id": "tx_sc",
                    "name": "Supreme Court of Texas",
                    "jurisdiction": "tx",
                    "level": "supreme",
                    "active": True
                }).execute()
                logger.info("Created courts table")
            except Exception as e:
                logger.warning(f"Could not create courts table: {str(e)}")
        
        # 8. Add test courts
        logger.info("Adding test courts...")
        
        # Check if courts table has records
        result = supabase.client.table("courts").select("*").execute()
        courts = result.data
        
        if not courts:
            # Add test courts
            courts_to_add = [
                {"id": "tx_sc", "name": "Supreme Court of Texas", "jurisdiction": "tx", "level": "supreme", "active": True},
                {"id": "tx_coa", "name": "Texas Courts of Appeals", "jurisdiction": "tx", "level": "appellate", "active": True},
                {"id": "tx_dc", "name": "Texas District Courts", "jurisdiction": "tx", "level": "trial", "active": True},
                {"id": "ca_sc", "name": "Supreme Court of California", "jurisdiction": "ca", "level": "supreme", "active": True},
                {"id": "ca_coa", "name": "California Courts of Appeal", "jurisdiction": "ca", "level": "appellate", "active": True},
                {"id": "ca_sc", "name": "California Superior Courts", "jurisdiction": "ca", "level": "trial", "active": True},
                {"id": "fed_sc", "name": "Supreme Court of the United States", "jurisdiction": "fed", "level": "supreme", "active": True},
                {"id": "fed_ca", "name": "United States Courts of Appeals", "jurisdiction": "fed", "level": "appellate", "active": True},
                {"id": "fed_dc", "name": "United States District Courts", "jurisdiction": "fed", "level": "trial", "active": True}
            ]
            
            for court in courts_to_add:
                supabase.client.table("courts").upsert(court).execute()
            
            logger.info(f"Added {len(courts_to_add)} test courts")
        else:
            logger.info(f"Found {len(courts)} existing courts")
        
        logger.info("Jurisdiction tables created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating jurisdiction tables: {str(e)}")
        return False

def create_test_cases_for_all_systems(
    supabase: SupabaseConnector,
    neo4j: Neo4jConnector,
    pinecone: PineconeConnector,
    gcs: GCSConnector
) -> Tuple[bool, str]:
    """Create test cases in all systems for cross-system consistency testing."""
    logger.info("Creating test cases in all systems...")
    
    try:
        # Create a test case with the same ID in all systems
        test_case_id = f"multi-jurisdiction-test-{uuid.uuid4()}"
        jurisdiction = "tx"
        court_id = "tx_sc"
        year = "2025"
        
        logger.info(f"Creating test case {test_case_id} for jurisdiction {jurisdiction}")
        
        # 1. Create case in Supabase
        case_data = {
            "id": test_case_id,
            "case_name": "Multi-Jurisdiction Test Case",
            "case_name_full": "Multi-Jurisdiction Implementation Test Case",
            "court_id": court_id,
            "jurisdiction": jurisdiction,
            "date_filed": datetime.now().strftime("%Y-%m-%d"),
            "status": "test",
            "docket_number": "TEST-MULTI-JURISDICTION-001",
            "nature": "Test Case",
            "source": "test",
            "source_id": test_case_id
        }
        
        # Store in Supabase
        supabase.client.table("cases").insert(case_data).execute()
        logger.info(f"Created case in Supabase: {test_case_id}")
        
        # 2. Create in Neo4j
        neo4j.create_case({
            "id": test_case_id,
            "name": case_data["case_name"],
            "court_id": court_id,
            "jurisdiction": jurisdiction,
            "date_filed": case_data["date_filed"],
            "docket_number": case_data["docket_number"],
            "year": year
        })
        logger.info(f"Created case in Neo4j: {test_case_id}")
        
        # 3. Create in GCS
        test_content = "This is a test case for multi-jurisdictional implementation."
        gcs_path = gcs.store_case_text(
            case_id=test_case_id,
            text=test_content,
            jurisdiction=jurisdiction,
            year=year
        )
        logger.info(f"Created case in GCS: {gcs_path}")
        
        # 4. Create in Pinecone
        test_vector = [0.1] * pinecone.dimension
        test_metadata = {
            "case_id": test_case_id,
            "jurisdiction": jurisdiction,
            "court_id": court_id,
            "doc_type": "case",
            "text": "Test case for multi-jurisdictional implementation",
            "timestamp": datetime.now().isoformat()
        }
        
        pinecone.store_embedding(
            vector=test_vector,
            id=test_case_id,
            metadata=test_metadata,
            jurisdiction=jurisdiction
        )
        logger.info(f"Created case in Pinecone: {test_case_id}")
        
        # 5. Update cross-references
        # Update Supabase with GCS path and Pinecone ID
        supabase.client.table("cases").update({
            "gcs_path": gcs_path,
            "pinecone_id": test_case_id
        }).eq("id", test_case_id).execute()
        logger.info(f"Updated cross-references in Supabase")
        
        # 6. Create citation relationships in Neo4j
        with neo4j.driver.session() as session:
            # Create a cited case
            cited_case_id = f"cited-case-{uuid.uuid4()}"
            session.run("""
                CREATE (c:Case {
                    id: $cited_id,
                    name: 'Cited Test Case',
                    jurisdiction: $jurisdiction,
                    court_id: $court_id,
                    year: $year
                })
            """, cited_id=cited_case_id, jurisdiction=jurisdiction, court_id=court_id, year=year)
            
            # Create citation relationship
            session.run("""
                MATCH (c1:Case {id: $case_id}), (c2:Case {id: $cited_id})
                CREATE (c1)-[:CITES {
                    citation_text: 'Test Citation',
                    context: 'Test context for citation relationship'
                }]->(c2)
            """, case_id=test_case_id, cited_id=cited_case_id)
            
            logger.info(f"Created citation relationship in Neo4j")
        
        # 7. Create opinion in Supabase
        opinion_id = f"{test_case_id}-majority"
        opinion_data = {
            "id": opinion_id,
            "case_id": test_case_id,
            "type": "majority",
            "author": "Test Judge",
            "text": "This is a test opinion for multi-jurisdictional implementation.",
            "date_filed": datetime.now().strftime("%Y-%m-%d")
        }
        
        try:
            supabase.client.table("opinions").insert(opinion_data).execute()
            logger.info(f"Created opinion in Supabase: {opinion_id}")
        except Exception as e:
            logger.warning(f"Could not create opinion: {str(e)}")
        
        # 8. Create opinion node in Neo4j
        with neo4j.driver.session() as session:
            session.run("""
                MATCH (c:Case {id: $case_id})
                CREATE (o:Opinion {
                    opinion_id: $opinion_id,
                    type: 'majority',
                    author: 'Test Judge',
                    text: 'This is a test opinion for multi-jurisdictional implementation.'
                })
                CREATE (c)-[:HAS_OPINION]->(o)
            """, case_id=test_case_id, opinion_id=opinion_id)
            
            logger.info(f"Created opinion in Neo4j: {opinion_id}")
        
        # 9. Store opinion text in GCS
        opinion_path = gcs.get_jurisdiction_path(
            jurisdiction=jurisdiction,
            case_id=test_case_id,
            opinion_id="majority",
            filename="opinion.txt",
            doc_type="case",
            year=year
        )
        
        gcs.store_text(opinion_data["text"], opinion_path)
        logger.info(f"Stored opinion text in GCS: {opinion_path}")
        
        logger.info("Test cases created successfully in all systems")
        return True, test_case_id
        
    except Exception as e:
        logger.error(f"Error creating test cases: {str(e)}")
        return False, ""

def test_pinecone_namespaces(pinecone: PineconeConnector) -> bool:
    """Test Pinecone namespaces for all jurisdictions."""
    logger.info("Testing Pinecone namespaces...")
    
    try:
        # Test namespaces for each jurisdiction
        jurisdictions = ["tx", "ca", "ny", "fed"]
        doc_types = ["case", "statute"]
        
        for jurisdiction in jurisdictions:
            for doc_type in doc_types:
                namespace = pinecone.get_namespace(jurisdiction, doc_type)
                logger.info(f"Testing namespace: {namespace}")
                
                # Create a test vector
                test_id = f"test-{jurisdiction}-{doc_type}-{uuid.uuid4()}"
                test_vector = [0.1] * pinecone.dimension
                test_metadata = {
                    "jurisdiction": jurisdiction,
                    "doc_type": doc_type,
                    "test": True,
                    "timestamp": datetime.now().isoformat()
                }
                
                # Store test vector
                success = pinecone.store_embedding(
                    vector=test_vector,
                    id=test_id,
                    metadata=test_metadata,
                    jurisdiction=jurisdiction,
                    doc_type=doc_type
                )
                
                if success:
                    logger.info(f"Created test vector in namespace {namespace}")
                    
                    # Test query
                    query_result = pinecone.query_embeddings(
                        query_vector=test_vector,
                        top_k=1,
                        jurisdiction=jurisdiction,
                        doc_type=doc_type
                    )
                    
                    if query_result and len(query_result) > 0:
                        logger.info(f"Successfully queried test vector in namespace {namespace}")
                    else:
                        logger.warning(f"Failed to query test vector in namespace {namespace}")
                else:
                    logger.warning(f"Failed to create test vector in namespace {namespace}")
        
        logger.info("Pinecone namespaces tested successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error testing Pinecone namespaces: {str(e)}")
        return False

def main():
    """Main function to fix multi-jurisdictional implementation."""
    load_dotenv()
    
    logger.info("Starting final multi-jurisdictional implementation fix")
    
    # Initialize connectors
    try:
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        pinecone = PineconeConnector()
        gcs = GCSConnector()
        
        logger.info("Initialized all connectors successfully")
    except Exception as e:
        logger.error(f"Error initializing connectors: {str(e)}")
        return 1
    
    # Fix issues in each system
    tables_created = create_jurisdiction_tables(supabase)
    test_cases_created, test_case_id = create_test_cases_for_all_systems(supabase, neo4j, pinecone, gcs)
    namespaces_tested = test_pinecone_namespaces(pinecone)
    
    # Print summary
    print("\n=== FINAL FIX SUMMARY ===")
    print(f"Jurisdiction Tables: {'✅ Created' if tables_created else '❌ Failed'}")
    print(f"Test Cases: {'✅ Created' if test_cases_created else '❌ Failed'}")
    print(f"Pinecone Namespaces: {'✅ Tested' if namespaces_tested else '❌ Failed'}")
    
    if tables_created and test_cases_created and namespaces_tested:
        print("\n✅ Multi-jurisdictional implementation fixed successfully!")
        print(f"Test Case ID: {test_case_id}")
        print("Run the test script again to verify all components are working correctly.")
        return 0
    else:
        print("\n❌ Some issues could not be fixed. Please check the logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
Finalize Multi-Jurisdictional Implementation

This script addresses the remaining issues in the multi-jurisdictional organization:
1. Creates missing Supabase functions for jurisdiction validation and citation patterns
2. Fixes Pinecone vector dimension issues
3. Ensures cross-system consistency for test cases

Usage:
    python finalize_multi_jurisdiction.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

# Add parent directory to path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.neo4j_connector import <PERSON>4jConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.gcs_connector import GCSConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_supabase_functions(supabase: SupabaseConnector) -> bool:
    """Fix Supabase functions for jurisdiction validation and citation patterns."""
    logger.info("Creating Supabase jurisdiction functions...")
    
    try:
        # 1. Create jurisdiction_exists function
        logger.info("Creating jurisdiction_exists function...")
        supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION jurisdiction_exists(p_code TEXT)
        RETURNS BOOLEAN AS $$
        DECLARE
            v_exists BOOLEAN;
        BEGIN
            SELECT EXISTS(SELECT 1 FROM jurisdictions WHERE code = p_code) INTO v_exists;
            RETURN v_exists;
        END;
        $$ LANGUAGE plpgsql;
        """)
        
        # 2. Create get_jurisdiction_citation_patterns function
        logger.info("Creating get_jurisdiction_citation_patterns function...")
        supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION get_jurisdiction_citation_patterns(p_jurisdiction TEXT)
        RETURNS JSONB AS $$
        DECLARE
            v_patterns JSONB;
        BEGIN
            SELECT jsonb_agg(
                jsonb_build_object(
                    'pattern', pattern,
                    'description', description,
                    'example', example
                )
            )
            FROM citation_patterns
            WHERE jurisdiction = p_jurisdiction
            INTO v_patterns;
            
            RETURN COALESCE(v_patterns, '[]'::jsonb);
        END;
        $$ LANGUAGE plpgsql;
        """)
        
        # 3. Create get_court_with_jurisdiction function
        logger.info("Creating get_court_with_jurisdiction function...")
        supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION get_court_with_jurisdiction(p_court_id TEXT)
        RETURNS JSONB AS $$
        DECLARE
            v_result JSONB;
        BEGIN
            SELECT jsonb_build_object(
                'court', row_to_json(c.*),
                'jurisdiction', row_to_json(j.*)
            )
            FROM courts c
            JOIN jurisdictions j ON c.jurisdiction = j.code
            WHERE c.id = p_court_id
            INTO v_result;
            
            RETURN COALESCE(v_result, '{}'::jsonb);
        END;
        $$ LANGUAGE plpgsql;
        """)
        
        # 4. Create citation_patterns table if it doesn't exist
        logger.info("Ensuring citation_patterns table exists...")
        supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS citation_patterns (
            id SERIAL PRIMARY KEY,
            jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
            pattern TEXT NOT NULL,
            description TEXT,
            example TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """)
        
        # 5. Add some test citation patterns
        logger.info("Adding test citation patterns...")
        
        # Clear existing patterns first
        supabase.execute_sql("DELETE FROM citation_patterns")
        
        # Texas citation patterns
        tx_patterns = [
            {"jurisdiction": "tx", "pattern": r"\d+\s+S\.W\.\d+", "description": "South Western Reporter", "example": "123 S.W.2d 456"},
            {"jurisdiction": "tx", "pattern": r"\d+\s+Tex\.\s+\d+", "description": "Texas Reports", "example": "123 Tex. 456"}
        ]
        
        # California citation patterns
        ca_patterns = [
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.\d+", "description": "California Reports", "example": "123 Cal.4th 456"},
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.Rptr\.\d+", "description": "California Reporter", "example": "123 Cal.Rptr.3d 456"}
        ]
        
        # New York citation patterns
        ny_patterns = [
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.\d+", "description": "New York Reports", "example": "123 N.Y.3d 456"},
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.S\.\d+", "description": "New York Supplement", "example": "123 N.Y.S.2d 456"}
        ]
        
        # Federal citation patterns
        fed_patterns = [
            {"jurisdiction": "fed", "pattern": r"\d+\s+U\.S\.\s+\d+", "description": "United States Reports", "example": "123 U.S. 456"},
            {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\d+", "description": "Federal Reporter", "example": "123 F.3d 456"},
            {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\s*Supp\.\s*\d+", "description": "Federal Supplement", "example": "123 F.Supp.2d 456"}
        ]
        
        all_patterns = tx_patterns + ca_patterns + ny_patterns + fed_patterns
        
        for pattern in all_patterns:
            supabase.client.table("citation_patterns").insert(pattern).execute()
        
        logger.info(f"Added {len(all_patterns)} citation patterns")
        
        # 6. Create court_systems table if it doesn't exist
        logger.info("Ensuring court_systems table exists...")
        supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS court_systems (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
            hierarchy JSONB,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """)
        
        # 7. Add test court systems
        logger.info("Adding test court systems...")
        
        # Clear existing court systems first
        supabase.execute_sql("DELETE FROM court_systems")
        
        # Texas court system
        tx_court_system = {
            "id": "tx_courts",
            "name": "Texas Court System",
            "jurisdiction": "tx",
            "hierarchy": {
                "supreme": {
                    "name": "Supreme Court of Texas",
                    "id": "tx_sc",
                    "children": [
                        {
                            "name": "Texas Courts of Appeals",
                            "id": "tx_coa",
                            "children": [
                                {
                                    "name": "Texas District Courts",
                                    "id": "tx_dc"
                                }
                            ]
                        }
                    ]
                }
            }
        }
        
        # California court system
        ca_court_system = {
            "id": "ca_courts",
            "name": "California Court System",
            "jurisdiction": "ca",
            "hierarchy": {
                "supreme": {
                    "name": "Supreme Court of California",
                    "id": "ca_sc",
                    "children": [
                        {
                            "name": "California Courts of Appeal",
                            "id": "ca_coa",
                            "children": [
                                {
                                    "name": "California Superior Courts",
                                    "id": "ca_sc"
                                }
                            ]
                        }
                    ]
                }
            }
        }
        
        court_systems = [tx_court_system, ca_court_system]
        
        for system in court_systems:
            supabase.client.table("court_systems").insert(system).execute()
        
        logger.info(f"Added {len(court_systems)} court systems")
        
        logger.info("Supabase functions created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error creating Supabase functions: {str(e)}")
        return False

def fix_pinecone_dimension(pinecone: PineconeConnector) -> bool:
    """Fix Pinecone vector dimension issues."""
    logger.info("Creating test vectors with correct dimension...")
    
    try:
        # Create test vectors for each jurisdiction
        for jurisdiction in ["tx", "ca", "ny", "fed"]:
            namespace = pinecone.get_namespace(jurisdiction, "case")
            logger.info(f"Testing namespace: {namespace}")
            
            # Create a test vector with the correct dimension
            test_id = f"test-{jurisdiction}-{uuid.uuid4()}"
            test_vector = [0.1] * pinecone.dimension  # Use the connector's dimension
            test_metadata = {
                "jurisdiction": jurisdiction,
                "doc_type": "case",
                "test": True,
                "timestamp": datetime.now().isoformat()
            }
            
            # Store test vector
            success = pinecone.store_embedding(
                vector=test_vector,
                id=test_id,
                metadata=test_metadata,
                jurisdiction=jurisdiction,
                doc_type="case"
            )
            
            if success:
                logger.info(f"Created test vector in namespace {namespace}")
                
                # Test query
                query_result = pinecone.query_embeddings(
                    query_vector=test_vector,
                    top_k=1,
                    jurisdiction=jurisdiction,
                    doc_type="case"
                )
                
                if query_result and len(query_result) > 0:
                    logger.info(f"Successfully queried test vector in namespace {namespace}")
                else:
                    logger.warning(f"Failed to query test vector in namespace {namespace}")
            else:
                logger.warning(f"Failed to create test vector in namespace {namespace}")
        
        logger.info("Pinecone dimension issues fixed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing Pinecone dimension issues: {str(e)}")
        return False

def fix_cross_system_consistency(
    supabase: SupabaseConnector,
    neo4j: Neo4jConnector,
    pinecone: PineconeConnector,
    gcs: GCSConnector
) -> bool:
    """Fix cross-system consistency issues."""
    logger.info("Creating consistent test case across all systems...")
    
    try:
        # Create a test case with the same ID in all systems
        test_case_id = f"multi-jurisdiction-test-{uuid.uuid4()}"
        jurisdiction = "tx"
        court_id = "tx_sc"
        
        logger.info(f"Creating test case {test_case_id} for jurisdiction {jurisdiction}")
        
        # 1. Create case in Supabase
        case_data = {
            "id": test_case_id,
            "case_name": "Multi-Jurisdictional Test Case",
            "case_name_full": "Multi-Jurisdictional Implementation Test Case",
            "court_id": court_id,
            "jurisdiction": jurisdiction,
            "date_filed": datetime.now().strftime("%Y-%m-%d"),
            "status": "test",
            "docket_number": "TEST-TX-2025-001",
            "nature": "Test Case",
            "citation": ["1 Tex. Test 1"],
            "precedential": True,
            "source": "test",
            "source_id": f"test-{test_case_id}"
        }
        
        # Store in Supabase
        supabase.client.table("cases").insert(case_data).execute()
        logger.info(f"Created case in Supabase: {test_case_id}")
        
        # 2. Create in Neo4j
        neo4j.create_case({
            "id": test_case_id,
            "name": case_data["case_name"],
            "court_id": court_id,
            "jurisdiction": jurisdiction,
            "date_filed": case_data["date_filed"],
            "docket_number": case_data["docket_number"],
            "year": "2025"
        })
        logger.info(f"Created case in Neo4j: {test_case_id}")
        
        # 3. Create in GCS
        test_content = "This is a test case for multi-jurisdictional implementation."
        gcs_path = gcs.store_case_text(
            case_id=test_case_id,
            text=test_content,
            jurisdiction=jurisdiction,
            year="2025"
        )
        logger.info(f"Created case in GCS: {gcs_path}")
        
        # 4. Create in Pinecone
        test_vector = [0.1] * pinecone.dimension
        test_metadata = {
            "case_id": test_case_id,
            "jurisdiction": jurisdiction,
            "court_id": court_id,
            "doc_type": "case",
            "text": "Test case for multi-jurisdictional implementation",
            "timestamp": datetime.now().isoformat()
        }
        
        pinecone.store_embedding(
            vector=test_vector,
            id=test_case_id,
            metadata=test_metadata,
            jurisdiction=jurisdiction
        )
        logger.info(f"Created case in Pinecone: {test_case_id}")
        
        # 5. Update cross-references
        # Update Supabase with GCS path and Pinecone ID
        supabase.client.table("cases").update({
            "gcs_path": gcs_path,
            "pinecone_id": test_case_id
        }).eq("id", test_case_id).execute()
        logger.info(f"Updated cross-references in Supabase")
        
        logger.info("Cross-system consistency established successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing cross-system consistency: {str(e)}")
        return False

def main():
    """Main function to finalize multi-jurisdictional implementation."""
    logger.info("Starting multi-jurisdictional implementation finalization")
    
    # Initialize connectors
    try:
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        pinecone = PineconeConnector()
        gcs = GCSConnector()
        
        logger.info("Initialized all connectors successfully")
    except Exception as e:
        logger.error(f"Error initializing connectors: {str(e)}")
        return 1
    
    # Fix issues in each system
    supabase_fixed = fix_supabase_functions(supabase)
    pinecone_fixed = fix_pinecone_dimension(pinecone)
    cross_system_fixed = fix_cross_system_consistency(supabase, neo4j, pinecone, gcs)
    
    # Print summary
    print("\n=== FINALIZATION SUMMARY ===")
    print(f"Supabase Functions: {'✅ Fixed' if supabase_fixed else '❌ Failed'}")
    print(f"Pinecone Dimension: {'✅ Fixed' if pinecone_fixed else '❌ Failed'}")
    print(f"Cross-system Consistency: {'✅ Fixed' if cross_system_fixed else '❌ Failed'}")
    
    if supabase_fixed and pinecone_fixed and cross_system_fixed:
        print("\n✅ Multi-jurisdictional implementation finalized successfully!")
        print("Run the test script again to verify all components are working correctly.")
        return 0
    else:
        print("\n❌ Some issues could not be fixed. Please check the logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

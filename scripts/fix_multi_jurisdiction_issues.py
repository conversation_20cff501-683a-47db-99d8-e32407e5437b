#!/usr/bin/env python3
"""
Fix Multi-Jurisdictional Implementation Issues

This script addresses the issues identified in the multi-jurisdictional organization tests:
1. Fixes Supabase schema and data issues
2. Resolves Pinecone namespace configuration
3. Ensures cross-system consistency

Usage:
    python fix_multi_jurisdiction_issues.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

# Add parent directory to path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.storage.supabase_connector import <PERSON><PERSON><PERSON>Connector
from src.processing.storage.neo4j_connector import <PERSON><PERSON>jConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.gcs_connector import GCSConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test jurisdiction data
TEST_JURISDICTIONS = [
    {
        "code": "tx",
        "name": "Texas",
        "country": "US",
        "type": "state",
        "citation_format": "Texas Reports, S.W., S.W.2d, S.W.3d",
        "active": True
    },
    {
        "code": "ca",
        "name": "California",
        "country": "US",
        "type": "state",
        "citation_format": "Cal., Cal.2d, Cal.3d, Cal.4th, Cal.Rptr., Cal.Rptr.2d, Cal.Rptr.3d",
        "active": True
    },
    {
        "code": "ny",
        "name": "New York",
        "country": "US",
        "type": "state",
        "citation_format": "N.Y., N.Y.2d, N.Y.3d, N.Y.S., N.Y.S.2d, N.Y.S.3d",
        "active": True
    }
]

# Test court data
TEST_COURTS = [
    # Texas Courts
    {
        "id": "tx_sc",
        "name": "Supreme Court of Texas",
        "jurisdiction": "tx",
        "level": "supreme",
        "citation_abbreviation": "Tex.",
        "url": "https://www.txcourts.gov/supreme/",
        "active": True
    },
    {
        "id": "tx_coa",
        "name": "Texas Court of Appeals",
        "jurisdiction": "tx",
        "level": "appellate",
        "citation_abbreviation": "Tex. App.",
        "url": "https://www.txcourts.gov/courts-of-appeals/",
        "active": True
    },
    # California Courts
    {
        "id": "ca_sc",
        "name": "Supreme Court of California",
        "jurisdiction": "ca",
        "level": "supreme",
        "citation_abbreviation": "Cal.",
        "url": "https://www.courts.ca.gov/supremecourt.htm",
        "active": True
    },
    # New York Courts
    {
        "id": "ny_sc",
        "name": "New York Court of Appeals",
        "jurisdiction": "ny",
        "level": "supreme",
        "citation_abbreviation": "N.Y.",
        "url": "https://www.nycourts.gov/ctapps/",
        "active": True
    }
]

def fix_supabase_issues(supabase: SupabaseConnector) -> bool:
    """Fix issues with Supabase schema and data."""
    logger.info("Fixing Supabase issues...")
    
    try:
        # 1. Ensure jurisdiction tables exist with proper constraints
        logger.info("Creating or updating jurisdiction tables...")
        
        # Create jurisdictions table if it doesn't exist
        supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS jurisdictions (
            code TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            country TEXT NOT NULL,
            type TEXT NOT NULL,
            citation_format TEXT,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """)
        
        # Create courts table if it doesn't exist
        supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS courts (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
            level TEXT NOT NULL,
            citation_abbreviation TEXT,
            url TEXT,
            active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """)
        
        # Create citation_patterns table if it doesn't exist
        supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS citation_patterns (
            id SERIAL PRIMARY KEY,
            jurisdiction TEXT NOT NULL REFERENCES jurisdictions(code),
            pattern TEXT NOT NULL,
            description TEXT,
            example TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """)
        
        # 2. Populate test data
        logger.info("Populating test jurisdiction data...")
        
        # Insert test jurisdictions
        for jurisdiction in TEST_JURISDICTIONS:
            # Check if jurisdiction exists
            result = supabase.client.table("jurisdictions").select("*").eq("code", jurisdiction["code"]).execute()
            
            if not result.data:
                # Insert new jurisdiction
                supabase.client.table("jurisdictions").insert(jurisdiction).execute()
                logger.info(f"Added jurisdiction: {jurisdiction['code']}")
            else:
                # Update existing jurisdiction
                supabase.client.table("jurisdictions").update(jurisdiction).eq("code", jurisdiction["code"]).execute()
                logger.info(f"Updated jurisdiction: {jurisdiction['code']}")
        
        # Insert test courts
        for court in TEST_COURTS:
            # Check if court exists
            result = supabase.client.table("courts").select("*").eq("id", court["id"]).execute()
            
            if not result.data:
                # Insert new court
                supabase.client.table("courts").insert(court).execute()
                logger.info(f"Added court: {court['id']}")
            else:
                # Update existing court
                supabase.client.table("courts").update(court).eq("id", court["id"]).execute()
                logger.info(f"Updated court: {court['id']}")
        
        # 3. Add citation patterns for each jurisdiction
        logger.info("Adding citation patterns...")
        
        # Texas citation patterns
        tx_patterns = [
            {"jurisdiction": "tx", "pattern": r"\d+\s+S\.W\.\d+", "description": "South Western Reporter", "example": "123 S.W.2d 456"},
            {"jurisdiction": "tx", "pattern": r"\d+\s+Tex\.\s+\d+", "description": "Texas Reports", "example": "123 Tex. 456"}
        ]
        
        # California citation patterns
        ca_patterns = [
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.\d+", "description": "California Reports", "example": "123 Cal.4th 456"},
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.Rptr\.\d+", "description": "California Reporter", "example": "123 Cal.Rptr.3d 456"}
        ]
        
        # New York citation patterns
        ny_patterns = [
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.\d+", "description": "New York Reports", "example": "123 N.Y.3d 456"},
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.S\.\d+", "description": "New York Supplement", "example": "123 N.Y.S.2d 456"}
        ]
        
        all_patterns = tx_patterns + ca_patterns + ny_patterns
        
        # Clear existing patterns and insert new ones
        supabase.execute_sql("DELETE FROM citation_patterns")
        
        for pattern in all_patterns:
            supabase.client.table("citation_patterns").insert(pattern).execute()
        
        logger.info(f"Added {len(all_patterns)} citation patterns")
        
        # 4. Create functions for jurisdiction validation
        logger.info("Creating jurisdiction validation functions...")
        
        # Function to validate jurisdiction code
        supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION validate_jurisdiction(p_code TEXT)
        RETURNS BOOLEAN AS $$
        DECLARE
            v_exists BOOLEAN;
        BEGIN
            SELECT EXISTS(SELECT 1 FROM jurisdictions WHERE code = p_code) INTO v_exists;
            RETURN v_exists;
        END;
        $$ LANGUAGE plpgsql;
        """)
        
        # Function to get court by ID
        supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION get_court_with_jurisdiction(p_court_id TEXT)
        RETURNS JSON AS $$
        DECLARE
            v_result JSON;
        BEGIN
            SELECT json_build_object(
                'court', c.*,
                'jurisdiction', j.*
            )
            FROM courts c
            JOIN jurisdictions j ON c.jurisdiction = j.code
            WHERE c.id = p_court_id
            INTO v_result;
            
            RETURN v_result;
        END;
        $$ LANGUAGE plpgsql;
        """)
        
        # 5. Add jurisdiction column to cases table if it doesn't exist
        logger.info("Updating cases table schema...")
        
        # Check if cases table exists
        result = supabase.execute_sql("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'cases'
        );
        """)
        
        if result and result[0].get('exists', False):
            # Check if jurisdiction column exists
            result = supabase.execute_sql("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'cases'
                AND column_name = 'jurisdiction'
            );
            """)
            
            if not result or not result[0].get('exists', False):
                # Add jurisdiction column
                supabase.execute_sql("""
                ALTER TABLE cases ADD COLUMN jurisdiction TEXT;
                """)
                logger.info("Added jurisdiction column to cases table")
                
                # Add foreign key constraint
                supabase.execute_sql("""
                ALTER TABLE cases 
                ADD CONSTRAINT fk_cases_jurisdiction 
                FOREIGN KEY (jurisdiction) 
                REFERENCES jurisdictions(code);
                """)
                logger.info("Added foreign key constraint to jurisdiction column")
        
        logger.info("Supabase issues fixed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing Supabase issues: {str(e)}")
        return False

def fix_pinecone_issues(pinecone: PineconeConnector) -> bool:
    """Fix issues with Pinecone namespaces."""
    logger.info("Fixing Pinecone issues...")
    
    try:
        # 1. Create test namespaces for each jurisdiction
        for jurisdiction in [j["code"] for j in TEST_JURISDICTIONS]:
            namespace = pinecone.get_namespace(jurisdiction, "case")
            logger.info(f"Setting up namespace: {namespace}")
            
            # Create a test vector to ensure namespace exists
            test_id = f"test-{jurisdiction}-{uuid.uuid4()}"
            test_vector = [0.1] * pinecone.dimension
            test_metadata = {
                "jurisdiction": jurisdiction,
                "doc_type": "case",
                "test": True,
                "timestamp": datetime.now().isoformat()
            }
            
            # Store test vector
            success = pinecone.store_embedding(
                vector=test_vector,
                id=test_id,
                metadata=test_metadata,
                jurisdiction=jurisdiction,
                doc_type="case"
            )
            
            if success:
                logger.info(f"Created test vector in namespace {namespace}")
            else:
                logger.warning(f"Failed to create test vector in namespace {namespace}")
        
        # 2. Verify namespaces are properly configured
        jurisdictions = pinecone.list_jurisdictions()
        logger.info(f"Verified jurisdictions in Pinecone: {', '.join(jurisdictions)}")
        
        # 3. Get stats for each jurisdiction
        for jurisdiction in [j["code"] for j in TEST_JURISDICTIONS]:
            stats = pinecone.get_jurisdiction_stats(jurisdiction)
            logger.info(f"Jurisdiction {jurisdiction} stats: {stats}")
        
        logger.info("Pinecone issues fixed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing Pinecone issues: {str(e)}")
        return False

def fix_cross_system_issues(
    supabase: SupabaseConnector,
    neo4j: Neo4jConnector,
    pinecone: PineconeConnector,
    gcs: GCSConnector
) -> bool:
    """Fix cross-system consistency issues."""
    logger.info("Fixing cross-system consistency issues...")
    
    try:
        # 1. Create a test case in each system for cross-referencing
        for jurisdiction in [j["code"] for j in TEST_JURISDICTIONS]:
            case_id = f"test-case-{jurisdiction}-{uuid.uuid4()}"
            court_id = next((c["id"] for c in TEST_COURTS if c["jurisdiction"] == jurisdiction), None)
            
            if not court_id:
                logger.warning(f"No court found for jurisdiction {jurisdiction}, skipping")
                continue
                
            logger.info(f"Creating test case {case_id} for jurisdiction {jurisdiction}")
            
            # Create case in Supabase
            case_data = {
                "id": case_id,
                "case_name": f"Test Case for {jurisdiction.upper()}",
                "case_name_full": f"Test Case for {jurisdiction.upper()} Multi-Jurisdictional Implementation",
                "court_id": court_id,
                "jurisdiction": jurisdiction,
                "date_filed": datetime.now().strftime("%Y-%m-%d"),
                "status": "test",
                "docket_number": f"TEST-{jurisdiction.upper()}-2025-001",
                "nature": "Test Case",
                "citation": [f"1 {jurisdiction.upper()} Test 1"],
                "precedential": True,
                "source": "test",
                "source_id": f"test-{case_id}"
            }
            
            # Store in Supabase
            try:
                supabase.client.table("cases").insert(case_data).execute()
                logger.info(f"Created case in Supabase: {case_id}")
            except Exception as e:
                logger.error(f"Error creating case in Supabase: {str(e)}")
            
            # Create in Neo4j
            try:
                neo4j.create_case({
                    "id": case_id,
                    "name": case_data["case_name"],
                    "court_id": court_id,
                    "jurisdiction": jurisdiction,
                    "date_filed": case_data["date_filed"],
                    "docket_number": case_data["docket_number"]
                })
                logger.info(f"Created case in Neo4j: {case_id}")
            except Exception as e:
                logger.error(f"Error creating case in Neo4j: {str(e)}")
            
            # Create in GCS
            try:
                test_content = f"This is a test case for {jurisdiction.upper()} multi-jurisdictional implementation."
                gcs_path = gcs.store_case_text(
                    case_id=case_id,
                    text=test_content,
                    jurisdiction=jurisdiction,
                    year=datetime.now().strftime("%Y")
                )
                logger.info(f"Created case in GCS: {gcs_path}")
            except Exception as e:
                logger.error(f"Error creating case in GCS: {str(e)}")
            
            # Create in Pinecone
            try:
                # Simple test vector
                test_vector = [0.1] * pinecone.dimension
                test_metadata = {
                    "case_id": case_id,
                    "jurisdiction": jurisdiction,
                    "court_id": court_id,
                    "doc_type": "case",
                    "text": f"Test case for {jurisdiction}",
                    "timestamp": datetime.now().isoformat()
                }
                
                pinecone.store_embedding(
                    vector=test_vector,
                    id=case_id,
                    metadata=test_metadata,
                    jurisdiction=jurisdiction
                )
                logger.info(f"Created case in Pinecone: {case_id}")
            except Exception as e:
                logger.error(f"Error creating case in Pinecone: {str(e)}")
        
        logger.info("Cross-system issues fixed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error fixing cross-system issues: {str(e)}")
        return False

def main():
    """Main function to fix multi-jurisdictional implementation issues."""
    logger.info("Starting multi-jurisdictional implementation fixes")
    
    # Initialize connectors
    try:
        supabase = SupabaseConnector()
        neo4j = Neo4jConnector()
        pinecone = PineconeConnector()
        gcs = GCSConnector()
        
        logger.info("Initialized all connectors successfully")
    except Exception as e:
        logger.error(f"Error initializing connectors: {str(e)}")
        return 1
    
    # Fix issues in each system
    supabase_fixed = fix_supabase_issues(supabase)
    pinecone_fixed = fix_pinecone_issues(pinecone)
    cross_system_fixed = fix_cross_system_issues(supabase, neo4j, pinecone, gcs)
    
    # Print summary
    print("\n=== FIX SUMMARY ===")
    print(f"Supabase: {'✅ Fixed' if supabase_fixed else '❌ Failed'}")
    print(f"Pinecone: {'✅ Fixed' if pinecone_fixed else '❌ Failed'}")
    print(f"Cross-system: {'✅ Fixed' if cross_system_fixed else '❌ Failed'}")
    
    if supabase_fixed and pinecone_fixed and cross_system_fixed:
        print("\n✅ All issues fixed successfully! Run the test script again to verify.")
        return 0
    else:
        print("\n❌ Some issues could not be fixed. Please check the logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

-- Drop existing RLS policies for case_processing_batches
DROP POLICY IF EXISTS "Partners can see all batches" ON case_processing_batches;
DROP POLICY IF EXISTS "Users can see their own batches" ON case_processing_batches;
DROP POLICY IF EXISTS "Users can see batches for their tenant" ON case_processing_batches;

-- Create more permissive policies for testing
CREATE POLICY "Allow all operations on case_processing_batches"
    ON case_processing_batches
    FOR ALL
    USING (true)
    WITH CHECK (true);

-- Drop existing RLS policies for processing_history
DROP POLICY IF EXISTS "Partners can see all history" ON processing_history;
DROP POLICY IF EXISTS "Users can see their own history" ON processing_history;
DROP POLICY IF EXISTS "Users can see history for their tenant" ON processing_history;

-- Create more permissive policies for testing
CREATE POLICY "Allow all operations on processing_history"
    ON processing_history
    FOR ALL
    USING (true)
    WITH CHECK (true);

-- Drop existing RLS policies for cases
DROP POLICY IF EXISTS "Partners and attorneys can see all cases" ON cases;
DROP POLICY IF EXISTS "Paralegals and staff can see cases for their tenant" ON cases;
DROP POLICY IF EXISTS "Clients can only see their own cases" ON cases;

-- Create more permissive policies for testing
CREATE POLICY "Allow all operations on cases"
    ON cases
    FOR ALL
    USING (true)
    WITH CHECK (true);

-- Drop existing RLS policies for opinions
DROP POLICY IF EXISTS "Opinions inherit case access" ON opinions;

-- Create more permissive policies for testing
CREATE POLICY "Allow all operations on opinions"
    ON opinions
    FOR ALL
    USING (true)
    WITH CHECK (true);

-- Drop existing RLS policies for citations
DROP POLICY IF EXISTS "Citations inherit case access" ON citations;

-- Create more permissive policies for testing
CREATE POLICY "Allow all operations on citations"
    ON citations
    FOR ALL
    USING (true)
    WITH CHECK (true);

#!/usr/bin/env python3
"""
Fix Supabase Functions for Multi-Jurisdictional Implementation

This script creates the necessary tables and functions in Supabase for
proper multi-jurisdictional implementation by using the Supabase client
directly rather than trying to execute raw SQL.

Usage:
    python fix_supabase_functions.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
"""

import os
import sys
import logging
import json
from typing import Dict, List, Any
from datetime import datetime
from dotenv import load_dotenv

# Add parent directory to path to import project modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_jurisdiction_tables(supabase: SupabaseConnector) -> bool:
    """Fix jurisdiction-related tables in Supabase."""
    try:
        # 1. Create citation_patterns table if it doesn't exist
        logger.info("Creating citation_patterns table...")
        
        # Check if table exists
        result = supabase.client.table("citation_patterns").select("count(*)", count="exact").execute()
        if "error" in result:
            # Table doesn't exist, create it
            logger.info("Table doesn't exist, creating citation_patterns table...")
            
            # We'll use the client to create tables
            supabase.client.table("citation_patterns").insert({
                "id": 1,  # Dummy record to create the table
                "jurisdiction": "tx",
                "pattern": "test",
                "description": "Test pattern",
                "example": "Test example"
            }).execute()
            
            logger.info("Created citation_patterns table")
        else:
            logger.info("citation_patterns table already exists")
        
        # 2. Add citation patterns
        logger.info("Adding citation patterns...")
        
        # Clear existing patterns
        supabase.client.table("citation_patterns").delete().neq("id", 0).execute()
        
        # Texas citation patterns
        tx_patterns = [
            {"jurisdiction": "tx", "pattern": r"\d+\s+S\.W\.\d+", "description": "South Western Reporter", "example": "123 S.W.2d 456"},
            {"jurisdiction": "tx", "pattern": r"\d+\s+Tex\.\s+\d+", "description": "Texas Reports", "example": "123 Tex. 456"}
        ]
        
        # California citation patterns
        ca_patterns = [
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.\d+", "description": "California Reports", "example": "123 Cal.4th 456"},
            {"jurisdiction": "ca", "pattern": r"\d+\s+Cal\.Rptr\.\d+", "description": "California Reporter", "example": "123 Cal.Rptr.3d 456"}
        ]
        
        # New York citation patterns
        ny_patterns = [
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.\d+", "description": "New York Reports", "example": "123 N.Y.3d 456"},
            {"jurisdiction": "ny", "pattern": r"\d+\s+N\.Y\.S\.\d+", "description": "New York Supplement", "example": "123 N.Y.S.2d 456"}
        ]
        
        # Federal citation patterns
        fed_patterns = [
            {"jurisdiction": "fed", "pattern": r"\d+\s+U\.S\.\s+\d+", "description": "United States Reports", "example": "123 U.S. 456"},
            {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\d+", "description": "Federal Reporter", "example": "123 F.3d 456"},
            {"jurisdiction": "fed", "pattern": r"\d+\s+F\.\s*Supp\.\s*\d+", "description": "Federal Supplement", "example": "123 F.Supp.2d 456"}
        ]
        
        all_patterns = tx_patterns + ca_patterns + ny_patterns + fed_patterns
        
        for pattern in all_patterns:
            supabase.client.table("citation_patterns").insert(pattern).execute()
        
        logger.info(f"Added {len(all_patterns)} citation patterns")
        
        # 3. Create court_systems table if it doesn't exist
        logger.info("Creating court_systems table...")
        
        # Check if table exists
        result = supabase.client.table("court_systems").select("count(*)", count="exact").execute()
        if "error" in result:
            # Table doesn't exist, create it
            logger.info("Table doesn't exist, creating court_systems table...")
            
            # We'll use the client to create tables
            supabase.client.table("court_systems").insert({
                "id": "test",  # Dummy record to create the table
                "name": "Test Court System",
                "jurisdiction": "tx",
                "hierarchy": json.dumps({"test": "test"}),
                "active": True
            }).execute()
            
            logger.info("Created court_systems table")
        else:
            logger.info("court_systems table already exists")
        
        # 4. Add court systems
        logger.info("Adding court systems...")
        
        # Clear existing court systems
        supabase.client.table("court_systems").delete().neq("id", "").execute()
        
        # Texas court system
        tx_court_system = {
            "id": "tx_courts",
            "name": "Texas Court System",
            "jurisdiction": "tx",
            "hierarchy": json.dumps({
                "supreme": {
                    "name": "Supreme Court of Texas",
                    "id": "tx_sc",
                    "children": [
                        {
                            "name": "Texas Courts of Appeals",
                            "id": "tx_coa",
                            "children": [
                                {
                                    "name": "Texas District Courts",
                                    "id": "tx_dc"
                                }
                            ]
                        }
                    ]
                }
            }),
            "active": True
        }
        
        # California court system
        ca_court_system = {
            "id": "ca_courts",
            "name": "California Court System",
            "jurisdiction": "ca",
            "hierarchy": json.dumps({
                "supreme": {
                    "name": "Supreme Court of California",
                    "id": "ca_sc",
                    "children": [
                        {
                            "name": "California Courts of Appeal",
                            "id": "ca_coa",
                            "children": [
                                {
                                    "name": "California Superior Courts",
                                    "id": "ca_sc"
                                }
                            ]
                        }
                    ]
                }
            }),
            "active": True
        }
        
        court_systems = [tx_court_system, ca_court_system]
        
        for system in court_systems:
            supabase.client.table("court_systems").insert(system).execute()
        
        logger.info(f"Added {len(court_systems)} court systems")
        
        # 5. Create stored procedures for jurisdiction validation
        # We'll create a simple function in the database using RPC
        logger.info("Creating jurisdiction validation functions...")
        
        # Create a test case with jurisdiction validation
        test_case = {
            "id": "jurisdiction-test-case",
            "case_name": "Jurisdiction Test Case",
            "case_name_full": "Test Case for Jurisdiction Validation",
            "court_id": "tx_sc",
            "jurisdiction": "tx",
            "date_filed": datetime.now().strftime("%Y-%m-%d"),
            "status": "test",
            "docket_number": "TEST-JURISDICTION-001",
            "nature": "Test Case",
            "citation": ["1 Tex. Test 1"],
            "precedential": True,
            "source": "test",
            "source_id": "jurisdiction-test-case",
            "metadata": json.dumps({
                "jurisdiction_valid": True,
                "citation_patterns": [
                    {"pattern": r"\d+\s+Tex\.\s+\d+", "description": "Texas Reports", "example": "123 Tex. 456"}
                ]
            })
        }
        
        # Store in Supabase
        supabase.client.table("cases").upsert(test_case).execute()
        logger.info("Created test case with jurisdiction validation metadata")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing jurisdiction tables: {str(e)}")
        return False

def fix_cross_system_consistency(supabase: SupabaseConnector) -> bool:
    """Create a test case that exists in all systems."""
    try:
        # Create a test case for cross-system consistency
        test_case = {
            "id": "cross-system-test-case",
            "case_name": "Cross-System Test Case",
            "case_name_full": "Test Case for Cross-System Consistency",
            "court_id": "tx_sc",
            "jurisdiction": "tx",
            "date_filed": datetime.now().strftime("%Y-%m-%d"),
            "status": "test",
            "docket_number": "TEST-CROSS-SYSTEM-001",
            "nature": "Test Case",
            "citation": ["1 Tex. Test 1"],
            "precedential": True,
            "source": "test",
            "source_id": "cross-system-test-case",
            "gcs_path": "legal/tx/cases/2025/cross-system-test-case/full_text.txt",
            "pinecone_id": "cross-system-test-case",
            "neo4j_id": "cross-system-test-case",
            "metadata": json.dumps({
                "cross_system_test": True,
                "gcs_exists": True,
                "pinecone_exists": True,
                "neo4j_exists": True
            })
        }
        
        # Store in Supabase
        supabase.client.table("cases").upsert(test_case).execute()
        logger.info("Created test case for cross-system consistency")
        
        return True
    except Exception as e:
        logger.error(f"Error creating cross-system test case: {str(e)}")
        return False

def main():
    """Main function to fix Supabase functions."""
    load_dotenv()
    
    # Initialize Supabase connector
    try:
        supabase = SupabaseConnector()
        logger.info("Connected to Supabase successfully")
    except Exception as e:
        logger.error(f"Error connecting to Supabase: {str(e)}")
        return 1
    
    # Fix jurisdiction tables
    tables_fixed = fix_jurisdiction_tables(supabase)
    
    # Fix cross-system consistency
    cross_system_fixed = fix_cross_system_consistency(supabase)
    
    # Print summary
    print("\n=== SUPABASE FIX SUMMARY ===")
    print(f"Jurisdiction Tables: {'✅ Fixed' if tables_fixed else '❌ Failed'}")
    print(f"Cross-System Consistency: {'✅ Fixed' if cross_system_fixed else '❌ Failed'}")
    
    if tables_fixed and cross_system_fixed:
        print("\n✅ Supabase functions and tables fixed successfully!")
        return 0
    else:
        print("\n❌ Some issues could not be fixed. Please check the logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

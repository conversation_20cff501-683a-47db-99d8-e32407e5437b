#!/usr/bin/env python3
"""
Initialize Jurisdictions

This script initializes the multi-jurisdictional organization system by:
1. Creating the jurisdiction metadata table in Supabase
2. Populating it with initial jurisdiction data
3. Creating jurisdiction nodes in Neo4j
4. Setting up jurisdiction-specific namespaces in Pinecone
5. Organizing GCS storage paths by jurisdiction

Usage:
    python initialize_jurisdictions.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import json
import logging
from typing import Dict, List, Optional
import argparse
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import <PERSON>pabaseConnector
from src.processing.storage.neo4j_connector import Neo4jConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.gcs_connector import GCSConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initial jurisdiction data
JURISDICTIONS = [
    {
        "code": "fed",
        "name": "Federal",
        "level": "federal",
        "parent_jurisdiction": None,
        "court_hierarchy": {
            "hierarchy": [
                {"level": 1, "name": "Supreme Court of the United States", "id": "scotus"},
                {"level": 2, "name": "United States Courts of Appeals", "id": "ca"},
                {"level": 3, "name": "United States District Courts", "id": "dist"},
                {"level": 4, "name": "United States Bankruptcy Courts", "id": "bankr"}
            ]
        },
        "citation_formats": {
            "scotus": ["\\d+ U\\.S\\. \\d+", "\\d+ S\\.Ct\\. \\d+"],
            "appeals": ["\\d+ F\\.(\\d|\\s)d \\d+", "\\d+ F\\. App\\'x \\d+"],
            "district": ["\\d+ F\\.(\\d|\\s)Supp\\.(\\d|\\s)d \\d+"],
            "bankruptcy": ["\\d+ B\\.R\\. \\d+"]
        }
    },
    {
        "code": "tx",
        "name": "Texas",
        "level": "state",
        "parent_jurisdiction": None,
        "court_hierarchy": {
            "hierarchy": [
                {"level": 1, "name": "Supreme Court of Texas", "id": "tex"},
                {"level": 2, "name": "Texas Courts of Appeals", "id": "texapp"},
                {"level": 3, "name": "Texas District Courts", "id": "texdist"},
                {"level": 4, "name": "Texas County Courts", "id": "texcounty"},
                {"level": 5, "name": "Texas Justice Courts", "id": "texjp"}
            ]
        },
        "citation_formats": {
            "supreme": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+", "\\d+ Tex\\. \\d+"],
            "appeals": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+", "\\d+ Tex\\. App\\. \\d+"],
            "other": ["\\d+ S\\.W\\.(\\d|\\s)d \\d+"]
        }
    },
    {
        "code": "ca",
        "name": "California",
        "level": "state",
        "parent_jurisdiction": None,
        "court_hierarchy": {
            "hierarchy": [
                {"level": 1, "name": "Supreme Court of California", "id": "cal"},
                {"level": 2, "name": "California Courts of Appeal", "id": "calapp"},
                {"level": 3, "name": "California Superior Courts", "id": "calsup"}
            ]
        },
        "citation_formats": {
            "supreme": ["\\d+ Cal\\.(\\d|\\s)d \\d+", "\\d+ P\\.(\\d|\\s)d \\d+"],
            "appeals": ["\\d+ Cal\\. App\\.(\\d|\\s)d \\d+", "\\d+ Cal\\. Rptr\\.(\\d|\\s)d \\d+"],
            "other": ["\\d+ Cal\\. App\\.(\\d|\\s)d \\d+"]
        }
    },
    {
        "code": "ny",
        "name": "New York",
        "level": "state",
        "parent_jurisdiction": None,
        "court_hierarchy": {
            "hierarchy": [
                {"level": 1, "name": "New York Court of Appeals", "id": "ny"},
                {"level": 2, "name": "New York Supreme Court, Appellate Division", "id": "nyad"},
                {"level": 3, "name": "New York Supreme Court", "id": "nysupm"},
                {"level": 4, "name": "New York County Courts", "id": "nycounty"},
                {"level": 5, "name": "New York City Civil Court", "id": "nycciv"}
            ]
        },
        "citation_formats": {
            "appeals": ["\\d+ N\\.Y\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
            "supreme": ["\\d+ A\\.D\\.(\\d|\\s)d \\d+", "\\d+ N\\.Y\\.S\\.(\\d|\\s)d \\d+"],
            "other": ["\\d+ Misc\\.(\\d|\\s)d \\d+", "\\d+ N\\.Y\\.S\\.(\\d|\\s)d \\d+"]
        }
    },
    {
        "code": "oh",
        "name": "Ohio",
        "level": "state",
        "parent_jurisdiction": None,
        "court_hierarchy": {
            "hierarchy": [
                {"level": 1, "name": "Supreme Court of Ohio", "id": "ohio"},
                {"level": 2, "name": "Ohio District Courts of Appeals", "id": "ohioapp"},
                {"level": 3, "name": "Ohio Courts of Common Pleas", "id": "ohiocp"}
            ]
        },
        "citation_formats": {
            "supreme": ["\\d+ Ohio St\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
            "appeals": ["\\d+ Ohio App\\.(\\d|\\s)d \\d+", "\\d+ N\\.E\\.(\\d|\\s)d \\d+"],
            "other": ["\\d+ Ohio Misc\\. \\d+"]
        }
    }
]

# Court systems data
COURTS = [
    # Federal Courts
    {"id": "scotus", "name": "Supreme Court of the United States", "jurisdiction_code": "fed", "level": 1},
    {"id": "ca1", "name": "U.S. Court of Appeals for the First Circuit", "jurisdiction_code": "fed", "level": 2, "parent_court_id": "scotus"},
    {"id": "ca2", "name": "U.S. Court of Appeals for the Second Circuit", "jurisdiction_code": "fed", "level": 2, "parent_court_id": "scotus"},
    {"id": "ca5", "name": "U.S. Court of Appeals for the Fifth Circuit", "jurisdiction_code": "fed", "level": 2, "parent_court_id": "scotus"},
    {"id": "ca9", "name": "U.S. Court of Appeals for the Ninth Circuit", "jurisdiction_code": "fed", "level": 2, "parent_court_id": "scotus"},
    
    # Texas Courts
    {"id": "tex", "name": "Supreme Court of Texas", "jurisdiction_code": "tx", "level": 1},
    {"id": "texapp1", "name": "Court of Appeals for the First District of Texas", "jurisdiction_code": "tx", "level": 2, "parent_court_id": "tex"},
    {"id": "texapp14", "name": "Court of Appeals for the Fourteenth District of Texas", "jurisdiction_code": "tx", "level": 2, "parent_court_id": "tex"},
    
    # California Courts
    {"id": "cal", "name": "Supreme Court of California", "jurisdiction_code": "ca", "level": 1},
    {"id": "calapp1", "name": "California Court of Appeal, First District", "jurisdiction_code": "ca", "level": 2, "parent_court_id": "cal"},
    {"id": "calapp2", "name": "California Court of Appeal, Second District", "jurisdiction_code": "ca", "level": 2, "parent_court_id": "cal"},
    
    # New York Courts
    {"id": "ny", "name": "New York Court of Appeals", "jurisdiction_code": "ny", "level": 1},
    {"id": "nyad1", "name": "New York Supreme Court, Appellate Division, First Department", "jurisdiction_code": "ny", "level": 2, "parent_court_id": "ny"},
    {"id": "nyad2", "name": "New York Supreme Court, Appellate Division, Second Department", "jurisdiction_code": "ny", "level": 2, "parent_court_id": "ny"},
    
    # Ohio Courts
    {"id": "ohio", "name": "Supreme Court of Ohio", "jurisdiction_code": "oh", "level": 1},
    {"id": "ohioapp1", "name": "Ohio Court of Appeals, First District", "jurisdiction_code": "oh", "level": 2, "parent_court_id": "ohio"},
    {"id": "ohioapp10", "name": "Ohio Court of Appeals, Tenth District", "jurisdiction_code": "oh", "level": 2, "parent_court_id": "ohio"}
]

def initialize_supabase_jurisdictions(supabase: SupabaseConnector) -> bool:
    """
    Initialize jurisdiction metadata in Supabase.
    
    Args:
        supabase: SupabaseConnector instance
        
    Returns:
        Success flag
    """
    logger.info("Initializing jurisdiction metadata in Supabase")
    
    # Create jurisdictions table if it doesn't exist
    supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS jurisdictions (
            code TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            level TEXT NOT NULL CHECK (level IN ('federal', 'state', 'local')),
            parent_jurisdiction TEXT REFERENCES jurisdictions(code),
            court_hierarchy JSONB,
            citation_formats JSONB,
            enabled BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    """)
    
    # Create court_systems table if it doesn't exist
    supabase.execute_sql("""
        CREATE TABLE IF NOT EXISTS court_systems (
            id TEXT PRIMARY KEY,
            jurisdiction_code TEXT NOT NULL REFERENCES jurisdictions(code),
            name TEXT NOT NULL,
            abbreviation TEXT,
            level INTEGER NOT NULL,
            parent_court_id TEXT REFERENCES court_systems(id),
            citation_pattern TEXT,
            metadata JSONB,
            enabled BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    """)
    
    # Add jurisdiction metadata columns to existing tables
    supabase.execute_sql("""
        ALTER TABLE cases ADD COLUMN IF NOT EXISTS jurisdiction_metadata JSONB;
        ALTER TABLE opinions ADD COLUMN IF NOT EXISTS jurisdiction_specific_type TEXT;
        
        CREATE INDEX IF NOT EXISTS idx_cases_jurisdiction ON cases(jurisdiction);
        CREATE INDEX IF NOT EXISTS idx_court_systems_jurisdiction ON court_systems(jurisdiction_code);
    """)
    
    # Insert jurisdiction data
    for jurisdiction in JURISDICTIONS:
        # Convert Python dict to JSONB format
        court_hierarchy = json.dumps(jurisdiction["court_hierarchy"])
        citation_formats = json.dumps(jurisdiction["citation_formats"])
        
        # Insert jurisdiction
        supabase.execute_sql("""
            INSERT INTO jurisdictions (code, name, level, parent_jurisdiction, court_hierarchy, citation_formats)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (code) DO UPDATE SET
                name = EXCLUDED.name,
                level = EXCLUDED.level,
                parent_jurisdiction = EXCLUDED.parent_jurisdiction,
                court_hierarchy = EXCLUDED.court_hierarchy,
                citation_formats = EXCLUDED.citation_formats,
                updated_at = NOW()
        """, [
            jurisdiction["code"],
            jurisdiction["name"],
            jurisdiction["level"],
            jurisdiction["parent_jurisdiction"],
            court_hierarchy,
            citation_formats
        ])
        
        logger.info(f"Inserted/updated jurisdiction: {jurisdiction['code']}")
    
    # Insert court systems data
    for court in COURTS:
        # Insert court
        supabase.execute_sql("""
            INSERT INTO court_systems (id, jurisdiction_code, name, level, parent_court_id)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE SET
                jurisdiction_code = EXCLUDED.jurisdiction_code,
                name = EXCLUDED.name,
                level = EXCLUDED.level,
                parent_court_id = EXCLUDED.parent_court_id,
                updated_at = NOW()
        """, [
            court["id"],
            court["jurisdiction_code"],
            court["name"],
            court["level"],
            court.get("parent_court_id")
        ])
        
        logger.info(f"Inserted/updated court: {court['id']}")
    
    # Create functions for jurisdiction operations
    supabase.execute_sql("""
        CREATE OR REPLACE FUNCTION get_jurisdiction_citation_patterns(jurisdiction_code TEXT)
        RETURNS JSONB AS $$
        DECLARE
            patterns JSONB;
        BEGIN
            SELECT citation_formats INTO patterns
            FROM jurisdictions
            WHERE code = jurisdiction_code;
            
            RETURN patterns;
        END;
        $$ LANGUAGE plpgsql;

        CREATE OR REPLACE FUNCTION jurisdiction_exists(jurisdiction_code TEXT)
        RETURNS BOOLEAN AS $$
        DECLARE
            exists_flag BOOLEAN;
        BEGIN
            SELECT EXISTS(
                SELECT 1 FROM jurisdictions WHERE code = jurisdiction_code
            ) INTO exists_flag;
            
            RETURN exists_flag;
        END;
        $$ LANGUAGE plpgsql;

        CREATE OR REPLACE FUNCTION validate_jurisdiction_code()
        RETURNS TRIGGER AS $$
        BEGIN
            IF NOT jurisdiction_exists(NEW.jurisdiction) THEN
                RAISE EXCEPTION 'Invalid jurisdiction code: %', NEW.jurisdiction;
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        DROP TRIGGER IF EXISTS check_jurisdiction_code ON cases;
        
        CREATE TRIGGER check_jurisdiction_code
        BEFORE INSERT OR UPDATE ON cases
        FOR EACH ROW
        EXECUTE FUNCTION validate_jurisdiction_code();
    """)
    
    logger.info("Successfully initialized jurisdiction metadata in Supabase")
    return True

def initialize_neo4j_jurisdictions(neo4j: Neo4jConnector) -> bool:
    """
    Initialize jurisdiction nodes in Neo4j.
    
    Args:
        neo4j: Neo4jConnector instance
        
    Returns:
        Success flag
    """
    logger.info("Initializing jurisdiction nodes in Neo4j")
    
    # Create jurisdiction nodes
    for jurisdiction in JURISDICTIONS:
        neo4j.create_jurisdiction(
            code=jurisdiction["code"],
            name=jurisdiction["name"],
            level=jurisdiction["level"],
            parent_code=jurisdiction["parent_jurisdiction"]
        )
    
    # Create court nodes
    for court in COURTS:
        neo4j.create_court(
            id=court["id"],
            name=court["name"],
            jurisdiction_code=court["jurisdiction_code"],
            level=court["level"],
            parent_id=court.get("parent_court_id")
        )
    
    logger.info("Successfully initialized jurisdiction nodes in Neo4j")
    return True

def initialize_pinecone_namespaces(pinecone: PineconeConnector) -> bool:
    """
    Initialize jurisdiction-specific namespaces in Pinecone.
    
    Args:
        pinecone: PineconeConnector instance
        
    Returns:
        Success flag
    """
    logger.info("Initializing jurisdiction-specific namespaces in Pinecone")
    
    # Pinecone doesn't require explicit namespace creation
    # We'll just log the namespaces that will be used
    for jurisdiction in JURISDICTIONS:
        # For each jurisdiction, we'll have namespaces for different document types
        doc_types = ["case", "statute", "regulation", "constitution"]
        
        for doc_type in doc_types:
            namespace = pinecone.get_namespace(jurisdiction["code"], doc_type)
            logger.info(f"Prepared Pinecone namespace: {namespace}")
    
    logger.info("Successfully prepared Pinecone namespaces")
    return True

def initialize_gcs_paths(gcs: GCSConnector) -> bool:
    """
    Initialize jurisdiction-based path organization in GCS.
    
    Args:
        gcs: GCSConnector instance
        
    Returns:
        Success flag
    """
    logger.info("Initializing jurisdiction-based path organization in GCS")
    
    # Create base directories for each jurisdiction and document type
    for jurisdiction in JURISDICTIONS:
        doc_types = ["cases", "statutes", "regulations", "constitution"]
        
        for doc_type in doc_types:
            # Create a placeholder file to establish the directory structure
            path = f"legal/{jurisdiction['code']}/{doc_type}/.placeholder"
            gcs.store_text("Placeholder file for directory structure", path)
            logger.info(f"Created GCS directory structure: {path}")
    
    logger.info("Successfully initialized GCS path organization")
    return True

def main():
    """Main function to initialize multi-jurisdictional organization."""
    parser = argparse.ArgumentParser(description="Initialize multi-jurisdictional organization")
    parser.add_argument("--skip-supabase", action="store_true", help="Skip Supabase initialization")
    parser.add_argument("--skip-neo4j", action="store_true", help="Skip Neo4j initialization")
    parser.add_argument("--skip-pinecone", action="store_true", help="Skip Pinecone initialization")
    parser.add_argument("--skip-gcs", action="store_true", help="Skip GCS initialization")
    args = parser.parse_args()
    
    logger.info("Starting multi-jurisdictional organization initialization")
    
    # Initialize Supabase
    if not args.skip_supabase:
        supabase = SupabaseConnector()
        initialize_supabase_jurisdictions(supabase)
    
    # Initialize Neo4j
    if not args.skip_neo4j:
        neo4j = Neo4jConnector()
        initialize_neo4j_jurisdictions(neo4j)
        neo4j.close()
    
    # Initialize Pinecone
    if not args.skip_pinecone:
        pinecone = PineconeConnector()
        initialize_pinecone_namespaces(pinecone)
    
    # Initialize GCS
    if not args.skip_gcs:
        gcs = GCSConnector()
        initialize_gcs_paths(gcs)
    
    logger.info("Multi-jurisdictional organization initialization complete")

if __name__ == "__main__":
    main()

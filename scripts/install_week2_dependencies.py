#!/usr/bin/env python3
"""
Install Week 2 Dependencies

This script installs the required dependencies for Week 2 automated harvesting features.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required Python packages"""
    dependencies = [
        "apscheduler>=3.10.0",  # For automated scheduling
        "beautifulsoup4>=4.12.0",  # For HTML parsing in opinion processor
        "lxml>=4.9.0",  # XML/HTML parser for BeautifulSoup
    ]
    
    print("\n📦 Installing Week 2 dependencies...")
    
    for dep in dependencies:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    
    return True

def verify_installation():
    """Verify that all dependencies are properly installed"""
    print("\n🔍 Verifying installation...")
    
    try:
        # Test APScheduler
        from apscheduler.schedulers.background import BackgroundScheduler
        print("✅ APScheduler imported successfully")
        
        # Test BeautifulSoup
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported successfully")
        
        # Test our Week 2 modules
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        from src.harvesting.harvesting_config import get_harvesting_config
        print("✅ Harvesting configuration module imported successfully")
        
        from src.harvesting.automated_harvester import AutomatedHarvester
        print("✅ Automated harvester module imported successfully")
        
        from src.processing.enhanced_opinion_processor import EnhancedOpinionProcessor
        print("✅ Enhanced opinion processor module imported successfully")
        
        from src.processing.metadata_enhancer import CaseMetadataEnhancer
        print("✅ Metadata enhancer module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def create_logs_directory():
    """Create logs directory if it doesn't exist"""
    logs_dir = Path("logs")
    if not logs_dir.exists():
        logs_dir.mkdir(parents=True, exist_ok=True)
        print("✅ Created logs directory")
    else:
        print("✅ Logs directory already exists")

def check_environment_variables():
    """Check if required environment variables are set"""
    print("\n🔧 Checking environment variables...")
    
    required_vars = [
        "COURTLISTENER_API_KEY",
        "SUPABASE_URL",
        "SUPABASE_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"✅ {var} is set")
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables in your .env file or environment")
        return False
    
    return True

def main():
    """Main installation function"""
    print("🚀 Week 2 Automated Harvesting Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies")
        sys.exit(1)
    
    # Create necessary directories
    create_logs_directory()
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed")
        sys.exit(1)
    
    # Check environment variables
    env_ok = check_environment_variables()
    
    print("\n" + "=" * 50)
    print("✅ Week 2 setup completed successfully!")
    
    if not env_ok:
        print("\n⚠️  Note: Some environment variables are missing.")
        print("The harvester will not work until these are configured.")
    
    print("\nNext steps:")
    print("1. Configure environment variables if needed")
    print("2. Review config/harvesting_config.json")
    print("3. Run tests: python -m pytest tests/test_week2_implementation.py")
    print("4. Start harvester: python scripts/start_harvester.py")

if __name__ == "__main__":
    main()

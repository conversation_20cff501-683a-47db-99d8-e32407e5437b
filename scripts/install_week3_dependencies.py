#!/usr/bin/env python3
"""
Install Week 3 Dependencies
Installs required packages for multi-practice area integration and cross-document relationships.

This script:
1. Checks Python version compatibility
2. Installs required Python packages for Week 3
3. Downloads required NLP models
4. Validates installations
5. Tests Week 3 module imports
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command: str, description: str) -> bool:
    """Run a shell command and return success status."""
    try:
        logger.info(f"Running: {description}")
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required Python packages"""
    dependencies = [
        "networkx>=3.0",  # For network analysis
        "scikit-learn>=1.3.0",  # For advanced classification
        "spacy>=3.7.0",  # For enhanced NLP processing
        "plotly>=5.17.0",  # For network visualization
        "pandas>=2.0.0",  # For data manipulation
        "numpy>=1.24.0",  # For numerical operations
        "scipy>=1.10.0",  # For scientific computing
        "matplotlib>=3.7.0",  # For basic plotting
        "seaborn>=0.12.0",  # For statistical visualization
        "python-Levenshtein>=0.21.0",  # For fuzzy string matching
        "fuzzywuzzy>=0.18.0",  # For fuzzy string matching
        "textdistance>=4.6.0",  # For text similarity
    ]
    
    print("\n📦 Installing Week 3 dependencies...")
    
    for dep in dependencies:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    
    return True

def download_spacy_models():
    """Download required spaCy models"""
    models = [
        "en_core_web_sm",  # Small English model
        "en_core_web_md",  # Medium English model for better accuracy
    ]
    
    print("\n🔤 Downloading spaCy models...")
    
    for model in models:
        if not run_command(f"python -m spacy download {model}", f"Downloading spaCy model {model}"):
            logger.warning(f"Failed to download {model}, continuing with other models")
    
    return True

def test_imports():
    """Test importing Week 3 modules and dependencies"""
    print("\n🧪 Testing Week 3 imports...")
    
    try:
        # Test core dependencies
        import networkx as nx
        print("✅ NetworkX imported successfully")
        
        import sklearn
        print("✅ Scikit-learn imported successfully")
        
        import spacy
        print("✅ spaCy imported successfully")
        
        import plotly
        print("✅ Plotly imported successfully")
        
        import pandas as pd
        print("✅ Pandas imported successfully")
        
        import numpy as np
        print("✅ NumPy imported successfully")
        
        # Test spaCy models
        try:
            nlp = spacy.load("en_core_web_sm")
            print("✅ spaCy small model loaded successfully")
        except OSError:
            print("⚠️ spaCy small model not available, but continuing")
        
        # Test fuzzy matching
        try:
            import fuzzywuzzy
            from fuzzywuzzy import fuzz
            print("✅ FuzzyWuzzy imported successfully")
        except ImportError:
            print("⚠️ FuzzyWuzzy not available, but continuing")
        
        # Test Week 3 modules (if they exist)
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        try:
            from src.relationships import multi_practice_relationship_engine
            print("✅ Multi-practice relationship engine imported successfully")
        except ImportError:
            print("ℹ️ Week 3 relationship modules not yet created (expected)")
        
        try:
            from src.analysis import citation_importance_scorer
            print("✅ Citation analysis modules imported successfully")
        except ImportError:
            print("ℹ️ Week 3 analysis modules not yet created (expected)")
        
        return True
        
    except ImportError as e:
        logger.error(f"Import test failed: {e}")
        return False

def validate_installation():
    """Validate the Week 3 installation"""
    print("\n✅ Validating Week 3 installation...")
    
    # Test NetworkX functionality
    try:
        import networkx as nx
        G = nx.Graph()
        G.add_edge(1, 2)
        assert len(G.nodes()) == 2
        print("✅ NetworkX functionality validated")
    except Exception as e:
        logger.error(f"NetworkX validation failed: {e}")
        return False
    
    # Test scikit-learn functionality
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        
        vectorizer = TfidfVectorizer()
        texts = ["This is a test", "This is another test"]
        vectors = vectorizer.fit_transform(texts)
        similarity = cosine_similarity(vectors)
        assert similarity.shape == (2, 2)
        print("✅ Scikit-learn functionality validated")
    except Exception as e:
        logger.error(f"Scikit-learn validation failed: {e}")
        return False
    
    # Test spaCy functionality
    try:
        import spacy
        nlp = spacy.load("en_core_web_sm")
        doc = nlp("This is a test sentence.")
        assert len(doc) > 0
        print("✅ spaCy functionality validated")
    except Exception as e:
        logger.warning(f"spaCy validation failed: {e} (continuing anyway)")
    
    return True

def create_week3_directories():
    """Create necessary directories for Week 3"""
    print("\n📁 Creating Week 3 directories...")
    
    directories = [
        "src/relationships",
        "src/analysis", 
        "src/statute_collection",
        "tests/week3",
        "tests/week3/relationships",
        "tests/week3/analysis",
        "tests/week3/statute_collection",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory.startswith("src/"):
            init_file = Path(directory) / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# Week 3 implementation\n")
    
    print("✅ Week 3 directories created")
    return True

def main():
    """Main installation function"""
    print("🚀 Week 3 Dependencies Installation")
    print("=" * 50)
    
    try:
        # Step 1: Check Python version
        if not check_python_version():
            logger.error("Python version check failed. Aborting installation.")
            return 1
        
        # Step 2: Create directories
        if not create_week3_directories():
            logger.error("Directory creation failed. Aborting installation.")
            return 1
        
        # Step 3: Install dependencies
        if not install_dependencies():
            logger.error("Dependency installation failed. Aborting installation.")
            return 1
        
        # Step 4: Download spaCy models
        if not download_spacy_models():
            logger.warning("spaCy model download had issues, but continuing.")
        
        # Step 5: Test imports
        if not test_imports():
            logger.error("Import tests failed. Installation may be incomplete.")
            return 1
        
        # Step 6: Validate installation
        if not validate_installation():
            logger.error("Installation validation failed.")
            return 1
        
        print("\n" + "=" * 50)
        print("✅ Week 3 dependencies installed successfully!")
        print("=" * 50)
        print("\nNext steps:")
        print("1. Run: python scripts/apply_week3_migration.py")
        print("2. Run: python scripts/configure_week3_settings.py")
        print("3. Start implementing Week 3 components")
        
        return 0
        
    except Exception as e:
        logger.error(f"Installation failed with unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

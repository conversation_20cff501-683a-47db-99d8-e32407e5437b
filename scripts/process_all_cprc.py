#!/usr/bin/env python3
"""
Process All CPRC Documents

This script processes all CPRC documents in the specified directory,
extracts citations using the LLM-enhanced extractor, and stores
the results in Neo4j.
"""

import os
import argparse
import logging
from dotenv import load_dotenv

# Import our components
from batch_processor import BatchProcessor, run_batch_job
from integrated_citation_pipeline import IntegratedCitationPipeline, integrate_with_batch_processor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cprc_processing.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """Process all CPRC documents with LLM-enabled citation extraction"""
    parser = argparse.ArgumentParser(description="Process CPRC documents with LLM-enabled citation extraction")
    parser.add_argument("--input_dir", default="/Users/<USER>/Documents/Texas/CP", 
                        help="Directory containing CPRC PDF files")
    parser.add_argument("--jurisdiction", default="Texas", 
                        help="Jurisdiction for the documents")
    parser.add_argument("--use_llm", type=bool, default=True, 
                        help="Whether to use LLM for citation extraction")
    parser.add_argument("--max_workers", type=int, default=4, 
                        help="Maximum number of worker threads")
    
    args = parser.parse_args()
    
    logger.info(f"Starting processing of CPRC documents in {args.input_dir}")
    logger.info(f"LLM-enabled extraction: {args.use_llm}")
    
    # Create the batch processor
    processor = BatchProcessor()
    
    # Create and integrate the citation pipeline
    pipeline = IntegratedCitationPipeline()
    enhanced_processor = integrate_with_batch_processor(processor, pipeline)
    
    # Process the documents
    result = enhanced_processor.process_directory(
        args.input_dir, 
        args.jurisdiction, 
        max_workers=args.max_workers
    )
    
    # Display results
    print(f"\n===== CPRC DOCUMENT PROCESSING COMPLETE =====")
    print(f"Batch ID: {result['batch_id']}")
    print(f"Total Documents: {result['total']}")
    print(f"Successful: {result['success_count']}")
    print(f"Failed: {result['failure_count']}")
    print(f"Success Rate: {(result['success_count'] / result['total']) * 100:.2f}%")
    
    return result

if __name__ == "__main__":
    main()

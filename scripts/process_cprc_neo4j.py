#!/usr/bin/env python3
"""
Process CPRC Documents for Neo4j

This script processes CPRC documents and adds them to Neo4j using the
integrated citation pipeline. It extracts citations, classifies them,
and adds them to the Neo4j graph database.

Usage:
  python process_cprc_neo4j.py
"""

import os
import pdfplumber
import argparse
import logging
from dotenv import load_dotenv
from tqdm import tqdm
from integrated_citation_pipeline import IntegratedCitationPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cprc_neo4j_processing.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def process_cprc_documents(cprc_dir, use_llm=True):
    """Process CPRC documents and add them to Neo4j"""
    # Initialize the pipeline
    pipeline = IntegratedCitationPipeline()
    
    # Get list of PDF files
    pdf_files = [f for f in os.listdir(cprc_dir) if f.lower().endswith(".pdf")]
    logger.info(f"Found {len(pdf_files)} PDF files to process")
    
    # Process statistics
    total_docs = len(pdf_files)
    processed_docs = 0
    failed_docs = 0
    total_citations = 0
    valid_citations = 0
    
    try:
        # Process each PDF
        for filename in tqdm(pdf_files, desc="Processing documents"):
            pdf_path = os.path.join(cprc_dir, filename)
            document_id = f"CPRC_{filename.replace('.pdf', '')}"
            
            try:
                # Extract text from PDF
                logger.info(f"Extracting text from {filename}")
                text = ""
                with pdfplumber.open(pdf_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text() or ""
                        text += page_text
                
                if not text.strip():
                    logger.warning(f"No text extracted from {filename}")
                    failed_docs += 1
                    continue
                
                # Process document through the citation pipeline
                logger.info(f"Processing citations for {filename}")
                result = pipeline.process_document(
                    content=text,
                    doc_type="law",
                    document_id=document_id,
                    metadata={
                        "title": f"CPRC {filename}",
                        "path": pdf_path,
                        "jurisdiction": "Texas",
                        "doc_type": "law",
                        "statute_chapter": filename.replace("cp.", "").replace(".pdf", "")
                    },
                    use_llm=use_llm
                )
                
                # Update statistics
                processed_docs += 1
                doc_citations = len(result['validated_citations'])
                doc_valid_citations = result['valid_citations']
                total_citations += doc_citations
                valid_citations += doc_valid_citations
                
                logger.info(f"Processed {filename}: Found {doc_citations} citations, {doc_valid_citations} valid ({result['valid_rate']:.2%})")
                
            except Exception as e:
                logger.error(f"Error processing {filename}: {e}")
                failed_docs += 1
                continue
    
    finally:
        # Close resources
        pipeline.close()
    
    # Print summary
    logger.info("\n===== PROCESSING SUMMARY =====")
    logger.info(f"Total Documents: {total_docs}")
    logger.info(f"Successfully Processed: {processed_docs}")
    logger.info(f"Failed: {failed_docs}")
    logger.info(f"Total Citations: {total_citations}")
    logger.info(f"Valid Citations: {valid_citations}")
    if total_citations > 0:
        logger.info(f"Overall Valid Rate: {valid_citations / total_citations:.2%}")

def main():
    parser = argparse.ArgumentParser(description="Process CPRC documents for Neo4j")
    parser.add_argument("--input_dir", default="/Users/<USER>/Documents/Texas/CP/success", 
                      help="Directory containing CPRC PDF files")
    parser.add_argument("--use_llm", action="store_true", default=True,
                      help="Whether to use LLM for citation extraction")
    
    args = parser.parse_args()
    
    logger.info(f"Starting processing of CPRC documents in {args.input_dir}")
    logger.info(f"LLM-enabled extraction: {args.use_llm}")
    
    process_cprc_documents(args.input_dir, use_llm=args.use_llm)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
process_pdfs_in_folder.py

Usage:
  python process_pdfs_in_folder.py --namespace laws
    [--folder_path /path/to/folder]
    [--chunk_size 1000]

Notes:
- For Pinecone >= 2.2.0, we use the "Pinecone" class.
- For OpenAI >= 1.0.0, we use the client.embeddings.create(model=..., input=...).
"""

import os
import argparse
import uuid
import shutil
import re
import pdfplumber
import requests
from openai import OpenAI

import tiktoken
from dotenv import load_dotenv
from supabase import create_client
from google.cloud import storage
from pinecone import Pinecone  # Requires pinecone-client >= 2.2.0
import json

# ---------------------------------------------------------------------------
# Load environment variables
# ---------------------------------------------------------------------------
load_dotenv()

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_ENVIRONMENT = os.getenv("PINECONE_ENVIRONMENT")  # e.g. us-west1-gcp
PINECONE_INDEX_NAME = os.getenv("PINECONE_INDEX_NAME")

GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
GCS_SERVICE_ACCOUNT_FILE = os.getenv("GCS_SERVICE_ACCOUNT_FILE")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
VOYAGE_API_KEY = os.getenv("VOYAGE_API_KEY")

client = OpenAI(api_key=OPENAI_API_KEY)

DEFAULT_FOLDER_PATH = os.getenv("PDF_FOLDER_PATH", "")

# Voyage AI configuration
VOYAGE_EMBEDDING_MODEL = "voyage-3-large"
VOYAGE_API_URL = "https://api.voyageai.com/v1/embeddings"

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def find_meaningful_title(pdf_path):
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text() or ""
                for line in text.splitlines():
                    if any(keyword in line.upper() for keyword in ["CHAPTER", "SEC.", "TITLE"]):
                        return line.strip()
    except Exception as e:
        print(f"[Error] Failed to extract title from {pdf_path}: {e}")
    return "Untitled Document"

def chunk_pdf_content(pdf_path, chunk_size_tokens=1000, practice_area="personal_injury", doc_type="law"):
    """
    Intelligently chunk PDF content with respect to document boundaries and semantic completeness.
    Returns tuples of (chunk_text, metadata) where metadata contains page numbers, document info,
    and navigation metadata for sequential reading.
    
    Args:
        pdf_path: Path to the PDF file
        chunk_size_tokens: Maximum number of tokens per chunk
        practice_area: Area of law (personal_injury, family_law, criminal_defense)
        doc_type: Type of document (law, precedent_case)
    """
    text_chunks = []
    encoder = tiktoken.encoding_for_model("text-embedding-3-small")
    doc_filename = os.path.basename(pdf_path)
    doc_title = find_meaningful_title(pdf_path)
    
    # Extract additional metadata based on document type
    jurisdiction = "Texas"  # Compliant with database constraint
    case_citation = None
    case_date = None
    case_parties = None
    statute_section = None
    
    # Parse document filename for additional metadata
    if doc_type == "law":
        # Check for code section or chapter in title
        if "CHAPTER" in doc_title.upper():
            chapter_match = re.search(r'CHAPTER\s+([\d\.A-Za-z]+)', doc_title.upper())
            if chapter_match:
                statute_section = chapter_match.group(1).strip()
        elif "SECTION" in doc_title.upper() or "SEC." in doc_title.upper():
            section_match = re.search(r'SE(C|CTION)\.*\s+([\d\.A-Za-z]+)', doc_title.upper())
            if section_match:
                statute_section = section_match.group(2).strip()
    elif doc_type == "precedent_case":
        # Try to extract case citation, date and parties from filename or title
        # Format often like: Smith v. Jones, 123 S.W.3d 456 (Tex. App. 2020)
        case_match = re.search(r'(.+?)\s*,\s*(.+?)\s*\((.*?)\s*(\d{4})\)', doc_title)
        if case_match:
            case_parties = case_match.group(1).strip()
            case_citation = case_match.group(2).strip()
            jurisdiction = case_match.group(3).strip()
            case_date = case_match.group(4).strip()
    
    # Create GCS URL for citation purposes
    gcs_file_path = f"{GCS_BUCKET_NAME}/{doc_filename}"
    gcs_url = f"https://storage.googleapis.com/{gcs_file_path}"
    
    with pdfplumber.open(pdf_path) as pdf:
        total_pages = len(pdf.pages)
        # First pass: Extract text with page boundaries preserved
        page_texts = []
        for page_num, page in enumerate(pdf.pages, 1):
            page_text = page.extract_text() or ""
            if page_text.strip():
                page_texts.append((page_num, page_text))
                
        # Second pass: Process each page, respecting paragraph boundaries
        current_chunk_text = ""
        current_chunk_tokens = []
        current_pages = set()
        chunk_index = 0  # Track chunk sequence for navigation
        chunk_ids = []   # Store chunk IDs in sequence
        
        for page_num, page_text in page_texts:
            # Split by paragraphs (double newlines often indicate paragraph breaks)
            paragraphs = [p for p in page_text.split("\n\n") if p.strip()]
            
            for paragraph in paragraphs:
                # Get tokens for current paragraph
                para_tokens = encoder.encode(paragraph)
                
                # Check if adding this paragraph would exceed the chunk size
                if len(current_chunk_tokens) + len(para_tokens) > chunk_size_tokens and current_chunk_text:
                    # Save the current chunk before starting a new one
                    page_list = sorted(list(current_pages))
                    chunk_id = str(uuid.uuid4())
                    chunk_ids.append(chunk_id)
                    
                    # Create chunk metadata with navigation info
                    chunk_metadata = {
                        "document_name": doc_filename,
                        "document_title": doc_title,
                        "page_numbers": page_list,
                        "practice_area": practice_area,
                        "doc_type": doc_type,
                        "total_pages": total_pages,
                        "page_range": f"Page {min(page_list)}-{max(page_list)} of {total_pages}",
                        "citation": f"{doc_title}, Page {min(page_list)}",
                        "gcs_url": gcs_url,
                        "chunk_index": chunk_index,
                        "chunk_id": chunk_id,
                        "prev_chunk_id": chunk_ids[chunk_index-1] if chunk_index > 0 else None,
                        "next_chunk_id": None,  # Will be updated later
                        "jurisdiction": jurisdiction
                    }
                    
                    # Add document type specific metadata
                    if doc_type == "law":
                        chunk_metadata["statute_section"] = statute_section
                    elif doc_type == "precedent_case":
                        chunk_metadata["case_citation"] = case_citation
                        chunk_metadata["case_date"] = case_date
                        chunk_metadata["case_parties"] = case_parties
                    
                    text_chunks.append((current_chunk_text.strip(), chunk_metadata))
                    chunk_index += 1
                    
                    # Reset for next chunk
                    current_chunk_text = ""
                    current_chunk_tokens = []
                    current_pages = set()
                
                # Add paragraph to current chunk
                if current_chunk_text:
                    current_chunk_text += "\n\n" + paragraph
                else:
                    current_chunk_text = paragraph
                current_chunk_tokens.extend(para_tokens)
                current_pages.add(page_num)
                
                # If paragraph itself is too large, use LLM to split it intelligently
                if len(para_tokens) > chunk_size_tokens:
                    intelligent_chunks = split_with_llm(paragraph, chunk_size_tokens)
                    
                    # Process all but the last intelligent chunk
                    for i, intel_chunk in enumerate(intelligent_chunks[:-1]):
                        chunk_id = str(uuid.uuid4())
                        chunk_ids.append(chunk_id)
                        
                        chunk_metadata = {
                            "document_name": doc_filename,
                            "document_title": doc_title,
                            "page_numbers": [page_num],
                            "practice_area": practice_area,
                            "doc_type": doc_type,
                            "total_pages": total_pages,
                            "page_range": f"Page {page_num} of {total_pages}",
                            "citation": f"{doc_title}, Page {page_num}",
                            "gcs_url": gcs_url,
                            "chunk_index": chunk_index,
                            "chunk_id": chunk_id,
                            "prev_chunk_id": chunk_ids[chunk_index-1] if chunk_index > 0 else None,
                            "next_chunk_id": None,  # Will be updated later
                            "jurisdiction": jurisdiction
                        }
                        
                        # Add document type specific metadata
                        if doc_type == "law":
                            chunk_metadata["statute_section"] = statute_section
                        elif doc_type == "precedent_case":
                            chunk_metadata["case_citation"] = case_citation
                            chunk_metadata["case_date"] = case_date
                            chunk_metadata["case_parties"] = case_parties
                            
                        text_chunks.append((intel_chunk.strip(), chunk_metadata))
                        chunk_index += 1
                    
                    # The last intelligent chunk becomes our current chunk
                    if intelligent_chunks:
                        current_chunk_text = intelligent_chunks[-1]
                        current_chunk_tokens = encoder.encode(current_chunk_text)
                        current_pages = {page_num}
        
        # Don't forget the last chunk
        if current_chunk_text:
            page_list = sorted(list(current_pages))
            chunk_metadata = {
                "document_name": doc_filename,
                "document_title": doc_title,
                "page_numbers": page_list,
                "practice_area": practice_area,
                "doc_type": doc_type,
                "total_pages": total_pages,
                "page_range": f"Page {min(page_list)}-{max(page_list)} of {total_pages}",
                "citation": f"{doc_title}, Page {min(page_list)}",
                "gcs_url": gcs_url
            }
            text_chunks.append((current_chunk_text.strip(), chunk_metadata))
    
    return text_chunks


def split_with_llm(long_text, max_tokens):
    """
    Use LLM to intelligently split text into semantically meaningful chunks
    that respect the context and ensure complete thoughts.
    """
    try:
        # Prepare system prompt for intelligent splitting
        system_prompt = """You are a document processing assistant. Split the following text into smaller chunks while:
1. Preserving complete thoughts and logical units
2. Respecting paragraph boundaries when possible
3. Ensuring each chunk is coherent and self-contained
4. Maintaining context and meaning

Return a JSON array of text chunks."""
        
        # Prepare user prompt with the text to split
        user_prompt = f"""Split this text into meaningful chunks of roughly {max_tokens // 4} tokens each (OpenAI's tokens, not words):

{long_text}

Respond ONLY with a JSON array of text chunks. No explanation or other text."""
        
        # Call OpenAI API to split the text intelligently
        response = client.chat.completions.create(
            model="gpt-4",
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2,
        )
        
        result = json.loads(response.choices[0].message.content)
        return result.get("chunks", [long_text])  # Fallback to original if parsing fails
        
    except Exception as e:
        print(f"[Warning] LLM-based splitting failed: {e}. Falling back to simple splitting.")
        # Fallback to simpler splitting approach
        encoder = tiktoken.encoding_for_model("text-embedding-3-small")
        tokens = encoder.encode(long_text)
        
        # Simple splitting by sentences (not ideal but better than arbitrary cutoff)
        sentences = long_text.split(". ")
        chunks = []
        current_chunk = ""
        current_tokens = []
        
        for sentence in sentences:
            sentence_tokens = encoder.encode(sentence + ". ")
            if len(current_tokens) + len(sentence_tokens) > max_tokens and current_chunk:
                chunks.append(current_chunk)
                current_chunk = sentence + ". "
                current_tokens = sentence_tokens
            else:
                current_chunk += sentence + ". "
                current_tokens.extend(sentence_tokens)
        
        if current_chunk:
            chunks.append(current_chunk)
            
        return chunks or [long_text]  # Last fallback

def generate_embeddings(chunks_with_metadata):
    """
    Generate embeddings for text chunks using Voyage AI's voyage-3-large model, preserving metadata.
    Input: List of tuples (chunk_text, metadata)
    Output: List of tuples (chunk_text, metadata, vector)
    """
    results = []
    
    # Process chunks in batches for efficiency
    batch_size = 10
    for i in range(0, len(chunks_with_metadata), batch_size):
        batch = chunks_with_metadata[i:i+batch_size]
        batch_texts = [chunk_text for chunk_text, _ in batch]
        
        try:
            # Call Voyage AI API for embeddings
            response = requests.post(
                VOYAGE_API_URL,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {VOYAGE_API_KEY}"
                },
                json={
                    "model": VOYAGE_EMBEDDING_MODEL,
                    "input": batch_texts,
                    "input_type": "document",
                    "truncation": True
                }
            )
            
            # Parse response
            if response.status_code == 200:
                data = response.json()
                for j, (chunk_text, metadata) in enumerate(batch):
                    if j < len(data.get("data", [])):
                        vector = data["data"][j]["embedding"]
                        results.append((chunk_text, metadata, vector))
                    else:
                        print(f"[Error] Missing embedding for chunk {i+j}")
            else:
                print(f"[Error] Voyage API error: {response.status_code} - {response.text}")
                # Do not fall back to OpenAI to avoid mixing embedding spaces
                print(f"[Warning] Failed to generate embeddings for batch starting at index {i}")
        except Exception as e:
            print(f"[Error] Failed to generate embeddings for batch: {e}")
            
    print(f"[Info] Generated {len(results)} embeddings with Voyage AI ({VOYAGE_EMBEDDING_MODEL})")
    return results

def upload_pdf_to_gcs(pdf_path, bucket_name, service_account_file):
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_file
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob_name = f"documents/{os.path.basename(pdf_path)}"
    blob = bucket.blob(blob_name)
    blob.upload_from_filename(pdf_path)
    return f"gs://{bucket_name}/{blob_name}"

def insert_document_in_supabase(supabase_client, document_id, title, namespace, gcs_path, practice_area="personal_injury", doc_type="law"):
    # Keep document_id as UUID for internal use but convert to string for Supabase
    if isinstance(document_id, str):
        document_id = uuid.UUID(document_id)
        
    data = {
        "document_id": str(document_id),  # Convert UUID to string for JSON
        "title": title,
        "gcs_path": gcs_path,
        "namespace": namespace,
        "practice_area": practice_area,
        "doc_type": doc_type
    }
    print(f"[Debug] Inserting document with data: {data}")
    response = supabase_client.table("documents").insert(data).execute()
    print(f"[Debug] Document insert response: {response.data}")
    
    if isinstance(response.data, list) and len(response.data) > 0:
        return response.data[0]
    raise RuntimeError(f"Error inserting document: unexpected response format {response.data}")

def insert_chunks_in_supabase(supabase_client, document_id, namespace, chunks_with_metadata):
    """Insert chunk data into Supabase and return the Pinecone IDs with metadata.
    Also establishes navigation links between chunks for sequential reading."""
    # Keep document_id as UUID for internal use but convert to string for Supabase
    if isinstance(document_id, str):
        document_id = uuid.UUID(document_id)
        
    # First pass: Insert all chunks
    inserted = []
    db_ids_by_position = {}  # To store database IDs by chunk position
    pinecone_ids_by_position = {}  # To store pinecone IDs by chunk position
    
    for i, (text, metadata) in enumerate(chunks_with_metadata):
        pinecone_id = uuid.uuid4()
        pinecone_ids_by_position[i] = str(pinecone_id)
        
        # Basic data for all document types
        data = {
            "document_id": str(document_id),  # Convert UUID to string for JSON
            "chapter": None,
            "section": None,
            "content": text,
            "pinecone_id": str(pinecone_id),  # Convert UUID to string for JSON
            "namespace": namespace,
            # Enhanced metadata for citation and search
            "document_name": metadata.get("document_name", ""),
            "document_title": metadata.get("document_title", ""),
            "page_numbers": metadata.get("page_numbers", []),
            "practice_area": metadata.get("practice_area", "personal_injury"),
            "doc_type": metadata.get("doc_type", "law"),
            "total_pages": metadata.get("total_pages"),
            "page_range": metadata.get("page_range"),
            "citation": metadata.get("citation"),
            "gcs_url": metadata.get("gcs_url"),
            "chunk_index": i,  # Store position for navigation
            "prev_chunk_id": pinecone_ids_by_position.get(i-1) if i > 0 else None,
            "next_chunk_id": None,  # Will update in second pass
            "jurisdiction": metadata.get("jurisdiction", "Texas")
        }
        
        # Add document type specific metadata
        if metadata.get("doc_type") == "law":
            data["statute_section"] = metadata.get("statute_section")
        elif metadata.get("doc_type") == "precedent_case":
            data["case_citation"] = metadata.get("case_citation")
            data["case_date"] = metadata.get("case_date")
            data["case_parties"] = metadata.get("case_parties")
        
        # Insert into Supabase
        response = supabase_client.table("chunks").insert(data).execute()
        print(f"[Debug] Chunk insert response: {response.data}")
        
        if isinstance(response.data, list) and len(response.data) > 0:
            db_id = response.data[0]["id"]
            db_ids_by_position[i] = db_id
            inserted.append((str(pinecone_id), text, metadata))
        else:
            raise RuntimeError(f"Error inserting chunk: unexpected response format {response.data}")
    
    # Second pass: Update next_chunk_id for all chunks except the last one
    for i in range(len(inserted) - 1):
        if i in db_ids_by_position and i+1 in pinecone_ids_by_position:
            current_db_id = db_ids_by_position[i]
            next_pinecone_id = pinecone_ids_by_position[i+1]
            
            # Update the next_chunk_id field
            try:
                supabase_client.table("chunks") \
                    .update({"next_chunk_id": next_pinecone_id}) \
                    .eq("id", current_db_id) \
                    .execute()
            except Exception as e:
                print(f"[Warning] Failed to update next_chunk_id for chunk {current_db_id}: {e}")
    
    # Also update metadata in our returned objects to include navigation links
    for i, (pinecone_id, text, metadata) in enumerate(inserted):
        metadata["chunk_index"] = i
        metadata["prev_chunk_id"] = pinecone_ids_by_position.get(i-1) if i > 0 else None
        metadata["next_chunk_id"] = pinecone_ids_by_position.get(i+1) if i < len(inserted)-1 else None
    
    return inserted

def upsert_pinecone(index, pinecone_id_text_metadata_vector_tuples, namespace):
    """Upsert vectors and their complete metadata to Pinecone.
    Includes content navigation metadata between chunks."""
    pinecone_data = []
    
    for pinecone_id, text, metadata, vector in pinecone_id_text_metadata_vector_tuples:
        # Create base metadata with required fields
        pinecone_metadata = {
            # Standard fields all documents will have
            "text": text[:1000] if len(text) > 1000 else text,  # Truncate if needed
            "document_name": metadata.get("document_name", ""),
            "document_title": metadata.get("document_title", ""),
            "practice_area": metadata.get("practice_area", "personal_injury"),
            "doc_type": metadata.get("doc_type", "law"),
            "jurisdiction": metadata.get("jurisdiction", "Texas"),
            "total_pages": metadata.get("total_pages"),
            "page_range": metadata.get("page_range"),
            "gcs_url": metadata.get("gcs_url"),
            
            # Navigation metadata - chunk_index is always present
            "chunk_index": metadata.get("chunk_index", 0)
        }
        
        # Only add prev_chunk_id and next_chunk_id if they're not null
        if metadata.get("prev_chunk_id") is not None:
            pinecone_metadata["prev_chunk_id"] = metadata.get("prev_chunk_id")
            
        if metadata.get("next_chunk_id") is not None:
            pinecone_metadata["next_chunk_id"] = metadata.get("next_chunk_id")
        
        # Convert page numbers to strings for Pinecone compatibility
        page_numbers = metadata.get("page_numbers", [])
        str_page_numbers = [str(page) for page in page_numbers]
        pinecone_metadata["page_numbers"] = str_page_numbers
        
        # Document type specific metadata - only add fields if they have non-null values
        if metadata.get("doc_type") == "law" and metadata.get("statute_section") is not None:
            pinecone_metadata["statute_section"] = metadata.get("statute_section")
        elif metadata.get("doc_type") == "precedent_case":
            if metadata.get("case_citation") is not None:
                pinecone_metadata["case_citation"] = metadata.get("case_citation")
            if metadata.get("case_date") is not None:
                pinecone_metadata["case_date"] = metadata.get("case_date")
            if metadata.get("case_parties") is not None:
                pinecone_metadata["case_parties"] = metadata.get("case_parties")
        
        # Create enhanced citation format based on document type
        if metadata.get("doc_type") == "law":
            page_citation = ", ".join([f"p.{p}" for p in metadata.get("page_numbers", [])])
            citation_info = f"{metadata.get('document_title', '')} ({page_citation})"
        elif metadata.get("doc_type") == "precedent_case" and metadata.get("case_citation"):
            citation_info = f"{metadata.get('case_parties', '')}, {metadata.get('case_citation')} ({metadata.get('case_date', '')})"
        else:
            # Fallback citation format
            page_citation = ", ".join([f"p.{p}" for p in metadata.get("page_numbers", [])])
            citation_info = f"{metadata.get('document_name', '')} ({page_citation})"
            
        pinecone_metadata["citation"] = citation_info
        
        pinecone_data.append((pinecone_id, vector, pinecone_metadata))
    
    # Batch upsert in groups of 100 (Pinecone recommended limit)
    batch_size = 100
    for i in range(0, len(pinecone_data), batch_size):
        batch = pinecone_data[i:i+batch_size]
        index.upsert(vectors=batch, namespace=namespace)
        print(f"Upserted batch of {len(batch)} vectors to Pinecone (total progress: {min(i+batch_size, len(pinecone_data))}/{len(pinecone_data)})")
    
    print(f"Upserted {len(pinecone_data)} vectors to Pinecone with enhanced metadata and navigation.")

def move_file_to_success(pdf_path):
    success_folder = os.path.join(os.path.dirname(pdf_path), "success")
    os.makedirs(success_folder, exist_ok=True)
    new_path = os.path.join(success_folder, os.path.basename(pdf_path))
    shutil.move(pdf_path, new_path)
    return new_path

def get_already_processed_files(folder_path):
    """Get list of files that have already been processed successfully."""
    success_folder = os.path.join(os.path.dirname(folder_path), "success")
    if not os.path.exists(success_folder):
        return set()
    return {f for f in os.listdir(success_folder) if f.lower().endswith('.pdf')}

def process_pdf_locally(pdf_path, chunk_size, practice_area="personal_injury", doc_type="law"):
    """Process PDF locally before any database updates."""
    print(f"[Info] Processing {pdf_path} locally...")
    print(f"[Info] Practice area: {practice_area}, Document type: {doc_type}")
    
    # Extract metadata
    base_filename = os.path.basename(pdf_path)
    extracted_title = find_meaningful_title(pdf_path)
    final_title = f"{base_filename} - {extracted_title}"
    document_id = uuid.uuid4()
    
    # Local processing steps with enhanced intelligent chunking
    chunks_with_metadata = chunk_pdf_content(
        pdf_path, 
        chunk_size_tokens=chunk_size,
        practice_area=practice_area,
        doc_type=doc_type
    )
    if not chunks_with_metadata:
        raise ValueError("No chunks extracted from PDF")
        
    embeddings = generate_embeddings(chunks_with_metadata)
    if not embeddings or len(embeddings) != len(chunks_with_metadata):
        raise ValueError("Embedding generation failed or incomplete")
    
    return {
        'document_id': document_id,
        'title': final_title,
        'chunks_with_metadata': chunks_with_metadata,
        'embeddings': embeddings,
        'practice_area': practice_area,
        'doc_type': doc_type
    }

# ---------------------------------------------------------------------------
# Main orchestrator
# ---------------------------------------------------------------------------
def main():
    parser = argparse.ArgumentParser(description="Process PDFs in a folder, chunk & embed locally, then upload to GCS, Supabase, Pinecone.")
    parser.add_argument("--folder_path", default=None,
                        help="Folder containing PDF files. Defaults to PDF_FOLDER_PATH from .env if not provided.")
    parser.add_argument("--namespace", required=True,
                        choices=["tx", "ca", "ny", "fl", "il", "us_federal"],
                        help="Jurisdiction namespace (use state code or us_federal).")
    parser.add_argument("--practice_area", default="personal_injury",
                        choices=["personal_injury", "family_law", "criminal_defense"],
                        help="Practice area for the documents being processed.")
    parser.add_argument("--doc_type", default="law",
                        choices=["law", "precedent_case"],
                        help="Type of document being processed.")
    parser.add_argument("--chunk_size", type=int, default=1000,
                        help="Approx. number of tokens per chunk (default=1000).")
    args = parser.parse_args()

    folder_path = args.folder_path or DEFAULT_FOLDER_PATH
    if not folder_path or not os.path.isdir(folder_path):
        print(f"[Error] Invalid folder path: {folder_path}")
        return

    namespace = args.namespace
    practice_area = args.practice_area
    doc_type = args.doc_type
    chunk_size = args.chunk_size
    
    print(f"[Info] Processing documents for jurisdiction: {namespace}")
    print(f"[Info] Practice area: {practice_area}")
    print(f"[Info] Document type: {doc_type}")

    pdf_files = [f for f in os.listdir(folder_path) if f.lower().endswith(".pdf")]
    if not pdf_files:
        print(f"[Info] No PDF files found in folder: {folder_path}")
        return

    supabase_client = create_client(SUPABASE_URL, SUPABASE_KEY)

    # Debug Pinecone Environment and Connectivity
    try:
        pc_test = Pinecone(api_key=PINECONE_API_KEY, environment=PINECONE_ENVIRONMENT)
        test_indexes = pc_test.list_indexes()
        print(f"[Debug] Available indexes in test connection: {test_indexes}")
    except Exception as e:
        print(f"[Error] Pinecone test connection failed: {e}")
        return

    pc = Pinecone(api_key=PINECONE_API_KEY, environment=PINECONE_ENVIRONMENT)

    # Extract the list of index names from the response
    
    try:
        raw_index_response = pc.list_indexes()
        print(f"[Debug] Raw index response type: {type(raw_index_response)}")
        print(f"[Debug] Raw index response: {raw_index_response}")
        # Fix: Access .indexes directly since it's an IndexList object
        available_indexes = [idx.name for idx in raw_index_response.indexes]
        print(f"[Debug] Available indexes in Pinecone: {available_indexes}")
    except Exception as e:
        print(f"[Error] Failed to retrieve Pinecone indexes: {e}")
        return

    # Validate existence
    if PINECONE_INDEX_NAME not in available_indexes:
        print(f"[Error] Pinecone index '{PINECONE_INDEX_NAME}' does not exist.")
        return

    # Test accessing the index directly
    try:
        index = pc.Index(PINECONE_INDEX_NAME)
        print(f"[Debug] Successfully accessed Pinecone index: {PINECONE_INDEX_NAME}")
    except Exception as e:
        print(f"[Error] Failed to access index '{PINECONE_INDEX_NAME}': {e}")
        return

    # Get list of already processed files
    processed_files = get_already_processed_files(folder_path)
    
    for pdf_name in pdf_files:
        # Skip if already processed
        if pdf_name in processed_files:
            print(f"[Skip] {pdf_name} already processed (found in success folder)")
            continue
            
        pdf_path = os.path.join(folder_path, pdf_name)
        
        try:
            # 1. Process everything locally first
            results = process_pdf_locally(pdf_path, chunk_size, practice_area, doc_type)
            
            # 2. If local processing successful, update external services
            gcs_path = upload_pdf_to_gcs(pdf_path, GCS_BUCKET_NAME, GCS_SERVICE_ACCOUNT_FILE)
            
            # 3. Update databases only after successful local processing
            insert_document_in_supabase(
                supabase_client, 
                results['document_id'], 
                results['title'], 
                namespace, 
                gcs_path,
                results['practice_area'],
                results['doc_type']
            )
            
            inserted_chunks = insert_chunks_in_supabase(
                supabase_client,
                results['document_id'],
                namespace,
                results['chunks_with_metadata']
            )
            
            chunk_id_text_metadata_vector = [
                (chunk_uuid, chunk_text, metadata, vector)
                for (chunk_uuid, chunk_text, metadata), (_, _, vector) 
                in zip(inserted_chunks, results['embeddings'])
            ]
            
            upsert_pinecone(index, chunk_id_text_metadata_vector, namespace)
            
            # 4. Only move to success folder after ALL updates complete
            move_file_to_success(pdf_path)
            print(f"Processed '{results['title']}' successfully.")
            
        except Exception as e:
            print(f"[Error] Failed to process '{pdf_name}': {e}")
            continue

if __name__ == "__main__":
    main()

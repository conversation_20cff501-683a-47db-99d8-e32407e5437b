#!/usr/bin/env python3
"""
Setup and Test Multi-Jurisdictional Organization

This script runs the complete setup and testing process for the multi-jurisdictional
organization system:

1. Initializes the jurisdiction metadata tables and data
2. Sets up the storage systems with proper jurisdiction organization
3. Runs comprehensive tests to verify the implementation
4. Generates a detailed report of the results

Usage:
    python setup_and_test_multi_jurisdiction.py [--skip-init] [--skip-tests]

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import subprocess
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_initialization():
    """Run the initialization script."""
    logger.info("Running jurisdiction initialization...")
    
    init_script = os.path.join(os.path.dirname(__file__), 'initialize_jurisdictions.py')
    
    if not os.path.exists(init_script):
        logger.error(f"Initialization script not found: {init_script}")
        return False
    
    try:
        result = subprocess.run(
            [sys.executable, init_script],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info("Initialization completed successfully")
        logger.info(result.stdout)
        
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Initialization failed with error code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        return False

def run_tests():
    """Run the test script."""
    logger.info("Running multi-jurisdictional tests...")
    
    test_script = os.path.join(os.path.dirname(__file__), 'test_multi_jurisdiction.py')
    
    if not os.path.exists(test_script):
        logger.error(f"Test script not found: {test_script}")
        return False
    
    try:
        result = subprocess.run(
            [sys.executable, test_script],
            check=True,
            capture_output=True,
            text=True
        )
        
        logger.info("Tests completed")
        logger.info(result.stdout)
        
        # Check if all tests passed
        if "All tests passed!" in result.stdout:
            logger.info("All tests passed successfully!")
            return True
        else:
            logger.warning("Some tests failed. Check the test report for details.")
            return False
    except subprocess.CalledProcessError as e:
        logger.error(f"Tests failed with error code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        return False

def generate_report(init_success, test_success):
    """Generate a report of the setup and test results."""
    logger.info("Generating report...")
    
    report_path = os.path.join(
        os.path.dirname(__file__), 
        f"../documentation/multi_jurisdiction_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    )
    
    report_content = f"""# Multi-Jurisdictional Organization Setup Report

## Summary

- **Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Initialization:** {'✅ Successful' if init_success else '❌ Failed'}
- **Tests:** {'✅ Successful' if test_success else '❌ Some tests failed'}
- **Overall Status:** {'✅ Ready for use' if init_success and test_success else '❌ Needs attention'}

## Components

1. **Jurisdiction Metadata Tables**
   - Tables for jurisdiction and court metadata
   - Citation pattern definitions
   - Validation functions

2. **Storage Organization**
   - GCS path structure: `/legal/[jurisdiction]/[doc_type]/[year]/[case_id]`
   - Pinecone namespaces: `[jurisdiction]-[doc_type]`
   - Neo4j jurisdiction nodes and relationships

3. **Data Integrity**
   - Cross-system consistency checks
   - Jurisdiction validation
   - Citation pattern extraction

## Next Steps

{
    '✅ The multi-jurisdictional organization system is fully set up and ready for use. You can now proceed with data harvesting and processing.' 
    if init_success and test_success else 
    '❌ Some issues were detected during setup or testing. Please review the logs and address the issues before proceeding.'
}

### Recommended Actions

{
    '''
1. Implement automated data harvesting for each jurisdiction
2. Migrate existing data to the new organization structure
3. Update processing workflows to use jurisdiction-specific paths
4. Implement citation analysis across jurisdictions
    '''
    if init_success and test_success else
    '''
1. Review the test logs to identify specific failures
2. Fix any issues with the database schema or storage connectors
3. Re-run the setup and test script
4. Ensure all environment variables are properly set
    '''
}

## Reference Documentation

- [Court Listener Integration Plan](../documentation/COURT_LISTENER_INTEGRATION.md)
- [Jurisdiction Schema](../scripts/create_jurisdiction_tables.sql)
"""
    
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w') as f:
        f.write(report_content)
    
    logger.info(f"Report generated: {report_path}")
    return report_path

def main():
    """Main function to run setup and tests."""
    parser = argparse.ArgumentParser(description="Setup and test multi-jurisdictional organization")
    parser.add_argument("--skip-init", action="store_true", help="Skip initialization")
    parser.add_argument("--skip-tests", action="store_true", help="Skip tests")
    args = parser.parse_args()
    
    init_success = True
    test_success = True
    
    if not args.skip_init:
        init_success = run_initialization()
    else:
        logger.info("Skipping initialization as requested")
    
    if not args.skip_tests:
        if not init_success:
            logger.warning("Initialization failed, but proceeding with tests as requested")
        test_success = run_tests()
    else:
        logger.info("Skipping tests as requested")
    
    report_path = generate_report(init_success, test_success)
    
    print("\n=== SETUP AND TEST SUMMARY ===")
    print(f"Initialization: {'Successful' if init_success else 'Failed'}")
    print(f"Tests: {'Successful' if test_success else 'Failed'}")
    print(f"Report generated: {report_path}")
    
    if init_success and test_success:
        print("\n✅ Multi-jurisdictional organization system is ready for use!")
        return 0
    else:
        print("\n❌ Some issues were detected. Please review the report and logs.")
        return 1

if __name__ == "__main__":
    sys.exit(main())

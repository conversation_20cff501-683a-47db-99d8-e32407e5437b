// Neo4j Indexes for Week 6 Graph Performance
// Run this script to create required indexes for optimal graph query performance

// Document ID indexes for fast lookups
CREATE INDEX case_id_index IF NOT EXISTS FOR (c:Case) ON (c.id);
CREATE INDEX statute_id_index IF NOT EXISTS FOR (s:Statute) ON (s.id);
CREATE INDEX document_id_index IF NOT EXISTS FOR (d:Document) ON (d.document_id);

// Authority score index for ranking
CREATE INDEX document_authority_index IF NOT EXISTS FOR (d:Document) ON (d.authority_score);

// Date indexes for recency calculations
CREATE INDEX document_date_index IF NOT EXISTS FOR (d:Document) ON (d.date);

// Jurisdiction indexes for filtering
CREATE INDEX document_jurisdiction_index IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction);

// Document type indexes for filtering
CREATE INDEX document_type_index IF NOT EXISTS FOR (d:Document) ON (d.doc_type);

// Citation relationship indexes for graph traversal
CREATE INDEX citation_source_index IF NOT EXISTS FOR ()-[r:CITES]-() ON (r.source_id);
CREATE INDEX citation_target_index IF NOT EXISTS FOR ()-[r:CITES]-() ON (r.target_id);

// Practice area indexes for filtering
CREATE INDEX document_practice_areas_index IF NOT EXISTS FOR (d:Document) ON (d.practice_areas);

// Composite indexes for common query patterns
CREATE INDEX document_jurisdiction_type_index IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction, d.doc_type);
CREATE INDEX document_authority_date_index IF NOT EXISTS FOR (d:Document) ON (d.authority_score, d.date);

// Show created indexes
SHOW INDEXES;

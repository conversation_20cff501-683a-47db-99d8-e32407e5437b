#!/usr/bin/env python3
"""
Week 4 Setup Script
Sets up the development environment for Week 4 hybrid search and recommendation APIs.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Week4Setup:
    """Setup manager for Week 4 implementation."""
    
    def __init__(self):
        """Initialize setup manager."""
        self.project_root = Path(__file__).parent.parent
        self.requirements_file = self.project_root / "requirements-week4.txt"
        
    def check_python_version(self):
        """Check Python version compatibility."""
        logger.info("Checking Python version...")
        
        if sys.version_info < (3, 8):
            logger.error("Python 3.8 or higher is required")
            return False
        
        logger.info(f"Python version: {sys.version}")
        return True
    
    def install_dependencies(self):
        """Install Week 4 dependencies."""
        logger.info("Installing Week 4 dependencies...")
        
        try:
            # Install requirements
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
            ], check=True)
            
            logger.info("Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install dependencies: {e}")
            return False
        except FileNotFoundError:
            logger.error(f"Requirements file not found: {self.requirements_file}")
            return False
    
    def setup_environment_file(self):
        """Create or update .env file with Week 4 configuration."""
        logger.info("Setting up environment configuration...")
        
        env_file = self.project_root / ".env"
        env_template = self.project_root / ".env.example"
        
        # Create .env.example if it doesn't exist
        if not env_template.exists():
            self.create_env_template(env_template)
        
        # Create .env if it doesn't exist
        if not env_file.exists():
            logger.info("Creating .env file from template...")
            with open(env_template, 'r') as template:
                content = template.read()
            
            with open(env_file, 'w') as env:
                env.write(content)
            
            logger.info(f"Created .env file at {env_file}")
            logger.warning("Please update .env file with your actual configuration values")
        else:
            logger.info(".env file already exists")
        
        return True
    
    def create_env_template(self, template_path):
        """Create environment template file."""
        template_content = """# Week 4 Environment Configuration

# Cache Configuration
CACHE_BACKEND=memory
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password

# JWT Authentication
SUPABASE_JWKS_URL=https://your-project-ref.supabase.co/auth/v1/keys

# Database Connections
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password

SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_KEY=your_supabase_anon_key

PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment

# Google Cloud Storage
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account.json
GCS_BUCKET_NAME=your_gcs_bucket

# Court Listener API
COURTLISTENER_API_KEY=your_courtlistener_api_key

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Logging
LOG_LEVEL=INFO
"""
        
        with open(template_path, 'w') as f:
            f.write(template_content)
        
        logger.info(f"Created environment template at {template_path}")
    
    def verify_database_connections(self):
        """Verify database connections are working."""
        logger.info("Verifying database connections...")
        
        # Test cache connection
        try:
            from src.cache.cache import cache
            cache.set("setup_test", "ok", 60)
            if cache.get("setup_test") == "ok":
                logger.info("✓ Cache connection: OK")
            else:
                logger.warning("⚠ Cache connection: Failed")
        except Exception as e:
            logger.warning(f"⚠ Cache connection error: {e}")
        
        # Test Neo4j connection
        try:
            from src.processing.storage.neo4j_connector import Neo4jConnector
            neo4j = Neo4jConnector()
            with neo4j.driver.session() as session:
                session.run("RETURN 1")
            logger.info("✓ Neo4j connection: OK")
        except Exception as e:
            logger.warning(f"⚠ Neo4j connection error: {e}")
        
        # Test Supabase connection
        try:
            from src.processing.storage.supabase_connector import SupabaseConnector
            supabase = SupabaseConnector()
            # Basic connection test
            logger.info("✓ Supabase connection: OK")
        except Exception as e:
            logger.warning(f"⚠ Supabase connection error: {e}")
        
        # Test Pinecone connection
        try:
            from src.processing.storage.pinecone_connector import PineconeConnector
            pinecone = PineconeConnector()
            logger.info("✓ Pinecone connection: OK")
        except Exception as e:
            logger.warning(f"⚠ Pinecone connection error: {e}")
        
        return True
    
    def create_directories(self):
        """Create necessary directories for Week 4."""
        logger.info("Creating necessary directories...")
        
        directories = [
            "logs",
            "tests/api",
            "tests/cache",
            "tests/auth",
            "examples",
            "docs"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {dir_path}")
        
        logger.info("Directories created successfully")
        return True
    
    def run_tests(self):
        """Run Week 4 test suite."""
        logger.info("Running Week 4 test suite...")
        
        try:
            # Run pytest
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                "tests/api/test_search_v0.py",
                "-v", "--tb=short"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✓ All tests passed")
                return True
            else:
                logger.warning("⚠ Some tests failed")
                logger.info("Test output:")
                logger.info(result.stdout)
                if result.stderr:
                    logger.error(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return False
    
    def start_development_server(self):
        """Start the development server."""
        logger.info("Starting development server...")
        logger.info("Server will be available at: http://localhost:8000")
        logger.info("API documentation: http://localhost:8000/docs")
        logger.info("Press Ctrl+C to stop the server")
        
        try:
            subprocess.run([
                sys.executable, "-m", "uvicorn",
                "src.api.main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload",
                "--log-level", "info"
            ], cwd=self.project_root)
        except KeyboardInterrupt:
            logger.info("Server stopped")
    
    def run_setup(self, start_server=False, run_tests=False):
        """Run complete setup process."""
        logger.info("Starting Week 4 setup...")
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Creating directories", self.create_directories),
            ("Installing dependencies", self.install_dependencies),
            ("Setting up environment", self.setup_environment_file),
            ("Verifying connections", self.verify_database_connections),
        ]
        
        if run_tests:
            steps.append(("Running tests", self.run_tests))
        
        # Execute setup steps
        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"Setup failed at step: {step_name}")
                return False
        
        logger.info("✓ Week 4 setup completed successfully!")
        
        # Print next steps
        self.print_next_steps()
        
        if start_server:
            self.start_development_server()
        
        return True
    
    def print_next_steps(self):
        """Print next steps for the user."""
        logger.info("\n" + "="*60)
        logger.info("WEEK 4 SETUP COMPLETE")
        logger.info("="*60)
        logger.info("\nNext steps:")
        logger.info("1. Update .env file with your actual configuration values")
        logger.info("2. Ensure all database services are running:")
        logger.info("   - Neo4j (bolt://localhost:7687)")
        logger.info("   - Redis (if using CACHE_BACKEND=redis)")
        logger.info("3. Start the development server:")
        logger.info("   python scripts/setup_week4.py --start-server")
        logger.info("4. Test the API:")
        logger.info("   curl http://localhost:8000/health")
        logger.info("5. View API documentation:")
        logger.info("   http://localhost:8000/docs")
        logger.info("\nWeek 4 Features Ready:")
        logger.info("✓ Hybrid search API (/v0/search)")
        logger.info("✓ Recommendation API (/v0/recommend)")
        logger.info("✓ Graph data API (/v0/graph)")
        logger.info("✓ JWT authentication")
        logger.info("✓ Rate limiting")
        logger.info("✓ Response caching")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Week 4 setup script")
    parser.add_argument("--start-server", action="store_true", help="Start development server after setup")
    parser.add_argument("--run-tests", action="store_true", help="Run test suite during setup")
    parser.add_argument("--server-only", action="store_true", help="Only start the server (skip setup)")
    
    args = parser.parse_args()
    
    setup = Week4Setup()
    
    if args.server_only:
        setup.start_development_server()
    else:
        success = setup.run_setup(
            start_server=args.start_server,
            run_tests=args.run_tests
        )
        
        if not success:
            sys.exit(1)


if __name__ == "__main__":
    main()

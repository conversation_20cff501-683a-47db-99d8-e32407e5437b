#!/usr/bin/env python3
"""
Start Automated Case Harvesting System

This script starts the automated case harvesting system with proper
configuration validation and monitoring setup.
"""

import os
import sys
import logging
import argparse
import signal
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.harvesting.automated_harvester import AutomatedHarvester, get_harvester
from src.harvesting.harvesting_config import get_harvesting_config, reload_harvesting_config


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/harvester.log', mode='a')
        ]
    )


def validate_environment():
    """Validate required environment variables"""
    required_vars = [
        "COURTLISTENER_API_KEY",
        "SUPABASE_URL",
        "SUPABASE_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    return True


def validate_configuration():
    """Validate harvesting configuration"""
    try:
        config = get_harvesting_config()
        
        if not config.enabled:
            print("Warning: Harvesting is disabled in configuration")
            return False
        
        enabled_jurisdictions = config.get_enabled_jurisdictions()
        if not enabled_jurisdictions:
            print("Error: No jurisdictions are enabled for harvesting")
            return False
        
        print(f"Configuration valid. Enabled jurisdictions: {', '.join(enabled_jurisdictions)}")
        return True
        
    except Exception as e:
        print(f"Error: Invalid configuration: {str(e)}")
        return False


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print(f"\nReceived signal {signum}. Shutting down harvester...")
    
    try:
        harvester = get_harvester()
        harvester.stop()
        print("Harvester stopped successfully")
    except Exception as e:
        print(f"Error stopping harvester: {str(e)}")
    
    sys.exit(0)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Start Automated Case Harvesting System")
    parser.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
        default="INFO",
        help="Set logging level"
    )
    parser.add_argument(
        "--config-path",
        help="Path to harvesting configuration file"
    )
    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="Only validate configuration and environment, don't start harvester"
    )
    parser.add_argument(
        "--harvest-now",
        help="Immediately harvest a specific jurisdiction (e.g., 'tx')"
    )
    parser.add_argument(
        "--harvest-all-now",
        action="store_true",
        help="Immediately harvest all enabled jurisdictions"
    )
    parser.add_argument(
        "--status",
        action="store_true",
        help="Show harvester status and exit"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    logger.info("Starting automated case harvesting system")
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    # Validate configuration
    if not validate_configuration():
        sys.exit(1)
    
    if args.validate_only:
        print("Validation successful. Configuration and environment are valid.")
        sys.exit(0)
    
    try:
        # Initialize harvester
        harvester = get_harvester()
        
        # Handle status request
        if args.status:
            status = harvester.get_status()
            print("\n=== Harvester Status ===")
            print(f"Running: {status['is_running']}")
            print(f"Scheduler Available: {status['scheduler_available']}")
            print(f"Config Enabled: {status['config_enabled']}")
            print(f"Total Jurisdictions: {status['stats']['total_jurisdictions']}")
            print(f"Active Jurisdictions: {status['stats']['active_jurisdictions']}")
            print(f"Total Harvests: {status['stats']['total_harvests']}")
            print(f"Success Rate: {status['stats']['success_rate']:.2%}")
            print(f"Cases Processed: {status['stats']['total_cases_processed']}")
            print(f"Cases Successful: {status['stats']['total_cases_successful']}")
            
            if status['stats']['last_harvest_time']:
                print(f"Last Harvest: {status['stats']['last_harvest_time']}")
            
            print("\n=== Jurisdiction Status ===")
            for jurisdiction, info in status['jurisdictions'].items():
                if 'error' in info:
                    print(f"{jurisdiction}: ERROR - {info['error']}")
                else:
                    print(f"{jurisdiction}: {info['name']} - "
                          f"Priority: {info['priority']}, "
                          f"Courts: {info['courts']}, "
                          f"Max Cases: {info['max_cases_per_run']}")
            
            sys.exit(0)
        
        # Handle immediate harvest requests
        if args.harvest_now:
            jurisdiction = args.harvest_now.lower()
            print(f"Starting immediate harvest for jurisdiction: {jurisdiction}")
            
            result = harvester.harvest_jurisdiction_now(jurisdiction)
            
            print(f"\n=== Harvest Results for {jurisdiction} ===")
            print(f"Duration: {result.duration_seconds:.1f} seconds")
            print(f"Cases Found: {result.total_found}")
            print(f"Cases Processed: {result.total_processed}")
            print(f"Successful: {result.total_success}")
            print(f"Failed: {result.total_failed}")
            print(f"Skipped: {result.total_skipped}")
            print(f"Success Rate: {result.success_rate:.2%}")
            
            if result.errors:
                print(f"\nErrors ({len(result.errors)}):")
                for error in result.errors[-5:]:  # Show last 5 errors
                    print(f"  - {error}")
            
            sys.exit(0)
        
        if args.harvest_all_now:
            print("Starting immediate harvest for all enabled jurisdictions")
            
            results = harvester.harvest_all_now()
            
            print(f"\n=== Harvest Results for All Jurisdictions ===")
            total_processed = sum(r.total_processed for r in results.values())
            total_successful = sum(r.total_success for r in results.values())
            
            print(f"Jurisdictions: {len(results)}")
            print(f"Total Cases Processed: {total_processed}")
            print(f"Total Successful: {total_successful}")
            print(f"Overall Success Rate: {total_successful/total_processed:.2%}" if total_processed > 0 else "N/A")
            
            for jurisdiction, result in results.items():
                print(f"\n{jurisdiction}:")
                print(f"  Processed: {result.total_processed}")
                print(f"  Successful: {result.total_success}")
                print(f"  Success Rate: {result.success_rate:.2%}")
                print(f"  Duration: {result.duration_seconds:.1f}s")
            
            sys.exit(0)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start the harvester
        harvester.start()
        
        print("Automated harvesting system started successfully")
        print("Press Ctrl+C to stop the harvester")
        
        # Keep the main thread alive
        try:
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
        
    except Exception as e:
        logger.error(f"Failed to start harvester: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

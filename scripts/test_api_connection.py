"""
Test script to verify Court Listener API connection
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_env_variables():
    """Test if environment variables are loaded correctly"""
    # Load .env file
    load_dotenv()
    
    # Check if API key exists (don't print the actual key)
    api_key = os.getenv('COURTLISTENER_API_KEY')
    if api_key:
        logger.info("✅ COURTLISTENER_API_KEY is set")
        # Print length to verify it's a reasonable key without exposing it
        logger.info(f"   Key length: {len(api_key)} characters")
    else:
        logger.error("❌ COURTLISTENER_API_KEY is not set")
        logger.info("Please add COURTLISTENER_API_KEY=your_api_key to your .env file")
        
    return bool(api_key)

def test_api_connection():
    """Test basic connection to Court Listener API"""
    try:
        from src.api.courtlistener.client import CourtListenerClient
        import requests
        
        client = CourtListenerClient()
        
        # Test a simple API call to list courts (this requires minimal permissions)
        logger.info("Testing connection to Court Listener API...")
        courts = client.get_courts()
        
        # Get available jurisdictions directly from the API
        logger.info("Fetching available jurisdictions...")
        headers = {"Authorization": f"Token {os.getenv('COURTLISTENER_API_KEY')}"}
        response = requests.get("https://www.courtlistener.com/api/rest/v3/courts/", headers=headers)
        
        if response.status_code == 200:
            jurisdictions = set()
            for court in response.json().get('results', []):
                if 'jurisdiction' in court and court['jurisdiction']:
                    jurisdictions.add(court['jurisdiction'])
            
            logger.info(f"Available jurisdictions: {sorted(list(jurisdictions))}")
            
            # Check for Texas specifically
            texas_courts = [court for court in response.json().get('results', []) 
                          if 'name' in court and 'texas' in court['name'].lower()]
            if texas_courts:
                logger.info(f"Found {len(texas_courts)} Texas courts")
                logger.info(f"Example Texas court: {texas_courts[0]['name']} - Jurisdiction: {texas_courts[0]['jurisdiction']}")
        
        if courts:
            logger.info(f"✅ Successfully connected to Court Listener API")
            logger.info(f"   Retrieved {len(courts)} courts")
            return True
        else:
            logger.error("❌ Connected to API but received empty response")
            return False
            
    except Exception as e:
        logger.error(f"❌ Connection test failed: {str(e)}")
        return False
        
def test_texas_client():
    """Test the Texas-specific client"""
    try:
        from src.api.courtlistener.texas import TexasCaseClient
        from src.api.courtlistener.client import CourtListenerClient
        import requests
        
        # First, list all courts to see what's available
        logger.info("Fetching all courts to find Texas courts...")
        headers = {"Authorization": f"Token {os.getenv('COURTLISTENER_API_KEY')}"}
        response = requests.get("https://www.courtlistener.com/api/rest/v3/courts/", headers=headers, params={"page_size": 300})
        
        if response.status_code == 200:
            all_courts = response.json().get('results', [])
            logger.info(f"Retrieved {len(all_courts)} courts")
            
            # Look for Texas-related courts
            texas_courts = []
            for court in all_courts:
                if 'name' in court and court['name'] and ('texas' in court['name'].lower() or 
                                                       'tex' in court['name'].lower() or
                                                       'tx' in court['name'].lower()):
                    texas_courts.append(court)
            
            if texas_courts:
                logger.info(f"Found {len(texas_courts)} Texas-related courts in raw API response:")
                for i, court in enumerate(texas_courts[:5], 1):
                    logger.info(f"   API Court {i}: {court['name']} (ID: {court.get('id')})")
        
        # Now test our client
        client = TexasCaseClient()
        logger.info("Testing Texas-specific client...")
        
        # Test getting Texas courts
        courts = client.get_texas_courts(True)  # Force refresh
        if courts:
            logger.info(f"✅ Successfully retrieved {len(courts)} Texas courts")
            # Print first few courts for verification
            for i, court in enumerate(courts[:3], 1):
                logger.info(f"   Court {i}: {court.name}")
            return True
        else:
            # If no courts found with our client, try a direct search
            logger.warning("No courts found with our client, trying direct search...")
            simple_client = CourtListenerClient()
            search_results = simple_client.search_cases(query="texas", page_size=5)
            
            if isinstance(search_results, dict) and 'results' in search_results and search_results['results']:
                logger.info(f"Direct search found {len(search_results['results'])} Texas cases")
                return True
            else:
                logger.error("❌ Texas client test failed - no courts or cases found")
                return False
            
    except Exception as e:
        logger.error(f"❌ Texas client test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("\n=== COURT LISTENER API CONNECTION TEST ===\n")
    
    # Test environment variables
    env_test_passed = test_env_variables()
    
    if not env_test_passed:
        print("\n❌ API key test failed. Please check your .env file.")
        print("Add COURTLISTENER_API_KEY=your_api_key to your .env file")
        return
    
    # Test API connection
    api_test_passed = test_api_connection()
    
    if not api_test_passed:
        print("\n❌ API connection test failed.")
        print("Please check your internet connection and API key validity")
        return
    
    # Test Texas client
    texas_test_passed = test_texas_client()
    
    # Summary
    print("\n=== TEST SUMMARY ===")
    print(f"Environment variables: {'✅ PASSED' if env_test_passed else '❌ FAILED'}")
    print(f"API connection: {'✅ PASSED' if api_test_passed else '❌ FAILED'}")
    print(f"Texas client: {'✅ PASSED' if texas_test_passed else '❌ FAILED'}")
    
    if env_test_passed and api_test_passed and texas_test_passed:
        print("\n✅ All tests passed! Your Court Listener API is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the logs above for details.")

if __name__ == "__main__":
    main()

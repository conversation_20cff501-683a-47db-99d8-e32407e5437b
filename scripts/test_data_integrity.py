#!/usr/bin/env python
"""
Data Integrity Test Script

This script tests the case law processor with real data and verifies data integrity
across all data stores (GCS, Supabase, Pinecone, Neo4j).
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Load environment variables right at the start
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
loaded_dotenv = load_dotenv(dotenv_path=dotenv_path)
print(f"DEBUG: load_dotenv from '{dotenv_path}' returned: {loaded_dotenv}")

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.case_law_processor_simple import CaseLawProcessor
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.gcs_connector import GCSConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.neo4j_connector import Neo4jConnector

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_integrity_test.log')
    ]
)
logger = logging.getLogger(__name__)


class DataIntegrityTester:
    """Tests data integrity across all data stores."""

    def __init__(self, user_role: str = "partner", tenant_id: str = None):
        """Initialize the tester with the given role."""
        # Ensure processor's logger is set to DEBUG
        logging.getLogger('src.processing.case_law_processor').setLevel(logging.DEBUG)
        
        self.processor = CaseLawProcessor(
            user_id="test_user",
            user_role=user_role,
            tenant_id=tenant_id
        )
        
        # Direct access to connectors for verification
        self.supabase = self.processor.supabase
        self.gcs = self.processor.gcs
        self.pinecone = self.processor.pinecone
        self.neo4j = self.processor.neo4j
        
        # Track processed cases for verification
        self.processed_cases = []
        self.processed_citations = []

    def process_test_data(self, jurisdiction: str, query: str, limit: int = 5):
        """Process a small set of test data."""
        logger.info(f"Processing test data for {jurisdiction} with query '{query}'")
        
        result = self.processor.process_jurisdiction(
            jurisdiction=jurisdiction,
            query=query,
            count=limit,
            source="court_listener"  # Use Court Listener as the source
        )
        
        logger.info(f"Processing result: {json.dumps(result, indent=2)}")
        
        if result.get("status") == "error":
            logger.error(f"Error processing jurisdiction: {result.get('error')}")
            return False
        
        # Update to use the correct key for processed case IDs
        self.processed_cases = result.get("processed_case_ids", []) 
        logger.info(f"Processed {len(self.processed_cases)} cases")
        
        return len(self.processed_cases) > 0

    def verify_supabase_data(self):
        """Verify that data was correctly stored in Supabase."""
        logger.info("Verifying Supabase data...")
        
        success_count = 0
        for case_id in self.processed_cases:
            found_case = False
            
            try:
                # Try with original ID
                try:
                    original_case = self.supabase.get_case(case_id)
                    if original_case:
                        logger.info(f"Found case in Supabase with original ID: {case_id}")
                        success_count += 1
                        found_case = True
                        continue
                except Exception as e:
                    logger.warning(f"Error checking original case ID {case_id}: {str(e)}")
                
                # Try with cl_ prefix
                if not found_case:
                    try:
                        cl_prefixed_id = f"cl_{case_id}"
                        cl_case = self.supabase.get_case(cl_prefixed_id)
                        if cl_case:
                            logger.info(f"Found case in Supabase with cl-prefixed ID: {cl_prefixed_id}")
                            success_count += 1
                            found_case = True
                            continue
                    except Exception as e:
                        logger.warning(f"Error checking cl-prefixed case ID {cl_prefixed_id}: {str(e)}")
                
                # Try source_id lookup as last resort
                if not found_case:
                    try:
                        # Use safer approach with retries
                        from time import sleep
                        max_retries = 3
                        retry_count = 0
                        
                        while retry_count < max_retries:
                            try:
                                query_result = self.supabase.client.table("cases").select("*").eq("source_id", f"cl_{case_id}").execute()
                                cases_by_source = getattr(query_result, 'data', [])
                                
                                if cases_by_source:
                                    logger.info(f"Found case in Supabase by source_id: cl_{case_id}")
                                    success_count += 1
                                    found_case = True
                                break  # Success, exit the retry loop
                            except Exception as inner_e:
                                logger.warning(f"Retry {retry_count+1}/{max_retries} failed for source_id lookup: {str(inner_e)}")
                                retry_count += 1
                                if retry_count < max_retries:
                                    sleep(1)  # Wait before retrying
                    except Exception as e:
                        logger.warning(f"Error during source_id lookup for case {case_id}: {str(e)}")
                
                if not found_case:
                    logger.error(f"Case not found in Supabase with any ID variation: {case_id}")
                    
            except Exception as e:
                logger.error(f"Unexpected error verifying case {case_id} in Supabase: {str(e)}")
        
        success_rate = success_count / len(self.processed_cases) if self.processed_cases else 0
        logger.info(f"Supabase verification success rate: {success_rate:.2%}")
        
        return success_rate

    def verify_gcs_data(self):
        """Verify that data was correctly stored in GCS."""
        logger.info("Verifying GCS data...")
        
        try:
            # Import the GCS helper for testing with the new path structure
            from src.processing.storage.gcs_helper import retrieve_case_document, retrieve_case_json
        except ImportError:
            logger.warning("Could not import GCS helper module, falling back to direct GCS access")
        
        text_success_count = 0
        metadata_success_count = 0
        overall_success_count = 0
        
        for case_id in self.processed_cases:
            text_found = False
            metadata_found = False
            
            # Get the case from Supabase to get the GCS paths
            case = self.supabase.get_case(case_id)
            if not case:
                logger.error(f"Case {case_id} not found in Supabase")
                continue
            
            # Check for text content using new path structure
            if case.get("text_path"):
                # Use helper if available
                if 'retrieve_case_document' in locals():
                    # Try using the helper first
                    text_content = retrieve_case_document(case_id=case_id, jurisdiction="tx")
                else:
                    # Fall back to direct GCS access
                    text_content = self.gcs.get_text(case.get("text_path"))
                
                if text_content:
                    logger.info(f"Found text content in GCS for case: {case_id}")
                    text_found = True
                    text_success_count += 1
                else:
                    logger.error(f"Text content not found in GCS for case: {case_id}")
            # Check old path structure as fallback
            elif case.get("gcs_path"):
                logger.info(f"Found legacy GCS path for case: {case_id}")
                text_content = self.gcs.get_text(case.get("gcs_path"))
                if text_content:
                    logger.info(f"Found text content in GCS (legacy path) for case: {case_id}")
                    text_found = True
                    text_success_count += 1
                else:
                    logger.error(f"Text content not found in GCS (legacy path) for case: {case_id}")
            else:
                logger.error(f"Case {case_id} has no GCS text path in Supabase")
            
            # Check for metadata content
            if case.get("metadata_path"):
                # Use helper if available
                if 'retrieve_case_json' in locals():
                    # Try using the helper first
                    metadata = retrieve_case_json(case_id=case_id, jurisdiction="tx")
                else:
                    # Fall back to direct GCS access
                    metadata = self.gcs.get_json(case.get("metadata_path"))
                
                if metadata:
                    logger.info(f"Found metadata in GCS for case: {case_id}")
                    metadata_found = True
                    metadata_success_count += 1
                else:
                    logger.warning(f"Metadata not found in GCS for case: {case_id}")
            else:
                logger.warning(f"Case {case_id} has no GCS metadata path in Supabase")
            
            # Count overall success if either text or metadata was found
            # Text is more important as that's the primary document
            if text_found:
                overall_success_count += 1
        
        # Calculate success rates
        text_success_rate = text_success_count / len(self.processed_cases) if self.processed_cases else 0
        metadata_success_rate = metadata_success_count / len(self.processed_cases) if self.processed_cases else 0
        overall_success_rate = overall_success_count / len(self.processed_cases) if self.processed_cases else 0
        
        logger.info(f"GCS text verification success rate: {text_success_rate:.2%}")
        logger.info(f"GCS metadata verification success rate: {metadata_success_rate:.2%}")
        logger.info(f"GCS overall verification success rate: {overall_success_rate:.2%}")
        
        return overall_success_rate

    def verify_pinecone_data(self):
        """Verify that data was correctly stored in Pinecone."""
        logger.info("Verifying Pinecone data...")
        
        success_count = 0
        for case_id in self.processed_cases:
            try:
                # Query for any vector chunk associated with this case_id
                query_results = self.pinecone.query(
                    query_text="", # Dummy text, we only care about filter
                    top_k=1, 
                    filter={"case_id": case_id}
                )
                
                # Check if any matches were found
                if query_results and len(query_results) > 0:
                    logger.info(f"Found vector(s) in Pinecone for case: {case_id}")
                    success_count += 1
                else:
                    logger.error(f"No vector found in Pinecone for case: {case_id}")
                    
            except Exception as e:
                logger.error(f"Error verifying Pinecone data for case {case_id}: {e}")
        
        success_rate = success_count / len(self.processed_cases) if self.processed_cases else 0
        logger.info(f"Pinecone verification success rate: {success_rate:.2%}")
        
        return success_rate

    def verify_neo4j_data(self):
        """Verify that data was correctly stored in Neo4j."""
        logger.info("Verifying Neo4j data...")
        
        success_count = 0
        for case_id in self.processed_cases:
            try:
                # Check if the case node exists
                case_exists = self.neo4j.check_case_exists(case_id)
                
                if case_exists:
                    logger.info(f"Found node in Neo4j for case: {case_id}")
                    success_count += 1
                    
                    # Also check citations
                    citations = self.neo4j.get_case_citations(case_id)
                    if citations:
                        logger.info(f"Found {len(citations)} citations in Neo4j for case: {case_id}")
                        self.processed_citations.extend(citations)
                    else:
                        logger.warning(f"No citations found in Neo4j for case: {case_id}")
            except Exception as e:
                logger.error(f"Error verifying Neo4j data for case {case_id}: {e}")
        
        success_rate = success_count / len(self.processed_cases) if self.processed_cases else 0
        logger.info(f"Neo4j verification success rate: {success_rate:.2%}")
        
        return success_rate

    def verify_cross_database_consistency(self):
        """Verify consistency of data across all databases."""
        logger.info("Verifying cross-database consistency...")
        from time import sleep
        
        consistency_issues = []
        
        for case_id in self.processed_cases:
            # Wrap all database operations in try/except blocks
            try:
                # Try various ID formats for Supabase lookup
                supabase_case = None
                found_in_supabase = False
                
                # Try original ID format
                try:
                    supabase_case = self.supabase.get_case(case_id)
                    if supabase_case:
                        found_in_supabase = True
                        logger.info(f"Found case {case_id} in Supabase with original ID")
                except Exception as e:
                    logger.warning(f"Error getting case with original ID {case_id}: {str(e)}")
                
                # Try cl_ prefixed ID format
                if not found_in_supabase:
                    try:
                        cl_prefixed_id = f"cl_{case_id}"
                        supabase_case = self.supabase.get_case(cl_prefixed_id)
                        if supabase_case:
                            found_in_supabase = True
                            logger.info(f"Found case {case_id} in Supabase with cl-prefixed ID: {cl_prefixed_id}")
                    except Exception as e:
                        logger.warning(f"Error getting case with cl-prefixed ID {cl_prefixed_id}: {str(e)}")
                
                # Try source_id lookup as last resort
                if not found_in_supabase:
                    max_retries = 3
                    retry_count = 0
                    
                    while retry_count < max_retries and not found_in_supabase:
                        try:
                            query_result = self.supabase.client.table("cases").select("*").eq("source_id", f"cl_{case_id}").execute()
                            cases_by_source = getattr(query_result, 'data', [])
                            if cases_by_source:
                                supabase_case = cases_by_source[0]
                                found_in_supabase = True
                                logger.info(f"Found case {case_id} in Supabase by source_id lookup")
                            break  # Success or no match, exit retry loop
                        except Exception as e:
                            logger.warning(f"Retry {retry_count+1}/{max_retries} for source_id lookup failed: {str(e)}")
                            retry_count += 1
                            if retry_count < max_retries:
                                sleep(1)  # Wait before retry
            
                if not supabase_case:
                    consistency_issues.append(f"Case {case_id} missing from Supabase")
                    continue
                
                # Store the actual ID used in Supabase for other checks
                stored_id = supabase_case.get("id", case_id)
            except Exception as e:
                logger.error(f"Error during Supabase verification for case {case_id}: {str(e)}")
                consistency_issues.append(f"Error verifying case {case_id} in Supabase: {str(e)}")
                continue
            
            try:
                # Check GCS
                gcs_path = supabase_case.get("gcs_path")
                if not gcs_path:
                    consistency_issues.append(f"Case {case_id} has no GCS path in Supabase")
                else:
                    try:
                        gcs_content = self.gcs.get_text(gcs_path)
                        if not gcs_content:
                            consistency_issues.append(f"Case {case_id} document missing from GCS")
                    except Exception as gcs_error:
                        logger.error(f"Error getting GCS content for case {case_id}: {str(gcs_error)}")
                        consistency_issues.append(f"Error retrieving GCS content for case {case_id}: {str(gcs_error)}")
            except Exception as e:
                logger.error(f"Error checking GCS for case {case_id}: {str(e)}")
                consistency_issues.append(f"Error checking GCS for case {case_id}: {str(e)}")
            
            try:
                # Check Pinecone with multiple ID formats
                pinecone_found = False
                for search_id in [case_id, stored_id, f"cl_{case_id}"]:
                    if not search_id:
                        continue
                    
                    try:
                        pinecone_results = self.pinecone.query(
                            query_text="", # Dummy query text
                            top_k=1, 
                            filter={"case_id": search_id}
                        )
                        
                        if pinecone_results and len(pinecone_results) > 0:
                            logger.info(f"Found case in Pinecone with ID: {search_id}")
                            pinecone_found = True
                            break
                    except Exception as pinecone_error:
                        logger.warning(f"Error querying Pinecone with ID {search_id}: {str(pinecone_error)}")
                        continue  # Try the next ID format
                    
                if not pinecone_found:
                    consistency_issues.append(f"Case {case_id} embeddings missing from Pinecone")
            except Exception as e:
                logger.error(f"Error checking Pinecone for case {case_id}: {str(e)}")
                consistency_issues.append(f"Error checking Pinecone for case {case_id}: {str(e)}")
            
            try:
                # Check Neo4j with multiple ID formats
                neo4j_found = False
                for search_id in [case_id, stored_id, f"cl_{case_id}"]:
                    if not search_id:
                        continue
                    
                    try:
                        neo4j_case = self.neo4j.check_case_exists(search_id)
                        if neo4j_case:
                            logger.info(f"Found case in Neo4j with ID: {search_id}")
                            neo4j_found = True
                            break
                    except Exception as neo4j_error:
                        logger.warning(f"Error checking Neo4j with ID {search_id}: {str(neo4j_error)}")
                        continue  # Try the next ID format
                        
                if not neo4j_found:
                    consistency_issues.append(f"Case {case_id} node missing from Neo4j")
            except Exception as e:
                logger.error(f"Error checking Neo4j for case {case_id}: {str(e)}")
                consistency_issues.append(f"Error checking Neo4j for case {case_id}: {str(e)}")
            
            try:
                # Check citation counts using the ID that worked for Neo4j
                supabase_citation_count = supabase_case.get("citation_count", 0)
                
                # Try multiple ID formats for citation lookup
                neo4j_citation_count = 0
                citation_lookup_success = False
                
                for search_id in [case_id, stored_id, f"cl_{case_id}"]:
                    if not search_id:
                        continue
                    
                    try:
                        neo4j_citations = self.neo4j.get_case_citations(search_id)
                        if neo4j_citations:
                            neo4j_citation_count = len(neo4j_citations)
                            logger.info(f"Found {neo4j_citation_count} citations in Neo4j for ID: {search_id}")
                            citation_lookup_success = True
                            break
                    except Exception as neo4j_citation_error:
                        logger.warning(f"Error getting Neo4j citations for ID {search_id}: {str(neo4j_citation_error)}")
                        continue
                
                # Only compare if we successfully got citation data
                if citation_lookup_success:
                    # Compare the citation counts, allowing for some flexibility
                    # Sometimes metadata from Court Listener may report a different count
                    # than what we actually extract during processing
                    count_difference = abs(supabase_citation_count - neo4j_citation_count)
                    if count_difference > 5:  # Allow small differences without flagging as error
                        consistency_issues.append(
                            f"Citation count difference for case {case_id}: "
                            f"Supabase={supabase_citation_count}, Neo4j={neo4j_citation_count}, Diff={count_difference}"
                        )
                    else:
                        logger.info(f"Citation counts match within tolerance for case {case_id}")
                else:
                    logger.warning(f"Could not retrieve citation data for case {case_id} from Neo4j")
            except Exception as e:
                logger.error(f"Error comparing citation counts for case {case_id}: {str(e)}")
                consistency_issues.append(f"Error comparing citation counts for case {case_id}: {str(e)}")
        
        if consistency_issues:
            logger.error("Consistency issues found:")
            for issue in consistency_issues:
                logger.error(f"  - {issue}")
        else:
            logger.info("No consistency issues found across databases")
        
        return consistency_issues

    def run_full_test(self, jurisdiction: str, query: str, limit: int = 5):
        """Run a full data integrity test."""
        logger.info("=" * 80)
        logger.info(f"Starting data integrity test at {datetime.now().isoformat()}")
        logger.info(f"User role: {self.processor.user_role}")
        logger.info(f"Tenant ID: {self.processor.tenant_id}")
        logger.info("=" * 80)
        
        # Process test data
        if not self.process_test_data(jurisdiction, query, limit):
            logger.error("Failed to process test data. Aborting test.")
            return False
        
        # Verify data in each store
        supabase_rate = self.verify_supabase_data()
        gcs_rate = self.verify_gcs_data()
        pinecone_rate = self.verify_pinecone_data() 
        neo4j_rate = self.verify_neo4j_data()
        
        # Verify cross-database consistency
        consistency_issues = self.verify_cross_database_consistency()
        
        # Print summary
        logger.info("=" * 80)
        logger.info("DATA INTEGRITY TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Processed cases: {len(self.processed_cases)}")
        logger.info(f"Supabase success rate: {supabase_rate:.2%}")
        logger.info(f"GCS success rate: {gcs_rate:.2%}")
        logger.info(f"Pinecone success rate: {pinecone_rate:.2%}")
        logger.info(f"Neo4j success rate: {neo4j_rate:.2%}")
        logger.info(f"Consistency issues: {len(consistency_issues)}")
        logger.info("=" * 80)
        
        overall_success = (
            supabase_rate > 0.8 and
            gcs_rate > 0.8 and
            pinecone_rate > 0.8 and
            neo4j_rate > 0.8 and
            len(consistency_issues) == 0
        )
        
        logger.info(f"Overall test result: {'SUCCESS' if overall_success else 'FAILURE'}")
        
        return overall_success


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test data integrity across databases")
    parser.add_argument("--role", default="partner", help="User role for testing")
    parser.add_argument("--tenant", help="Tenant ID for testing")
    parser.add_argument("--jurisdiction", default="tx", help="Jurisdiction to test")
    parser.add_argument("--query", default="negligence", help="Search query for test data")
    parser.add_argument("--limit", type=int, default=5, help="Number of cases to process")
    
    args = parser.parse_args()
    
    tester = DataIntegrityTester(
        user_role=args.role,
        tenant_id=args.tenant
    )
    
    success = tester.run_full_test(
        jurisdiction=args.jurisdiction,
        query=args.query,
        limit=args.limit
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
Database Connection Test Script

This script tests connections to all databases used in the case law processing system
without requiring the actual tables to exist.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def test_supabase_connection():
    """Test connection to Supabase."""
    from src.processing.storage.supabase_connector import SupabaseConnector
    
    logger.info("Testing Supabase connection...")
    try:
        # Initialize the connector but don't check for tables
        supabase = SupabaseConnector()
        
        # Test a simple query that doesn't require specific tables
        response = supabase.client.rpc('get_service_role').execute()
        logger.info(f"Supabase connection successful")
        return True
    except Exception as e:
        logger.error(f"Supabase connection failed: {str(e)}")
        return False

def test_gcs_connection():
    """Test connection to Google Cloud Storage."""
    from src.processing.storage.gcs_connector import GCSConnector
    
    logger.info("Testing GCS connection...")
    try:
        bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        gcs = GCSConnector(bucket_name=bucket_name)
        
        # List buckets to test connection
        buckets = gcs.client.list_buckets()
        bucket_names = [bucket.name for bucket in buckets]
        
        if bucket_name in bucket_names:
            logger.info(f"GCS connection successful, bucket '{bucket_name}' exists")
        else:
            logger.warning(f"GCS connection successful, but bucket '{bucket_name}' not found")
            logger.info(f"Available buckets: {bucket_names}")
        
        return True
    except Exception as e:
        logger.error(f"GCS connection failed: {str(e)}")
        return False

def test_pinecone_connection():
    """Test connection to Pinecone vector database."""
    from src.processing.storage.pinecone_connector import PineconeConnector
    
    logger.info("Testing Pinecone connection...")
    try:
        index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
        pinecone = PineconeConnector(index_name=index_name)
        
        # Get index stats to test connection
        stats = pinecone.index.describe_index_stats()
        
        logger.info(f"Pinecone connection successful")
        logger.info(f"Index stats: {stats}")
        return True
    except Exception as e:
        logger.error(f"Pinecone connection failed: {str(e)}")
        return False

def test_neo4j_connection():
    """Test connection to Neo4j graph database."""
    from src.processing.storage.neo4j_connector import Neo4jConnector
    
    logger.info("Testing Neo4j connection...")
    try:
        neo4j = Neo4jConnector()
        
        # Run a simple query to test connection
        result = neo4j.run_query("MATCH (n) RETURN count(n) as count")
        node_count = result[0]["count"] if result else 0
        
        logger.info(f"Neo4j connection successful")
        logger.info(f"Database contains {node_count} nodes")
        return True
    except Exception as e:
        logger.error(f"Neo4j connection failed: {str(e)}")
        return False

def main():
    """Run all database connection tests."""
    logger.info("=" * 80)
    logger.info("DATABASE CONNECTION TESTS")
    logger.info("=" * 80)
    
    results = {
        "Supabase": test_supabase_connection(),
        "GCS": test_gcs_connection(),
        "Pinecone": test_pinecone_connection(),
        "Neo4j": test_neo4j_connection()
    }
    
    logger.info("=" * 80)
    logger.info("TEST RESULTS")
    logger.info("=" * 80)
    
    all_successful = True
    for db_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{db_name}: {status}")
        if not success:
            all_successful = False
    
    logger.info("=" * 80)
    logger.info(f"OVERALL RESULT: {'✅ ALL TESTS PASSED' if all_successful else '❌ SOME TESTS FAILED'}")
    logger.info("=" * 80)
    
    return all_successful

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test Multi-Jurisdictional Organization

This script tests the multi-jurisdictional organization implementation by:
1. Verifying jurisdiction metadata tables in Supabase
2. Testing jurisdiction-specific GCS paths
3. Verifying Pinecone namespaces
4. Testing Neo4j jurisdiction nodes and relationships
5. Performing cross-storage system verification

Usage:
    python test_multi_jurisdiction.py

Environment variables required:
    SUPABASE_URL: Supabase project URL
    SUPABASE_KEY: Supabase service role key
    NEO4J_URI: Neo4j database URI
    NEO4J_USERNAME: Neo4j username
    NEO4J_PASSWORD: Neo4j password
    PINECONE_API_KEY: Pinecone API key
    PINECONE_ENVIRONMENT: Pinecone environment
    GCS_BUCKET_NAME: Google Cloud Storage bucket name
    GCS_SERVICE_ACCOUNT_FILE: Path to GCS service account file
"""

import os
import sys
import json
import logging
import argparse
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.neo4j_connector import Neo4jConnector
from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.gcs_connector import GCSConnector

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class MultiJurisdictionTester:
    """Test the multi-jurisdictional organization implementation."""
    
    def __init__(self):
        """Initialize the tester with storage connectors."""
        self.supabase = SupabaseConnector()
        self.neo4j = Neo4jConnector()
        self.pinecone = PineconeConnector()
        self.gcs = GCSConnector()
        self.test_results = {
            "supabase": {"passed": 0, "failed": 0, "details": []},
            "neo4j": {"passed": 0, "failed": 0, "details": []},
            "pinecone": {"passed": 0, "failed": 0, "details": []},
            "gcs": {"passed": 0, "failed": 0, "details": []},
            "cross_system": {"passed": 0, "failed": 0, "details": []}
        }
    
    def cleanup(self):
        """Clean up resources."""
        self.neo4j.close()
    
    def record_result(self, system: str, test_name: str, passed: bool, message: str):
        """Record a test result."""
        result = "PASSED" if passed else "FAILED"
        logger.info(f"[{system.upper()}] {test_name}: {result} - {message}")
        
        if passed:
            self.test_results[system]["passed"] += 1
        else:
            self.test_results[system]["failed"] += 1
        
        self.test_results[system]["details"].append({
            "test": test_name,
            "result": result,
            "message": message
        })
    
    def test_supabase_jurisdictions(self):
        """Test jurisdiction metadata tables in Supabase."""
        logger.info("Testing Supabase jurisdiction metadata tables...")
        
        # Test 1: Check if jurisdictions table exists
        try:
            result = self.supabase.execute_sql("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'jurisdictions'
                ) AS table_exists
            """)
            table_exists = False
            if result and isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and "table_exists" in result[0]:
                table_exists = result[0]["table_exists"]
            else:
                logger.warning(f"Unexpected result format for jurisdictions table check: {result}")
            self.record_result(
                "supabase", 
                "Jurisdictions Table Exists", 
                table_exists, 
                "Jurisdictions table exists" if table_exists else "Jurisdictions table does not exist"
            )
        except Exception as e:
            logger.exception("Error occurred while checking jurisdictions table existence:")
            self.record_result(
                "supabase", 
                "Jurisdictions Table Exists", 
                False, 
                f"Exception occurred (see logs for details)"
            )
        
        # Test 2: Check if court_systems table exists
        try:
            result = self.supabase.execute_sql("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'court_systems'
                ) AS table_exists
            """)
            table_exists = False
            if result and isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and "table_exists" in result[0]:
                table_exists = result[0]["table_exists"]
            else:
                logger.warning(f"Unexpected result format for court_systems table check: {result}")
            self.record_result(
                "supabase", 
                "Court Systems Table Exists", 
                table_exists, 
                "Court Systems table exists" if table_exists else "Court Systems table does not exist"
            )
        except Exception as e:
            logger.exception("Error occurred while checking court_systems table existence:")
            self.record_result(
                "supabase", 
                "Court Systems Table Exists", 
                False, 
                f"Exception occurred (see logs for details)"
            )
        
        # Test 3: Check if jurisdictions have been populated
        try:
            result = self.supabase.execute_sql("""
                SELECT COUNT(*) AS jurisdiction_count FROM jurisdictions
            """)
            jurisdiction_count = 0
            if result and isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and "jurisdiction_count" in result[0]:
                jurisdiction_count = result[0]["jurisdiction_count"]
            else:
                logger.warning(f"Unexpected result format for jurisdiction count check: {result}")
            self.record_result(
                "supabase", 
                "Jurisdictions Populated", 
                jurisdiction_count > 0, 
                f"Found {jurisdiction_count} jurisdictions"
            )
        except Exception as e:
            logger.exception("Error occurred while checking jurisdiction population:")
            self.record_result(
                "supabase", 
                "Jurisdictions Populated", 
                False, 
                f"Exception occurred (see logs for details)"
            )
        
        # Test 4: Check if jurisdiction validation function works
        try:
            result = self.supabase.execute_sql("""
                SELECT jurisdiction_exists('tx') AS tx_exists,
                       jurisdiction_exists('invalid') AS invalid_exists
            """)
            tx_exists = False
            invalid_exists = True # Default to invalid if check fails
            if result and isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and "tx_exists" in result[0] and "invalid_exists" in result[0]:
                tx_exists = result[0]["tx_exists"]
                invalid_exists = result[0]["invalid_exists"]
            else:
                logger.warning(f"Unexpected result format for jurisdiction validation check: {result}")
            self.record_result(
                "supabase", 
                "Jurisdiction Validation Function", 
                tx_exists and not invalid_exists, 
                f"Validation function correctly identifies valid and invalid jurisdictions"
            )
        except Exception as e:
            logger.exception("Error occurred while testing jurisdiction validation function:")
            self.record_result(
                "supabase", 
                "Jurisdiction Validation Function", 
                False, 
                f"Exception occurred (see logs for details)"
            )
        
        # Test 5: Check if citation patterns function works
        try:
            result = self.supabase.execute_sql("""
                SELECT get_jurisdiction_citation_patterns('tx') AS tx_patterns
            """)
            tx_patterns = None
            if result and isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and "tx_patterns" in result[0]:
                tx_patterns = result[0]["tx_patterns"]
            else:
                logger.warning(f"Unexpected result format for citation patterns check: {result}")
            self.record_result(
                "supabase", 
                "Citation Patterns Function", 
                tx_patterns is not None and len(tx_patterns) > 0, 
                f"Citation patterns function returns valid patterns"
            )
        except Exception as e:
            logger.exception("Error occurred while testing citation patterns function:")
            self.record_result(
                "supabase", 
                "Citation Patterns Function", 
                False, 
                f"Exception occurred (see logs for details)"
            )
    
    def test_neo4j_jurisdictions(self):
        """Test Neo4j jurisdiction nodes and relationships."""
        logger.info("Testing Neo4j jurisdiction nodes and relationships...")
        
        # Test 1: Check if jurisdiction nodes exist
        with self.neo4j.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (j:Jurisdiction)
                    RETURN COUNT(j) AS jurisdiction_count
                """).single()
                jurisdiction_count = result["jurisdiction_count"]
                self.record_result(
                    "neo4j", 
                    "Jurisdiction Nodes Exist", 
                    jurisdiction_count > 0, 
                    f"Found {jurisdiction_count} jurisdiction nodes"
                )
            except Exception as e:
                self.record_result(
                    "neo4j", 
                    "Jurisdiction Nodes Exist", 
                    False, 
                    f"Error checking jurisdiction nodes: {str(e)}"
                )
        
        # Test 2: Check if court nodes exist
        with self.neo4j.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (c:Court)
                    RETURN COUNT(c) AS court_count
                """).single()
                court_count = result["court_count"]
                self.record_result(
                    "neo4j", 
                    "Court Nodes Exist", 
                    court_count > 0, 
                    f"Found {court_count} court nodes"
                )
            except Exception as e:
                self.record_result(
                    "neo4j", 
                    "Court Nodes Exist", 
                    False, 
                    f"Error checking court nodes: {str(e)}"
                )
        
        # Test 3: Check court-jurisdiction relationships
        with self.neo4j.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (c:Court)-[:IN_JURISDICTION]->(j:Jurisdiction)
                    RETURN COUNT(c) AS relationship_count
                """).single()
                relationship_count = result["relationship_count"]
                self.record_result(
                    "neo4j", 
                    "Court-Jurisdiction Relationships", 
                    relationship_count > 0, 
                    f"Found {relationship_count} court-jurisdiction relationships"
                )
            except Exception as e:
                self.record_result(
                    "neo4j", 
                    "Court-Jurisdiction Relationships", 
                    False, 
                    f"Error checking court-jurisdiction relationships: {str(e)}"
                )
        
        # Test 4: Check court hierarchy relationships
        with self.neo4j.driver.session() as session:
            try:
                result = session.run("""
                    MATCH (c:Court)-[:SUBORDINATE_TO]->(parent:Court)
                    RETURN COUNT(c) AS hierarchy_count
                """).single()
                hierarchy_count = result["hierarchy_count"]
                self.record_result(
                    "neo4j", 
                    "Court Hierarchy Relationships", 
                    hierarchy_count > 0, 
                    f"Found {hierarchy_count} court hierarchy relationships"
                )
            except Exception as e:
                self.record_result(
                    "neo4j", 
                    "Court Hierarchy Relationships", 
                    False, 
                    f"Error checking court hierarchy relationships: {str(e)}"
                )
    
    def test_pinecone_namespaces(self):
        """Test Pinecone jurisdiction-specific namespaces."""
        logger.info("Testing Pinecone jurisdiction-specific namespaces...")
        
        # Test 1: Check namespace generation
        try:
            tx_case_namespace = self.pinecone.get_namespace("tx", "case")
            fed_statute_namespace = self.pinecone.get_namespace("fed", "statute")
            
            namespace_format_valid = (
                tx_case_namespace == "tx-case" and 
                fed_statute_namespace == "fed-statute"
            )
            
            self.record_result(
                "pinecone", 
                "Namespace Generation", 
                namespace_format_valid, 
                f"Namespace format is valid: {tx_case_namespace}, {fed_statute_namespace}"
            )
        except Exception as e:
            self.record_result(
                "pinecone", 
                "Namespace Generation", 
                False, 
                f"Error generating namespaces: {str(e)}"
            )
        
        # Test 2: Store and retrieve a test embedding
        try:
            # Create a test embedding
            test_vector = [0.1] * 1024  # Changed dimension from 1536 to 1024
            test_id = f"test-{int(datetime.now().timestamp())}"
            test_metadata = {
                "title": "Test Document",
                "court_id": "Test Court", # Changed 'court' to 'court_id'
                "year": 2025
            }
            
            # Store the embedding
            logger.info(f"Storing test embedding {test_id} in Pinecone...")
            store_result = self.pinecone.store_embedding(
                vector=test_vector,
                id=test_id,
                metadata=test_metadata,
                jurisdiction="tx",
                doc_type="case"
            )
            
            # Add a short delay for potential consistency issues
            logger.info("Waiting 1 second before querying...")
            time.sleep(1)
            
            # Query the embedding
            logger.info(f"Querying for test embedding {test_id}...")
            query_result = self.pinecone.query_embeddings(
                query_vector=test_vector,
                top_k=1,
                jurisdiction="tx",
                doc_type="case"
            )
            logger.info(f"Pinecone query result: {query_result}")
            
            # Check if we got the test embedding back
            retrieval_successful = False
            if query_result and isinstance(query_result, list) and len(query_result) > 0:
                if isinstance(query_result[0], dict) and query_result[0].get("id") == test_id:
                     retrieval_successful = True
                elif hasattr(query_result[0], 'id') and query_result[0].id == test_id: # Handle potential object response
                     retrieval_successful = True
            
            self.record_result(
                "pinecone", 
                "Embedding Storage and Retrieval", 
                store_result and retrieval_successful, 
                f"Store success: {store_result}, Retrieval success: {retrieval_successful}"
            )
            
            # Clean up the test embedding
            if store_result: # Only attempt delete if store might have succeeded
                 logger.info(f"Cleaning up test embedding {test_id}...")
                 self.pinecone.delete_embedding(test_id, "tx", "case")
        
        except Exception as e:
            logger.error(f"Exception during Pinecone storage/retrieval test: {e}", exc_info=True) # Log full exception
            self.record_result(
                "pinecone", 
                "Embedding Storage and Retrieval", 
                False, 
                f"Error testing embedding storage and retrieval: {str(e)}"
            )
    
    def test_gcs_paths(self):
        """Test GCS jurisdiction-based path organization."""
        logger.info("Testing GCS jurisdiction-based path organization...")
        
        # Test 1: Check jurisdiction path generation
        try:
            # Test case path
            case_path = self.gcs.get_jurisdiction_path(
                jurisdiction="tx",
                case_id="2025-0001",
                opinion_id="majority",
                filename="opinion.txt",
                doc_type="case",
                year="2025"
            )
            
            # Test statute path
            statute_path = self.gcs.get_jurisdiction_path(
                jurisdiction="tx",
                case_id="penal-code",
                filename="chapter1.txt",
                doc_type="statute"
            )
            
            # Check if paths follow the expected format
            case_path_valid = case_path == "legal/tx/cases/2025/2025-0001/opinions/majority/opinion.txt"
            statute_path_valid = statute_path == "legal/tx/statutes/penal-code/chapter1.txt"
            
            self.record_result(
                "gcs", 
                "Jurisdiction Path Generation", 
                case_path_valid and statute_path_valid, 
                f"Path generation is valid: {case_path}, {statute_path}"
            )
        except Exception as e:
            self.record_result(
                "gcs", 
                "Jurisdiction Path Generation", 
                False, 
                f"Error generating jurisdiction paths: {str(e)}"
            )
        
        # Test 2: Store and retrieve a test file
        try:
            # Create a test file
            test_content = "This is a test file for jurisdiction-based storage."
            test_path = self.gcs.get_jurisdiction_path(
                jurisdiction="tx",
                case_id="test-case",
                filename="test.txt",
                doc_type="case",
                year="2025"
            )
            
            # Store the file
            self.gcs.store_text(test_content, test_path)
            
            # Check if file exists
            file_exists = self.gcs.file_exists(test_path)
            
            # Retrieve the file
            retrieved_content = self.gcs.get_text(test_path)
            
            # Check if content matches
            content_matches = retrieved_content == test_content
            
            self.record_result(
                "gcs", 
                "File Storage and Retrieval", 
                file_exists and content_matches, 
                f"Successfully stored and retrieved test file with jurisdiction-based path"
            )
            
            # Clean up the test file
            self.gcs.delete_file(test_path)
            
        except Exception as e:
            self.record_result(
                "gcs", 
                "File Storage and Retrieval", 
                False, 
                f"Error testing file storage and retrieval: {str(e)}"
            )
        
        # Test 3: Test jurisdiction file listing
        try:
            # Create test files for different jurisdictions and document types
            test_paths = [
                self.gcs.get_jurisdiction_path(jurisdiction="tx", case_id="test1", doc_type="case", year="2025"),
                self.gcs.get_jurisdiction_path(jurisdiction="tx", case_id="test2", doc_type="statute"),
                self.gcs.get_jurisdiction_path(jurisdiction="fed", case_id="test3", doc_type="case", year="2025")
            ]
            
            for path in test_paths:
                self.gcs.store_text("Test content", path)
            
            # List files for TX jurisdiction
            tx_files = self.gcs.list_jurisdiction_files(jurisdiction="tx")
            
            # List files for TX cases
            tx_cases = self.gcs.list_jurisdiction_files(jurisdiction="tx", doc_type="case")
            
            # List files for FED jurisdiction
            fed_files = self.gcs.list_jurisdiction_files(jurisdiction="fed")
            
            # Check if listings are correct
            tx_listing_valid = len(tx_files) >= 2  # Should have at least our 2 test files
            tx_cases_valid = len(tx_cases) >= 1  # Should have at least our 1 test case
            fed_listing_valid = len(fed_files) >= 1  # Should have at least our 1 test file
            
            self.record_result(
                "gcs", 
                "Jurisdiction File Listing", 
                tx_listing_valid and tx_cases_valid and fed_listing_valid, 
                f"Successfully listed files by jurisdiction and document type"
            )
            
            # Clean up test files
            for path in test_paths:
                self.gcs.delete_file(path)
            
        except Exception as e:
            self.record_result(
                "gcs", 
                "Jurisdiction File Listing", 
                False, 
                f"Error testing jurisdiction file listing: {str(e)}"
            )
    
    def test_cross_system_consistency(self):
        """Test consistency across storage systems."""
        logger.info("Testing cross-system consistency...")
        
        # Test 1: Create a test case across all systems
        try:
            # Generate a unique test case ID
            test_id = f"test-{int(datetime.now().timestamp())}"
            test_jurisdiction = "tx"
            test_year = "2025"
            
            # 1. Create case in Supabase
            case_data = {
                "id": test_id,
                "case_name": "Test v. Cross-System", # Corrected key
                "jurisdiction": test_jurisdiction,
                "court_id": "texapp1",  # Changed 'court' to 'court_id'
                "date_filed": f"{test_year}-01-01",
                "docket_number": "2025-CV-001"
            }
            
            supabase_insert_success = False
            try:
                self.supabase.insert_record('cases', case_data)
                supabase_insert_success = True
                logger.info(f"Supabase insert successful for {test_id}")
            except Exception as e:
                logger.error(f"Supabase insert failed in cross-system test: {e}")
                supabase_insert_success = False # Explicitly set just in case

            # Check if insert succeeded before proceeding
            if not supabase_insert_success:
                 logger.error(f"Supabase insert failed for {test_id}, cannot proceed with cross-system check.")
                 # Record failure and exit this test function early
                 self.record_result(
                     "cross_system", 
                     "Case Consistency Across Systems", 
                     False, 
                     f"Supabase insert failed for case {test_id}."
                 )
                 return # Stop this test method
            
            # 2. Create case in Neo4j
            neo4j_result = self.neo4j.create_case({
                "id": test_id,
                "name": case_data["case_name"], # Updated reference
                "jurisdiction": test_jurisdiction,
                "court_id": case_data["court_id"], # Ensure Neo4j uses the same key
                "date_filed": case_data["date_filed"],
                "docket_number": case_data["docket_number"],
                "year": int(test_year)
            })
            
            # 3. Store case document in GCS
            test_content = "This is a test case document for cross-system consistency testing."
            gcs_path = self.gcs.get_jurisdiction_path(
                jurisdiction=test_jurisdiction,
                case_id=test_id,
                filename="opinion.txt",
                doc_type="case",
                year=test_year
            )
            gcs_result = self.gcs.store_text(test_content, gcs_path)
            
            # 4. Store case embedding in Pinecone
            test_vector = [0.1] * 1024  # Changed dimension from 1536 to 1024
            pinecone_result = self.pinecone.store_embedding(
                vector=test_vector,
                id=test_id,
                metadata={
                    "title": case_data["case_name"], # Updated reference
                    "court_id": case_data["court_id"], # Ensure Pinecone uses the same key
                    "year": test_year,
                    "docket_number": case_data["docket_number"]
                },
                jurisdiction=test_jurisdiction,
                doc_type="case"
            )
            
            # Add a delay for Pinecone index update
            logger.info("Waiting 10 seconds for Pinecone index to update...")
            time.sleep(10)
            
            # Check if case exists in all systems
            supabase_check = len(self.supabase.select_records(
                table_name='cases', 
                columns='id', 
                filters={'id': test_id}, 
                limit=1
            )) > 0
            
            with self.neo4j.driver.session() as session:
                neo4j_check = session.run("""
                    MATCH (c:Case {id: $id})
                    RETURN COUNT(c) AS count
                """, id=test_id).single()["count"] > 0
            
            gcs_check = self.gcs.file_exists(gcs_path)
            
            fetched_pinecone_data = self.pinecone.fetch_embedding(
                id=test_id,
                jurisdiction=test_jurisdiction,
                doc_type="case"
            )
            pinecone_check = fetched_pinecone_data is not None
            
            all_systems_consistent = (
                supabase_check and 
                neo4j_check and 
                gcs_check and 
                pinecone_check
            )
            
            self.record_result(
                "cross_system", 
                "Case Consistency Across Systems", 
                all_systems_consistent, 
                f"Case {test_id} exists in all systems: Supabase={supabase_check}, Neo4j={neo4j_check}, GCS={gcs_check}, Pinecone={pinecone_check}"
            )
            
            # Clean up test case
            delete_success = self.supabase.delete_record('cases', filters={'id': test_id})
            if not delete_success:
                 logger.warning(f"Failed to cleanup Supabase test case {test_id}")
            
            with self.neo4j.driver.session() as session:
                session.run("MATCH (c:Case {id: $id}) DETACH DELETE c", id=test_id)
            
            self.gcs.delete_file(gcs_path)
            self.pinecone.delete_embedding(test_id, test_jurisdiction, "case")
            
        except Exception as e:
            self.record_result(
                "cross_system", 
                "Case Consistency Across Systems", 
                False, 
                f"Error testing cross-system consistency: {str(e)}"
            )
    
    def run_all_tests(self):
        """Run all tests."""
        logger.info("Starting multi-jurisdictional organization tests...")
        
        self.test_supabase_jurisdictions()
        self.test_neo4j_jurisdictions()
        self.test_pinecone_namespaces()
        self.test_gcs_paths()
        self.test_cross_system_consistency()
        
        logger.info("All tests completed.")
        
        # Print summary
        print("\n=== TEST SUMMARY ===")
        for system, results in self.test_results.items():
            total = results["passed"] + results["failed"]
            if total > 0:
                pass_rate = (results["passed"] / total) * 100
            else:
                pass_rate = 0
            print(f"{system.upper()}: {results['passed']}/{total} passed ({pass_rate:.1f}%)")
        
        # Print details for failed tests
        print("\n=== FAILED TESTS ===")
        has_failures = False
        for system, results in self.test_results.items():
            for detail in results["details"]:
                if detail["result"] == "FAILED":
                    has_failures = True
                    print(f"{system.upper()} - {detail['test']}: {detail['message']}")
        
        if not has_failures:
            print("All tests passed!")
        
        # Return overall success
        return all(results["failed"] == 0 for results in self.test_results.values())

def main():
    """Main function to run tests."""
    parser = argparse.ArgumentParser(description="Test multi-jurisdictional organization")
    parser.add_argument("--skip-supabase", action="store_true", help="Skip Supabase tests")
    parser.add_argument("--skip-neo4j", action="store_true", help="Skip Neo4j tests")
    parser.add_argument("--skip-pinecone", action="store_true", help="Skip Pinecone tests")
    parser.add_argument("--skip-gcs", action="store_true", help="Skip GCS tests")
    parser.add_argument("--skip-cross-system", action="store_true", help="Skip cross-system tests")
    args = parser.parse_args()
    
    tester = MultiJurisdictionTester()
    
    try:
        if args.skip_supabase:
            logger.info("Skipping Supabase tests")
            tester.test_results["supabase"]["skipped"] = True
        else:
            tester.test_supabase_jurisdictions()
        
        if args.skip_neo4j:
            logger.info("Skipping Neo4j tests")
            tester.test_results["neo4j"]["skipped"] = True
        else:
            tester.test_neo4j_jurisdictions()
        
        if args.skip_pinecone:
            logger.info("Skipping Pinecone tests")
            tester.test_results["pinecone"]["skipped"] = True
        else:
            tester.test_pinecone_namespaces()
        
        if args.skip_gcs:
            logger.info("Skipping GCS tests")
            tester.test_results["gcs"]["skipped"] = True
        else:
            tester.test_gcs_paths()
        
        if args.skip_cross_system:
            logger.info("Skipping cross-system tests")
            tester.test_results["cross_system"]["skipped"] = True
        else:
            tester.test_cross_system_consistency()
        
        # Print summary
        print("\n=== TEST SUMMARY ===")
        for system, results in tester.test_results.items():
            if results.get("skipped", False):
                print(f"{system.upper()}: SKIPPED")
                continue
                
            total = results["passed"] + results["failed"]
            if total > 0:
                pass_rate = (results["passed"] / total) * 100
            else:
                pass_rate = 0
            print(f"{system.upper()}: {results['passed']}/{total} passed ({pass_rate:.1f}%)")
    
    finally:
        tester.cleanup()

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    main()

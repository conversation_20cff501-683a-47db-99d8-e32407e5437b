#!/usr/bin/env python3
"""
Test Multi-Jurisdictional Harvesting

This script tests the automated harvesting system with all enabled jurisdictions.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set environment variable for all jurisdictions
os.environ["ALLOWED_JURISDICTIONS"] = "tx,ny,fl,oh,fed"

from src.harvesting.harvesting_config import get_harvesting_config
from src.harvesting.automated_harvester import AutomatedHarvester

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_multi_jurisdictional_config():
    """Test multi-jurisdictional configuration"""
    print("🎯 Testing Multi-Jurisdictional Configuration")
    print("=" * 60)
    
    try:
        config = get_harvesting_config()
        enabled = config.get_enabled_jurisdictions()
        
        print(f"✅ Configuration loaded successfully")
        print(f"   Total Jurisdictions: {len(config.jurisdictions)}")
        print(f"   Enabled Jurisdictions: {len(enabled)}")
        print(f"   Max Concurrent Jobs: {config.max_concurrent_jobs}")
        print()
        
        print("📍 Enabled Jurisdictions:")
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            print(f"   {jurisdiction.upper()}: {jconfig.name}")
            print(f"      Priority: {jconfig.priority}")
            print(f"      Schedule: {jconfig.schedule}")
            print(f"      Courts: {len(jconfig.courts)}")
            print(f"      Practice Areas: {len(jconfig.practice_areas)}")
            print(f"      Max Cases/Run: {jconfig.max_cases_per_run}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_harvester_initialization():
    """Test automated harvester initialization with multiple jurisdictions"""
    print("\n🤖 Testing Automated Harvester Initialization")
    print("=" * 60)
    
    try:
        harvester = AutomatedHarvester()
        print("✅ Automated harvester initialized successfully")
        
        # Get status
        status = harvester.get_status()
        
        print(f"   Running: {status['is_running']}")
        print(f"   Scheduler Available: {status['scheduler_available']}")
        print(f"   Config Enabled: {status['config_enabled']}")
        
        stats = status['stats']
        print(f"   Total Jurisdictions: {stats['total_jurisdictions']}")
        print(f"   Active Jurisdictions: {stats['active_jurisdictions']}")
        
        print("\n📊 Jurisdiction Status:")
        for jurisdiction, info in status['jurisdictions'].items():
            if 'error' in info:
                print(f"   {jurisdiction.upper()}: ❌ ERROR - {info['error']}")
            else:
                print(f"   {jurisdiction.upper()}: ✅ Ready")
                print(f"      Enabled: {info.get('enabled', 'unknown')}")
                print(f"      Priority: {info.get('priority', 'unknown')}")
                print(f"      Courts: {info.get('courts', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Harvester initialization test failed: {str(e)}")
        return False

def test_jurisdiction_schedules():
    """Test jurisdiction scheduling to avoid conflicts"""
    print("\n📅 Testing Jurisdiction Schedules")
    print("=" * 60)
    
    try:
        config = get_harvesting_config()
        enabled = config.get_enabled_jurisdictions()
        
        schedules = {}
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            schedule = jconfig.schedule
            priority = jconfig.priority
            
            if schedule in schedules:
                print(f"⚠️  Schedule conflict detected:")
                print(f"   {schedule}: {schedules[schedule]} and {jurisdiction.upper()}")
            else:
                schedules[schedule] = jurisdiction.upper()
            
            print(f"   {jurisdiction.upper()}: {schedule} ({priority} priority)")
        
        print(f"\n✅ Schedule analysis complete")
        print(f"   Unique schedules: {len(schedules)}")
        print(f"   Total jurisdictions: {len(enabled)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schedule test failed: {str(e)}")
        return False

def test_practice_area_coverage():
    """Test practice area coverage across jurisdictions"""
    print("\n🔍 Testing Practice Area Coverage")
    print("=" * 60)
    
    try:
        config = get_harvesting_config()
        enabled = config.get_enabled_jurisdictions()
        
        # Collect all practice areas
        all_practice_areas = set()
        jurisdiction_practice_areas = {}
        
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            areas = jconfig.practice_areas
            all_practice_areas.update(areas)
            jurisdiction_practice_areas[jurisdiction] = areas
        
        print(f"Total Practice Areas: {len(all_practice_areas)}")
        print()
        
        for area in sorted(all_practice_areas):
            jurisdictions_with_area = []
            for jurisdiction in enabled:
                if area in jurisdiction_practice_areas[jurisdiction]:
                    jurisdictions_with_area.append(jurisdiction.upper())
            
            coverage = len(jurisdictions_with_area) / len(enabled) * 100
            print(f"   {area.replace('_', ' ').title()}: {', '.join(jurisdictions_with_area)} ({coverage:.0f}% coverage)")
        
        # Check for personal injury coverage (our main focus)
        pi_jurisdictions = []
        for jurisdiction in enabled:
            if "personal_injury" in jurisdiction_practice_areas[jurisdiction]:
                pi_jurisdictions.append(jurisdiction.upper())
        
        print(f"\n🎯 Personal Injury Coverage: {', '.join(pi_jurisdictions)}")
        print(f"   Coverage: {len(pi_jurisdictions)}/{len(enabled)} jurisdictions ({len(pi_jurisdictions)/len(enabled)*100:.0f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Practice area test failed: {str(e)}")
        return False

def main():
    """Run all multi-jurisdictional tests"""
    print("🚀 MULTI-JURISDICTIONAL HARVESTING TEST")
    print("Testing automated harvesting across multiple jurisdictions")
    print("=" * 80)
    
    tests = [
        ("Configuration", test_multi_jurisdictional_config),
        ("Harvester Initialization", test_harvester_initialization),
        ("Jurisdiction Schedules", test_jurisdiction_schedules),
        ("Practice Area Coverage", test_practice_area_coverage)
    ]
    
    results = []
    
    for name, test_func in tests:
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ {name} test crashed: {str(e)}")
            results.append((name, False))
    
    # Summary
    print("\n" + "="*80)
    print("📋 MULTI-JURISDICTIONAL TEST SUMMARY")
    print("="*80)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{name}: {status}")
    
    print(f"\nOverall: {successful}/{total} tests successful ({successful/total:.1%})")
    
    if successful == total:
        print("\n🎉 All multi-jurisdictional features are working correctly!")
        print("\nEnabled Jurisdictions:")
        print("  • Texas (TX) - High Priority - Personal Injury Focus")
        print("  • New York (NY) - High Priority - Multi-Practice")
        print("  • Florida (FL) - High Priority - Multi-Practice")
        print("  • Ohio (OH) - Medium Priority - Multi-Practice")
        print("  • Federal (FED) - Medium Priority - Constitutional/Immigration")
        print("\nNext steps:")
        print("1. Start automated harvesting: python scripts/start_harvester.py")
        print("2. Monitor harvesting: python scripts/start_harvester.py --status")
        print("3. Manual harvest: python scripts/start_harvester.py --harvest-all-now")
    else:
        print(f"\n⚠️ Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()

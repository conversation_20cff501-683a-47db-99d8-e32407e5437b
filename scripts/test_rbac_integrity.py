#!/usr/bin/env python
"""
Role-Based Access Control Integrity Test Script

This script tests the case law processor with different user roles to verify
that role-based access control is properly enforced across all data stores.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from scripts.test_data_integrity import DataIntegrityTester

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('rbac_integrity_test.log')
    ]
)
logger = logging.getLogger(__name__)


def test_role(role: str, tenant_id: str, jurisdiction: str, query: str, count: int) -> Dict[str, Any]:
    """Run a test for a specific role and tenant."""
    logger.info(f"Testing role: {role}, tenant: {tenant_id}")
    
    tester = DataIntegrityTester(
        user_role=role,
        tenant_id=tenant_id
    )
    
    # Track which jurisdictions this role should have access to
    expected_access = {
        "partner": ["tx", "ca", "ny", "fed"],  # Partners have access to all
        "attorney": ["tx", "ca", "ny", "fed"],  # Attorneys have access to all
        "paralegal": [],  # Will be filled based on tenant
        "staff": [],      # Will be filled based on tenant
        "client": []      # Will be filled based on client cases
    }
    
    # Set expected access based on tenant
    if tenant_id == "tenant1":
        expected_access["paralegal"] = ["tx", "ca"]
        expected_access["staff"] = ["tx", "ca"]
    elif tenant_id == "tenant2":
        expected_access["paralegal"] = ["ny", "fed"]
        expected_access["staff"] = ["ny", "fed"]
    elif tenant_id == "tenant3":
        expected_access["paralegal"] = ["tx"]
        expected_access["staff"] = ["tx"]
    
    # Special case for client role - we'll use the user_id instead of tenant_id
    # This is just for testing purposes
    if role == "client":
        if tenant_id == "tenant1":
            tester.processor.user_id = "client1"  # client1 has access to TX
            expected_access["client"] = ["tx"]
        elif tenant_id == "tenant2":
            tester.processor.user_id = "client2"  # client2 has access to CA
            expected_access["client"] = ["ca"]
    
    # Check if this role should have access to the requested jurisdiction
    should_have_access = jurisdiction in expected_access.get(role, [])
    
    # Run the test
    start_time = datetime.now()
    success = tester.run_full_test(jurisdiction, query, count)
    end_time = datetime.now()
    
    # Return the results
    return {
        "role": role,
        "tenant_id": tenant_id,
        "jurisdiction": jurisdiction,
        "should_have_access": should_have_access,
        "actual_access": success,
        "access_correct": should_have_access == (len(tester.processed_cases) > 0),
        "processed_cases": len(tester.processed_cases),
        "duration": (end_time - start_time).total_seconds()
    }


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Test RBAC integrity across databases")
    parser.add_argument("--jurisdiction", default="tx", help="Jurisdiction to test")
    parser.add_argument("--query", default="negligence", help="Search query for test data")
    parser.add_argument("--count", type=int, default=3, help="Number of cases to process")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    
    args = parser.parse_args()
    
    # Define test scenarios
    test_scenarios = [
        # Role, Tenant ID
        ("partner", "tenant1"),
        ("attorney", "tenant1"),
        ("paralegal", "tenant1"),
        ("staff", "tenant1"),
        ("client", "tenant1"),  # client1 - access to TX
        
        ("partner", "tenant2"),
        ("attorney", "tenant2"),
        ("paralegal", "tenant2"),
        ("staff", "tenant2"),
        ("client", "tenant2"),  # client2 - access to CA
        
        ("partner", "tenant3"),
        ("attorney", "tenant3"),
        ("paralegal", "tenant3"),
        ("staff", "tenant3")
    ]
    
    logger.info("=" * 80)
    logger.info(f"Starting RBAC integrity test at {datetime.now().isoformat()}")
    logger.info(f"Testing jurisdiction: {args.jurisdiction}")
    logger.info(f"Query: {args.query}")
    logger.info(f"Count: {args.count}")
    logger.info(f"Parallel execution: {args.parallel}")
    logger.info("=" * 80)
    
    results = []
    
    if args.parallel:
        # Run tests in parallel
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            for role, tenant_id in test_scenarios:
                future = executor.submit(
                    test_role, role, tenant_id, args.jurisdiction, args.query, args.count
                )
                futures.append(future)
            
            for future in futures:
                results.append(future.result())
    else:
        # Run tests sequentially
        for role, tenant_id in test_scenarios:
            result = test_role(role, tenant_id, args.jurisdiction, args.query, args.count)
            results.append(result)
    
    # Print summary
    logger.info("=" * 80)
    logger.info("RBAC INTEGRITY TEST SUMMARY")
    logger.info("=" * 80)
    
    access_correct_count = 0
    for result in results:
        access_status = "CORRECT" if result["access_correct"] else "INCORRECT"
        logger.info(
            f"Role: {result['role']:<10} "
            f"Tenant: {result['tenant_id']:<10} "
            f"Should access: {result['should_have_access']:<5} "
            f"Actual access: {len(result['processed_cases']) > 0:<5} "
            f"Status: {access_status:<10} "
            f"Cases: {result['processed_cases']:<3} "
            f"Time: {result['duration']:.2f}s"
        )
        
        if result["access_correct"]:
            access_correct_count += 1
    
    success_rate = access_correct_count / len(results)
    logger.info("=" * 80)
    logger.info(f"Total scenarios: {len(results)}")
    logger.info(f"Correct access control: {access_correct_count}")
    logger.info(f"Success rate: {success_rate:.2%}")
    logger.info("=" * 80)
    
    overall_success = success_rate >= 0.95  # Allow for some minor issues
    logger.info(f"Overall RBAC test result: {'SUCCESS' if overall_success else 'FAILURE'}")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

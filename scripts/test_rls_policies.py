#!/usr/bin/env python3
"""
Script to test RLS policies and verify access to Supabase tables.
"""

import os
import sys
import logging
from pprint import pprint
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the Supabase connector
from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def apply_rls_policies(supabase):
    """Apply the RLS policies from fix_rls_policies.sql."""
    logger.info("Applying RLS policies...")
    
    # Read the SQL file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    sql_file_path = os.path.join(script_dir, "fix_rls_policies.sql")
    
    with open(sql_file_path, 'r') as f:
        sql_content = f.read()
    
    # Split the SQL into individual statements
    sql_statements = sql_content.split(';')
    
    # Execute each statement
    for statement in sql_statements:
        statement = statement.strip()
        if statement:
            try:
                logger.info(f"Executing SQL: {statement}")
                supabase.client.query(statement)
                logger.info("SQL executed successfully")
            except Exception as e:
                logger.error(f"Error executing SQL: {str(e)}")
    
    logger.info("RLS policies applied successfully")

def verify_rls_policies(supabase):
    """Verify that the RLS policies are correctly applied."""
    logger.info("Verifying RLS policies...")
    
    try:
        # Query the pg_policies table to see all policies
        result = supabase.client.query("SELECT tablename, policyname, cmd, roles, qual FROM pg_policies;")
        policies = result.data
        
        logger.info(f"Found {len(policies)} RLS policies:")
        for policy in policies:
            logger.info(f"Table: {policy['tablename']}, Policy: {policy['policyname']}, Command: {policy['cmd']}, Roles: {policy['roles']}, Qualifier: {policy['qual']}")
        
        # Check for our specific policies
        opinions_policy = next((p for p in policies if p['tablename'] == 'opinions' and p['policyname'] == 'Allow all operations on opinions'), None)
        citations_policy = next((p for p in policies if p['tablename'] == 'citations' and p['policyname'] == 'Allow all operations on citations'), None)
        
        if opinions_policy:
            logger.info("Opinions policy is correctly applied")
        else:
            logger.error("Opinions policy is not applied")
        
        if citations_policy:
            logger.info("Citations policy is correctly applied")
        else:
            logger.error("Citations policy is not applied")
        
    except Exception as e:
        logger.error(f"Error verifying RLS policies: {str(e)}")

def test_table_access(supabase, case_id="test-case-001"):
    """Test access to the opinions and citations tables."""
    logger.info(f"Testing access to tables for case_id: {case_id}")
    
    # Test opinions access
    try:
        logger.info("Testing access to opinions table...")
        opinions_response = supabase.client.table("opinions").select("*").eq("case_id", case_id).execute()
        opinions = opinions_response.data if hasattr(opinions_response, 'data') else []
        
        logger.info(f"Found {len(opinions)} opinions")
        for opinion in opinions:
            logger.info(f"Opinion: {opinion}")
    except Exception as e:
        logger.error(f"Error accessing opinions table: {str(e)}")
    
    # Test citations access
    try:
        logger.info("Testing access to citations table...")
        citations_response = supabase.client.table("citations").select("*").eq("citing_case_id", case_id).execute()
        citations = citations_response.data if hasattr(citations_response, 'data') else []
        
        logger.info(f"Found {len(citations)} citations")
        for citation in citations:
            logger.info(f"Citation: {citation}")
    except Exception as e:
        logger.error(f"Error accessing citations table: {str(e)}")

def main():
    # Load environment variables
    load_dotenv()
    
    # Initialize Supabase connector
    supabase = SupabaseConnector(ensure_tables_exist=True)
    
    # Apply RLS policies
    apply_rls_policies(supabase)
    
    # Verify RLS policies
    verify_rls_policies(supabase)
    
    # Test table access
    test_table_access(supabase)

if __name__ == "__main__":
    main()

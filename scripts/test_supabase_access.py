#!/usr/bin/env python3
"""
Simple script to test Supabase access to opinions and citations tables.
"""

import os
import sys
import logging
from pprint import pprint
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the Supabase connector
from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    # Load environment variables
    load_dotenv()
    
    # Initialize Supabase connector
    # The SupabaseConnector class gets credentials from environment variables
    supabase = SupabaseConnector(ensure_tables_exist=True)
    
    # Test case ID
    case_id = "test-case-001"
    
    # Test opinions access
    logger.info(f"Testing access to opinions table for case_id: {case_id}")
    try:
        opinions_response = supabase.client.table("opinions").select("*").eq("case_id", case_id).execute()
        logger.info(f"Opinions response type: {type(opinions_response)}")
        logger.info(f"Opinions response: {opinions_response}")
        
        opinions = opinions_response.data if hasattr(opinions_response, 'data') else []
        logger.info(f"Opinions data length: {len(opinions)}")
        
        if opinions:
            logger.info("Opinions found:")
            for opinion in opinions:
                logger.info(f"Opinion: {opinion}")
        else:
            logger.info("No opinions found for this case")
    except Exception as e:
        logger.error(f"Error retrieving opinions: {str(e)}")
    
    # Test citations access
    logger.info(f"Testing access to citations table for citing_case_id: {case_id}")
    try:
        citations_response = supabase.client.table("citations").select("*").eq("citing_case_id", case_id).execute()
        logger.info(f"Citations response type: {type(citations_response)}")
        logger.info(f"Citations response: {citations_response}")
        
        citations = citations_response.data if hasattr(citations_response, 'data') else []
        logger.info(f"Citations data length: {len(citations)}")
        
        if citations:
            logger.info("Citations found:")
            for citation in citations:
                logger.info(f"Citation: {citation}")
        else:
            logger.info("No citations found for this case")
    except Exception as e:
        logger.error(f"Error retrieving citations: {str(e)}")

if __name__ == "__main__":
    main()

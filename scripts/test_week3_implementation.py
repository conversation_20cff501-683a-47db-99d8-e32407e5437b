#!/usr/bin/env python3
"""
Test Week 3 Implementation
Tests the multi-practice area relationship detection and cross-document linking.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_practice_area_classifier():
    """Test the practice area classifier."""
    print("\n🧪 Testing Practice Area Classifier")
    print("=" * 50)
    
    try:
        from src.relationships.practice_area_classifier import PracticeAreaClassifier
        
        classifier = PracticeAreaClassifier()
        
        # Test documents for different practice areas
        test_documents = [
            {
                'content': 'The defendant was charged with aggravated assault under Texas Penal Code Section 22.02. The court found the defendant guilty of a felony offense.',
                'title': 'State v. Johnson Criminal Case',
                'expected_area': 'criminal_law'
            },
            {
                'content': 'The plaintiff filed a negligence claim seeking damages for personal injury sustained in a car accident. The court awarded compensatory damages.',
                'title': 'Smith v. Jones Personal Injury',
                'expected_area': 'personal_injury'
            },
            {
                'content': 'The parties filed for divorce and sought custody of their minor children. The court awarded joint custody based on the best interests of the child.',
                'title': 'In re Marriage of Brown',
                'expected_area': 'family_law'
            },
            {
                'content': 'The corporation filed articles of incorporation and sought to establish a board of directors. Securities regulations apply to the stock offering.',
                'title': 'Business Formation Case',
                'expected_area': 'business_law'
            }
        ]
        
        correct_classifications = 0
        total_tests = len(test_documents)
        
        for doc in test_documents:
            primary_area, confidence, all_scores = classifier.classify_with_confidence(doc)
            
            print(f"\nDocument: {doc['title']}")
            print(f"Expected: {doc['expected_area']}")
            print(f"Classified: {primary_area} (confidence: {confidence:.2f})")
            print(f"All scores: {[(area, score) for area, score in all_scores[:3]]}")
            
            if primary_area == doc['expected_area']:
                correct_classifications += 1
                print("✅ Correct classification")
            else:
                print("❌ Incorrect classification")
        
        accuracy = correct_classifications / total_tests
        print(f"\n📊 Classification Accuracy: {accuracy:.2%} ({correct_classifications}/{total_tests})")
        
        if accuracy >= 0.75:
            print("✅ Practice area classifier test passed")
            return True
        else:
            print("❌ Practice area classifier test failed")
            return False
            
    except Exception as e:
        logger.error(f"Practice area classifier test failed: {e}")
        return False

def test_case_statute_linker():
    """Test the case-statute linking engine."""
    print("\n🧪 Testing Case-Statute Linker")
    print("=" * 50)
    
    try:
        from src.relationships.case_statute_linker import CaseStatuteLinkingEngine
        
        linker = CaseStatuteLinkingEngine()
        
        # Test citation extraction
        test_case_text = """
        The defendant violated Texas Penal Code § 22.01 by committing assault.
        The court also considered Civil Practice and Remedies Code § 41.003 
        regarding exemplary damages. Federal law under 18 U.S.C. § 924 was also applicable.
        """
        
        print("Test case text:")
        print(test_case_text)
        
        # Test citation extraction (this would normally be called internally)
        citations = linker._extract_statute_citations(test_case_text, 'tx', 'criminal_law')
        
        print(f"\n📋 Extracted {len(citations)} citations:")
        for i, citation in enumerate(citations, 1):
            print(f"{i}. {citation.citation_text}")
            print(f"   Normalized: {citation.normalized_citation}")
            print(f"   Practice Area: {citation.practice_area}")
            print(f"   Confidence: {citation.confidence_score:.2f}")
        
        # Test missing statute detection
        missing_statutes = linker.get_missing_statutes(practice_area='criminal_law', limit=5)
        print(f"\n📊 Found {len(missing_statutes)} missing statutes for criminal law")
        
        if len(citations) >= 2:  # Should find at least 2 citations
            print("✅ Case-statute linker test passed")
            return True
        else:
            print("❌ Case-statute linker test failed - insufficient citations found")
            return False
            
    except Exception as e:
        logger.error(f"Case-statute linker test failed: {e}")
        return False

def test_relationship_validator():
    """Test the relationship validator."""
    print("\n🧪 Testing Relationship Validator")
    print("=" * 50)
    
    try:
        from src.relationships.relationship_validator import RelationshipValidator
        
        validator = RelationshipValidator()
        
        # Test validation scenarios
        test_relationships = [
            {
                'source_doc': {
                    'id': 'case_1',
                    'jurisdiction': 'tx',
                    'primary_practice_area': 'criminal_law',
                    'date_filed': '2023-01-15'
                },
                'target_doc': {
                    'id': 'case_2',
                    'jurisdiction': 'tx',
                    'primary_practice_area': 'constitutional_law',
                    'date_filed': '2020-05-10'
                },
                'relationship_type': 'cites',
                'citation_text': '123 S.W.3d 456',
                'context_text': 'The court in Smith v. Jones, 123 S.W.3d 456, held that constitutional rights apply.',
                'confidence_score': 0.85,
                'description': 'Valid cross-practice citation'
            },
            {
                'source_doc': {
                    'id': 'case_3',
                    'jurisdiction': 'tx',
                    'primary_practice_area': 'family_law',
                    'date_filed': '2020-01-15'
                },
                'target_doc': {
                    'id': 'case_4',
                    'jurisdiction': 'tx',
                    'primary_practice_area': 'family_law',
                    'date_filed': '2023-05-10'
                },
                'relationship_type': 'cites',
                'citation_text': '456 S.W.3d 789',
                'context_text': 'Following the decision in Brown v. Green, 456 S.W.3d 789.',
                'confidence_score': 0.90,
                'description': 'Invalid temporal relationship (citing older case)'
            }
        ]
        
        valid_count = 0
        total_tests = len(test_relationships)
        
        for i, rel in enumerate(test_relationships, 1):
            print(f"\nTest {i}: {rel['description']}")
            
            result = validator.validate_relationship(
                source_doc=rel['source_doc'],
                target_doc=rel['target_doc'],
                relationship_type=rel['relationship_type'],
                citation_text=rel['citation_text'],
                context_text=rel['context_text'],
                confidence_score=rel['confidence_score']
            )
            
            print(f"Valid: {result.is_valid}")
            print(f"Confidence: {result.confidence_score:.2f}")
            print(f"Issues: {result.validation_issues}")
            
            if result.is_valid:
                valid_count += 1
                print("✅ Validation passed")
            else:
                print("❌ Validation failed")
        
        print(f"\n📊 Validation Results: {valid_count}/{total_tests} passed")
        
        # Test should pass if at least one relationship is valid
        if valid_count >= 1:
            print("✅ Relationship validator test passed")
            return True
        else:
            print("❌ Relationship validator test failed")
            return False
            
    except Exception as e:
        logger.error(f"Relationship validator test failed: {e}")
        return False

def test_citation_network_analyzer():
    """Test the citation network analyzer."""
    print("\n🧪 Testing Citation Network Analyzer")
    print("=" * 50)
    
    try:
        from src.relationships.citation_network_analyzer import CitationNetworkAnalyzer
        import networkx as nx
        
        analyzer = CitationNetworkAnalyzer()
        
        # Create a simple test network
        test_network = nx.DiGraph()
        test_network.add_node('case_1', practice_area='criminal_law')
        test_network.add_node('case_2', practice_area='constitutional_law')
        test_network.add_node('case_3', practice_area='criminal_law')
        
        test_network.add_edge('case_1', 'case_2', relationship_type='cites', confidence_score=0.9)
        test_network.add_edge('case_3', 'case_2', relationship_type='cites', confidence_score=0.8)
        test_network.add_edge('case_1', 'case_3', relationship_type='distinguishes', confidence_score=0.7)
        
        print(f"Test network: {test_network.number_of_nodes()} nodes, {test_network.number_of_edges()} edges")
        
        # Test network metrics calculation
        metrics = analyzer._calculate_network_metrics(test_network)
        print(f"\nNetwork Metrics:")
        print(f"  Nodes: {metrics.node_count}")
        print(f"  Edges: {metrics.edge_count}")
        print(f"  Density: {metrics.density:.3f}")
        print(f"  Average Degree: {metrics.average_degree:.3f}")
        
        # Test authority score calculation
        authority_scores = analyzer._calculate_authority_scores(test_network, 'criminal_law')
        print(f"\nAuthority Scores:")
        for score in authority_scores:
            print(f"  {score.document_id}: {score.authority_score:.2f}")
        
        # Test document authority score calculation
        doc_score = analyzer.calculate_document_authority_score('case_2')
        if doc_score:
            print(f"\nDocument case_2 Authority Score: {doc_score.authority_score:.2f}")
        
        if metrics.node_count == 3 and metrics.edge_count == 3:
            print("✅ Citation network analyzer test passed")
            return True
        else:
            print("❌ Citation network analyzer test failed")
            return False
            
    except Exception as e:
        logger.error(f"Citation network analyzer test failed: {e}")
        return False

def test_multi_practice_relationship_engine():
    """Test the multi-practice area relationship engine."""
    print("\n🧪 Testing Multi-Practice Relationship Engine")
    print("=" * 50)

    try:
        from src.relationships.multi_practice_relationship_engine import MultiPracticeAreaRelationshipEngine

        # Create engine with mock config to avoid database dependency
        mock_config = {'skip_database_init': True}
        engine = MultiPracticeAreaRelationshipEngine(config=mock_config)

        # Test practice area configuration
        print("Practice Areas Configured:")
        for area, config in engine.practice_areas.items():
            print(f"  {area}: {len(config['keywords'])} keywords")

        # Test relationship pattern detection
        test_context = "The court in Smith v. Jones distinguished the holding in Brown v. Green"
        relationship_type = engine._determine_relationship_type(test_context, "Smith v. Jones")
        print(f"\nRelationship type for '{test_context}': {relationship_type}")

        # Test practice area relationship checking
        related = engine._are_practice_areas_related('criminal_law', 'constitutional_law')
        print(f"Criminal law related to constitutional law: {related}")

        # Test confidence calculation (mock data)
        mock_citation = {'text': 'Smith v. Jones', 'context': test_context}
        mock_source = {'primary_practice_area': 'criminal_law'}
        mock_target = {'primary_practice_area': 'constitutional_law', 'title': 'Constitutional Rights Case'}

        confidence = engine._calculate_confidence_score(mock_citation, mock_source, mock_target)
        print(f"Confidence score: {confidence:.2f}")

        if len(engine.practice_areas) == 9 and relationship_type == 'distinguishes':
            print("✅ Multi-practice relationship engine test passed")
            return True
        else:
            print(f"❌ Multi-practice relationship engine test failed - areas: {len(engine.practice_areas)}, type: {relationship_type}")
            return False

    except Exception as e:
        logger.error(f"Multi-practice relationship engine test failed: {e}")
        return False

def test_cross_jurisdiction_handler():
    """Test the cross-jurisdiction handler."""
    print("\n🧪 Testing Cross-Jurisdiction Handler")
    print("=" * 50)

    try:
        from src.relationships.cross_jurisdiction_handler import CrossJurisdictionHandler

        handler = CrossJurisdictionHandler()

        # Test jurisdiction relationships
        print("Testing jurisdiction relationships:")

        # Test federal over state (binding)
        rel_type, strength = handler.get_jurisdiction_relationship('tx', 'fed')
        print(f"Texas citing Federal: {rel_type} (strength: {strength:.2f})")

        # Test state over state (persuasive)
        rel_type, strength = handler.get_jurisdiction_relationship('ca', 'tx')
        print(f"California citing Texas: {rel_type} (strength: {strength:.2f})")

        # Test same jurisdiction (binding)
        rel_type, strength = handler.get_jurisdiction_relationship('tx', 'tx')
        print(f"Texas citing Texas: {rel_type} (strength: {strength:.2f})")

        # Test authority scores
        fed_score = handler.get_jurisdiction_authority_score('fed')
        tx_score = handler.get_jurisdiction_authority_score('tx')
        print(f"\nAuthority scores - Federal: {fed_score:.2f}, Texas: {tx_score:.2f}")

        # Test binding jurisdictions
        binding = handler.find_binding_jurisdictions('tx')
        print(f"Jurisdictions with binding authority over Texas: {binding}")

        if len(binding) > 0 and fed_score > tx_score:
            print("✅ Cross-jurisdiction handler test passed")
            return True
        else:
            print("❌ Cross-jurisdiction handler test failed")
            return False

    except Exception as e:
        logger.error(f"Cross-jurisdiction handler test failed: {e}")
        return False

def run_all_tests():
    """Run all Week 3 tests."""
    print("🚀 Week 3 Implementation Tests")
    print("=" * 60)

    tests = [
        ("Practice Area Classifier", test_practice_area_classifier),
        ("Case-Statute Linker", test_case_statute_linker),
        ("Relationship Validator", test_relationship_validator),
        ("Citation Network Analyzer", test_citation_network_analyzer),
        ("Cross-Jurisdiction Handler", test_cross_jurisdiction_handler),
        ("Multi-Practice Relationship Engine", test_multi_practice_relationship_engine),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {passed_tests/total_tests:.1%}")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Week 3 implementation is working correctly.")
        return True
    elif passed_tests >= total_tests * 0.8:
        print("⚠️ Most tests passed. Week 3 implementation is mostly working.")
        return True
    else:
        print("❌ Many tests failed. Week 3 implementation needs attention.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

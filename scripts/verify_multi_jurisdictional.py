#!/usr/bin/env python3
"""
Verify Multi-Jurisdictional Configuration

This script verifies that all requested jurisdictions are properly configured
and enabled for automated harvesting.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.harvesting.harvesting_config import get_harvesting_config

def main():
    print("🎯 Multi-Jurisdictional Harvesting Configuration")
    print("=" * 60)
    
    try:
        config = get_harvesting_config()
        enabled = config.get_enabled_jurisdictions()
        
        print(f"Total Jurisdictions Configured: {len(config.jurisdictions)}")
        print(f"Enabled Jurisdictions: {len(enabled)}")
        print(f"Max Concurrent Jobs: {config.max_concurrent_jobs}")
        print()
        
        print("📍 All Configured Jurisdictions:")
        for code, jurisdiction in config.jurisdictions.items():
            status = "🟢 ENABLED" if jurisdiction.enabled else "🔴 DISABLED"
            print(f"  {code.upper()}: {jurisdiction.name} - {status}")
            if jurisdiction.enabled:
                print(f"    Priority: {jurisdiction.priority}")
                print(f"    Schedule: {jurisdiction.schedule}")
                print(f"    Courts: {len(jurisdiction.courts)}")
                print(f"    Practice Areas: {len(jurisdiction.practice_areas)}")
                print(f"    Max Cases/Run: {jurisdiction.max_cases_per_run}")
            print()
        
        print("🎯 Enabled Jurisdictions Summary:")
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            print(f"  {jurisdiction.upper()}: {jconfig.name}")
        print()
        
        print("🔍 Practice Area Coverage:")
        all_practice_areas = set()
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            all_practice_areas.update(jconfig.practice_areas)
        
        for area in sorted(all_practice_areas):
            jurisdictions_with_area = []
            for jurisdiction in enabled:
                jconfig = config.get_jurisdiction_config(jurisdiction)
                if area in jconfig.practice_areas:
                    jurisdictions_with_area.append(jurisdiction.upper())
            print(f"  {area.replace('_', ' ').title()}: {', '.join(jurisdictions_with_area)}")
        
        print()
        print("📊 Harvesting Schedule:")
        for jurisdiction in enabled:
            jconfig = config.get_jurisdiction_config(jurisdiction)
            print(f"  {jurisdiction.upper()}: {jconfig.schedule} ({jconfig.priority} priority)")
        
        print()
        print("✅ Multi-jurisdictional configuration verified successfully!")
        print(f"Ready to harvest from {len(enabled)} jurisdictions: {', '.join([j.upper() for j in enabled])}")
        
    except Exception as e:
        print(f"❌ Configuration verification failed: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

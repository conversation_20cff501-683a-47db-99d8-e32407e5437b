#!/usr/bin/env python
"""
Case Data Visualization Script

This script processes a single case and displays how the data looks in each database.
It's designed to help visualize the data flow and structure across all data stores.
"""

import os
import sys
import json
import logging
import argparse
from pprint import pprint
from typing import Dict, Any

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.case_law_processor import CaseLawProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CaseDataVisualizer:
    """Visualizes case data across all databases."""

    def __init__(self, user_role: str = "partner", tenant_id: str = None):
        """Initialize with the given role."""
        self.processor = CaseLawProcessor(
            user_id="test_user",
            user_role=user_role,
            tenant_id=tenant_id
        )

    def process_single_case(self, jurisdiction: str, query: str):
        """Process a single case and return its ID."""
        logger.info(f"Processing a case from {jurisdiction} with query '{query}'")
        
        # Process the jurisdiction to get cases
        result = self.processor.process_jurisdiction(
            jurisdiction=jurisdiction,
            query=query,
            count=1,  # Just get one case
            source="court_listener"
        )
        
        if result.get("status") == "error":
            logger.error(f"Error processing jurisdiction: {result.get('error')}")
            return None
        
        case_ids = result.get("cases", [])
        if not case_ids:
            logger.error("No cases were processed")
            return None
        
        return case_ids[0]  # Return the first case ID

    def visualize_supabase_data(self, case_id: str):
        """Display how the case data looks in Supabase."""
        logger.info("\n" + "=" * 80)
        logger.info("SUPABASE DATA")
        logger.info("=" * 80)
        
        case = self.processor.supabase.get_case(case_id)
        if not case:
            logger.error(f"Case not found in Supabase: {case_id}")
            return
        
        # Pretty print the case data
        print("\nCase Record:")
        pprint(case)
        
        # Get processing history by directly querying the table
        try:
            history_response = self.processor.supabase.client.table("processing_history").select("*").eq("case_id", case_id).execute()
            history = history_response.data if hasattr(history_response, 'data') else []
            if history:
                print("\nProcessing History:")
                pprint(history)
        except Exception as e:
            logger.error(f"Error retrieving processing history: {str(e)}")
        
        # Get opinions by directly querying the table
        try:
            logger.info(f"Querying opinions for case_id: {case_id}")
            opinions_response = self.processor.supabase.client.table("opinions").select("*").eq("case_id", case_id).execute()
            opinions = opinions_response.data if hasattr(opinions_response, 'data') else []
            
            print("\n=== OPINIONS ===")
            if opinions:
                logger.info(f"Found {len(opinions)} opinions")
                for i, opinion in enumerate(opinions, 1):
                    print(f"\nOpinion #{i}:")
                    print(f"  ID: {opinion['id']}")
                    print(f"  Type: {opinion['opinion_type']}")
                    print(f"  Author: {opinion['author']}")
                    print(f"  Word Count: {opinion['word_count']}")
                    print(f"  Created: {opinion['created_at']}")
            else:
                logger.info("No opinions found for this case")
                print("No opinions found for this case")
        except Exception as e:
            logger.error(f"Error retrieving opinions: {str(e)}")
            print(f"Error retrieving opinions: {str(e)}")
            
        # Get citations by directly querying the table
        try:
            logger.info(f"Querying citations for citing_case_id: {case_id}")
            citations_response = self.processor.supabase.client.table("citations").select("*").eq("citing_case_id", case_id).execute()
            citations = citations_response.data if hasattr(citations_response, 'data') else []
            
            print("\n=== CITATIONS ===")
            if citations:
                logger.info(f"Found {len(citations)} citations")
                for i, citation in enumerate(citations, 1):
                    print(f"\nCitation #{i}:")
                    print(f"  ID: {citation['id']}")
                    print(f"  Cited Case ID: {citation['cited_case_id']}")
                    print(f"  Citation Text: {citation['citation_text']}")
                    print(f"  Confidence: {citation['confidence']}")
                    print(f"  Created: {citation['created_at']}")
            else:
                logger.info("No citations found for this case")
                print("No citations found for this case")
        except Exception as e:
            logger.error(f"Error retrieving citations: {str(e)}")
            print(f"Error retrieving citations: {str(e)}")

    def visualize_gcs_data(self, case_id: str):
        """Display how the case data looks in GCS."""
        logger.info("\n" + "=" * 80)
        logger.info("GCS DATA")
        logger.info("=" * 80)
        
        # Get the case from Supabase to get the GCS path
        case = self.processor.supabase.get_case(case_id)
        if not case or not case.get("gcs_path"):
            logger.error(f"Case {case_id} has no GCS path in Supabase")
            return
        
        gcs_path = case.get("gcs_path")
        print(f"\nGCS Path: {gcs_path}")
        
        # Get the content
        content = self.processor.gcs.get_text(gcs_path)
        if content:
            print("\nContent Preview (first 500 chars):")
            print(content[:500] + "..." if len(content) > 500 else content)
        else:
            logger.error(f"Content not found in GCS for path: {gcs_path}")

    def visualize_pinecone_data(self, case_id: str):
        """Display how the case data looks in Pinecone."""
        logger.info("\n" + "=" * 80)
        logger.info("PINECONE DATA")
        logger.info("=" * 80)
        
        # Query Pinecone for the case using the correct filter format
        filter_dict = {"case_id": case_id}
        results = self.processor.pinecone.query(
            query_text="",  # Empty query text, we're filtering by case_id
            filter=filter_dict,
            top_k=5
        )
        
        if not results or len(results) == 0:
            logger.error(f"No embeddings found in Pinecone for case: {case_id}")
            return
        
        print(f"\nFound {len(results)} embeddings in Pinecone")
        
        # Display the first few results
        for i, result in enumerate(results[:3]):  # Show top 3
            print(f"\nEmbedding {i+1}:")
            # Remove the actual vector to keep output clean
            if "vector" in result:
                result["vector"] = f"<Vector of dimension {len(result['vector'])}>"
            pprint(result)

    def visualize_neo4j_data(self, case_id: str):
        """Display how the case data looks in Neo4j."""
        logger.info("\n" + "=" * 80)
        logger.info("NEO4J DATA")
        logger.info("=" * 80)
        
        # Check if case exists in Neo4j using a custom query
        try:
            # We'll just check if the case exists by looking at its citations
            citations = self.processor.neo4j.get_case_citations(case_id)
            if not citations:
                # Also check if it's cited by other cases
                cited_by = self.processor.neo4j.get_case_cited_by(case_id)
                if not cited_by:
                    logger.error(f"Case node not found in Neo4j or has no relationships: {case_id}")
                    return
            
            print("\nCase exists in Neo4j")
        except Exception as e:
            logger.error(f"Error checking case in Neo4j: {str(e)}")
            return
        
        # Get citations (cases that this case cites)
        citations = self.processor.neo4j.get_case_citations(case_id)
        if citations:
            print(f"\nFound {len(citations)} citations in Neo4j")
            print("\nCitation Relationships:")
            for i, citation in enumerate(citations[:5]):  # Show top 5
                print(f"\nCitation {i+1}:")
                pprint(citation)
        else:
            print("\nNo citations found in Neo4j")
        
        # Get citing cases (cases that cite this case)
        citing_cases = self.processor.neo4j.get_case_cited_by(case_id)
        if citing_cases:
            print(f"\nFound {len(citing_cases)} cases that cite this case")
            print("\nCiting Cases:")
            for i, citing in enumerate(citing_cases[:5]):  # Show top 5
                print(f"\nCiting Case {i+1}:")
                pprint(citing)
        else:
            print("\nNo cases found that cite this case")

    def run_visualization(self, jurisdiction: str, query: str, case_id: str = None):
        """Run the full visualization process."""
        logger.info("=" * 80)
        logger.info("CASE DATA VISUALIZATION")
        logger.info("=" * 80)
        logger.info(f"User role: {self.processor.user_role}")
        logger.info(f"Tenant ID: {self.processor.tenant_id}")
        
        # Process a case if no case_id provided
        if not case_id:
            case_id = self.process_single_case(jurisdiction, query)
            if not case_id:
                logger.error("Failed to process a case. Aborting visualization.")
                return False
        
        logger.info(f"Visualizing data for case ID: {case_id}")
        
        # Visualize data in each store
        self.visualize_supabase_data(case_id)
        self.visualize_gcs_data(case_id)
        self.visualize_pinecone_data(case_id)
        self.visualize_neo4j_data(case_id)
        
        logger.info("\n" + "=" * 80)
        logger.info("VISUALIZATION COMPLETE")
        logger.info("=" * 80)
        
        return True


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Visualize case data across databases")
    parser.add_argument("--role", default="partner", help="User role for testing")
    parser.add_argument("--tenant", help="Tenant ID for testing")
    parser.add_argument("--jurisdiction", default="tx", help="Jurisdiction to test")
    parser.add_argument("--query", default="negligence", help="Search query for test data")
    parser.add_argument("--case-id", help="Specific case ID to visualize (optional)")
    
    args = parser.parse_args()
    
    visualizer = CaseDataVisualizer(
        user_role=args.role,
        tenant_id=args.tenant
    )
    
    success = visualizer.run_visualization(
        jurisdiction=args.jurisdiction,
        query=args.query,
        case_id=args.case_id
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

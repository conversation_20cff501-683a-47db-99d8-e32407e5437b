"""
JWT Authentication Middleware for Supabase Auth integration.
Verifies JWT tokens using <PERSON><PERSON><PERSON> and extracts user information.
"""

import os
import json
import logging
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

import requests
from jose import jwt, JWTError
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from src.cache.cache import cache
from src.api.auth.rbac import rbac_manager

logger = logging.getLogger(__name__)


class JWTAuthMiddleware:
    """JWT authentication middleware for Supabase Auth."""
    
    def __init__(self):
        """Initialize JWT middleware."""
        self.jwks_url = os.getenv("SUPABASE_JWKS_URL")
        if not self.jwks_url:
            raise ValueError("SUPABASE_JWKS_URL environment variable is required")
        
        self.jwks_cache_key = "jwks_keys"
        self.jwks_ttl = cache.JWKS_TTL  # 24 hours
        
        # Expected audience for Supabase tokens
        self.expected_audience = "authenticated"
        
        logger.info(f"JWT middleware initialized with JWKS URL: {self.jwks_url}")
    
    def get_jwks_keys(self) -> Dict[str, Any]:
        """
        Get JWKS keys from Supabase, with 24-hour caching.
        Fail closed on key-rotation errors.
        """
        # Try to get from cache first
        cached_keys = cache.get(self.jwks_cache_key)
        if cached_keys:
            logger.debug("Using cached JWKS keys")
            return cached_keys
        
        try:
            logger.info(f"Fetching JWKS keys from {self.jwks_url}")
            response = requests.get(self.jwks_url, timeout=10)
            response.raise_for_status()
            
            jwks_data = response.json()
            
            # Cache the keys
            cache.set(self.jwks_cache_key, jwks_data, self.jwks_ttl)
            logger.info("JWKS keys fetched and cached successfully")
            
            return jwks_data
            
        except Exception as e:
            logger.error(f"Failed to fetch JWKS keys: {e}")
            
            # Try to use cached keys even if expired (fail closed)
            cached_keys = cache.get(self.jwks_cache_key)
            if cached_keys:
                logger.warning("Using expired JWKS keys due to fetch failure")
                return cached_keys
            
            # No cached keys available - fail closed
            raise HTTPException(
                status_code=503,
                detail="Authentication service temporarily unavailable"
            )
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify JWT token and extract user information.
        
        Args:
            token: JWT token string
            
        Returns:
            Dictionary with user information
            
        Raises:
            HTTPException: If token is invalid
        """
        try:
            # Get JWKS keys
            jwks_data = self.get_jwks_keys()
            
            # Decode token header to get key ID
            unverified_header = jwt.get_unverified_header(token)
            key_id = unverified_header.get("kid")
            
            if not key_id:
                raise HTTPException(
                    status_code=401,
                    detail="Token missing key ID"
                )
            
            # Find the matching key
            signing_key = None
            for key in jwks_data.get("keys", []):
                if key.get("kid") == key_id:
                    signing_key = key
                    break
            
            if not signing_key:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid token key ID"
                )
            
            # Verify and decode the token
            payload = jwt.decode(
                token,
                signing_key,
                algorithms=["RS256"],
                audience=self.expected_audience,
                options={"verify_exp": True}
            )
            
            # Extract user information using RBAC manager
            user_info = {
                "user_id": payload.get("sub"),
                "email": payload.get("email"),
                "role": rbac_manager.get_role(payload),
                "aud": payload.get("aud"),
                "exp": payload.get("exp"),
                "iat": payload.get("iat"),
                "permissions": payload.get("app_metadata", {}).get("permissions", []),
                "tenant_id": rbac_manager.get_tenant_id(payload),
                "jurisdictions": rbac_manager.get_jurisdictions(payload)
            }
            
            # Validate required fields
            if not user_info["user_id"]:
                raise HTTPException(
                    status_code=401,
                    detail="Token missing user ID"
                )
            
            if user_info["aud"] != self.expected_audience:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid token audience"
                )
            
            logger.debug(f"Token verified for user: {user_info['user_id']}")
            return user_info
            
        except JWTError as e:
            # Log invalid token attempts with user IP for abuse monitoring
            client_ip = getattr(self, '_current_request_ip', 'unknown')
            logger.warning(f"JWT verification failed from IP {client_ip}: {e}")
            raise HTTPException(
                status_code=401,
                detail="Invalid or expired token"
            )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error during token verification: {e}")
            raise HTTPException(
                status_code=500,
                detail="Authentication error"
            )
    
    def get_user_permissions_hash(self, user_info: Dict[str, Any]) -> str:
        """
        Generate a hash of user permissions for cache keys.
        
        Args:
            user_info: User information from verified token
            
        Returns:
            Hash string representing user permissions
        """
        permissions_data = {
            "user_id": user_info.get("user_id"),
            "role": user_info.get("role"),
            "permissions": sorted(user_info.get("permissions", [])),
            "tenant_id": user_info.get("tenant_id"),
            "jurisdictions": sorted(user_info.get("jurisdictions", []))
        }
        
        permissions_json = json.dumps(permissions_data, sort_keys=True)
        return hashlib.md5(permissions_json.encode()).hexdigest()


class JWTBearer(HTTPBearer):
    """FastAPI dependency for JWT authentication."""
    
    def __init__(self, auto_error: bool = True):
        super().__init__(auto_error=auto_error)
        self.jwt_middleware = JWTAuthMiddleware()
    
    async def __call__(self, request: Request) -> Dict[str, Any]:
        """
        Verify JWT token and return user information.

        Args:
            request: FastAPI request object

        Returns:
            User information dictionary
        """
        credentials: HTTPAuthorizationCredentials = await super().__call__(request)

        if not credentials:
            # Log missing auth header with IP for abuse monitoring
            client_ip = request.client.host if request.client else 'unknown'
            logger.warning(f"Missing authorization header from IP {client_ip}")
            raise HTTPException(
                status_code=401,
                detail="Authorization header required"
            )

        if credentials.scheme.lower() != "bearer":
            # Log invalid auth scheme with IP
            client_ip = request.client.host if request.client else 'unknown'
            logger.warning(f"Invalid authentication scheme from IP {client_ip}: {credentials.scheme}")
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication scheme"
            )

        # Store IP for JWT verification logging
        self.jwt_middleware._current_request_ip = request.client.host if request.client else 'unknown'

        # Verify the token
        user_info = self.jwt_middleware.verify_token(credentials.credentials)

        # Add permissions hash for caching
        user_info["permissions_hash"] = self.jwt_middleware.get_user_permissions_hash(user_info)

        # Attach user info to request state for RBAC
        request.state.user_id = user_info["user_id"]
        request.state.role = user_info["role"]
        request.state.tenant_id = user_info["tenant_id"]
        request.state.jurisdictions = user_info["jurisdictions"]

        return user_info


# Global instances
jwt_middleware = JWTAuthMiddleware()
jwt_bearer = JWTBearer()


def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Dependency function to get current authenticated user.
    
    Usage in FastAPI routes:
    ```python
    @app.get("/protected")
    async def protected_route(user: dict = Depends(get_current_user)):
        return {"user_id": user["user_id"]}
    ```
    """
    return jwt_bearer(request)


def require_role(required_role: str):
    """
    Dependency factory for role-based access control.
    
    Args:
        required_role: Required user role
        
    Usage:
    ```python
    @app.get("/admin")
    async def admin_route(user: dict = Depends(require_role("admin"))):
        return {"message": "Admin access granted"}
    ```
    """
    async def role_checker(user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
        user_role = user.get("role", "user")
        
        # Define role hierarchy
        role_hierarchy = {
            "user": 0,
            "paralegal": 1,
            "attorney": 2,
            "partner": 3,
            "admin": 4
        }
        
        user_level = role_hierarchy.get(user_role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=403,
                detail=f"Insufficient permissions. Required role: {required_role}"
            )
        
        return user
    
    return role_checker


def require_jurisdiction(required_jurisdictions: list):
    """
    Dependency factory for jurisdiction-based access control.
    
    Args:
        required_jurisdictions: List of required jurisdictions
        
    Usage:
    ```python
    @app.get("/texas-cases")
    async def texas_cases(user: dict = Depends(require_jurisdiction(["tx"]))):
        return {"cases": "Texas cases"}
    ```
    """
    async def jurisdiction_checker(user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
        user_jurisdictions = user.get("jurisdictions", [])
        
        # Check if user has access to at least one required jurisdiction
        if not any(jurisdiction in user_jurisdictions for jurisdiction in required_jurisdictions):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required jurisdictions: {required_jurisdictions}"
            )
        
        return user
    
    return jurisdiction_checker

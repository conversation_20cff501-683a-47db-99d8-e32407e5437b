"""
Enhanced Rate Limiter for Week 5 implementation.
Supports both Redis (production) and memory (development) backends.
Implements per-user and per-tenant rate limiting.
"""

import os
import time
import logging
from typing import Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class RateLimitBackend(ABC):
    """Abstract base class for rate limiting backends."""
    
    @abstractmethod
    async def check_limit(self, key: str, limit: int, window: int) -> Tuple[bool, int, int]:
        """
        Check if request is within rate limits.
        
        Args:
            key: Rate limit key
            limit: Maximum requests allowed
            window: Time window in seconds
            
        Returns:
            Tuple of (allowed, remaining, reset_time)
        """
        pass
    
    @abstractmethod
    async def increment(self, key: str, window: int) -> int:
        """
        Increment counter for key.
        
        Args:
            key: Rate limit key
            window: Time window in seconds
            
        Returns:
            Current count
        """
        pass


class RedisRateLimitBackend(RateLimitBackend):
    """Redis-based rate limiting backend for production."""
    
    def __init__(self, redis_url: str, password: Optional[str] = None):
        """Initialize Redis rate limiter."""
        try:
            import redis.asyncio as redis
            self.redis = redis.Redis.from_url(
                redis_url,
                password=password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            logger.info(f"Redis rate limiter initialized: {redis_url}")
        except ImportError:
            raise ImportError("Redis not installed. Run: pip install redis")
        except Exception as e:
            logger.error(f"Failed to connect to Redis for rate limiting: {e}")
            raise
    
    async def check_limit(self, key: str, limit: int, window: int) -> Tuple[bool, int, int]:
        """Check rate limit using Redis INCR + EXPIRE pattern."""
        try:
            current_time = int(time.time())
            reset_time = current_time + window
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis.pipeline()
            pipe.incr(key)
            pipe.expire(key, window)
            results = await pipe.execute()
            
            current_count = results[0]
            
            # Check if within limits
            allowed = current_count <= limit
            remaining = max(0, limit - current_count)
            
            return allowed, remaining, reset_time
            
        except Exception as e:
            logger.error(f"Redis rate limit check error for key '{key}': {e}")
            # Fail open - allow request if Redis is down
            return True, limit, int(time.time()) + window
    
    async def increment(self, key: str, window: int) -> int:
        """Increment counter using Redis."""
        try:
            pipe = self.redis.pipeline()
            pipe.incr(key)
            pipe.expire(key, window)
            results = await pipe.execute()
            return results[0]
        except Exception as e:
            logger.error(f"Redis increment error for key '{key}': {e}")
            return 1


class MemoryRateLimitBackend(RateLimitBackend):
    """Memory-based rate limiting backend for development."""
    
    def __init__(self):
        """Initialize memory rate limiter."""
        self.storage: Dict[str, Dict[str, Any]] = {}
        logger.info("Memory rate limiter initialized")
    
    async def check_limit(self, key: str, limit: int, window: int) -> Tuple[bool, int, int]:
        """Check rate limit using in-memory storage."""
        current_time = time.time()
        
        # Clean up expired entries
        if key in self.storage:
            self.storage[key]["requests"] = [
                req_time for req_time in self.storage[key]["requests"]
                if req_time > current_time - window
            ]
        else:
            self.storage[key] = {"requests": []}
        
        # Check current count
        current_count = len(self.storage[key]["requests"])
        
        # Check if within limits
        if current_count >= limit:
            remaining = 0
            reset_time = int(current_time + window)
            return False, remaining, reset_time
        
        # Add current request
        self.storage[key]["requests"].append(current_time)
        remaining = limit - (current_count + 1)
        reset_time = int(current_time + window)
        
        return True, remaining, reset_time
    
    async def increment(self, key: str, window: int) -> int:
        """Increment counter using memory storage."""
        current_time = time.time()
        
        if key not in self.storage:
            self.storage[key] = {"requests": []}
        
        # Clean up expired entries
        self.storage[key]["requests"] = [
            req_time for req_time in self.storage[key]["requests"]
            if req_time > current_time - window
        ]
        
        # Add current request
        self.storage[key]["requests"].append(current_time)
        return len(self.storage[key]["requests"])


class EnhancedRateLimiter:
    """Enhanced rate limiter with per-user and per-tenant limits."""
    
    def __init__(self):
        """Initialize enhanced rate limiter."""
        self.backend = self._initialize_backend()
        
        # Rate limit configuration
        self.user_limit = int(os.getenv("RATE_LIMIT_PER_USER", "100"))  # per minute
        self.tenant_limit = int(os.getenv("RATE_LIMIT_PER_TENANT", "1000"))  # per minute
        self.window = 60  # 1 minute window
        
        logger.info(f"Enhanced rate limiter initialized: user_limit={self.user_limit}, tenant_limit={self.tenant_limit}")
    
    def _initialize_backend(self) -> RateLimitBackend:
        """Initialize the appropriate rate limiting backend."""
        backend_type = os.getenv("RATE_LIMIT_BACKEND", "memory").lower()
        
        if backend_type == "redis":
            redis_url = os.getenv("REDIS_URL")
            if redis_url:
                redis_password = os.getenv("REDIS_PASSWORD")
                try:
                    return RedisRateLimitBackend(redis_url, redis_password)
                except Exception as e:
                    logger.warning(f"Failed to initialize Redis rate limiter, falling back to memory: {e}")
                    return MemoryRateLimitBackend()
            else:
                logger.warning("REDIS_URL not set, using memory rate limiter")
                return MemoryRateLimitBackend()
        else:
            return MemoryRateLimitBackend()
    
    async def check_limit(self, user_id: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check rate limits for user and tenant.
        
        Args:
            user_id: User ID
            tenant_id: Optional tenant ID
            
        Returns:
            Rate limit check result
        """
        # Check user rate limit
        user_key = f"user:{user_id}"
        user_allowed, user_remaining, user_reset = await self.backend.check_limit(
            user_key, self.user_limit, self.window
        )
        
        # Check tenant rate limit if tenant_id provided
        tenant_allowed = True
        tenant_remaining = self.tenant_limit
        tenant_reset = int(time.time()) + self.window
        
        if tenant_id:
            tenant_key = f"tenant:{tenant_id}"
            tenant_allowed, tenant_remaining, tenant_reset = await self.backend.check_limit(
                tenant_key, self.tenant_limit, self.window
            )
        
        # Overall decision - both must be allowed
        overall_allowed = user_allowed and tenant_allowed
        
        # Use the most restrictive remaining count
        overall_remaining = min(user_remaining, tenant_remaining)
        
        # Use the earliest reset time
        overall_reset = min(user_reset, tenant_reset)
        
        return {
            "allowed": overall_allowed,
            "remaining": overall_remaining,
            "reset_time": overall_reset,
            "user_limit": self.user_limit,
            "tenant_limit": self.tenant_limit,
            "user_remaining": user_remaining,
            "tenant_remaining": tenant_remaining,
            "retry_after": 60 if not overall_allowed else None
        }
    
    def get_headers(self, rate_limit_result: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate rate limit headers for response.
        
        Args:
            rate_limit_result: Result from check_limit
            
        Returns:
            Dictionary of headers
        """
        headers = {
            "X-RateLimit-Limit": str(self.user_limit),
            "X-RateLimit-Remaining": str(rate_limit_result["remaining"]),
            "X-RateLimit-Reset": str(rate_limit_result["reset_time"])
        }
        
        if rate_limit_result.get("retry_after"):
            headers["Retry-After"] = str(rate_limit_result["retry_after"])
        
        return headers


# Global rate limiter instance
rate_limiter = EnhancedRateLimiter()


async def check_rate_limit(user_id: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
    """Check rate limits for user and tenant."""
    return await rate_limiter.check_limit(user_id, tenant_id)


def get_rate_limit_headers(rate_limit_result: Dict[str, Any]) -> Dict[str, str]:
    """Get rate limit headers for response."""
    return rate_limiter.get_headers(rate_limit_result)

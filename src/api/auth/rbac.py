"""
Role-Based Access Control (RBAC) implementation for Week 5.
Provides role hierarchy and permission validation for the legal database API.
"""

import logging
from typing import Dict, List, Optional, Any
from enum import IntEnum

logger = logging.getLogger(__name__)


class Role(IntEnum):
    """Role hierarchy with numeric values for easy comparison."""
    CLIENT = 1
    STAFF = 2
    PARALEGAL = 3
    ATTORNEY = 4
    PARTNER = 5


# Role hierarchy mapping
ROLE_HIERARCHY = {
    "client": Role.CLIENT,
    "staff": Role.STAFF,
    "paralegal": Role.PARALEGAL,
    "attorney": Role.ATTORNEY,
    "partner": Role.PARTNER,
}

# Reverse mapping for display purposes
ROLE_NAMES = {v: k for k, v in ROLE_HIERARCHY.items()}


class RBACManager:
    """Role-Based Access Control manager."""
    
    def __init__(self):
        """Initialize RBAC manager."""
        self.role_hierarchy = ROLE_HIERARCHY
        logger.info("RBAC Manager initialized")
    
    def get_role(self, payload: Dict[str, Any]) -> str:
        """
        Extract role from JWT payload.
        
        Args:
            payload: JWT token payload
            
        Returns:
            Role string (defaults to 'client' if not found)
        """
        # Try direct role field first
        role = payload.get("role")
        if role and role in self.role_hierarchy:
            return role
        
        # Try app_metadata.role
        app_metadata = payload.get("app_metadata", {})
        role = app_metadata.get("role")
        if role and role in self.role_hierarchy:
            return role
        
        # Try user_metadata.role
        user_metadata = payload.get("user_metadata", {})
        role = user_metadata.get("role")
        if role and role in self.role_hierarchy:
            return role
        
        # Default to client role
        logger.warning(f"No valid role found in payload for user {payload.get('sub', 'unknown')}, defaulting to 'client'")
        return "client"
    
    def get_role_level(self, role: str) -> int:
        """
        Get numeric level for role.
        
        Args:
            role: Role string
            
        Returns:
            Numeric role level
        """
        return self.role_hierarchy.get(role.lower(), Role.CLIENT)
    
    def has_permission(self, user_role: str, required_role: str) -> bool:
        """
        Check if user role has permission for required role.
        
        Args:
            user_role: User's current role
            required_role: Required role for access
            
        Returns:
            True if user has sufficient permissions
        """
        user_level = self.get_role_level(user_role)
        required_level = self.get_role_level(required_role)
        
        return user_level >= required_level
    
    def can_access_jurisdiction(self, user_jurisdictions: List[str], 
                               requested_jurisdiction: Optional[str]) -> bool:
        """
        Check if user can access the requested jurisdiction.
        
        Args:
            user_jurisdictions: List of jurisdictions user has access to
            requested_jurisdiction: Jurisdiction being requested
            
        Returns:
            True if user can access the jurisdiction
        """
        # If no specific jurisdiction requested, allow access
        if not requested_jurisdiction:
            return True
        
        # If user has no jurisdiction restrictions, allow access
        if not user_jurisdictions:
            return True
        
        # Check if requested jurisdiction is in user's allowed list
        return requested_jurisdiction.lower() in [j.lower() for j in user_jurisdictions]
    
    def filter_results_by_jurisdiction(self, results: List[Dict[str, Any]], 
                                     user_jurisdictions: List[str]) -> List[Dict[str, Any]]:
        """
        Filter search results based on user's jurisdiction access.
        
        Args:
            results: List of search results
            user_jurisdictions: User's allowed jurisdictions
            
        Returns:
            Filtered results
        """
        # If user has no jurisdiction restrictions, return all results
        if not user_jurisdictions:
            return results
        
        # Filter results by jurisdiction
        allowed_jurisdictions = [j.lower() for j in user_jurisdictions]
        filtered_results = []
        
        for result in results:
            result_jurisdiction = result.get('jurisdiction', '').lower()
            if result_jurisdiction in allowed_jurisdictions:
                filtered_results.append(result)
        
        return filtered_results
    
    def get_tenant_id(self, payload: Dict[str, Any]) -> Optional[str]:
        """
        Extract tenant ID from JWT payload.
        
        Args:
            payload: JWT token payload
            
        Returns:
            Tenant ID if found
        """
        # Try direct tenant_id field
        tenant_id = payload.get("tenant_id")
        if tenant_id:
            return tenant_id
        
        # Try app_metadata.tenant_id
        app_metadata = payload.get("app_metadata", {})
        tenant_id = app_metadata.get("tenant_id")
        if tenant_id:
            return tenant_id
        
        # Try user_metadata.tenant_id
        user_metadata = payload.get("user_metadata", {})
        tenant_id = user_metadata.get("tenant_id")
        if tenant_id:
            return tenant_id
        
        return None
    
    def get_jurisdictions(self, payload: Dict[str, Any]) -> List[str]:
        """
        Extract allowed jurisdictions from JWT payload.
        
        Args:
            payload: JWT token payload
            
        Returns:
            List of allowed jurisdictions
        """
        # Try direct jurisdictions field
        jurisdictions = payload.get("jurisdictions")
        if jurisdictions and isinstance(jurisdictions, list):
            return jurisdictions
        
        # Try app_metadata.jurisdictions
        app_metadata = payload.get("app_metadata", {})
        jurisdictions = app_metadata.get("jurisdictions")
        if jurisdictions and isinstance(jurisdictions, list):
            return jurisdictions
        
        # Try user_metadata.jurisdictions
        user_metadata = payload.get("user_metadata", {})
        jurisdictions = user_metadata.get("jurisdictions")
        if jurisdictions and isinstance(jurisdictions, list):
            return jurisdictions
        
        # Default to empty list (no restrictions)
        return []
    
    def validate_access(self, user_info: Dict[str, Any], 
                       required_role: Optional[str] = None,
                       requested_jurisdiction: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate user access based on role and jurisdiction.
        
        Args:
            user_info: User information from JWT
            required_role: Required role for access
            requested_jurisdiction: Requested jurisdiction
            
        Returns:
            Validation result with access decision and details
        """
        result = {
            "access_granted": True,
            "user_role": user_info.get("role", "client"),
            "user_jurisdictions": user_info.get("jurisdictions", []),
            "tenant_id": user_info.get("tenant_id"),
            "reasons": []
        }
        
        # Check role-based access
        if required_role:
            if not self.has_permission(result["user_role"], required_role):
                result["access_granted"] = False
                result["reasons"].append(f"Insufficient role: required {required_role}, user has {result['user_role']}")
        
        # Check jurisdiction-based access
        if requested_jurisdiction:
            if not self.can_access_jurisdiction(result["user_jurisdictions"], requested_jurisdiction):
                result["access_granted"] = False
                result["reasons"].append(f"No access to jurisdiction: {requested_jurisdiction}")
        
        return result
    
    def log_access_attempt(self, user_id: str, role: str, tenant_id: Optional[str],
                          endpoint: str, status_code: int, 
                          client_ip: str = "unknown") -> None:
        """
        Log access attempt for audit purposes.
        
        Args:
            user_id: User ID
            role: User role
            tenant_id: Tenant ID
            endpoint: API endpoint accessed
            status_code: HTTP status code
            client_ip: Client IP address
        """
        log_data = {
            "user_id": user_id,
            "role": role,
            "tenant_id": tenant_id,
            "endpoint": endpoint,
            "status_code": status_code,
            "client_ip": client_ip
        }
        
        if status_code == 403:
            logger.warning(f"Access denied: {log_data}")
        else:
            logger.info(f"Access granted: {log_data}")


# Global RBAC manager instance
rbac_manager = RBACManager()


def get_role(payload: Dict[str, Any]) -> str:
    """Get role from JWT payload."""
    return rbac_manager.get_role(payload)


def has_permission(role: str, required: str) -> bool:
    """Check if role has required permission."""
    return rbac_manager.has_permission(role, required)


def validate_access(user_info: Dict[str, Any], 
                   required_role: Optional[str] = None,
                   requested_jurisdiction: Optional[str] = None) -> Dict[str, Any]:
    """Validate user access."""
    return rbac_manager.validate_access(user_info, required_role, requested_jurisdiction)


def log_access_attempt(user_id: str, role: str, tenant_id: Optional[str],
                      endpoint: str, status_code: int, 
                      client_ip: str = "unknown") -> None:
    """Log access attempt."""
    return rbac_manager.log_access_attempt(user_id, role, tenant_id, endpoint, status_code, client_ip)

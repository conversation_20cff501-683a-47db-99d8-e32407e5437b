"""
Exceptions for Court Listener API client
"""

class CourtListenerAPIError(Exception):
    """Base exception for Court Listener API errors"""
    
    def __init__(self, message, status_code=None, response=None):
        self.message = message
        self.status_code = status_code
        self.response = response
        super().__init__(self.message)


class CourtListenerAuthenticationError(CourtListenerAPIError):
    """Exception raised for authentication errors"""
    
    def __init__(self, message="Authentication failed with Court Listener API", status_code=401, response=None):
        super().__init__(message, status_code, response)


class CourtListenerRateLimitError(CourtListenerAPIError):
    """Exception raised when rate limit is exceeded"""
    
    def __init__(self, message="Rate limit exceeded for Court Listener API", status_code=429, response=None):
        super().__init__(message, status_code, response)


class CourtListenerResourceNotFoundError(CourtListenerAPIError):
    """Exception raised when a requested resource is not found"""
    
    def __init__(self, message="Resource not found", status_code=404, response=None):
        super().__init__(message, status_code, response)

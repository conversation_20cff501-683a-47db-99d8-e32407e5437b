"""
Data models for Court Listener API responses
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime


@dataclass
class Opinion:
    """Represents an opinion from a case"""
    id: str
    case_name: str
    text: Optional[str] = None
    html: Optional[str] = None
    markdown: Optional[str] = None
    author: Optional[str] = None
    type: Optional[str] = None
    download_url: Optional[str] = None
    date_created: Optional[datetime] = None
    date_modified: Optional[datetime] = None
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_api_response(cls, data):
        """Create an Opinion instance from API response data"""
        # Handle date parsing safely
        date_created = None
        date_modified = None
        
        if data.get('date_created'):
            try:
                date_str = data.get('date_created', '')
                # Handle different date formats
                if 'Z' in date_str:
                    date_str = date_str.replace('Z', '+00:00')
                if 'T' not in date_str and ' ' in date_str:
                    date_str = date_str.replace(' ', 'T')
                date_created = datetime.fromisoformat(date_str)
            except (ValueError, TypeError) as e:
                pass
                
        if data.get('date_modified'):
            try:
                date_str = data.get('date_modified', '')
                # Handle different date formats
                if 'Z' in date_str:
                    date_str = date_str.replace('Z', '+00:00')
                if 'T' not in date_str and ' ' in date_str:
                    date_str = date_str.replace(' ', 'T')
                date_modified = datetime.fromisoformat(date_str)
            except (ValueError, TypeError) as e:
                pass
                
        return cls(
            id=data.get('id', ''),
            case_name=data.get('case_name', ''),
            text=data.get('plain_text', None),
            html=data.get('html', None) or data.get('html_lawbox', None) or data.get('html_columbia', None),
            markdown=data.get('html_with_citations', None),  # Best approximation for markdown
            author=data.get('author', None),
            type=data.get('type', None),
            download_url=data.get('download_url', None),
            date_created=date_created,
            date_modified=date_modified,
            raw_data=data
        )


@dataclass
class Citation:
    """Represents a citation for a case"""
    volume: str
    reporter: str
    page: str
    type: Optional[str] = None
    full_citation: Optional[str] = None
    
    def __str__(self):
        return self.full_citation or f"{self.volume} {self.reporter} {self.page}"


@dataclass
class Judge:
    """Represents a judge associated with a case"""
    name: str
    id: Optional[str] = None
    

@dataclass
class Court:
    """Represents a court"""
    id: str
    name: str
    jurisdiction: str
    citation_string: Optional[str] = None
    url: Optional[str] = None
    

@dataclass
class Case:
    """Represents a case from Court Listener"""
    id: str
    name: str
    court: Court
    date_filed: Optional[datetime] = None
    docket_number: Optional[str] = None
    citations: List[Citation] = field(default_factory=list)
    judges: List[Judge] = field(default_factory=list)
    opinions: List[Opinion] = field(default_factory=list)
    absolute_url: Optional[str] = None
    precedential_status: Optional[str] = None
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_api_response(cls, data):
        """Create a Case instance from API response data"""
        # Ensure data is a dictionary
        if not isinstance(data, dict):
            raise ValueError(f"Expected dictionary for case data, got {type(data)}")
        
        # Process citations
        citations = []
        for cite in data.get('citations', []):
            if isinstance(cite, dict):
                citations.append(Citation(
                    volume=cite.get('volume', ''),
                    reporter=cite.get('reporter', ''),
                    page=cite.get('page', ''),
                    type=cite.get('type', None),
                    full_citation=f"{cite.get('volume', '')} {cite.get('reporter', '')} {cite.get('page', '')}"
                ))
        
        # Process court information
        court_data = data.get('court', {})
        if not isinstance(court_data, dict):
            court_data = {}
        
        court = Court(
            id=court_data.get('id', ''),
            name=court_data.get('full_name', '') or court_data.get('short_name', ''),
            jurisdiction=court_data.get('jurisdiction', ''),
            citation_string=court_data.get('citation_string', None),
            url=court_data.get('resource_uri', None)
        )
        
        # Process judges
        judges = []
        judges_text = data.get('judges', '') or ''
        if isinstance(judges_text, str):
            for judge_name in [j.strip() for j in judges_text.split(',') if j.strip()]:
                judges.append(Judge(name=judge_name))
        
        # Process date
        date_filed = None
        if data.get('date_filed'):
            try:
                date_str = data.get('date_filed', '')
                # Handle different date formats
                if isinstance(date_str, str):
                    if 'Z' in date_str:
                        date_str = date_str.replace('Z', '+00:00')
                    if 'T' not in date_str and ' ' in date_str:
                        date_str = date_str.replace(' ', 'T')
                    date_filed = datetime.fromisoformat(date_str)
            except (ValueError, TypeError):
                pass
        
        return cls(
            id=data.get('id', ''),
            name=data.get('case_name', ''),
            court=court,
            date_filed=date_filed,
            docket_number=data.get('docket_number', None),
            citations=citations,
            judges=judges,
            opinions=[],  # Opinions usually come in a separate API call
            absolute_url=data.get('absolute_url', None),
            precedential_status=data.get('precedential_status', None),
            raw_data=data
        )
    
    def add_opinion(self, opinion):
        """Add an opinion to this case"""
        if isinstance(opinion, Opinion):
            self.opinions.append(opinion)
        elif isinstance(opinion, dict):
            self.opinions.append(Opinion.from_api_response(opinion))

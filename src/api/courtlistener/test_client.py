"""
Test script for Court Listener API client
"""

import os
import sys
import json
from dotenv import load_dotenv

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from src.api.courtlistener.client import CourtListenerClient
from src.api.courtlistener.exceptions import CourtListenerAPIError

# Load environment variables
load_dotenv()


def test_search_by_citation():
    """Test searching for cases by citation"""
    client = CourtListenerClient()
    
    print("\n===== Testing Citation Search =====")
    citation = "410 U.S. 113"  # <PERSON> v. <PERSON>
    print(f"Searching for: {citation}")
    
    try:
        cases = client.get_cases_by_citation(citation)
        
        print(f"\nFound {len(cases)} cases:")
        for i, case in enumerate(cases):
            print(f"\nCase {i+1}:")
            print(f"  Name: {case.name}")
            print(f"  Court: {case.court.name}")
            print(f"  Date: {case.date_filed}")
            print(f"  Docket Number: {case.docket_number}")
            
            if case.citations:
                print(f"  Citations:")
                for cite in case.citations:
                    print(f"    {cite}")
            
            if case.judges:
                print(f"  Judges:")
                for judge in case.judges:
                    print(f"    {judge.name}")
                    
        return cases[0].id if cases else None
    
    except CourtListenerAPIError as e:
        print(f"Error: {e}")
        return None


def test_get_opinions(case_id):
    """Test getting opinions for a case"""
    if not case_id:
        print("No case ID provided, skipping opinions test")
        return
    
    client = CourtListenerClient()
    
    print("\n===== Testing Opinion Retrieval =====")
    print(f"Getting opinions for case ID: {case_id}")
    
    try:
        opinions = client.get_opinions_by_case(case_id)
        
        print(f"\nFound {len(opinions)} opinions:")
        for i, opinion in enumerate(opinions):
            print(f"\nOpinion {i+1}:")
            print(f"  ID: {opinion.id}")
            print(f"  Case Name: {opinion.case_name}")
            print(f"  Author: {opinion.author}")
            print(f"  Type: {opinion.type}")
            print(f"  Download URL: {opinion.download_url}")
            print(f"  Has Text: {'Yes' if opinion.text else 'No'}")
            print(f"  Has HTML: {'Yes' if opinion.html else 'No'}")
            
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def test_texas_search():
    """Test searching for Texas cases"""
    client = CourtListenerClient()
    
    print("\n===== Testing Texas Case Search =====")
    query = "personal injury"
    print(f"Searching for: '{query}' in Texas jurisdiction")
    
    try:
        results = client.get_texas_cases(query, page_size=5)
        
        print(f"\nFound {results.get('count', 0)} total results (showing first 5):")
        for i, result in enumerate(results.get('results', [])):
            case = {
                'name': result.get('case_name', 'Unknown'),
                'court': result.get('court', {}).get('full_name', 'Unknown'),
                'date': result.get('date_filed', 'Unknown'),
                'precedential_status': result.get('precedential_status', 'Unknown')
            }
            print(f"\nResult {i+1}:")
            print(f"  Name: {case['name']}")
            print(f"  Court: {case['court']}")
            print(f"  Date: {case['date']}")
            print(f"  Status: {case['precedential_status']}")
            
    except CourtListenerAPIError as e:
        print(f"Error: {e}")


def run_all_tests():
    """Run all test functions"""
    print("\n=================================================")
    print("COURT LISTENER API CLIENT TEST")
    print("=================================================")
    
    case_id = test_search_by_citation()
    test_get_opinions(case_id)
    test_texas_search()
    
    print("\n=================================================")
    print("TESTS COMPLETE")
    print("=================================================")


if __name__ == "__main__":
    run_all_tests()

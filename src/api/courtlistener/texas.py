"""
Texas Case Law Specialized Module
Provides optimized access to Texas case law through Court Listener API
"""

import os
import json
import logging
import time
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from enum import Enum

from .client import CourtListenerClient
from .models import Case, Opinion, Court
from .exceptions import CourtListenerAPIError, CourtListenerResourceNotFoundError
from .utils import standardize_jurisdiction, format_date

# Configure logging
logger = logging.getLogger(__name__)


class CourtLevel(Enum):
    """Enum representing different court levels in Texas"""
    SUPREME = "supreme"
    APPEALS = "appeals"
    CRIMINAL = "criminal"
    DISTRICT = "district"
    COUNTY = "county"
    ALL = "all"


class TexasCaseClient:
    """
    Specialized client for Texas case law
    Provides optimized access to Texas-specific case law with multi-stage retrieval
    for improved data completeness
    """
    
    # We'll use a court name filter approach instead of jurisdiction code
    # since the API doesn't have a specific Texas jurisdiction code
    TEXAS_STATE_CODE = "TX"
    TEXAS_SEARCH_TERMS = "texas"
    TEXAS_COURTS = {
        "Supreme Court of Texas": "tex-sc",
        "Texas Court of Criminal Appeals": "tex-cca",
        "Texas Court of Appeals": "tex-ca",  # Contains multiple district courts
        "Texas District Courts": "tex-dist",
        "Texas County Courts": "tex-county"
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Texas case client"""
        self.client = CourtListenerClient(api_key)
        self._courts = None  # Cache for Texas courts
        
        # Quality validation metrics
        self.validation_metrics = {
            "total_results": 0,
            "complete_records": 0,
            "incomplete_records": 0,
            "missing_fields": {},
            "search_terms": [],
            "court_levels_found": set(),
            "date_range": {"earliest": None, "latest": None},
            "completeness_percentage": 0
        }
    
    def search(self, query: str, page: int = 1, page_size: int = 20, 
               court_type: Optional[str] = None, year_min: Optional[int] = None, 
               year_max: Optional[int] = None, **kwargs) -> Dict:
        """
        Search for Texas cases
        
        Args:
            query: Search query
            page: Page number
            page_size: Results per page
            court_type: Type of court (supreme, appeals, district)
            year_min: Minimum year for date range
            year_max: Maximum year for date range
            **kwargs: Additional search parameters
            
        Returns:
            Search results
        """
        # Based on the Court Listener documentation, we'll use a text search approach
        # with state filtering to find Texas cases
        params = {
            "page": page,
            "page_size": page_size
        }
        
        # Build the query with Texas filter
        # The "texas" term will find cases with Texas in the text
        # which includes cases heard in Texas courts
        texas_query = f"{query} texas"
        
        # Add date range parameters if provided
        if year_min:
            params["filed_after"] = f"{year_min}-01-01"
        if year_max:
            params["filed_before"] = f"{year_max}-12-31"
        
        # Add court type specificity if provided
        if court_type:
            if court_type.lower() == "supreme":
                texas_query += " supreme court of texas"
            elif court_type.lower() == "appeals":
                texas_query += " court of appeals"
            elif court_type.lower() == "criminal":
                texas_query += " court of criminal appeals"
            elif court_type.lower() == "district":
                texas_query += " district court"
        
        # Add any additional parameters
        params.update(kwargs)
        
        # Use the text-based query approach to find Texas cases
        response = self.client.search_cases(query=texas_query, **params)
        
        # Log the search information
        logger.info(f"Searched for Texas cases with query: '{texas_query}'")
        
        return response
    
    def get_personal_injury_cases(self, subtype: Optional[str] = None, 
                                 page: int = 1, page_size: int = 20,
                                 court_level: Optional[CourtLevel] = CourtLevel.ALL,
                                 get_complete_data: bool = False,
                                 max_complete_cases: int = 5) -> Dict:
        """
        Get Texas personal injury cases
        
        Args:
            subtype: Injury subtype (slip-and-fall, auto-accident, etc.)
            page: Page number
            page_size: Results per page
            court_level: Level of court to filter by
            get_complete_data: Whether to fetch complete case data for each result
            max_complete_cases: Maximum number of cases to fetch complete data for
            
        Returns:
            Search results for personal injury cases, with complete data if requested
        """
        query = "personal injury"
        
        if subtype:
            if subtype.lower() == "slip-and-fall" or subtype.lower() == "premises":
                query += " premises liability slip and fall"
            elif subtype.lower() == "auto" or subtype.lower() == "car":
                query += " automobile accident car crash"
            elif subtype.lower() == "medical":
                query += " medical malpractice"
            elif subtype.lower() == "product":
                query += " product liability defective"
            else:
                query += f" {subtype}"
        
        # For backward compatibility with tests, use the original approach
        if get_complete_data:
            # First get basic search results
            basic_results = self.search(query, page=page, page_size=page_size)
            
            # Instead of using _enhance_search_results_with_complete_data,
            # directly use search_opinions for the top results
            enhanced_results = basic_results.copy()
            enhanced_count = 0
            enhanced_items = []
            
            # Process only up to max_complete_cases
            for i, result in enumerate(basic_results.get("results", [])):
                if i >= max_complete_cases:
                    enhanced_items.append(result)
                    continue
                    
                # Get cluster/case ID
                cluster_id = result.get("cluster_id") or result.get("id")
                if not cluster_id:
                    enhanced_items.append(result)
                    continue
                
                try:
                    # Get opinions with full text directly using search_opinions
                    opinion_results = self.client.search_opinions(
                        cluster=cluster_id,
                        full_case=True,
                        page=1,
                        page_size=5  # Limit to 5 opinions per case
                    )
                    
                    # Get docket information if available
                    docket_id = None
                    for opinion in opinion_results.get("results", []):
                        if "cluster" in opinion and "docket_id" in opinion.get("cluster", {}):
                            docket_id = opinion["cluster"]["docket_id"]
                            break
                    
                    # Add opinions to the result
                    enhanced_item = {**result, "_complete_data": True}
                    enhanced_item["opinions"] = opinion_results.get("results", [])
                    
                    # Always initialize docket field (required by tests)
                    enhanced_item["docket"] = {}
                    
                    # Fetch and add docket information if available
                    if docket_id:
                        try:
                            docket = self.client.get_docket(docket_id)
                            enhanced_item["docket"] = docket
                        except Exception as e:
                            logger.warning(f"Error getting docket {docket_id}: {str(e)}")
                    
                    enhanced_items.append(enhanced_item)
                    enhanced_count += 1
                except Exception as e:
                    logger.warning(f"Error getting opinions for case {cluster_id}: {str(e)}")
                    enhanced_items.append(result)
            
            # Update the results list with enhanced items
            enhanced_results["results"] = enhanced_items
            enhanced_results["enhanced_count"] = enhanced_count
            
            # Calculate validation metrics with the enhanced data
            self._calculate_validation_metrics(enhanced_items, [query])
            
            # Add validation report
            validation_report = self.get_validation_report()
            enhanced_results["validation"] = validation_report
            
            return enhanced_results
        else:
            # Just basic search for non-enhanced requests
            search_results = self.search(query, page=page, page_size=page_size)
            
            # Add validation report
            validation_report = self.get_validation_report()
            search_results["validation"] = validation_report
            
            return search_results
    
    def get_premises_liability_cases(self, page: int = 1, page_size: int = 20,
                                    court_level: Optional[CourtLevel] = CourtLevel.ALL,
                                    get_complete_data: bool = False,
                                    max_complete_cases: int = 5) -> Dict:
        """
        Get Texas premises liability cases
        
        Args:
            page: Page number
            page_size: Results per page
            court_level: Level of court to filter by
            get_complete_data: Whether to fetch complete case data for each result
            max_complete_cases: Maximum number of cases to fetch complete data for
            
        Returns:
            Search results for premises liability cases, with complete data if requested
        """
        # Get initial search results
        search_results = self.search("premises liability", page=page, page_size=page_size,
                                    court_type=court_level.value if court_level != CourtLevel.ALL else None)
        
        if get_complete_data:
            # Add complete case data for top results
            return self._enhance_search_results_with_complete_data(search_results, max_complete_cases)
        
        return search_results
    
    def get_case_by_id(self, case_id: str, get_complete_data: bool = True) -> Dict:
        """
        Get a case by ID
        
        Args:
            case_id: Case/cluster ID
            get_complete_data: Whether to fetch complete case data
            
        Returns:
            Case data with opinions and docket if requested
        """
        if get_complete_data:
            return self.client.get_complete_case(case_id)
        else:
            return self.client.get_case(case_id)
    
    def get_texas_statutes(self, statute: str, page: int = 1, page_size: int = 20) -> Dict:
        """
        Search for Texas cases referencing specific statutes
        
        Args:
            statute: Statute reference (e.g., "CPRC 101.021" for Texas Civil Practice and Remedies Code)
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results mentioning the statute
        """
        query = f"\"{statute}\""
        return self.search(query, page=page, page_size=page_size)
    
    def get_texas_courts(self, refresh: bool = False) -> List[Court]:
        """
        Get list of Texas courts
        
        Args:
            refresh: Whether to refresh the cached courts list
            
        Returns:
            List of Texas Court objects
        """
        if self._courts is None or refresh:
            # Get all courts and filter for Texas
            # Note: The Court Listener API may not have explicit Texas courts 
            # in its court list, but we can still search for Texas cases.
            # This is more of a helper method to identify any Texas-related courts
            # that might be in the system
            all_courts = self.client.get_courts()
            self._courts = []
            
            # Look for courts with Texas in the name
            for court in all_courts:
                if court.name and any(term in court.name.lower() 
                                    for term in ["texas", "tex", "tx"]):
                    self._courts.append(court)
                    
            # If no Texas courts found explicitly, create a synthetic list
            # based on what we know about Texas court structure
            if not self._courts:
                self._courts = [
                    Court(id="tx-sc", name="Supreme Court of Texas", 
                         jurisdiction="TX", citation_string=None, url=None),
                    Court(id="tx-cca", name="Texas Court of Criminal Appeals", 
                         jurisdiction="TX", citation_string=None, url=None),
                    Court(id="tx-ca", name="Texas Courts of Appeals", 
                         jurisdiction="TX", citation_string=None, url=None)
                ]
                
        return self._courts
    
    def get_texas_supreme_court_cases(self, query: str, page: int = 1, page_size: int = 20) -> Dict:
        """
        Get Texas Supreme Court cases
        
        Args:
            query: Search query
            page: Page number
            page_size: Results per page
            
        Returns:
            Search results from the Texas Supreme Court
        """
        court_ids = self._get_court_ids_by_type("supreme")
        results = self.search(query, page=page, page_size=page_size, court=court_ids)
        
        # Calculate validation metrics
        self._calculate_validation_metrics(results, [query, "supreme court"])
        
        return results
        
    def get_criminal_defense_cases(self, query: Optional[str] = None, 
                                   page: int = 1, page_size: int = 20,
                                   court_level: Optional[CourtLevel] = CourtLevel.ALL,
                                   get_complete_data: bool = False,
                                   max_complete_cases: int = 5) -> Dict:
        """
        Get Texas criminal defense cases
        
        Args:
            query: Additional search terms
            page: Page number
            page_size: Results per page
            court_level: Level of court to filter by
            get_complete_data: Whether to fetch complete case data for each result
            max_complete_cases: Maximum number of cases to fetch complete data for
            
        Returns:
            Search results for criminal defense cases, with complete data if requested
        """
        search_query = "criminal defense arrest trial evidence"
        
        if query:
            search_query = f"{search_query} {query}"
        
        court_type = None
        if court_level != CourtLevel.ALL:
            court_type = court_level.value
            
        results = self.search(search_query, page=page, page_size=page_size, court_type=court_type)
        
        # Calculate validation metrics
        self._calculate_validation_metrics(results, ["criminal defense", "arrest", "trial", "evidence"])
        
        if get_complete_data:
            # Add complete case data for top results
            return self._enhance_search_results_with_complete_data(results, max_complete_cases)
        
        return results
        
    def get_family_law_cases(self, query: Optional[str] = None,
                             page: int = 1, page_size: int = 20,
                             court_level: Optional[CourtLevel] = CourtLevel.ALL,
                             get_complete_data: bool = False,
                             max_complete_cases: int = 5) -> Dict:
        """
        Get Texas family law cases
        
        Args:
            query: Additional search terms
            page: Page number
            page_size: Results per page
            court_level: Level of court to filter by
            get_complete_data: Whether to fetch complete case data for each result
            max_complete_cases: Maximum number of cases to fetch complete data for
            
        Returns:
            Search results for family law cases, with complete data if requested
        """
        search_query = "family law divorce custody child support"
        
        if query:
            search_query = f"{search_query} {query}"
            
        court_type = None
        if court_level != CourtLevel.ALL:
            court_type = court_level.value
        
        results = self.search(search_query, page=page, page_size=page_size, court_type=court_type)
        
        # Calculate validation metrics
        self._calculate_validation_metrics(results, ["family law", "divorce", "custody", "child support"])
        
        if get_complete_data:
            # Add complete case data for top results
            return self._enhance_search_results_with_complete_data(results, max_complete_cases)
        
        return results
    
    def _get_court_ids_by_type(self, court_type: str) -> Optional[str]:
        """
        Get court IDs by type
        
        Args:
            court_type: Type of court (supreme, appeals, district)
            
        Returns:
            Court ID or comma-separated IDs
        """
        if court_type.lower() == "supreme":
            return self.TEXAS_COURTS["Supreme Court of Texas"]
        elif court_type.lower() == "appeals":
            return self.TEXAS_COURTS["Texas Court of Appeals"]
        elif court_type.lower() == "criminal":
            return self.TEXAS_COURTS["Texas Court of Criminal Appeals"]
        elif court_type.lower() == "district":
            return self.TEXAS_COURTS["Texas District Courts"]
        
        # If no match, return None
        return None
        
    def save_search_results(self, results: Union[Dict, List], filename: str) -> str:
        """
        Save search results to a file
        
        Args:
            results: Search results from the API (can be dict or list)
            filename: Filename to save to
            
        Returns:
            Path to the saved file
        """
        # Create the output directory if it doesn't exist
        output_dir = os.path.join("processed_documents", "texas_cases")
        os.makedirs(output_dir, exist_ok=True)
        
        # Add timestamp to filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if not filename.endswith(".json"):
            filename = f"{filename}.json"
        
        full_path = os.path.join(output_dir, f"{filename.replace('.json', '')}_{timestamp}.json")
        
        # Handle different input formats and normalize
        if isinstance(results, list):
            results_list = results
            count = len(results)
            page = 1
            page_count = 1
        elif isinstance(results, dict) and "results" in results:
            results_list = results.get("results", [])
            count = results.get("count", len(results_list))
            page = results.get("page", 1)
            page_count = results.get("page_count", 1)
        else:
            # Handle unexpected format
            logger.warning(f"Unexpected results format: {type(results)}")
            results_list = []
            count = 0
            page = 1
            page_count = 1
        
        # Format the results
        formatted_results = {
            "meta": {
                "timestamp": datetime.now().isoformat(),
                "total_results": count,
                "page": page,
                "page_count": page_count,
                "validation_metrics": self._serialize_validation_metrics()
            },
            "cases": []
        }
        
        # Format each case
        for result in results_list:
            if not isinstance(result, dict):
                logger.warning(f"Skipping non-dict result: {type(result)}")
                continue
                
            # Handle court safely
            court_info = result.get("court", {})
            court_name = "Unknown"
            if isinstance(court_info, dict):
                court_name = court_info.get("full_name", "Unknown")
            
            # Handle citations safely
            citations = result.get("citations", [])
            if not isinstance(citations, list):
                citations = []
            
            case = {
                "id": result.get("id", ""),
                "name": result.get("case_name", "Unknown"),
                "court": court_name,
                "date_filed": result.get("date_filed", "Unknown"),
                "docket_number": result.get("docket_number", "Unknown"),
                "status": result.get("precedential_status", "Unknown"),
                "citations": citations,
                "absolute_url": result.get("absolute_url", "")
            }
            
            formatted_results["cases"].append(case)
        
        # Write to file
        with open(full_path, "w") as f:
            json.dump(formatted_results, f, indent=2)
            
        logger.info(f"Saved {len(formatted_results['cases'])} Texas cases to {full_path}")
        return full_path
        
    def _calculate_validation_metrics(self, results: Union[Dict, List], search_terms: List[str]) -> None:
        """Calculate validation metrics for the search results
        
        Args:
            results: Search results from the API
            search_terms: Terms used in the search
        """
        # Reset metrics
        self.validation_metrics = {
            "total_results": 0,
            "complete_records": 0,
            "incomplete_records": 0,
            "missing_fields": {},
            "search_terms": search_terms,
            "court_levels_found": set(),
            "date_range": {"earliest": None, "latest": None},
            "completeness_percentage": 0
        }
        
        # Extract the result items and count
        if isinstance(results, dict):
            if "results" in results:
                result_items = results.get("results", [])
                self.validation_metrics["total_results"] = results.get("count", len(result_items))
            else:
                result_items = [results]
                self.validation_metrics["total_results"] = 1
        elif isinstance(results, list):
            result_items = results
            self.validation_metrics["total_results"] = len(results)
        else:
            self.validation_metrics["total_results"] = 0
            return
        
        # Key fields a complete record should have - basic metadata fields
        expected_basic_fields = [
            "id", "case_name", "caseName", "court", "dateFiled", "date_filed", 
            "docket_number", "docketNumber", "precedential_status", "status", 
            "citations", "citation"
        ]
        
        # Fields that indicate enhanced completeness - from multi-stage retrieval
        enhanced_completeness_fields = [
            "opinions",         # Has opinion text
            "docket",          # Has docket information
            "_complete_data"   # Flag from enhancement process
        ]
        
        # Evaluate each record
        for result in result_items:
            if not isinstance(result, dict):
                continue
                
            # Check for basic completeness - normalize field names for API v4
            has_basic_fields = True
            missing_basic_fields = []
            
            # Group alternative field names (API v3 vs v4) and check at least one exists
            field_alternatives = [
                ["id", "cluster_id"],
                ["case_name", "caseName", "caseNameFull"],
                ["court", "court_id"],
                ["date_filed", "dateFiled"],
                ["docket_number", "docketNumber"],
                ["precedential_status", "status"],
                ["citations", "citation"]
            ]
            
            for field_group in field_alternatives:
                # Check if at least one field in the group exists
                if not any(field in result and result.get(field) for field in field_group):
                    missing_field = field_group[0]  # Use first field name for reporting
                    missing_basic_fields.append(missing_field)
                    self.validation_metrics["missing_fields"][missing_field] = \
                        self.validation_metrics["missing_fields"].get(missing_field, 0) + 1
                    has_basic_fields = False
            
            # Check for enhanced completeness - opinions and docket
            has_enhanced_fields = any(field in result and result.get(field) for field in enhanced_completeness_fields)
            has_opinion_text = False
            
            # Check if we have actual opinion text, not just metadata
            # Court Listener API returns text in several possible HTML fields
            if "opinions" in result and result["opinions"]:
                for opinion in result["opinions"]:
                    if not isinstance(opinion, dict):
                        continue
                        
                    # Check for text content in various possible fields
                    # Priority order of fields to check based on Court Listener API documentation
                    # and preferred order of: 1) plain_text, 2) xml_harvard, 3) html_lawbox, 4) html_columbia
                    priority_fields = [
                        "plain_text",            # From court websites as PDF/Word documents (highest priority)
                        "xml_harvard",           # From Harvard's Caselaw Access Project
                        "html_lawbox",           # From Lawbox donation
                        "html_columbia",         # From Columbia collaboration
                        "html_with_citations",   # Generated with citation links (generally available)
                        "html",                  # From court websites as HTML or WordPerfect
                        "html_anon_2020"         # From anonymous source in 2020
                    ]
                    
                    # First check our priority fields in order
                    for field in priority_fields:
                        if field in opinion and opinion.get(field):
                            content = str(opinion.get(field, ""))
                            # Different threshold for plain text vs HTML content
                            min_length = 30 if field == "plain_text" else 50
                            
                            if len(content) > min_length:
                                # Log which field provided the text for debugging
                                logger.debug(f"Found opinion text in {field} field with length: {len(content)}")
                                has_opinion_text = True
                                break
                    
                    # If we haven't found opinion text yet, check any other text fields as fallback
                    if not has_opinion_text:
                        # Look for any field that might contain text/HTML content
                        for field in opinion.keys():
                            if (field.startswith("html_") or field.startswith("text") or 
                                field.startswith("plain_") or field.startswith("xml_")) and opinion.get(field):
                                content = str(opinion.get(field, ""))
                                if len(content) > 0:
                                    logger.debug(f"Found fallback opinion text in {field} field with length: {len(content)}")
                                    has_opinion_text = True
                                    break
                            
                    if has_opinion_text:
                        break
            
            # Determine overall completeness level
            if has_basic_fields and has_enhanced_fields and has_opinion_text:
                # Full completeness - has metadata, enhanced fields, and opinion text
                self.validation_metrics["complete_records"] += 1
                # Add to tracking that this is a document with full text (for storage in GCS/Pinecone/Neo4j)
                self.validation_metrics.setdefault("full_document_records", 0)
                self.validation_metrics["full_document_records"] += 1
            elif has_basic_fields and has_enhanced_fields:
                # Partial completeness - has metadata and enhanced fields but no full text
                self.validation_metrics["partial_records"] = \
                    self.validation_metrics.get("partial_records", 0) + 1
            elif has_basic_fields:
                # Basic completeness - only has metadata
                self.validation_metrics["basic_records"] = \
                    self.validation_metrics.get("basic_records", 0) + 1
            else:
                # Incomplete - missing basic metadata
                self.validation_metrics["incomplete_records"] += 1
            
            # Track court information
            if "court" in result and result["court"]:
                court_data = result["court"]
                court_name = None
                
                if isinstance(court_data, dict) and "full_name" in court_data:
                    court_name = court_data["full_name"]
                elif isinstance(court_data, str):
                    court_name = court_data
                    
                if court_name:
                    for level in ["Supreme", "Appeals", "Criminal", "District", "County"]:
                        if level.lower() in court_name.lower():
                            self.validation_metrics["court_levels_found"].add(level.lower())
            
            # Track date range
            if "date_filed" in result and result["date_filed"]:
                date = result["date_filed"]
                if (not self.validation_metrics["date_range"]["earliest"] or 
                    date < self.validation_metrics["date_range"]["earliest"]):
                    self.validation_metrics["date_range"]["earliest"] = date
                if (not self.validation_metrics["date_range"]["latest"] or 
                    date > self.validation_metrics["date_range"]["latest"]):
                    self.validation_metrics["date_range"]["latest"] = date
        
        # Calculate completeness percentages
        if result_items:
            total_count = len(result_items)
            
            # Calculate different levels of completeness
            full_document_records = self.validation_metrics.get("full_document_records", 0)
            partial_records = self.validation_metrics.get("partial_records", 0)
            basic_records = self.validation_metrics.get("basic_records", 0)
            
            # Calculate overall completeness percentage (includes all records with at least basic fields)
            records_with_basics = full_document_records + partial_records + basic_records
            self.validation_metrics["basic_completeness_percentage"] = round(
                (records_with_basics / total_count) * 100 if total_count > 0 else 0, 2
            )
            
            # Calculate full completeness percentage (only records with full text)
            self.validation_metrics["full_completeness_percentage"] = round(
                (full_document_records / total_count) * 100 if total_count > 0 else 0, 2
            )
            
            # Calculate enhanced completeness percentage (records with enhanced data)
            enhanced_records = full_document_records + partial_records
            self.validation_metrics["enhanced_completeness_percentage"] = round(
                (enhanced_records / total_count) * 100 if total_count > 0 else 0, 2
            )
            
            # For backward compatibility
            self.validation_metrics["completeness_percentage"] = self.validation_metrics["full_completeness_percentage"]
    
    def _serialize_validation_metrics(self) -> Dict:
        """Convert validation metrics to a serializable format
        
        Returns:
            Dict of validation metrics in a serializable format
        """
        metrics = self.validation_metrics.copy()
        
        # Convert sets to lists for JSON serialization
        if isinstance(metrics.get("court_levels_found"), set):
            metrics["court_levels_found"] = list(metrics["court_levels_found"])
            
        return metrics
    
    def get_validation_report(self) -> Dict:
        """Get a detailed validation report
        
        Returns:
            Dict containing validation metrics and quality assessment
        """
        metrics = self._serialize_validation_metrics()
        
        # Add quality assessment based on full completeness
        full_completeness = metrics.get("full_completeness_percentage", 0)
        enhanced_completeness = metrics.get("enhanced_completeness_percentage", 0)
        basic_completeness = metrics.get("basic_completeness_percentage", 0)
        
        # Determine document quality level (for GCS/Pinecone/Neo4j storage)
        # Lowered thresholds based on realistic API content availability
        if full_completeness >= 30:
            doc_quality_level = "Excellent"
        elif full_completeness >= 15:
            doc_quality_level = "Good"
        elif full_completeness >= 5:
            doc_quality_level = "Fair"
        else:
            doc_quality_level = "Poor"
            
        # Determine metadata quality level
        # Adjusted for the API's metadata availability
        if basic_completeness >= 80:
            metadata_quality_level = "Excellent"
        elif basic_completeness >= 60:
            metadata_quality_level = "Good"
        elif basic_completeness >= 40:
            metadata_quality_level = "Fair"
        else:
            metadata_quality_level = "Poor"
            
        # Add court coverage assessment
        court_levels = metrics.get("court_levels_found", [])
        if len(court_levels) >= 4:
            court_coverage = "Excellent"
        elif len(court_levels) >= 3:
            court_coverage = "Good"
        elif len(court_levels) >= 2:
            court_coverage = "Fair"
        else:
            court_coverage = "Limited"
        
        report = {
            "metrics": metrics,
            "quality_assessment": {
                "document_quality": doc_quality_level,
                "metadata_quality": metadata_quality_level,
                "court_coverage": court_coverage,
                "full_document_completeness": f"{full_completeness}%",
                "enhanced_data_completeness": f"{enhanced_completeness}%",
                "basic_metadata_completeness": f"{basic_completeness}%",
                "full_document_count": metrics.get("full_document_records", 0),
                "cases_suitable_for_storage": {
                    "gcs": metrics.get("full_document_records", 0),
                    "pinecone": metrics.get("full_document_records", 0),
                    "neo4j": metrics.get("full_document_records", 0) + metrics.get("partial_records", 0)
                },
                "most_common_missing_fields": sorted(
                    metrics["missing_fields"].items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:3] if metrics["missing_fields"] else []
            }
        }
        
        return report
        
    def _enhance_search_results_with_complete_data(self, search_results: Dict, max_cases: int = 5) -> Dict:
        """
        Enhance search results with complete case data for the top results
        
        Args:
            search_results: Original search results from the API
            max_cases: Maximum number of cases to fetch complete data for
            
        Returns:
            Enhanced search results with complete case data
        """
        if not search_results or "results" not in search_results or not search_results["results"]:
            return search_results
            
        results = search_results["results"]
        enhanced_results = []
        count = 0
        
        for result in results:
            if count >= max_cases:
                # For remaining results, just keep original data
                enhanced_results.append(result)
                continue
                
            # Get cluster ID from result
            cluster_id = None
            if "cluster_id" in result:
                cluster_id = result["cluster_id"]
            elif "id" in result:
                cluster_id = result["id"]
            elif "resource_uri" in result:
                # Extract ID from resource URI
                resource_uri = result["resource_uri"]
                cluster_id = resource_uri.rstrip("/").split("/")[-1]
                
            if not cluster_id:
                enhanced_results.append(result)
                continue
                
            try:
                # Get complete data for this case with full opinion text
                complete_data = self.client.get_complete_case(cluster_id, full_case=True)
                
                # Replace original result with the cluster data from complete_data
                # but preserve the search result fields that aren't in the cluster
                enhanced_result = {**result, **complete_data["cluster"], "_complete_data": True}
                
                # Add opinions and docket data
                enhanced_result["opinions"] = complete_data["opinions"]
                enhanced_result["docket"] = complete_data["docket"]
                if "court" in complete_data:
                    enhanced_result["court_details"] = complete_data["court"]
                
                enhanced_results.append(enhanced_result)
                count += 1
                
                # Add a small delay to avoid rate limiting
                time.sleep(0.1)
                
            except (CourtListenerResourceNotFoundError, Exception) as e:
                # If there's an error getting complete data, use original result
                logger.warning(f"Error getting complete data for case {cluster_id}: {str(e)}")
                enhanced_results.append(result)
                
        # Create a copy of the original results and update it
        enhanced_search_results = search_results.copy()
        enhanced_search_results["results"] = enhanced_results
        enhanced_search_results["enhanced_count"] = count
        
        # Re-calculate validation metrics with enhanced data
        self._calculate_validation_metrics(enhanced_results, [])
        validation_report = self.get_validation_report()
        
        # Include storage suitability metrics
        enhanced_search_results["validation"] = validation_report
        
        return enhanced_search_results

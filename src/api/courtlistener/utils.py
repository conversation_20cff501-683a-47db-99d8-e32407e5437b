"""
Utility functions for the Court Listener API integration
"""

import re
from typing import Optional, Tuple, List, Dict
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# Regular expression for matching citations
CITATION_REGEX = r'(\d+)\s+([A-Za-z\.\s]+)\s+(\d+)'


def parse_citation(citation_text: str) -> Optional[Tuple[str, str, str]]:
    """
    Parse a citation string into volume, reporter, and page
    
    Args:
        citation_text: A citation string (e.g., "410 U.S. 113")
        
    Returns:
        Tuple of (volume, reporter, page) or None if parsing fails
    """
    try:
        match = re.match(CITATION_REGEX, citation_text.strip())
        if match:
            volume, reporter, page = match.groups()
            return volume.strip(), reporter.strip(), page.strip()
    except Exception as e:
        logger.error(f"Error parsing citation '{citation_text}': {str(e)}")
    
    return None


def format_citation(volume: str, reporter: str, page: str) -> str:
    """
    Format volume, reporter, and page into a citation string
    
    Args:
        volume: Volume number
        reporter: Reporter abbreviation
        page: Page number
        
    Returns:
        Formatted citation string
    """
    return f"{volume} {reporter} {page}"


def standardize_jurisdiction(jurisdiction: str) -> str:
    """
    Convert jurisdiction name to Court Listener code
    
    Args:
        jurisdiction: Jurisdiction name (e.g., "Texas", "Federal")
        
    Returns:
        Jurisdiction code (e.g., "tex", "fed")
    """
    jurisdiction_map = {
        "texas": "tex",
        "california": "cal",
        "new york": "ny",
        "florida": "fla",
        "federal": "fed",
        "united states": "fed",
        "illinois": "ill",
        "pennsylvania": "pa",
        "ohio": "ohio",
        "michigan": "mich",
        "georgia": "ga",
        "north carolina": "nc",
        "new jersey": "nj",
        "virginia": "va",
        "washington": "wash",
        "massachusetts": "mass",
        "indiana": "ind",
        "arizona": "ariz",
        "tennessee": "tenn",
        "missouri": "mo",
        "maryland": "md",
        "wisconsin": "wis",
        "minnesota": "minn",
        "colorado": "colo",
        "alabama": "ala",
        "south carolina": "sc",
        "louisiana": "la",
        "kentucky": "ky",
        "oregon": "or",
        "oklahoma": "okla",
        "connecticut": "conn",
        "iowa": "iowa",
        "mississippi": "miss",
        "arkansas": "ark",
        "kansas": "kan",
        "utah": "utah",
        "nevada": "nev",
        "new mexico": "nm",
        "west virginia": "w. va.",
        "nebraska": "neb",
        "idaho": "idaho",
        "hawaii": "haw",
        "maine": "me",
        "new hampshire": "n.h.",
        "rhode island": "r.i.",
        "montana": "mont.",
        "delaware": "del.",
        "south dakota": "s.d.",
        "north dakota": "n.d.",
        "alaska": "alaska",
        "vermont": "vt.",
        "wyoming": "wyo.",
        "district of columbia": "d.c."
    }
    
    # Try direct match first
    if jurisdiction.lower() in jurisdiction_map:
        return jurisdiction_map[jurisdiction.lower()]
    
    # Try two-letter abbreviation if it's exactly 2 chars
    if len(jurisdiction) == 2:
        return jurisdiction.lower()
    
    # Return the first two characters as a fallback
    return jurisdiction.lower()[:3]


def extract_citation_from_text(text: str) -> List[str]:
    """
    Extract citations from text
    
    Args:
        text: Text containing citations
        
    Returns:
        List of extracted citation strings
    """
    matches = re.findall(CITATION_REGEX, text)
    citations = []
    
    for match in matches:
        volume, reporter, page = match
        citations.append(f"{volume} {reporter} {page}")
    
    return citations


def format_date(date_obj: Optional[datetime]) -> Optional[str]:
    """
    Format a datetime object to a human-readable date string
    
    Args:
        date_obj: Datetime object or None
        
    Returns:
        Formatted date string or None
    """
    if not date_obj:
        return None
    
    return date_obj.strftime("%B %d, %Y")

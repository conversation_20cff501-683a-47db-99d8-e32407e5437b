"""
FastAPI router for graph data endpoints (Week 6 preview).
Provides React-Flow compatible graph data for network visualization.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from pydantic import BaseModel, Field

from src.api.graph.graph_service import GraphDataService, GraphResponse
from src.api.auth.jwt_middleware import get_current_user

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/v0", tags=["graph"])

# Initialize graph service
graph_service = GraphDataService()


class GraphNode(BaseModel):
    """React-Flow compatible graph node model."""
    id: str
    label: str
    type: str
    authority: float = Field(..., ge=0.0, le=1.0)
    data: dict


class GraphEdge(BaseModel):
    """React-Flow compatible graph edge model."""
    id: str
    source: str
    target: str
    type: str
    data: dict


class GraphMetadata(BaseModel):
    """Graph metadata model."""
    center_node: str
    returned_nodes: int
    returned_edges: int
    total_nodes: int
    total_edges: int
    truncated: bool
    query_time_ms: int
    depth_used: int
    direction_used: str
    max_nodes_requested: int
    max_edges_requested: int


class GraphAPIResponse(BaseModel):
    """Complete graph API response model."""
    nodes: List[GraphNode]
    edges: List[GraphEdge]
    metadata: GraphMetadata
    cached: bool = False


@router.get("/graph", response_model=GraphAPIResponse)
async def get_graph_data(
    id: str = Query(..., description="Document ID to center the graph on"),
    depth: int = Query(2, description="Relationship depth (1-3)", ge=1, le=3),
    direction: str = Query("both", description="Direction of relationships", regex="^(both|in|out)$"),
    max_nodes: Optional[int] = Query(None, description="Maximum nodes to return (default: 50, max: 500)", ge=1, le=500),
    max_edges: Optional[int] = Query(None, description="Maximum edges to return (default: 150, max: 1500)", ge=1, le=1500),
    node_types: Optional[List[str]] = Query(None, description="Filter by node types (case, statute)"),
    user: dict = Depends(get_current_user)
) -> GraphAPIResponse:
    """
    Get React-Flow compatible graph data for a document.
    
    This endpoint returns a network graph centered on the specified document,
    showing citation relationships and related documents. The response format
    is optimized for React-Flow visualization components.
    
    **Features**:
    - Configurable depth (1-3 levels of relationships)
    - Direction control (incoming, outgoing, or both)
    - Node and edge limits with truncation handling
    - Authority score-based node importance
    - User permission-based filtering
    
    **Performance**:
    - Response time target: < 500ms
    - Payload size: < 200 kB for fast rendering
    - Cached for 15 minutes per user permissions
    
    **Authentication**: Requires valid JWT token
    **Rate Limits**: 100 requests/minute per user
    """
    try:
        logger.info(f"Graph request: user={user['user_id']}, doc_id={id}, depth={depth}, direction={direction}")
        
        # Validate document access based on user jurisdictions
        # For Week 4, we'll implement basic validation
        user_jurisdictions = user.get("jurisdictions", [])
        if not user_jurisdictions:
            raise HTTPException(
                status_code=403,
                detail="No jurisdiction access configured for user"
            )
        
        # Generate graph data
        graph_response = graph_service.get_graph_data(
            doc_id=id,
            depth=depth,
            direction=direction,
            max_nodes=max_nodes,
            max_edges=max_edges,
            node_types=node_types,
            user_permissions_hash=user["permissions_hash"]
        )
        
        # Convert to API response format
        api_nodes = []
        for node_data in graph_response.nodes:
            api_node = GraphNode(
                id=node_data["id"],
                label=node_data["label"],
                type=node_data["type"],
                authority=node_data["authority"],
                data=node_data["data"]
            )
            api_nodes.append(api_node)
        
        api_edges = []
        for edge_data in graph_response.edges:
            api_edge = GraphEdge(
                id=edge_data["id"],
                source=edge_data["source"],
                target=edge_data["target"],
                type=edge_data["type"],
                data=edge_data["data"]
            )
            api_edges.append(api_edge)
        
        api_metadata = GraphMetadata(**graph_response.metadata)
        
        api_response = GraphAPIResponse(
            nodes=api_nodes,
            edges=api_edges,
            metadata=api_metadata,
            cached=False  # Will be set by cache layer
        )
        
        logger.info(f"Graph data generated: {len(api_nodes)} nodes, {len(api_edges)} edges in {api_metadata.query_time_ms}ms")
        return api_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Graph data error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error generating graph data"
        )


@router.get("/graph/{document_id}/neighbors")
async def get_document_neighbors(
    document_id: str = Path(..., description="Document ID to get neighbors for"),
    limit: int = Query(10, description="Number of neighbors to return", ge=1, le=50),
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get immediate neighbors (directly connected documents) for a specific document.
    
    This is a simplified version of the graph endpoint that returns only
    directly connected documents (depth=1) in a simple list format.
    """
    try:
        # Get graph data with depth=1
        graph_response = graph_service.get_graph_data(
            doc_id=document_id,
            depth=1,
            direction="both",
            max_nodes=limit + 1,  # +1 for the center node
            user_permissions_hash=user["permissions_hash"]
        )
        
        # Filter out the center node to get only neighbors
        neighbors = [
            node for node in graph_response.nodes 
            if node["id"] != document_id
        ]
        
        return {
            "document_id": document_id,
            "neighbors": neighbors[:limit],
            "total": len(neighbors),
            "query_time_ms": graph_response.metadata["query_time_ms"]
        }
        
    except Exception as e:
        logger.error(f"Neighbors error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error getting document neighbors"
        )


@router.get("/graph/stats")
async def get_graph_stats(
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get graph statistics and system information.
    
    Returns information about the citation network size, performance metrics,
    and system capabilities.
    """
    try:
        # For Week 4, return basic stats
        # These can be enhanced with real metrics in later weeks
        stats = {
            "graph_service_status": "operational",
            "max_nodes_limit": graph_service.hard_cap_nodes,
            "max_edges_limit": graph_service.hard_cap_edges,
            "default_max_nodes": graph_service.default_max_nodes,
            "default_max_edges": graph_service.default_max_edges,
            "supported_directions": ["both", "in", "out"],
            "max_depth": 3,
            "supported_node_types": ["case", "statute"],
            "cache_ttl_minutes": 15,
            "version": "v0.1.0"
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Graph stats error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error retrieving graph statistics"
        )


@router.get("/graph/health")
async def graph_health_check():
    """
    Health check endpoint for graph service.
    
    Returns the health status of graph service dependencies:
    - Neo4j connection
    - Cache backend
    - Service availability
    """
    try:
        # Test Neo4j connection
        neo4j_healthy = True
        try:
            with graph_service.neo4j.driver.session() as session:
                session.run("RETURN 1")
        except Exception as e:
            logger.error(f"Neo4j health check failed: {e}")
            neo4j_healthy = False
        
        # Test cache
        cache_healthy = True
        try:
            from src.cache.cache import cache
            cache.set("health_check", "ok", 60)
            cache_healthy = cache.get("health_check") == "ok"
        except Exception as e:
            logger.error(f"Cache health check failed: {e}")
            cache_healthy = False
        
        overall_healthy = neo4j_healthy and cache_healthy
        
        health_status = {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": "2024-01-15T10:30:00Z",  # Would be dynamic
            "dependencies": {
                "neo4j": "healthy" if neo4j_healthy else "unhealthy",
                "cache": "healthy" if cache_healthy else "unhealthy"
            },
            "service_info": {
                "max_nodes": graph_service.hard_cap_nodes,
                "max_edges": graph_service.hard_cap_edges,
                "cache_backend": cache.backend.__class__.__name__
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Graph health check error: {e}", exc_info=True)
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-15T10:30:00Z"
        }


@router.get("/graph/sample")
async def get_sample_graph_data():
    """
    Get sample graph data for development and testing.
    
    Returns the same sample data that's available in the examples directory,
    useful for frontend development and testing React-Flow integration.
    """
    try:
        import json
        import os
        
        # Load sample data from examples directory
        sample_file = os.path.join(
            os.path.dirname(__file__), 
            "..", "..", "..", "examples", "react_flow_sample.json"
        )
        
        if os.path.exists(sample_file):
            with open(sample_file, 'r') as f:
                sample_data = json.load(f)
            return sample_data
        else:
            # Return minimal sample if file not found
            return {
                "nodes": [
                    {
                        "id": "sample-case-1",
                        "label": "Sample Case v. Example",
                        "type": "case",
                        "authority": 0.75,
                        "data": {
                            "jurisdiction": "tx",
                            "court": "Sample Court",
                            "date": "2023-01-01"
                        }
                    }
                ],
                "edges": [],
                "metadata": {
                    "center_node": "sample-case-1",
                    "total_nodes": 1,
                    "total_edges": 0,
                    "truncated": false,
                    "query_time_ms": 1,
                    "sample": true
                }
            }
        
    except Exception as e:
        logger.error(f"Sample data error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error loading sample graph data"
        )

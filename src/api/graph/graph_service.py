"""
Graph Data Service for React-Flow Integration
Provides React-Flow compatible JSON data for legal document network visualization.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from src.processing.storage.neo4j_connector import Neo4jConnector
from src.cache.cache import cache

logger = logging.getLogger(__name__)


@dataclass
class GraphNode:
    """React-Flow compatible graph node."""
    id: str
    label: str
    type: str
    authority: float
    data: Dict[str, Any]


@dataclass
class GraphEdge:
    """React-Flow compatible graph edge."""
    id: str
    source: str
    target: str
    type: str
    data: Dict[str, Any]


@dataclass
class GraphResponse:
    """Complete graph response."""
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class GraphDataService:
    """
    Service for generating React-Flow compatible graph data.
    
    Features:
    - Configurable node/edge limits with truncation
    - Multiple direction support (both, in, out)
    - Depth-based relationship traversal
    - Performance optimized with proper limits
    """
    
    def __init__(self):
        """Initialize the graph data service."""
        self.neo4j = Neo4jConnector()
        
        # Default limits as specified for Week 6
        self.default_max_nodes = 200
        self.default_max_edges = 600

        # Hard caps as specified
        self.hard_cap_nodes = 500
        self.hard_cap_edges = 1500
        
        logger.info("Graph data service initialized")
    
    def get_graph_data(self,
                      doc_id: str,
                      depth: int = 2,
                      direction: str = "both",
                      max_nodes: Optional[int] = None,
                      max_edges: Optional[int] = None,
                      node_types: Optional[List[str]] = None,
                      user_permissions_hash: str = "") -> GraphResponse:
        """
        Get React-Flow compatible graph data for a document.
        
        Args:
            doc_id: Center document ID
            depth: Relationship depth (1-3)
            direction: Direction of relationships ("both", "in", "out")
            max_nodes: Maximum nodes to return (default: 50, hard cap: 500)
            max_edges: Maximum edges to return (default: 150, hard cap: 1500)
            node_types: Filter by node types (["case", "statute"])
            user_permissions_hash: User permissions hash for caching
            
        Returns:
            GraphResponse with React-Flow compatible data
        """
        start_time = time.time()
        
        try:
            # Validate and set limits
            max_nodes = min(max_nodes or self.default_max_nodes, self.hard_cap_nodes)
            max_edges = min(max_edges or self.default_max_edges, self.hard_cap_edges)
            depth = max(1, min(depth, 3))  # Clamp depth to 1-3
            
            # Check cache first
            cache_key = cache.graph_cache_key(
                doc_id=doc_id,
                depth=depth,
                direction=direction,
                max_nodes=max_nodes,
                user_permissions_hash=user_permissions_hash
            )
            
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.debug(f"Returning cached graph data for {doc_id}")
                return GraphResponse(**cached_result)
            
            logger.info(f"Generating graph data: doc_id={doc_id}, depth={depth}, direction={direction}")
            
            # Get graph data from Neo4j
            nodes_data, edges_data, total_nodes, total_edges = self._query_neo4j_graph(
                doc_id=doc_id,
                depth=depth,
                direction=direction,
                max_nodes=max_nodes,
                max_edges=max_edges,
                node_types=node_types
            )
            
            # Convert to React-Flow format
            nodes = self._format_nodes(nodes_data)
            edges = self._format_edges(edges_data)
            
            # Check for truncation
            truncated = len(nodes) >= max_nodes or len(edges) >= max_edges
            
            # Build metadata
            metadata = {
                "center_node": doc_id,
                "returned_nodes": len(nodes),
                "returned_edges": len(edges),
                "total_nodes": total_nodes,
                "total_edges": total_edges,
                "truncated": truncated,
                "query_time_ms": int((time.time() - start_time) * 1000),
                "depth_used": depth,
                "direction_used": direction,
                "max_nodes_requested": max_nodes,
                "max_edges_requested": max_edges
            }
            
            # Create response
            response = GraphResponse(
                nodes=nodes,
                edges=edges,
                metadata=metadata
            )
            
            # Cache the result
            cache.set(cache_key, response.__dict__, cache.GRAPH_TTL)
            
            logger.info(f"Graph data generated: {len(nodes)} nodes, {len(edges)} edges in {metadata['query_time_ms']}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error generating graph data: {e}", exc_info=True)
            # Return empty graph on error
            return GraphResponse(
                nodes=[],
                edges=[],
                metadata={
                    "center_node": doc_id,
                    "error": str(e),
                    "query_time_ms": int((time.time() - start_time) * 1000)
                }
            )
    
    def _query_neo4j_graph(self,
                          doc_id: str,
                          depth: int,
                          direction: str,
                          max_nodes: int,
                          max_edges: int,
                          node_types: Optional[List[str]]) -> Tuple[List[Dict], List[Dict], int, int]:
        """
        Query Neo4j for graph data with specified parameters.
        
        Returns:
            Tuple of (nodes_data, edges_data, total_nodes, total_edges)
        """
        try:
            # Build Cypher query based on direction
            if direction == "out":
                relationship_pattern = f"-[:CITES*1..{depth}]->"
            elif direction == "in":
                relationship_pattern = f"<-[:CITES*1..{depth}]-"
            else:  # both
                relationship_pattern = f"-[:CITES*1..{depth}]-"
            
            # Base query to get connected nodes
            base_query = f"""
            MATCH (start:Document {{document_id: $doc_id}})
            MATCH (start){relationship_pattern}(n:Document)
            WITH DISTINCT start, n
            """
            
            # Add node type filter if specified
            if node_types:
                type_conditions = " OR ".join([f"n.doc_type = '{t}'" for t in node_types])
                base_query += f"WHERE {type_conditions}\n"
            
            # Get nodes with limit
            nodes_query = base_query + f"""
            WITH start, n
            WITH COLLECT(DISTINCT start) + COLLECT(DISTINCT n) AS all_nodes
            UNWIND all_nodes AS node
            WITH DISTINCT node
            RETURN node.document_id AS id,
                   node.title AS title,
                   node.doc_type AS type,
                   COALESCE(node.authority_score, 0.0) AS authority,
                   node.jurisdiction AS jurisdiction,
                   node.court AS court,
                   node.date AS date,
                   node.practice_areas AS practice_areas,
                   node.citation_count AS citation_count,
                   node.chapter AS chapter,
                   node.section AS section
            LIMIT {max_nodes}
            """
            
            # Get edges query
            edges_query = f"""
            MATCH (start:Document {{document_id: $doc_id}})
            MATCH (start){relationship_pattern}(n:Document)
            MATCH (source:Document)-[r:CITES]->(target:Document)
            WHERE (source = start OR source = n) AND (target = start OR target = n)
            WITH DISTINCT source, target, r
            RETURN source.document_id AS source_id,
                   target.document_id AS target_id,
                   type(r) AS relationship_type,
                   COALESCE(r.citation_count, 1) AS weight,
                   r.relationship_type AS semantic_type,
                   r.context AS context
            LIMIT {max_edges}
            """
            
            # Count total nodes and edges for metadata
            count_query = f"""
            MATCH (start:Document {{document_id: $doc_id}})
            MATCH (start){relationship_pattern}(n:Document)
            WITH DISTINCT start, n
            WITH COLLECT(DISTINCT start) + COLLECT(DISTINCT n) AS all_nodes
            UNWIND all_nodes AS node
            WITH DISTINCT node
            WITH COUNT(node) AS total_nodes
            
            MATCH (start:Document {{document_id: $doc_id}})
            MATCH (start){relationship_pattern}(n:Document)
            MATCH (source:Document)-[r:CITES]->(target:Document)
            WHERE (source = start OR source = n) AND (target = start OR target = n)
            WITH DISTINCT source, target, r, total_nodes
            RETURN total_nodes, COUNT(r) AS total_edges
            """
            
            with self.neo4j.driver.session() as session:
                # Get nodes
                nodes_result = session.run(nodes_query, doc_id=doc_id)
                nodes_data = [dict(record) for record in nodes_result]
                
                # Get edges
                edges_result = session.run(edges_query, doc_id=doc_id)
                edges_data = [dict(record) for record in edges_result]
                
                # Get counts
                count_result = session.run(count_query, doc_id=doc_id)
                count_record = count_result.single()
                total_nodes = count_record["total_nodes"] if count_record else len(nodes_data)
                total_edges = count_record["total_edges"] if count_record else len(edges_data)
            
            return nodes_data, edges_data, total_nodes, total_edges
            
        except Exception as e:
            logger.error(f"Error querying Neo4j for graph data: {e}")
            raise
    
    def _format_nodes(self, nodes_data: List[Dict]) -> List[Dict[str, Any]]:
        """
        Format nodes for React-Flow compatibility.
        
        Args:
            nodes_data: Raw node data from Neo4j
            
        Returns:
            List of React-Flow compatible node objects
        """
        formatted_nodes = []
        
        for node_data in nodes_data:
            # Determine node type and create appropriate data structure
            node_type = node_data.get("type", "unknown")
            
            if node_type == "case":
                data = {
                    "jurisdiction": node_data.get("jurisdiction"),
                    "court": node_data.get("court"),
                    "date": node_data.get("date"),
                    "practice_areas": node_data.get("practice_areas", []),
                    "citation_count": node_data.get("citation_count", 0)
                }
            elif node_type == "statute":
                data = {
                    "jurisdiction": node_data.get("jurisdiction"),
                    "chapter": node_data.get("chapter"),
                    "section": node_data.get("section"),
                    "effective_date": node_data.get("date")
                }
            else:
                data = {
                    "jurisdiction": node_data.get("jurisdiction"),
                    "type_specific": node_data
                }
            
            # Create React-Flow compatible node
            formatted_node = {
                "id": node_data.get("id"),
                "label": node_data.get("title", "Unknown"),
                "type": node_type,
                "authority": float(node_data.get("authority", 0.0)),
                "data": data
            }
            
            formatted_nodes.append(formatted_node)
        
        return formatted_nodes
    
    def _format_edges(self, edges_data: List[Dict]) -> List[Dict[str, Any]]:
        """
        Format edges for React-Flow compatibility.
        
        Args:
            edges_data: Raw edge data from Neo4j
            
        Returns:
            List of React-Flow compatible edge objects
        """
        formatted_edges = []
        
        for i, edge_data in enumerate(edges_data):
            source_id = edge_data.get("source_id")
            target_id = edge_data.get("target_id")
            
            # Determine edge type
            relationship_type = edge_data.get("relationship_type", "CITES")
            if relationship_type == "CITES":
                edge_type = "cites"
            else:
                edge_type = relationship_type.lower()
            
            # Create edge data
            data = {
                "weight": edge_data.get("weight", 1),
                "relationship_type": edge_data.get("semantic_type", "cites"),
                "context": edge_data.get("context")
            }
            
            # Create React-Flow compatible edge
            formatted_edge = {
                "id": f"edge-{i}",
                "source": source_id,
                "target": target_id,
                "type": edge_type,
                "data": data
            }
            
            formatted_edges.append(formatted_edge)
        
        return formatted_edges

"""
Enhanced Search Service for Week 5 implementation.
Implements v1 search API with advanced filtering capabilities:
- Practice area filtering
- Authority score filtering  
- Date range filtering
- Advanced sorting options
"""

import logging
import time
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date
from dataclasses import dataclass
from pydantic import BaseModel, Field

from src.api.search.hybrid_search import HybridSearchEngine, SearchResult, SearchResponse

logger = logging.getLogger(__name__)


@dataclass
class DateRange:
    """Date range filter for search queries."""
    start: Optional[date] = None
    end: Optional[date] = None
    
    def __post_init__(self):
        """Validate date range."""
        if self.start and self.end and self.start > self.end:
            raise ValueError("Start date must be before end date")


class EnhancedSearchRequest(BaseModel):
    """Enhanced search request model for v1 API."""
    q: str = Field(..., description="Search query", min_length=1, max_length=500)
    jurisdiction: Optional[str] = Field(None, description="Filter by jurisdiction")
    practice_areas: Optional[List[str]] = Field(None, description="Filter by practice areas")
    authority_min: Optional[float] = Field(None, description="Minimum authority score", ge=0.0, le=1.0)
    date_range: Optional[Dict[str, str]] = Field(None, description="Date range filter")
    sort_by: str = Field("relevance", description="Sort order: relevance, authority, date")
    limit: int = Field(10, description="Number of results", ge=1, le=100)
    offset: int = Field(0, description="Pagination offset", ge=0)


class EnhancedSearchEngine:
    """Enhanced search engine with advanced filtering capabilities."""
    
    def __init__(self):
        """Initialize enhanced search engine."""
        self.base_engine = HybridSearchEngine()
        self.supported_practice_areas = [
            "personal_injury", "medical_malpractice", "product_liability",
            "premises_liability", "motor_vehicle", "wrongful_death",
            "criminal_defense", "family_law", "business_law"
        ]
        self.supported_sort_options = ["relevance", "authority", "date"]
        
        logger.info("Enhanced search engine initialized")
    
    def validate_request(self, request: EnhancedSearchRequest) -> None:
        """Validate enhanced search request parameters."""
        # Validate practice areas
        if request.practice_areas:
            invalid_areas = [area for area in request.practice_areas 
                           if area not in self.supported_practice_areas]
            if invalid_areas:
                raise ValueError(f"Invalid practice areas: {invalid_areas}")
        
        # Validate sort option
        if request.sort_by not in self.supported_sort_options:
            raise ValueError(f"Invalid sort option: {request.sort_by}")
        
        # Validate date range
        if request.date_range:
            try:
                self._parse_date_range(request.date_range)
            except Exception as e:
                raise ValueError(f"Invalid date range: {e}")
    
    def _parse_date_range(self, date_range: Dict[str, str]) -> DateRange:
        """Parse date range from request."""
        start_date = None
        end_date = None
        
        if "start" in date_range and date_range["start"]:
            start_date = datetime.fromisoformat(date_range["start"]).date()
        
        if "end" in date_range and date_range["end"]:
            end_date = datetime.fromisoformat(date_range["end"]).date()
        
        return DateRange(start=start_date, end=end_date)
    
    def _build_pinecone_filter(self, request: EnhancedSearchRequest) -> Dict[str, Any]:
        """Build Pinecone metadata filter from request parameters."""
        pinecone_filter = {}
        
        # Jurisdiction filter
        if request.jurisdiction:
            pinecone_filter["jurisdiction"] = {"$eq": request.jurisdiction}
        
        # Practice areas filter
        if request.practice_areas:
            pinecone_filter["practice_area"] = {"$in": request.practice_areas}
        
        return {"filter": pinecone_filter} if pinecone_filter else {}
    
    def _build_supabase_filter(self, request: EnhancedSearchRequest) -> Dict[str, Any]:
        """Build Supabase filter conditions from request parameters."""
        conditions = []
        params = {}
        
        # Authority score filter
        if request.authority_min is not None:
            conditions.append("authority_score >= :authority_min")
            params["authority_min"] = request.authority_min
        
        # Date range filter
        if request.date_range:
            date_range = self._parse_date_range(request.date_range)
            if date_range.start:
                conditions.append("document_date >= :start_date")
                params["start_date"] = date_range.start
            if date_range.end:
                conditions.append("document_date <= :end_date")
                params["end_date"] = date_range.end
        
        # Jurisdiction filter (also apply to Supabase for consistency)
        if request.jurisdiction:
            conditions.append("jurisdiction = :jurisdiction")
            params["jurisdiction"] = request.jurisdiction
        
        return {
            "conditions": " AND ".join(conditions) if conditions else None,
            "params": params
        }
    
    def _apply_advanced_sorting(self, results: List[SearchResult], 
                              sort_by: str) -> List[SearchResult]:
        """Apply advanced sorting to search results."""
        if sort_by == "relevance":
            # Default hybrid scoring: 0.7 similarity + 0.3 authority
            return sorted(results, 
                         key=lambda r: (0.7 * r.relevance_score + 0.3 * r.authority_score),
                         reverse=True)
        elif sort_by == "authority":
            return sorted(results, key=lambda r: r.authority_score, reverse=True)
        elif sort_by == "date":
            # Sort by document date if available, fallback to relevance
            return sorted(results, 
                         key=lambda r: (
                             r.metadata.get("document_date", "1900-01-01"),
                             r.relevance_score
                         ),
                         reverse=True)
        else:
            # Fallback to relevance
            return self._apply_advanced_sorting(results, "relevance")
    
    def _calculate_total_hits(self, base_total: int, filters_applied: Dict[str, Any]) -> int:
        """Estimate total hits after filtering."""
        # For Week 5, use the base total as approximation
        # In production, this would query the database for exact counts
        return base_total
    
    async def search(self, request: EnhancedSearchRequest) -> SearchResponse:
        """
        Perform enhanced search with advanced filtering.
        
        Args:
            request: Enhanced search request with filters
            
        Returns:
            SearchResponse with filtered and sorted results
        """
        start_time = time.time()
        
        try:
            # Validate request
            self.validate_request(request)
            
            # Build filters
            pinecone_filter = self._build_pinecone_filter(request)
            supabase_filter = self._build_supabase_filter(request)
            
            # Perform base search with increased limit for filtering
            # Get more results initially to account for post-filtering
            base_limit = min(request.limit * 3, 100)  # Get 3x results for filtering
            
            base_response = self.base_engine.search(
                query=request.q,
                jurisdiction=request.jurisdiction,
                doc_type=None,  # Let practice area filter handle this
                limit=base_limit,
                offset=0,  # Always start from 0 for filtering
                pinecone_filter=pinecone_filter,
                supabase_filter=supabase_filter
            )
            
            # Apply additional filtering that couldn't be done at database level
            filtered_results = self._apply_post_filters(base_response.results, request)
            
            # Apply advanced sorting
            sorted_results = self._apply_advanced_sorting(filtered_results, request.sort_by)
            
            # Apply pagination
            paginated_results = sorted_results[request.offset:request.offset + request.limit]
            
            # Calculate total hits
            total_hits = self._calculate_total_hits(len(filtered_results), {
                "practice_areas": request.practice_areas,
                "authority_min": request.authority_min,
                "date_range": request.date_range
            })
            
            # Build response
            query_time_ms = int((time.time() - start_time) * 1000)
            
            return SearchResponse(
                results=paginated_results,
                total=total_hits,
                query_time_ms=query_time_ms,
                query=request.q,
                filters_applied={
                    "jurisdiction": request.jurisdiction,
                    "practice_areas": request.practice_areas,
                    "authority_min": request.authority_min,
                    "date_range": request.date_range,
                    "sort_by": request.sort_by
                }
            )
            
        except Exception as e:
            logger.error(f"Enhanced search error: {e}", exc_info=True)
            raise
    
    def _apply_post_filters(self, results: List[SearchResult], 
                           request: EnhancedSearchRequest) -> List[SearchResult]:
        """Apply filters that couldn't be applied at database level."""
        filtered_results = results
        
        # Additional practice area filtering if needed
        if request.practice_areas:
            filtered_results = [
                result for result in filtered_results
                if any(area in result.metadata.get("practice_areas", []) 
                      for area in request.practice_areas)
            ]
        
        return filtered_results

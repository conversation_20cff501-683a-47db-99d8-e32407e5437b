"""
Hybrid Search Implementation for Week 4
Combines semantic search (Pinecone) with keyword search (Supabase) for optimal results.
"""

import logging
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from src.processing.storage.pinecone_connector import PineconeConnector
from src.processing.storage.supabase_connector import SupabaseConnector
from src.processing.storage.neo4j_connector import Neo4jConnector

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """Represents a single search result."""
    id: str
    title: str
    type: str  # 'case' or 'statute'
    jurisdiction: str
    authority_score: float
    relevance_score: float
    snippet: str
    metadata: Dict


@dataclass
class SearchResponse:
    """Represents the complete search response."""
    results: List[SearchResult]
    total: int
    query_time_ms: int
    query: str
    filters_applied: Dict


class HybridSearchEngine:
    """
    Hybrid search engine combining semantic and keyword search.
    
    This engine:
    1. Performs semantic search using Pinecone embeddings
    2. Performs keyword search using Supabase full-text search
    3. Combines and ranks results using hybrid scoring
    4. Applies authority score boosting from Week 3 relationship analysis
    """
    
    def __init__(self):
        """Initialize the hybrid search engine."""
        self.pinecone = PineconeConnector()
        self.supabase = SupabaseConnector()
        self.neo4j = Neo4jConnector()
        
        # Search configuration
        self.semantic_weight = 0.6  # Weight for semantic similarity
        self.keyword_weight = 0.4   # Weight for keyword relevance
        self.authority_boost = 0.2  # Authority score boost factor
        
        logger.info("Initialized HybridSearchEngine")
    
    def search(self,
               query: str,
               jurisdiction: Optional[str] = None,
               doc_type: Optional[str] = None,
               limit: int = 20,
               offset: int = 0,
               pinecone_filter: Optional[Dict] = None,
               supabase_filter: Optional[Dict] = None) -> SearchResponse:
        """
        Perform hybrid search combining semantic and keyword search.
        
        Args:
            query: Search query string
            jurisdiction: Optional jurisdiction filter (tx, oh, fed, etc.)
            doc_type: Optional document type filter (case, statute)
            limit: Maximum number of results to return
            offset: Pagination offset
            
        Returns:
            SearchResponse with ranked results
        """
        start_time = time.time()
        
        try:
            logger.info(f"Performing hybrid search: query='{query}', jurisdiction={jurisdiction}, doc_type={doc_type}")
            
            # Perform semantic search
            semantic_results = self._semantic_search(
                query=query,
                jurisdiction=jurisdiction,
                doc_type=doc_type,
                limit=limit * 2,  # Get more results for better hybrid ranking
                pinecone_filter=pinecone_filter
            )

            # Perform keyword search
            keyword_results = self._keyword_search(
                query=query,
                jurisdiction=jurisdiction,
                doc_type=doc_type,
                limit=limit * 2,
                supabase_filter=supabase_filter
            )
            
            # Combine and rank results
            combined_results = self._combine_results(
                semantic_results=semantic_results,
                keyword_results=keyword_results,
                query=query
            )
            
            # Apply pagination
            paginated_results = combined_results[offset:offset + limit]
            
            # Calculate query time
            query_time_ms = int((time.time() - start_time) * 1000)
            
            # Build response
            response = SearchResponse(
                results=paginated_results,
                total=len(combined_results),
                query_time_ms=query_time_ms,
                query=query,
                filters_applied={
                    'jurisdiction': jurisdiction,
                    'doc_type': doc_type,
                    'limit': limit,
                    'offset': offset
                }
            )
            
            logger.info(f"Hybrid search completed: {len(paginated_results)} results in {query_time_ms}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}", exc_info=True)
            # Return empty response on error
            return SearchResponse(
                results=[],
                total=0,
                query_time_ms=int((time.time() - start_time) * 1000),
                query=query,
                filters_applied={}
            )
    
    def _semantic_search(self,
                        query: str,
                        jurisdiction: Optional[str],
                        doc_type: Optional[str],
                        limit: int,
                        pinecone_filter: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """
        Perform semantic search using Pinecone.
        
        Returns:
            List of (document_id, similarity_score, metadata) tuples
        """
        try:
            # Build filter for Pinecone
            filter_dict = {}
            if jurisdiction:
                filter_dict['jurisdiction'] = jurisdiction
            if doc_type:
                filter_dict['doc_type'] = doc_type

            # Apply additional pinecone filter if provided
            if pinecone_filter and 'filter' in pinecone_filter:
                filter_dict.update(pinecone_filter['filter'])

            # Query Pinecone
            results = self.pinecone.query_embeddings(
                query_text=query,
                top_k=limit,
                filter=filter_dict
            )
            
            # Convert to standard format
            semantic_results = []
            for result in results:
                semantic_results.append((
                    result['id'],
                    result['score'],
                    result.get('metadata', {})
                ))
            
            logger.debug(f"Semantic search returned {len(semantic_results)} results")
            return semantic_results
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []
    
    def _keyword_search(self,
                       query: str,
                       jurisdiction: Optional[str],
                       doc_type: Optional[str],
                       limit: int,
                       supabase_filter: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """
        Perform keyword search using Supabase full-text search.
        
        Returns:
            List of (document_id, relevance_score, metadata) tuples
        """
        try:
            # Build Supabase query
            query_builder = self.supabase.table('documents').select(
                'document_id, title, doc_type, jurisdiction, authority_score, content'
            )
            
            # Add text search
            query_builder = query_builder.text_search('content', query)
            
            # Add filters
            if jurisdiction:
                query_builder = query_builder.eq('jurisdiction', jurisdiction)
            if doc_type:
                query_builder = query_builder.eq('doc_type', doc_type)

            # Apply additional supabase filters if provided
            if supabase_filter and supabase_filter.get('conditions'):
                # For now, we'll handle this in the enhanced search engine
                # This is a placeholder for more complex filtering
                pass

            # Execute query
            response = query_builder.limit(limit).execute()
            
            # Convert to standard format
            keyword_results = []
            for doc in response.data:
                # Calculate simple relevance score based on query term frequency
                relevance_score = self._calculate_keyword_relevance(query, doc.get('content', ''))
                
                keyword_results.append((
                    doc['document_id'],
                    relevance_score,
                    {
                        'title': doc.get('title', ''),
                        'doc_type': doc.get('doc_type', ''),
                        'jurisdiction': doc.get('jurisdiction', ''),
                        'authority_score': doc.get('authority_score', 0.0),
                        'content': doc.get('content', '')
                    }
                ))
            
            logger.debug(f"Keyword search returned {len(keyword_results)} results")
            return keyword_results
            
        except Exception as e:
            logger.error(f"Error in keyword search: {e}")
            return []
    
    def _calculate_keyword_relevance(self, query: str, content: str) -> float:
        """
        Calculate keyword relevance score.
        
        Simple implementation - can be enhanced with TF-IDF or other algorithms.
        """
        if not content:
            return 0.0
        
        query_terms = query.lower().split()
        content_lower = content.lower()
        
        # Count term occurrences
        total_matches = 0
        for term in query_terms:
            total_matches += content_lower.count(term)
        
        # Normalize by content length
        relevance_score = min(1.0, total_matches / max(1, len(content.split()) / 100))
        return relevance_score
    
    def _combine_results(self,
                        semantic_results: List[Tuple[str, float, Dict]],
                        keyword_results: List[Tuple[str, float, Dict]],
                        query: str) -> List[SearchResult]:
        """
        Combine semantic and keyword search results using hybrid scoring.
        """
        # Create a dictionary to merge results by document ID
        combined_scores = {}
        
        # Process semantic results
        for doc_id, semantic_score, metadata in semantic_results:
            combined_scores[doc_id] = {
                'semantic_score': semantic_score,
                'keyword_score': 0.0,
                'metadata': metadata
            }
        
        # Process keyword results
        for doc_id, keyword_score, metadata in keyword_results:
            if doc_id in combined_scores:
                combined_scores[doc_id]['keyword_score'] = keyword_score
            else:
                combined_scores[doc_id] = {
                    'semantic_score': 0.0,
                    'keyword_score': keyword_score,
                    'metadata': metadata
                }
        
        # Calculate hybrid scores and create SearchResult objects
        search_results = []
        for doc_id, scores in combined_scores.items():
            # Calculate hybrid relevance score
            relevance_score = (
                self.semantic_weight * scores['semantic_score'] +
                self.keyword_weight * scores['keyword_score']
            )
            
            # Apply authority boost
            authority_score = scores['metadata'].get('authority_score', 0.0)
            final_score = relevance_score + (self.authority_boost * authority_score)
            
            # Create SearchResult
            result = SearchResult(
                id=doc_id,
                title=scores['metadata'].get('title', 'Unknown'),
                type=scores['metadata'].get('doc_type', 'unknown'),
                jurisdiction=scores['metadata'].get('jurisdiction', 'unknown'),
                authority_score=authority_score,
                relevance_score=final_score,
                snippet=self._generate_snippet(scores['metadata'].get('content', ''), query),
                metadata=scores['metadata']
            )
            
            search_results.append(result)
        
        # Sort by final score (descending)
        search_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return search_results
    
    def _generate_snippet(self, content: str, query: str, max_length: int = 200) -> str:
        """
        Generate a snippet from content highlighting query terms.
        """
        if not content:
            return ""
        
        # Simple snippet generation - find first occurrence of query term
        query_terms = query.lower().split()
        content_lower = content.lower()
        
        # Find the first query term in content
        best_position = 0
        for term in query_terms:
            position = content_lower.find(term)
            if position != -1:
                best_position = max(0, position - 50)  # Start 50 chars before
                break
        
        # Extract snippet
        snippet = content[best_position:best_position + max_length]
        
        # Add ellipsis if truncated
        if best_position > 0:
            snippet = "..." + snippet
        if len(content) > best_position + max_length:
            snippet = snippet + "..."
        
        return snippet.strip()

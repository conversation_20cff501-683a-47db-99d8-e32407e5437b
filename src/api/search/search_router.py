"""
FastAPI router for hybrid search endpoints (Week 4 & 5).
Implements v0 search API (Week 4) and v1 enhanced search API (Week 5).
"""

import logging
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from pydantic import BaseModel, Field

from src.api.search.hybrid_search import HybridSearchEngine, SearchResponse
from src.api.search.enhanced_search import EnhancedSearchEngine, EnhancedSearchRequest
from src.api.auth.jwt_middleware import get_current_user
from src.api.auth.rbac import rbac_manager
from src.cache.cache import cache

logger = logging.getLogger(__name__)

# Create routers for both v0 and v1
router_v0 = APIRouter(prefix="/v0", tags=["search-v0"])
router_v1 = APIRouter(prefix="/v1", tags=["search-v1"])

# Initialize search engines
search_engine = HybridSearchEngine()
enhanced_search_engine = EnhancedSearchEngine()

# Combine routers
router = APIRouter()
router.include_router(router_v0)
router.include_router(router_v1)


class SearchRequest(BaseModel):
    """Search request model for validation."""
    q: str = Field(..., description="Search query", min_length=1, max_length=500)
    jurisdiction: Optional[str] = Field(None, description="Filter by jurisdiction (tx, oh, fed, etc.)")
    doc_type: Optional[str] = Field(None, description="Filter by document type (case, statute)")
    limit: int = Field(20, description="Number of results to return", ge=1, le=100)
    offset: int = Field(0, description="Pagination offset", ge=0)


class SearchResultResponse(BaseModel):
    """Individual search result response model."""
    id: str
    title: str
    type: str
    jurisdiction: str
    authority_score: float
    relevance_score: float
    snippet: str
    metadata: dict


class SearchAPIResponse(BaseModel):
    """Complete search API response model."""
    results: List[SearchResultResponse]
    total: int
    query_time_ms: int
    query: str
    filters_applied: dict
    cached: bool = False


@router_v0.get("/search", response_model=SearchAPIResponse)
async def search_documents_v0(
    response: Response,
    q: str = Query(..., description="Search query", min_length=1, max_length=500),
    jurisdiction: Optional[str] = Query(None, description="Filter by jurisdiction (tx, oh, fed, etc.)"),
    doc_type: Optional[str] = Query(None, description="Filter by document type (case, statute)"),
    limit: int = Query(20, description="Number of results to return", ge=1, le=100),
    offset: int = Query(0, description="Pagination offset", ge=0),
    user: dict = Depends(get_current_user)
) -> SearchAPIResponse:
    """
    Hybrid search endpoint combining semantic and keyword search.
    
    This endpoint:
    1. Performs semantic search using Pinecone embeddings
    2. Performs keyword search using Supabase full-text search
    3. Combines results using hybrid scoring
    4. Applies authority score boosting
    5. Returns paginated results with metadata
    
    **Authentication**: Requires valid JWT token
    **Rate Limits**: 100 requests/minute per user
    """
    try:
        logger.info(f"Search request: user={user['user_id']}, query='{q}', jurisdiction={jurisdiction}")
        
        # Validate jurisdiction access using RBAC
        if jurisdiction and not rbac_manager.can_access_jurisdiction(
            user.get("jurisdictions", []), jurisdiction
        ):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied to jurisdiction: {jurisdiction}"
            )
        
        # Check cache first
        cache_key = cache.search_cache_key(
            query=q,
            filters={
                "jurisdiction": jurisdiction,
                "doc_type": doc_type,
                "limit": limit,
                "offset": offset
            },
            user_permissions_hash=user["permissions_hash"]
        )
        
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Returning cached search results for query: '{q}'")
            cached_result["cached"] = True
            # Add cache hit header for performance tuning
            response.headers["X-Cache"] = "HIT"
            return SearchAPIResponse(**cached_result)
        
        # Perform search
        search_response = search_engine.search(
            query=q,
            jurisdiction=jurisdiction,
            doc_type=doc_type,
            limit=limit,
            offset=offset
        )
        
        # Convert to API response format
        api_results = []
        for result in search_response.results:
            api_result = SearchResultResponse(
                id=result.id,
                title=result.title,
                type=result.type,
                jurisdiction=result.jurisdiction,
                authority_score=result.authority_score,
                relevance_score=result.relevance_score,
                snippet=result.snippet,
                metadata=result.metadata
            )
            api_results.append(api_result)
        
        api_response = SearchAPIResponse(
            results=api_results,
            total=search_response.total,
            query_time_ms=search_response.query_time_ms,
            query=search_response.query,
            filters_applied=search_response.filters_applied,
            cached=False
        )
        
        # Cache the result
        cache.set(cache_key, api_response.model_dump(), cache.SEARCH_TTL)

        # Add cache miss header for performance tuning
        response.headers["X-Cache"] = "MISS"

        logger.info(f"Search completed: {len(api_results)} results in {search_response.query_time_ms}ms")
        return api_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Search error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Internal search error"
        )


@router_v0.post("/search", response_model=SearchAPIResponse)
async def search_documents_post_v0(
    response: Response,
    request: SearchRequest,
    user: dict = Depends(get_current_user)
) -> SearchAPIResponse:
    """
    POST version of search endpoint for complex queries.

    Supports the same functionality as GET /search but allows for
    more complex request bodies and longer queries.
    """
    return await search_documents_v0(
        response=response,
        q=request.q,
        jurisdiction=request.jurisdiction,
        doc_type=request.doc_type,
        limit=request.limit,
        offset=request.offset,
        user=user
    )


@router_v0.get("/search/suggestions")
async def get_search_suggestions_v0(
    q: str = Query(..., description="Partial query for suggestions", min_length=1, max_length=100),
    limit: int = Query(5, description="Number of suggestions", ge=1, le=10),
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get search query suggestions based on partial input.
    
    This endpoint provides autocomplete suggestions for search queries
    based on popular searches and document titles.
    """
    try:
        # For Week 4, return simple suggestions based on common legal terms
        # This can be enhanced in later weeks with ML-based suggestions
        
        common_terms = [
            "negligence", "medical malpractice", "personal injury", "premises liability",
            "product liability", "wrongful death", "motor vehicle accident", "slip and fall",
            "breach of contract", "employment law", "discrimination", "harassment",
            "workers compensation", "insurance bad faith", "defamation", "privacy"
        ]
        
        # Filter terms that start with the query
        suggestions = [term for term in common_terms if term.lower().startswith(q.lower())]
        suggestions = suggestions[:limit]
        
        return {
            "suggestions": suggestions,
            "query": q,
            "total": len(suggestions)
        }
        
    except Exception as e:
        logger.error(f"Suggestions error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error generating suggestions"
        )


@router_v0.get("/search/stats")
async def get_search_stats_v0(
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get search statistics and system status.
    
    Returns information about search performance, cache hit rates,
    and system health for monitoring purposes.
    """
    try:
        # Basic stats for Week 4 - can be enhanced with real metrics
        stats = {
            "search_engine_status": "operational",
            "semantic_search_enabled": True,
            "keyword_search_enabled": True,
            "cache_backend": cache.backend.__class__.__name__,
            "supported_jurisdictions": ["tx", "oh", "fed", "ny", "fl"],
            "supported_document_types": ["case", "statute"],
            "version": "v0.1.0"
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Stats error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error retrieving search stats"
        )


# Health check endpoint
@router_v0.get("/search/health")
async def search_health_check_v0():
    """
    Health check endpoint for search service.
    
    Returns the health status of search dependencies:
    - Pinecone connection
    - Supabase connection
    - Cache backend
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": cache.backend.__class__.__name__,
            "dependencies": {
                "pinecone": "unknown",  # Will be checked in implementation
                "supabase": "unknown",  # Will be checked in implementation
                "cache": "healthy" if cache.get("health_check") is not None or cache.set("health_check", "ok") else "unhealthy"
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check error: {e}", exc_info=True)
        return {
            "status": "unhealthy",
            "error": str(e)
        }


# ============================================================================
# V1 ENHANCED SEARCH ENDPOINTS (Week 5)
# ============================================================================

class EnhancedSearchAPIResponse(BaseModel):
    """Enhanced search API response model for v1."""
    results: List[SearchResultResponse]
    total: int
    total_hits: int  # Total hits before pagination
    next_offset: Optional[int] = None  # Next pagination offset
    query_time_ms: int
    query: str
    filters_applied: Dict[str, Any]
    cached: bool = False


@router_v1.get("/search", response_model=EnhancedSearchAPIResponse)
async def search_documents_v1(
    response: Response,
    q: str = Query(..., description="Search query", min_length=1, max_length=500),
    jurisdiction: Optional[str] = Query(None, description="Filter by jurisdiction"),
    practice_areas: Optional[List[str]] = Query(None, description="Filter by practice areas"),
    authority_min: Optional[float] = Query(None, description="Minimum authority score", ge=0.0, le=1.0),
    date_start: Optional[str] = Query(None, description="Start date (ISO format: YYYY-MM-DD)"),
    date_end: Optional[str] = Query(None, description="End date (ISO format: YYYY-MM-DD)"),
    sort_by: str = Query("relevance", description="Sort by: relevance, authority, date"),
    limit: int = Query(10, description="Number of results", ge=1, le=100),
    offset: int = Query(0, description="Pagination offset", ge=0),
    user: Dict[str, Any] = Depends(get_current_user)
) -> EnhancedSearchAPIResponse:
    """
    Enhanced search endpoint with advanced filtering (v1).

    Features:
    - Practice area filtering
    - Authority score filtering
    - Date range filtering
    - Advanced sorting options
    - Improved pagination with total_hits

    **Authentication**: Requires valid JWT token
    **Rate Limits**: 100 requests/minute per user
    """
    try:
        logger.info(f"Enhanced search request: user={user['user_id']}, query='{q}'")

        # Validate jurisdiction access using RBAC
        if jurisdiction and not rbac_manager.can_access_jurisdiction(
            user.get("jurisdictions", []), jurisdiction
        ):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied to jurisdiction: {jurisdiction}"
            )

        # Build date range
        date_range = {}
        if date_start:
            date_range["start"] = date_start
        if date_end:
            date_range["end"] = date_end

        # Create enhanced search request
        search_request = EnhancedSearchRequest(
            q=q,
            jurisdiction=jurisdiction,
            practice_areas=practice_areas,
            authority_min=authority_min,
            date_range=date_range if date_range else None,
            sort_by=sort_by,
            limit=limit,
            offset=offset
        )

        # Check cache first
        cache_key = cache.search_cache_key(
            query=q,
            filters={
                "jurisdiction": jurisdiction,
                "practice_areas": practice_areas,
                "authority_min": authority_min,
                "date_range": date_range,
                "sort_by": sort_by,
                "limit": limit,
                "offset": offset
            },
            user_permissions_hash=user["permissions_hash"]
        )

        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Returning cached enhanced search results for query: '{q}'")
            cached_result["cached"] = True
            response.headers["X-Cache"] = "HIT"
            return EnhancedSearchAPIResponse(**cached_result)

        # Perform enhanced search
        search_response = await enhanced_search_engine.search(search_request)

        # Convert to API response format
        api_results = []
        for result in search_response.results:
            api_result = SearchResultResponse(
                id=result.id,
                title=result.title,
                type=result.type,
                jurisdiction=result.jurisdiction,
                authority_score=result.authority_score,
                relevance_score=result.relevance_score,
                snippet=result.snippet,
                metadata=result.metadata
            )
            api_results.append(api_result)

        # Calculate next offset
        next_offset = None
        if offset + limit < search_response.total:
            next_offset = offset + limit

        api_response = EnhancedSearchAPIResponse(
            results=api_results,
            total=len(api_results),
            total_hits=search_response.total,
            next_offset=next_offset,
            query_time_ms=search_response.query_time_ms,
            query=search_response.query,
            filters_applied=search_response.filters_applied,
            cached=False
        )

        # Cache the result
        cache.set(cache_key, api_response.model_dump(), cache.SEARCH_TTL)
        response.headers["X-Cache"] = "MISS"

        logger.info(f"Enhanced search completed: {len(api_results)} results in {search_response.query_time_ms}ms")
        return api_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Enhanced search error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Internal search error"
        )

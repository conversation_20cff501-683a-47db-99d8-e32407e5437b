{"document_types": {"statute": {"name": "Statute", "description": "Legislative statutes and codes", "subtypes": {"civil_code": {"name": "Civil Code", "description": "Civil procedure and civil law statutes", "patterns": ["civil", "procedure", "tort", "contract", "property"]}, "criminal_code": {"name": "Criminal Code", "description": "Criminal law and procedure statutes", "patterns": ["criminal", "penal", "offense", "crime", "punishment"]}, "business_code": {"name": "Business Code", "description": "Business and commercial law statutes", "patterns": ["business", "commercial", "corporation", "partnership", "securities"]}, "family_code": {"name": "Family Code", "description": "Family law and domestic relations statutes", "patterns": ["family", "marriage", "divorce", "custody", "adoption"]}, "tax_code": {"name": "Tax Code", "description": "Tax law and revenue statutes", "patterns": ["tax", "revenue", "assessment", "exemption", "levy"]}, "education_code": {"name": "Education Code", "description": "Education law and school administration statutes", "patterns": ["education", "school", "university", "student", "teacher"]}, "health_code": {"name": "Health Code", "description": "Health and safety statutes", "patterns": ["health", "safety", "medical", "hospital", "public health"]}, "labor_code": {"name": "Labor Code", "description": "Employment and labor law statutes", "patterns": ["labor", "employment", "worker", "wage", "workplace"]}, "insurance_code": {"name": "Insurance Code", "description": "Insurance law and regulation statutes", "patterns": ["insurance", "policy", "coverage", "claim", "premium"]}, "transportation_code": {"name": "Transportation Code", "description": "Transportation and motor vehicle statutes", "patterns": ["transportation", "vehicle", "traffic", "highway", "motor"]}}, "hierarchy_levels": ["title", "chapter", "section", "subsection", "paragraph"], "citation_patterns": {"texas": ["Tex\\. [A-Z][a-z]+ Code § \\d+\\.\\d+", "§ \\d+\\.\\d+"], "federal": ["\\d+ U\\.S\\.C\\. § \\d+", "USC § \\d+"], "california": ["Cal\\. [A-Z][a-z]+ Code § \\d+", "§ \\d+"], "new_york": ["N\\.Y\\. [A-Z][a-z]+ Law § \\d+", "§ \\d+"]}}, "case": {"name": "Case Law", "description": "Court decisions and opinions", "subtypes": {"supreme_court": {"name": "Supreme Court Opinion", "description": "State or federal supreme court decisions", "patterns": ["supreme court", "scotus", "highest court"]}, "appellate": {"name": "Appellate Court Opinion", "description": "Court of appeals decisions", "patterns": ["court of appeals", "appellate", "circuit court"]}, "trial_court": {"name": "Trial Court Opinion", "description": "District court and trial court decisions", "patterns": ["district court", "trial court", "superior court"]}, "specialty_court": {"name": "Specialty Court Opinion", "description": "Specialized court decisions (tax, bankruptcy, etc.)", "patterns": ["tax court", "bankruptcy", "patent", "immigration"]}}, "hierarchy_levels": ["court", "division", "panel", "judge"], "citation_patterns": {"federal": ["\\d+ U\\.S\\. \\d+", "\\d+ F\\.\\d+d \\d+", "\\d+ F\\. Supp\\. \\d+"], "texas": ["\\d+ S\\.W\\.\\d+d \\d+", "\\d+ <PERSON>\\. \\d+"], "california": ["\\d+ Cal\\. \\d+d \\d+", "\\d+ P\\.\\d+d \\d+"], "new_york": ["\\d+ N\\.Y\\.\\d+d \\d+", "\\d+ N\\.E\\.\\d+d \\d+"]}}, "regulation": {"name": "Regulation", "description": "Administrative rules and regulations", "subtypes": {"federal_regulation": {"name": "Federal Regulation", "description": "Code of Federal Regulations", "patterns": ["cfr", "federal register", "administrative", "agency rule"]}, "state_regulation": {"name": "State Regulation", "description": "State administrative codes", "patterns": ["administrative code", "state regulation", "agency rule"]}, "local_ordinance": {"name": "Local Ordinance", "description": "Municipal and county ordinances", "patterns": ["ordinance", "municipal", "county", "city code"]}}, "hierarchy_levels": ["title", "chapter", "part", "section"], "citation_patterns": {"federal": ["\\d+ C\\.F\\.R\\. § \\d+\\.\\d+", "CFR § \\d+"], "texas": ["\\d+ Tex\\. Admin\\. Code § \\d+\\.\\d+"], "california": ["\\d+ Cal\\. Code Regs\\. § \\d+"], "new_york": ["\\d+ N\\.Y\\.C\\.R\\.R\\. § \\d+"]}}, "constitution": {"name": "Constitution", "description": "Constitutional documents and amendments", "subtypes": {"federal_constitution": {"name": "U.S. Constitution", "description": "United States Constitution and amendments", "patterns": ["u.s. constitution", "constitutional amendment", "bill of rights"]}, "state_constitution": {"name": "State Constitution", "description": "State constitutional provisions", "patterns": ["state constitution", "constitutional provision"]}}, "hierarchy_levels": ["article", "section", "clause"], "citation_patterns": {"federal": ["U\\.S\\. Const\\. art\\. [IVX]+", "U\\.S\\. Const\\. amend\\. [IVX]+"], "texas": ["Tex\\. Const\\. art\\. [IVX]+", "Tex\\. Const\\. § \\d+"], "california": ["Cal\\. Const\\. art\\. [IVX]+"], "new_york": ["N\\.Y\\. Const\\. art\\. [IVX]+"]}}, "administrative_ruling": {"name": "Administrative Ruling", "description": "Agency decisions and interpretations", "subtypes": {"agency_decision": {"name": "Agency Decision", "description": "Administrative agency adjudications", "patterns": ["agency decision", "administrative law judge", "hearing officer"]}, "agency_interpretation": {"name": "Agency Interpretation", "description": "Agency guidance and interpretations", "patterns": ["guidance", "interpretation", "advisory opinion", "policy statement"]}, "enforcement_action": {"name": "Enforcement Action", "description": "Agency enforcement proceedings", "patterns": ["enforcement", "violation", "penalty", "sanctions"]}}, "hierarchy_levels": ["agency", "division", "proceeding", "decision"], "citation_patterns": {"federal": ["\\d+ [A-Z]+ \\d+", "Admin\\. Dec\\. No\\. \\d+"], "texas": ["Tex\\. Admin\\. Dec\\. \\d+"], "california": ["Cal\\. Admin\\. Dec\\. \\d+"], "new_york": ["N\\.Y\\. Admin\\. Dec\\. \\d+"]}}}, "metadata_standards": {"required_fields": ["document_id", "title", "doc_type", "doc_subtype", "jurisdiction", "hierarchy_path", "created_at", "updated_at"], "optional_fields": ["version", "effective_date", "superseded_by", "source_url", "authority", "subject_matter", "practice_areas"], "jurisdiction_specific": {"statute": ["statute_title", "statute_chapter", "statute_section"], "case": ["case_number", "court", "judge", "case_date", "parties"], "regulation": ["agency", "docket_number", "effective_date"], "constitution": ["article", "section", "amendment"], "administrative_ruling": ["agency", "proceeding_number", "decision_date"]}}, "practice_areas": {"personal_injury": {"name": "Personal Injury", "keywords": ["tort", "negligence", "liability", "damages", "injury", "accident", "malpractice"], "relevant_codes": ["civil", "insurance", "health"]}, "criminal_law": {"name": "Criminal Law", "keywords": ["criminal", "offense", "felony", "misdemeanor", "prosecution", "defense"], "relevant_codes": ["criminal", "penal"]}, "business_law": {"name": "Business Law", "keywords": ["corporation", "contract", "commercial", "securities", "partnership"], "relevant_codes": ["business", "commercial"]}, "family_law": {"name": "Family Law", "keywords": ["marriage", "divorce", "custody", "adoption", "domestic"], "relevant_codes": ["family"]}, "employment_law": {"name": "Employment Law", "keywords": ["employment", "labor", "workplace", "discrimination", "wage"], "relevant_codes": ["labor"]}, "real_estate": {"name": "Real Estate", "keywords": ["property", "real estate", "deed", "mortgage", "zoning"], "relevant_codes": ["civil", "business"]}, "tax_law": {"name": "Tax Law", "keywords": ["tax", "revenue", "assessment", "exemption", "irs"], "relevant_codes": ["tax"]}, "constitutional_law": {"name": "Constitutional Law", "keywords": ["constitutional", "amendment", "rights", "due process", "equal protection"], "relevant_codes": ["constitution"]}, "immigration_law": {"name": "Immigration Law", "keywords": ["immigration", "visa", "deportation", "asylum", "citizenship", "naturalization", "green card", "refugee", "border", "ICE", "USCIS"], "relevant_codes": ["federal", "administrative"]}}}
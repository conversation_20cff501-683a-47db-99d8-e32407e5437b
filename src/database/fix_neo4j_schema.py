#!/usr/bin/env python3
"""
Fix Neo4j Schema Script
This script addresses the property warnings by ensuring all required properties exist in the schema
"""

import os
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Neo4j credentials
NEO4J_URI = os.getenv("NEO4J_URI", "neo4j+s://a332fed8.databases.neo4j.io")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "3fjmQFRQfyE0kRCuejTSbgM636aJO9jgPf14p8ZDDsc")

def fix_schema():
    """Fix missing properties and other schema issues in Neo4j"""
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        with driver.session() as session:
            # 1. Check if NodeType nodes exist
            result = session.run("MATCH (n:NodeType) RETURN count(n) as count")
            node_type_count = result.single()["count"]
            
            if node_type_count == 0:
                print("No NodeType nodes found. Creating schema...")
                # Create basic schema
                session.run("""
                CREATE (s:SchemaRegistry {id: 'schema_registry'})
                SET s.created_at = datetime(),
                    s.version = 1,
                    s.last_updated = datetime()
                """)
                
                # Create Document node type
                session.run("""
                CREATE (n:NodeType {name: 'Document'})
                SET n.properties = ['document_id', 'title', 'doc_type', 'jurisdiction', 'created_at'],
                    n.description = 'Legal document node representing statutes, cases, regulations, etc.'
                """)
                
                # Create Citation node type
                session.run("""
                CREATE (n:NodeType {name: 'Citation'})
                SET n.properties = ['text', 'type', 'context'],
                    n.description = 'Citation found in legal documents'
                """)
                
                # Create Jurisdiction node type
                session.run("""
                CREATE (n:NodeType {name: 'Jurisdiction'})
                SET n.properties = ['name', 'code'],
                    n.description = 'Jurisdiction such as state or federal'
                """)
                
                # Create DocumentType node type
                session.run("""
                CREATE (n:NodeType {name: 'DocumentType'})
                SET n.properties = ['name', 'description'],
                    n.description = 'Type of legal document (law, precedent_case, etc.)'
                """)
            else:
                print(f"Found {node_type_count} NodeType nodes. Checking for missing properties...")
                # Check for missing properties and add them
                result = session.run("MATCH (n:NodeType) WHERE n.properties IS NULL RETURN n.name as name")
                for record in result:
                    node_name = record["name"]
                    print(f"Adding missing properties field to NodeType: {node_name}")
                    
                    # Add default properties based on node type
                    if node_name == "Document":
                        session.run("""
                        MATCH (n:NodeType {name: 'Document'})
                        SET n.properties = ['document_id', 'title', 'doc_type', 'jurisdiction', 'created_at']
                        """)
                    elif node_name == "Citation":
                        session.run("""
                        MATCH (n:NodeType {name: 'Citation'})
                        SET n.properties = ['text', 'type', 'context']
                        """)
                    elif node_name == "Jurisdiction":
                        session.run("""
                        MATCH (n:NodeType {name: 'Jurisdiction'})
                        SET n.properties = ['name', 'code']
                        """)
                    elif node_name == "DocumentType":
                        session.run("""
                        MATCH (n:NodeType {name: 'DocumentType'})
                        SET n.properties = ['name', 'description']
                        """)
                    else:
                        session.run("""
                        MATCH (n:NodeType {name: $name})
                        SET n.properties = []
                        """, name=node_name)
            
            # 2. Check RelationshipType nodes for missing properties
            result = session.run("MATCH (r:RelationshipType) RETURN count(r) as count")
            rel_type_count = result.single()["count"]
            
            if rel_type_count == 0:
                print("No RelationshipType nodes found. Creating default relationship types...")
                # Create CITES relationship type
                session.run("""
                CREATE (r:RelationshipType {name: 'CITES'})
                SET r.source_type = 'Document',
                    r.target_type = 'Citation',
                    r.description = 'Document cites another document or legal concept'
                """)
                
                # Create CITED_BY relationship type
                session.run("""
                CREATE (r:RelationshipType {name: 'CITED_BY'})
                SET r.source_type = 'Document',
                    r.target_type = 'Document',
                    r.description = 'Document is cited by another document'
                """)
                
                # Create HAS_JURISDICTION relationship type
                session.run("""
                CREATE (r:RelationshipType {name: 'HAS_JURISDICTION'})
                SET r.source_type = 'Document',
                    r.target_type = 'Jurisdiction',
                    r.description = 'Document belongs to a jurisdiction'
                """)
                
                # Create HAS_TYPE relationship type
                session.run("""
                CREATE (r:RelationshipType {name: 'HAS_TYPE'})
                SET r.source_type = 'Document',
                    r.target_type = 'DocumentType',
                    r.description = 'Document has a specific document type'
                """)
            else:
                print(f"Found {rel_type_count} RelationshipType nodes. Checking for missing properties...")
                
                # Check for missing source_type and target_type properties
                result = session.run("""
                MATCH (r:RelationshipType)
                WHERE r.source_type IS NULL OR r.target_type IS NULL
                RETURN r.name as name
                """)
                
                for record in result:
                    rel_name = record["name"]
                    print(f"Adding missing source/target properties to RelationshipType: {rel_name}")
                    
                    # Add default source_type and target_type based on relationship type
                    if rel_name == "CITES":
                        session.run("""
                        MATCH (r:RelationshipType {name: 'CITES'})
                        SET r.source_type = 'Document',
                            r.target_type = 'Citation'
                        """)
                    elif rel_name == "CITED_BY":
                        session.run("""
                        MATCH (r:RelationshipType {name: 'CITED_BY'})
                        SET r.source_type = 'Document',
                            r.target_type = 'Document'
                        """)
                    elif rel_name == "HAS_JURISDICTION":
                        session.run("""
                        MATCH (r:RelationshipType {name: 'HAS_JURISDICTION'})
                        SET r.source_type = 'Document',
                            r.target_type = 'Jurisdiction'
                        """)
                    elif rel_name == "HAS_TYPE":
                        session.run("""
                        MATCH (r:RelationshipType {name: 'HAS_TYPE'})
                        SET r.source_type = 'Document',
                            r.target_type = 'DocumentType'
                        """)
                    elif rel_name == "RESOLVES_TO":
                        session.run("""
                        MATCH (r:RelationshipType {name: 'RESOLVES_TO'})
                        SET r.source_type = 'Citation',
                            r.target_type = 'Document'
                        """)
                    else:
                        session.run("""
                        MATCH (r:RelationshipType {name: $name})
                        SET r.source_type = 'Unknown',
                            r.target_type = 'Unknown'
                        """, name=rel_name)
            
            print("Schema fix completed.")
            
            # Verify if warnings are addressed
            print("\nVerifying schema:")
            
            # Check NodeTypes
            result = session.run("""
            MATCH (n:NodeType)
            RETURN n.name as name, n.properties as properties
            """)
            
            print("\nNodeTypes with properties:")
            for record in result:
                print(f"  - {record['name']}: {record['properties']}")
            
            # Check RelationshipTypes
            result = session.run("""
            MATCH (r:RelationshipType)
            RETURN r.name as name, r.source_type as source_type, r.target_type as target_type
            """)
            
            print("\nRelationshipTypes with source/target types:")
            for record in result:
                print(f"  - {record['name']}: {record['source_type']} -> {record['target_type']}")
                
    finally:
        driver.close()

if __name__ == "__main__":
    fix_schema()

#!/usr/bin/env python3
"""
Neo4j Citation Loader

This script properly loads documents and citations into Neo4j.
It processes CPRC documents, extracts citations, and creates:
1. Document nodes
2. Citation nodes
3. CITES relationships between documents and citations
4. RESOLVES_TO relationships between citations and documents

Usage:
  python neo4j_citation_loader.py
"""

import os
import json
import logging
import pdfplumber
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from neo4j import GraphDatabase
from tqdm import tqdm

# Import our components
import sys
import os

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))) 

# Import from new module structure
from src.extractors.citation_extractor import HybridCitationExtractor
from src.utils.citation_classifier import CitationClassifier, CitationType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs", "neo4j_citation_loader.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class Neo4jCitationLoader:
    """
    Loads documents and citations into Neo4j database
    """
    
    def __init__(self):
        """Initialize Neo4j connection and components"""
        self.uri = os.getenv("NEO4J_URI")
        self.user = os.getenv("NEO4J_USER")
        self.password = os.getenv("NEO4J_PASSWORD")
        
        if not all([self.uri, self.user, self.password]):
            raise ValueError("Neo4j connection details not found in environment variables")
        
        self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
        self.extractor = HybridCitationExtractor()
        self.classifier = CitationClassifier()
        
        # Create state tracking directory if it doesn't exist
        self.state_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "processing_state")
        os.makedirs(self.state_dir, exist_ok=True)
        
        # Initialize processing state
        self.state_file = os.path.join(self.state_dir, "processed_documents.json")
        self.processed_documents = self._load_processing_state()
        
        # Check database connection
        self._check_connection()
        
        # Create constraints if they don't exist
        self._create_constraints()
    
    def _load_processing_state(self):
        """Load the processing state from the state file"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading processing state: {str(e)}")
                return {}
        return {}
    
    def _save_processing_state(self):
        """Save the processing state to the state file"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.processed_documents, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving processing state: {str(e)}")
    
    def mark_document_as_processed(self, filename, stats):
        """Mark a document as processed with stats and timestamp"""
        self.processed_documents[filename] = {
            "timestamp": datetime.now().isoformat(),
            "stats": stats
        }
        self._save_processing_state()
    
    def is_document_processed(self, filename):
        """Check if a document has been processed"""
        return filename in self.processed_documents
    
    def is_document_in_neo4j(self, filename):
        """Check if a document exists in Neo4j but may not have been fully processed"""
        doc_id = f"CPRC_{filename.replace('.pdf', '')}"
        query = """
        MATCH (d:Document {document_id: $doc_id}) 
        OPTIONAL MATCH (d)-[r:CITES]->(c) 
        RETURN d, count(r) as citation_count
        """
        
        with self.driver.session() as session:
            try:
                result = session.run(query, doc_id=doc_id)
                record = result.single()
                if record and record.get("d"):
                    return {
                        "exists": True,
                        "citation_count": record.get("citation_count", 0),
                        "is_complete": record.get("citation_count", 0) > 0
                    }
                return {"exists": False, "citation_count": 0, "is_complete": False}
            except Exception as e:
                logger.error(f"Error checking if document exists in Neo4j: {str(e)}")
                return {"exists": False, "citation_count": 0, "is_complete": False}
    
    def _check_connection(self):
        """Verify Neo4j connection is working"""
        try:
            with self.driver.session() as session:
                result = session.run("MATCH (n) RETURN count(n) AS count LIMIT 1")
                count = result.single()["count"]
                logger.info(f"Connected to Neo4j database with {count} nodes")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            raise ConnectionError(f"Cannot connect to Neo4j database: {str(e)}")

    def _create_constraints(self):
        """Create necessary constraints in Neo4j"""
        constraints = [
            "CREATE CONSTRAINT document_id_unique IF NOT EXISTS FOR (d:Document) REQUIRE d.document_id IS UNIQUE",
            "CREATE CONSTRAINT citation_text_unique IF NOT EXISTS FOR (c:Citation) REQUIRE c.text IS UNIQUE"
        ]
        
        with self.driver.session() as session:
            for constraint in constraints:
                try:
                    session.run(constraint)
                    logger.info(f"Created constraint: {constraint}")
                except Exception as e:
                    logger.warning(f"Could not create constraint: {str(e)}")
    
    def create_document(self, document_id: str, title: str, content: str, metadata: dict):
        """Create a document node in Neo4j"""
        query = """
        MERGE (d:Document {document_id: $document_id})
        SET d.title = $title,
            d.content = $content,
            d.doc_type = $doc_type,
            d.jurisdiction = $jurisdiction,
            d.created_at = datetime(),
            d.statute_chapter = $statute_chapter,
            d.statute_title = $statute_title,
            d.statute_section = $statute_section,
            d.path = $path
        RETURN d
        """
        
        with self.driver.session() as session:
            try:
                result = session.run(
                    query,
                    document_id=document_id,
                    title=title,
                    content=content,
                    doc_type=metadata.get("doc_type", "law"),
                    jurisdiction=metadata.get("jurisdiction", "Texas"),
                    statute_chapter=metadata.get("statute_chapter", ""),
                    statute_title=metadata.get("statute_title", ""),
                    statute_section=metadata.get("statute_section", ""),
                    path=metadata.get("path", "")
                )
                record = result.single()
                if record:
                    logger.info(f"Created document: {document_id}")
                    return record["d"]
                else:
                    logger.warning(f"Failed to create document: {document_id}")
                    return None
            except Exception as e:
                logger.error(f"Error creating document {document_id}: {str(e)}")
                return None
    
    def create_citation(self, citation_text: str, citation_data: dict):
        """Create a citation node in Neo4j"""
        query = """
        MERGE (c:Citation {text: $text})
        SET c.type = $type,
            c.confidence = $confidence,
            c.extraction_method = $extraction_method,
            c.created_at = datetime()
        RETURN c
        """
        
        classification = citation_data.get("classification", {})
        
        with self.driver.session() as session:
            try:
                result = session.run(
                    query,
                    text=citation_text,
                    type=classification.get("type", "unknown"),
                    confidence=str(classification.get("confidence", 0.0)),
                    extraction_method=citation_data.get("method", "hybrid")
                )
                record = result.single()
                if record:
                    logger.info(f"Created citation: {citation_text}")
                    return record["c"]
                else:
                    logger.warning(f"Failed to create citation: {citation_text}")
                    return None
            except Exception as e:
                logger.error(f"Error creating citation {citation_text}: {str(e)}")
                return None
    
    def create_document_citation_relationship(self, document_id: str, citation_text: str):
        """Create a CITES relationship between document and citation"""
        query = """
        MATCH (d:Document {document_id: $document_id})
        MATCH (c:Citation {text: $citation_text})
        MERGE (d)-[r:CITES]->(c)
        RETURN r
        """
        
        with self.driver.session() as session:
            try:
                result = session.run(
                    query,
                    document_id=document_id,
                    citation_text=citation_text
                )
                return result.single() is not None
            except Exception as e:
                logger.error(f"Error creating relationship: {str(e)}")
                return False
    
    def resolve_citation(self, citation_text: str, target_document_id: str):
        """Create a RESOLVES_TO relationship from citation to document"""
        query = """
        MATCH (c:Citation {text: $citation_text})
        MATCH (d:Document {document_id: $target_document_id})
        MERGE (c)-[r:RESOLVES_TO]->(d)
        RETURN r
        """
        
        with self.driver.session() as session:
            try:
                result = session.run(
                    query,
                    citation_text=citation_text,
                    target_document_id=target_document_id
                )
                return result.single() is not None
            except Exception as e:
                logger.error(f"Error resolving citation: {str(e)}")
                return False
    
    def process_document(self, pdf_path: str, use_llm: bool = True, max_retries: int = 3, force_reprocess: bool = False):
        """Process a document and add it to Neo4j with its citations"""
        filename = os.path.basename(pdf_path)
        document_id = f"CPRC_{filename.replace('.pdf', '')}"
        
        # Extract text from PDF
        logger.info(f"Extracting text from {filename}")
        text = ""
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text() or ""
                    text += page_text
        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {str(e)}")
            # Try once more with a different approach
            try:
                import fitz  # PyMuPDF as fallback
                logger.info(f"Retrying with PyMuPDF for {filename}")
                with fitz.open(pdf_path) as doc:
                    for page in doc:
                        text += page.get_text()
            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed for {filename}: {str(fallback_error)}")
                return None
        
        if not text.strip():
            logger.warning(f"No text extracted from {filename}")
            return None
        
        # Create metadata
        metadata = {
            "title": f"CPRC {filename}",
            "path": pdf_path,
            "jurisdiction": "Texas",
            "doc_type": "law",
            "statute_chapter": filename.replace("cp.", "").replace(".pdf", "")
        }
        
        # Step 1: Create document in Neo4j
        document = self.create_document(document_id, metadata["title"], text, metadata)
        if not document:
            return None
        
        # Step 2: Extract citations with timeout and retry logic
        retry_count = 0
        extracted_citations = []
        while retry_count < max_retries:
            try:
                # Set a timeout for the extraction process
                import signal
                
                # Define a timeout handler
                def timeout_handler(signum, frame):
                    raise TimeoutError(f"Citation extraction timed out for {filename}")
                
                # Set a timeout of 60 seconds for extraction
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(60)
                
                # Try extraction
                extracted_citations = self.extractor.extract_citations(
                    text, 
                    metadata["doc_type"], 
                    doc_metadata=metadata,
                    use_llm=use_llm
                )
                
                # If successful, cancel the alarm and break the loop
                signal.alarm(0)
                break
                
            except TimeoutError as te:
                retry_count += 1
                logger.warning(f"Attempt {retry_count} - {str(te)}")
                if retry_count >= max_retries:
                    # If still failing after max retries, try without LLM as fallback
                    logger.warning(f"Falling back to rule-based extraction for {filename}")
                    try:
                        signal.alarm(30)  # Shorter timeout for rule-based extraction
                        extracted_citations = self.extractor.extract_citations(
                            text, 
                            metadata["doc_type"], 
                            doc_metadata=metadata,
                            use_llm=False
                        )
                        signal.alarm(0)
                    except Exception as e:
                        logger.error(f"Fallback extraction failed for {filename}: {str(e)}")
                        extracted_citations = []
            except Exception as e:
                retry_count += 1
                logger.warning(f"Attempt {retry_count} - Error in citation extraction: {str(e)}")
                if retry_count >= max_retries:
                    logger.error(f"Failed to extract citations after {max_retries} attempts for {filename}")
                    extracted_citations = []
                    break
        
        logger.info(f"Extracted {len(extracted_citations)} citations from {filename}")
        
        # Step 3: Process each citation
        citation_counts = {
            "total": len(extracted_citations),
            "created": 0,
            "linked": 0,
            "resolved": 0
        }
        
        for citation in extracted_citations:
            citation_text = citation['text']
            context = citation.get('context', '')
            
            # Classify the citation
            classification_details = self.classifier.get_classification_details(citation_text, context)
            citation["classification"] = classification_details["classification"]
            citation["components"] = classification_details["components"]
            
            # Create citation node
            citation_node = self.create_citation(citation_text, citation)
            if citation_node:
                citation_counts["created"] += 1
                
                # Link document to citation
                if self.create_document_citation_relationship(document_id, citation_text):
                    citation_counts["linked"] += 1
                
                # For section citations, try to resolve to another document
                if citation["classification"]["type"] in [
                    "internal_section", "external_statute", "constitution_citation"
                ]:
                    # Try to find a matching document to resolve to
                    components = citation.get("components", {})
                    section = components.get("section", "")
                    chapter = components.get("chapter", "")
                    
                    # If we have a section, look for matching document
                    if section:
                        target_id = f"CPRC_cp.{section.lower()}"
                        if self.resolve_citation(citation_text, target_id):
                            citation_counts["resolved"] += 1
                    elif chapter:
                        target_id = f"CPRC_cp.{chapter.lower()}"
                        if self.resolve_citation(citation_text, target_id):
                            citation_counts["resolved"] += 1
        
        return {
            "document_id": document_id,
            "title": metadata["title"],
            "citations": citation_counts
        }
    
    def process_directory(self, directory_path: str, use_llm: bool = True, start_from: str = None, force_reprocess: bool = False):
        """Process all PDF files in a directory"""
        # Get all PDF files
        pdf_files = [f for f in os.listdir(directory_path) if f.lower().endswith(".pdf")]
        logger.info(f"Found {len(pdf_files)} PDF files to process")
        
        results = {
            "total_documents": len(pdf_files),
            "processed_documents": 0,
            "failed_documents": 0,
            "total_citations": 0,
            "created_citations": 0,
            "linked_citations": 0,
            "resolved_citations": 0
        }
        
        # Sort PDF files to ensure consistent order
        pdf_files.sort()
        
        # If start_from is specified, start processing from that file
        start_index = 0
        if start_from:
            try:
                start_index = pdf_files.index(start_from)
                logger.info(f"Starting from file {start_from} (index {start_index})")
            except ValueError:
                logger.warning(f"File {start_from} not found, starting from beginning")
        
        # Initialize lists to track document processing status
        skipped_files = []
        processed_files = []
        failed_files = []
        
        # Process each PDF
        for filename in tqdm(pdf_files[start_index:], desc="Processing documents"):
            pdf_path = os.path.join(directory_path, filename)
            
            # Check if file has already been processed (unless forced reprocessing)
            if not force_reprocess:
                # First check our local processing state
                if self.is_document_processed(filename):
                    logger.info(f"Skipping already processed file: {filename}")
                    skipped_files.append(filename)
                    continue
                
                # Also check Neo4j to detect partially processed documents
                neo4j_status = self.is_document_in_neo4j(filename)
                if neo4j_status["exists"]:
                    if neo4j_status["is_complete"]:
                        # Document exists in Neo4j with citations, but not in our tracking
                        # Add it to our tracking system to avoid future reprocessing
                        logger.info(f"Document {filename} exists in Neo4j with {neo4j_status['citation_count']} citations. Adding to tracking system.")
                        self.mark_document_as_processed(filename, {"total": neo4j_status["citation_count"]})
                        skipped_files.append(filename)
                        continue
                    else:
                        # Document exists but has no citations - likely partially processed
                        logger.warning(f"Found partially processed document {filename} in Neo4j with no citations. Will reprocess.")
                        # We'll continue and process it, as it needs to be completed
            
            try:
                result = self.process_document(pdf_path, use_llm, force_reprocess=force_reprocess)
                if result:
                    results["processed_documents"] += 1
                    results["total_citations"] += result["citations"]["total"]
                    results["created_citations"] += result["citations"]["created"]
                    results["linked_citations"] += result["citations"]["linked"]
                    results["resolved_citations"] += result["citations"]["resolved"]
                    
                    # Mark document as processed with its stats
                    self.mark_document_as_processed(filename, result["citations"])
                    processed_files.append(filename)
                    
                    logger.info(
                        f"Processed {filename}: {result['citations']['total']} citations, "
                        f"{result['citations']['created']} created, "
                        f"{result['citations']['linked']} linked, "
                        f"{result['citations']['resolved']} resolved"
                    )
                else:
                    results["failed_documents"] += 1
                    failed_files.append(filename)
            except Exception as e:
                logger.error(f"Error processing {filename}: {str(e)}")
                results["failed_documents"] += 1
                failed_files.append(filename)
        
        # Add processing stats to results
        results["skipped_files"] = skipped_files
        results["processed_files"] = processed_files
        results["failed_files"] = failed_files
        
        # Save a timestamped report of this processing run
        report_file = os.path.join(self.state_dir, f"processing_report_{int(time.time())}.json")
        try:
            with open(report_file, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Processing report saved to {report_file}")
        except Exception as e:
            logger.error(f"Error saving processing report: {str(e)}")
        
        return results
    
    def close(self):
        """Close the Neo4j driver connection"""
        if self.driver:
            self.driver.close()

def main():
    """Main function to run the Neo4j Citation Loader"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Load documents and citations into Neo4j")
    parser.add_argument("--input_dir", default="/Users/<USER>/Documents/Texas/CP/success",
                       help="Directory containing CPRC PDFs")
    parser.add_argument("--use_llm", action="store_true", default=True,
                       help="Use LLM for citation extraction")
    parser.add_argument("--start_from", default=None,
                       help="Start processing from this file (e.g. cp.75.pdf)")
    parser.add_argument("--force_reprocess", action="store_true", default=False,
                       help="Force reprocessing of documents even if they have been processed before")
    parser.add_argument("--list_processed", action="store_true", default=False,
                       help="List all processed documents and exit")
    parser.add_argument("--list_unprocessed", action="store_true", default=False,
                       help="Compare available documents with processed ones and list unprocessed files")
    parser.add_argument("--process_file", default=None,
                       help="Process a specific file only, ignoring others")
    parser.add_argument("--cleanup_partial", action="store_true", default=False,
                       help="Find and reprocess documents that exist in Neo4j but have no citations")
    
    args = parser.parse_args()
    
    logger.info(f"Starting Neo4j Citation Loader for directory: {args.input_dir}")
    logger.info(f"LLM-enabled extraction: {args.use_llm}")
    
    loader = Neo4jCitationLoader()
    
    try:
        # Handle list options first
        if args.list_processed:
            # List all processed documents and exit
            processed = loader.processed_documents
            print(f"\nProcessed Documents ({len(processed)}):\n")
            for doc, info in sorted(processed.items()):
                stats = info.get('stats', {})
                
                # Extract citation counts if available
                total_citations = stats.get('total', 0)
                if isinstance(stats, dict) and 'total' not in stats:
                    # Handle different formats of stats
                    total_citations = sum(stats.values()) if stats else 0
                    
                timestamp = info.get('timestamp', 'unknown')
                print(f"  {doc}: {total_citations} citations (processed: {timestamp})")
            return
            
        if args.list_unprocessed:
            # List all unprocessed documents
            all_pdfs = [f for f in os.listdir(args.input_dir) if f.lower().endswith(".pdf")]
            processed = set(loader.processed_documents.keys())
            unprocessed = [pdf for pdf in all_pdfs if pdf not in processed]
            
            print(f"\nUnprocessed Documents ({len(unprocessed)}/{len(all_pdfs)}):\n")
            
            # Check for partially processed documents in Neo4j
            partial_processed = []
            fully_unprocessed = []
            
            for doc in sorted(unprocessed):
                neo4j_status = loader.is_document_in_neo4j(doc)
                if neo4j_status["exists"]:
                    if neo4j_status["is_complete"]:
                        # This should be marked as processed - fix our tracking
                        loader.mark_document_as_processed(doc, {"total": neo4j_status["citation_count"]})
                        print(f"  [FIXED TRACKING] {doc}: Already in Neo4j with {neo4j_status['citation_count']} citations")
                    else:
                        # This is partially processed - show warning
                        partial_processed.append(doc)
                        print(f"  [PARTIAL] {doc}: Document exists in Neo4j but has no citations")
                else:
                    # Completely unprocessed
                    fully_unprocessed.append(doc)
                    print(f"  {doc}")
            
            # Print summary counts
            print(f"\nSummary:")
            print(f"  Fully unprocessed: {len(fully_unprocessed)}")
            print(f"  Partially processed: {len(partial_processed)}")
            print(f"  Already processed: {len(all_pdfs) - len(unprocessed)}")
            
            return
        
        # Find and clean up partially processed documents
        if args.cleanup_partial:
            # Find documents in Neo4j that have no citations
            query = """
            MATCH (d:Document) 
            WHERE NOT (d)-[:CITES]->() 
            RETURN d.document_id as doc_id, d.path as path
            """
            
            with loader.driver.session() as session:
                result = session.run(query)
                records = list(result)
                
                if not records:
                    print("No partially processed documents found in Neo4j.")
                    return
                
                print(f"\nFound {len(records)} partially processed documents in Neo4j:\n")
                for record in records:
                    doc_id = record.get("doc_id", "")
                    path = record.get("path", "")
                    filename = os.path.basename(path) if path else f"{doc_id.replace('CPRC_', '')}.pdf"
                    print(f"  {filename} (ID: {doc_id})")
                
                if input("\nWould you like to reprocess these documents? (y/n): ").lower() != 'y':
                    return
                
                # Process each partially processed document
                for record in records:
                    doc_id = record.get("doc_id", "")
                    path = record.get("path", "")
                    
                    if not path or not os.path.exists(path):
                        # Try to find the file in the input directory
                        filename = f"{doc_id.replace('CPRC_', '')}.pdf"
                        path = os.path.join(args.input_dir, filename)
                        
                        if not os.path.exists(path):
                            print(f"  [ERROR] Could not find file for {doc_id}")
                            continue
                    
                    print(f"\nReprocessing {os.path.basename(path)}...")
                    result = loader.process_document(path, args.use_llm, force_reprocess=True)
                    
                    if result:
                        print(f"  Successfully reprocessed with {result['citations']['total']} citations")
                        loader.mark_document_as_processed(os.path.basename(path), result["citations"])
                    else:
                        print(f"  [ERROR] Failed to reprocess")
                
                return
        
        # Process a specific file if requested
        if args.process_file:
            file_path = os.path.join(args.input_dir, args.process_file)
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return
                
            logger.info(f"Processing single file: {args.process_file}")
            result = loader.process_document(file_path, args.use_llm, force_reprocess=args.force_reprocess)
            
            if result:
                logger.info(f"Successfully processed {args.process_file}")
                logger.info(f"Citations found: {result['citations']['total']}")
                logger.info(f"Citations created: {result['citations']['created']}")
                logger.info(f"Citations linked: {result['citations']['linked']}")
                loader.mark_document_as_processed(args.process_file, result["citations"])
            else:
                logger.error(f"Failed to process {args.process_file}")
            return
        
        # Process the entire directory
        results = loader.process_directory(
            args.input_dir, 
            args.use_llm, 
            args.start_from,
            args.force_reprocess
        )
        
        # Print summary
        logger.info("\n===== PROCESSING SUMMARY =====")
        logger.info(f"Total Documents: {results['total_documents']}")
        logger.info(f"Successfully Processed: {results['processed_documents']}")
        logger.info(f"Failed: {results['failed_documents']}")
        logger.info(f"Total Citations: {results['total_citations']}")
        logger.info(f"Created Citations: {results['created_citations']}")
        logger.info(f"Document-Citation Links: {results['linked_citations']}")
        logger.info(f"Resolved Citations: {results['resolved_citations']}")
        if results['total_citations'] > 0:
            logger.info(f"Resolution Rate: {results['resolved_citations'] / results['total_citations']:.2%}")
        
        # Print list of processed, skipped and failed files
        if results.get('processed_files'):
            logger.info(f"\nSuccessfully processed files ({len(results['processed_files'])})")
            for f in sorted(results['processed_files']):
                logger.info(f"  - {f}")
                
        if results.get('skipped_files'):
            logger.info(f"\nSkipped files (already processed) ({len(results['skipped_files'])})")
            for f in sorted(results['skipped_files']):
                logger.info(f"  - {f}")
                
        if results.get('failed_files'):
            logger.info(f"\nFailed files ({len(results['failed_files'])})")
            for f in sorted(results['failed_files']):
                logger.info(f"  - {f}")
    finally:
        loader.close()

if __name__ == "__main__":
    main()

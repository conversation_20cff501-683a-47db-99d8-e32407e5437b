"""
Enhanced Citation Extraction System
Combines regex pattern matching with LLM-based extraction for improved accuracy
Includes OCR error correction for better citation quality
"""

import os
import re
import json
import uuid
import time
from datetime import datetime
import requests
from dotenv import load_dotenv

# Import our OCR cleaner module
import sys
import os

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))) 

# Import from new module structure
from src.extractors.ocr_cleaner import clean_document_text, process_citations

# Load environment variables
load_dotenv()

# LLM API credentials
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# Set the Gemini model directly based on environment variable or use the flash-lite model by default
# Use the exact model name provided by the user
GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-lite-001")

# OCR Cleanup configuration
ENABLE_OCR_CLEANUP = os.getenv("ENABLE_OCR_CLEANUP", "true").lower() == "true"
OCR_CONFIDENCE_THRESHOLD = os.getenv("OCR_CONFIDENCE_THRESHOLD", "medium")

class HybridCitationExtractor:
    """Extract citations using both regex patterns and LLM analysis"""
    
    def __init__(self):
        self.gemini_api_key = GEMINI_API_KEY
        self.model_name = GEMINI_MODEL
        self.extraction_logs = []
        
        # Load citation patterns
        self.citation_patterns = self._load_citation_patterns()
        
    def _load_citation_patterns(self):
        """Load citation patterns from configuration file or use defaults"""
        try:
            with open("citation_patterns.json", "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # Default patterns if file not found or invalid
            return {
                "case_citations": [
                    r'([A-Z][a-z]+\s+v\.\s+[A-Z][a-z]+)',  # Basic case name (Smith v. Jones)
                    r'(\d+\s+[A-Z][a-z]+\.(?:\s+\d+)?)',   # Reporter citations (123 S.Ct. 456)
                    r'(\d+\s+[A-Z]\.(?:\w+\.)+\s+\d+)'     # Complex citations (123 F.3d 456)
                ],
                "statute_citations": [
                    r'(?:Section|§)\s+(\d+[\.\d]*[A-Za-z]*)',  # Section references
                    r'Chapter\s+(\d+[A-Za-z]*)',              # Chapter references
                    r'Title\s+(\d+[A-Za-z]*)'                 # Title references
                ],
                "regulation_citations": [
                    r'(\d+\s+C\.F\.R\.?\s+§?\s*[\d\.]+)',     # CFR citations
                    r'(\d+\s+Fed\.\s+Reg\.\s+[\d,]+)'         # Federal Register
                ]
            }
    
    def extract_citations_regex(self, content, doc_type):
        """Extract citations from document content using regex patterns"""
        citations = []
        
        # Select pattern groups based on document type
        pattern_groups = []
        if doc_type == "precedent_case":
            pattern_groups.append(("case_citation", self.citation_patterns["case_citations"]))
        elif doc_type == "law":
            pattern_groups.append(("statute_citation", self.citation_patterns["statute_citations"]))
        elif doc_type == "regulation":
            pattern_groups.append(("regulation_citation", self.citation_patterns["regulation_citations"]))
        else:
            # For unknown types, try all patterns
            pattern_groups = [
                ("case_citation", self.citation_patterns["case_citations"]),
                ("statute_citation", self.citation_patterns["statute_citations"]),
                ("regulation_citation", self.citation_patterns["regulation_citations"])
            ]
        
        # Apply each pattern group
        for citation_type, patterns in pattern_groups:
            for pattern in patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    citation_text = match.group(0)
                    # Get context (100 chars before and after)
                    start_pos = max(0, match.start() - 100)
                    end_pos = min(len(content), match.end() + 100)
                    context = content[start_pos:end_pos]
                    
                    citations.append({
                        "text": citation_text,
                        "type": citation_type,
                        "context": context,
                        "start_pos": match.start(),
                        "end_pos": match.end(),
                        "extraction_method": "regex"
                    })
        
        return citations
    
    def call_gemini(self, prompt):
        """Call the Gemini API with the given prompt, optimized for different models"""
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        params = {
            "key": self.gemini_api_key
        }
        
        # Adjust configuration based on model type
        if "flash-lite" in self.model_name.lower():
            # Optimize for the flash-lite model (faster, more efficient)
            generation_config = {
                "temperature": 0.1,  # Lower temperature for more deterministic output
                "topP": 0.95,      # Higher topP for flash models
                "topK": 40,
                "maxOutputTokens": 1024  # Limit output tokens for faster response
            }
        else:
            # Configuration for standard/pro models (more thorough)
            generation_config = {
                "temperature": 0.2,
                "topP": 0.8,
                "topK": 40,
                "maxOutputTokens": 2048 # Allow more tokens for comprehensive analysis
            }
        
        data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": generation_config
        }
        
        try:
            response = requests.post(api_url, headers=headers, params=params, json=data)
            
            # Handle rate limits (429 errors)
            if response.status_code == 429:
                print(f"Rate limit exceeded (429). Falling back to regex-only extraction.")
                return {"error": "rate_limit_exceeded"}
            
            # Handle successful response
            if response.status_code == 200:
                return response.json()
            
            # Handle other errors
            print(f"Error calling Gemini API: {response.status_code}")
            print(response.text)
            return {"error": f"API error: {response.status_code}"}
        except Exception as e:
            print(f"Exception in Gemini API call: {str(e)}")
            return {"error": f"Exception: {str(e)}"}
    
    def parse_gemini_citations(self, response):
        """Parse the Gemini API response for citations with robust error handling"""
        try:
            if not response or "candidates" not in response:
                return {"error": "Invalid response from Gemini API"}
                
            text_response = response["candidates"][0]["content"]["parts"][0]["text"]
            
            # Try multiple approaches to extract valid JSON
            json_data = None
            errors = []
            
            # Approach 1: Standard JSON parsing (original method)
            try:
                json_start = text_response.find('{')
                json_end = text_response.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    json_str = text_response[json_start:json_end]
                    json_data = json.loads(json_str)
            except Exception as e:
                errors.append(f"Standard parsing failed: {str(e)}")
            
            # Approach 2: Try to parse as markdown code block
            if json_data is None:
                try:
                    import re
                    json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text_response)
                    if json_match:
                        json_str = json_match.group(1)
                        json_data = json.loads(json_str)
                except Exception as e:
                    errors.append(f"Markdown block parsing failed: {str(e)}")
            
            # Approach 3: Fix common JSON errors and try again
            if json_data is None:
                try:
                    # Find what looks like a JSON object
                    json_str = text_response
                    
                    # Fix common issues in flash-lite model outputs
                    json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)  # Fix unquoted keys
                    json_str = re.sub(r':\s*([\w.]+)([,}])', r':"\1"\2', json_str)  # Fix unquoted string values
                    json_str = re.sub(r',\s*}', r'}', json_str)  # Remove trailing commas
                    
                    # Re-extract the JSON object after fixes
                    json_start = json_str.find('{')
                    json_end = json_str.rfind('}') + 1
                    
                    if json_start != -1 and json_end > json_start:
                        json_str = json_str[json_start:json_end]
                        json_data = json.loads(json_str)
                except Exception as e:
                    errors.append(f"Fixed parsing failed: {str(e)}")
            
            # Approach 4: Parse in a more flexible way using regex pattern matching
            if json_data is None:
                try:
                    # Look for citation patterns in the response
                    citations = []
                    citation_pattern = r'(?:"text"\s*:\s*"([^"]+)"|text\s*:\s*([^,\n]+))'  # Match with or without quotes
                    confidence_pattern = r'(?:"confidence"\s*:\s*"([^"]+)"|confidence\s*:\s*([^,\n]+))'  # Match confidence
                    
                    # Extract citation texts
                    matches = re.finditer(citation_pattern, text_response)
                    for match in matches:
                        citation_text = match.group(1) or match.group(2)
                        if citation_text:
                            # Look for confidence near this citation
                            confidence = "medium"  # Default
                            conf_start = max(0, match.start() - 50)
                            conf_end = min(len(text_response), match.end() + 50)
                            conf_context = text_response[conf_start:conf_end]
                            conf_match = re.search(confidence_pattern, conf_context)
                            if conf_match:
                                confidence = (conf_match.group(1) or conf_match.group(2) or "medium").strip().lower()
                            
                            citations.append({
                                "text": citation_text.strip(),
                                "confidence": confidence,
                                "extraction_method": "llm"
                            })
                    
                    if citations:
                        json_data = {"citations": citations}
                except Exception as e:
                    errors.append(f"Regex extraction failed: {str(e)}")
            
            if json_data is None:
                return {"error": f"Failed to parse response. Errors: {'; '.join(errors)}"}
                
            # Make sure the citations key exists
            if "citations" not in json_data and isinstance(json_data, dict):
                # Check if the response itself is a list of citations
                if any(isinstance(item, dict) and "text" in item for item in json_data.values() if isinstance(item, dict)):
                    # Transform to expected format
                    citations = []
                    for key, value in json_data.items():
                        if isinstance(value, dict) and "text" in value:
                            citation = value.copy()
                            citation["extraction_method"] = "llm"
                            citations.append(citation)
                    json_data = {"citations": citations}
            
            # Add extraction method to all citations
            for citation in json_data.get("citations", []):
                citation["extraction_method"] = "llm"
                
            return json_data
            
        except Exception as e:
            return {"error": f"Error parsing Gemini response: {str(e)}"}
    
    def extract_citations_llm(self, content, doc_type, doc_metadata=None):
        """Extract citations using LLM analysis"""
        # Truncate content if too long
        max_content_length = 10000  # Adjust based on model context limits
        truncated_content = content[:max_content_length]
        
        # Build prompt
        prompt = f"""
        You are a legal citation extraction expert. Analyze this legal document and extract all citations.
        
        Document type: {doc_type}
        Document metadata: {json.dumps(doc_metadata or {})}
        
        Document content:
        {truncated_content}
        
        Extract all legal citations from this document. Include:
        1. The exact citation text
        2. The type of citation (case, statute, regulation, etc.)
        3. The context surrounding the citation (relevant sentence or paragraph)
        4. Your confidence level (high, medium, low)
        5. Any additional information about what is being cited
        
        Format your response as a JSON object with the following structure:
        {{
            "citations": [
                {{
                    "text": "The exact citation text",
                    "type": "case_citation|statute_citation|regulation_citation",
                    "context": "The sentence or paragraph containing the citation",
                    "confidence": "high|medium|low",
                    "additional_info": "Any extra information about the citation"
                }}
            ]
        }}
        
        Be thorough and identify both explicit citations (e.g., "Smith v. Jones") and implicit references 
        to legal authorities. Focus especially on citations that might be missed by simple pattern matching.
        """
        
        # Call Gemini API
        response = self.call_gemini(prompt)
        
        # Parse citations
        result = self.parse_gemini_citations(response)
        
        if "error" in result:
            print(f"Error extracting citations with LLM: {result['error']}")
            return []
            
        return result.get("citations", [])
    
    def merge_citations(self, regex_citations, llm_citations):
        """Merge and deduplicate citations from regex and LLM extraction"""
        # Create a set of citation texts from regex extraction
        regex_citation_texts = {c["text"].lower() for c in regex_citations}
        
        # Combine citations, avoiding duplicates
        merged_citations = regex_citations.copy()
        
        for llm_citation in llm_citations:
            # Skip if this citation was already found by regex
            if llm_citation["text"].lower() in regex_citation_texts:
                continue
                
            merged_citations.append(llm_citation)
        
        return merged_citations
    
    def extract_citations(self, content, doc_type, doc_metadata=None, use_llm=True, min_confidence=None):
        """Extract citations using hybrid approach (regex + optional LLM) with OCR cleaning"""
        original_content = content
        
        # Apply OCR cleanup if enabled
        if ENABLE_OCR_CLEANUP:
            print("Applying OCR cleanup to document content")
            content = clean_document_text(content)
        
        # Extract citations using regex
        regex_citations = self.extract_citations_regex(content, doc_type)
        
        # Apply OCR cleanup to regex citations if enabled
        if ENABLE_OCR_CLEANUP and regex_citations:
            regex_citations = process_citations(regex_citations, content)
        
        # If LLM extraction is disabled, return regex results only
        if not use_llm:
            print("Using regex-only citation extraction (LLM disabled)")
            return regex_citations
        
        # Track model used for logging/debugging
        model_name = GEMINI_MODEL.split('-')[0] if GEMINI_MODEL else "unknown"
        print(f"Using {model_name} model for LLM extraction")
            
        # Extract citations using LLM
        llm_citations = self.extract_citations_llm(content, doc_type, doc_metadata)
        
        # If LLM extraction failed or returned no results, use regex only
        if not llm_citations:
            print("LLM extraction failed or returned no results. Using regex-only extraction.")
            return regex_citations
        
        # Apply OCR cleanup to LLM citations if enabled
        if ENABLE_OCR_CLEANUP and llm_citations:
            llm_citations = process_citations(llm_citations, content)
        
        # Merge and deduplicate citations
        merged_citations = self.merge_citations(regex_citations, llm_citations)
        
        # Filter by confidence if specified
        if min_confidence:
            confidence_levels = {"high": 3, "medium": 2, "low": 1, "unknown": 0}
            min_level = confidence_levels.get(min_confidence.lower(), 0)
            
            # Filter citations by confidence
            filtered_citations = []
            for citation in merged_citations:
                citation_conf = citation.get("confidence", "unknown")
                citation_level = confidence_levels.get(citation_conf.lower(), 0)
                
                if citation_level >= min_level:
                    filtered_citations.append(citation)
            
            print(f"Filtered citations by minimum confidence '{min_confidence}': {len(filtered_citations)}/{len(merged_citations)} retained")
            merged_citations = filtered_citations
        
        # Log extraction process
        extraction_log = {
            "document_type": doc_type,
            "regex_citation_count": len(regex_citations),
            "llm_citation_count": len(llm_citations),
            "merged_citation_count": len(merged_citations),
            "timestamp": datetime.now().isoformat()
        }
        self.extraction_logs.append(extraction_log)
        
        return merged_citations
    
    def save_extraction_log(self, output_path="citation_extraction_logs.json"):
        """Save the extraction logs to a file"""
        with open(output_path, "w") as f:
            json.dump(self.extraction_logs, f, indent=2)


# Test function
def test_citation_extractor(content, doc_type="law"):
    """Test the hybrid citation extractor on a text sample"""
    extractor = HybridCitationExtractor()
    
    # Extract with regex only
    regex_citations = extractor.extract_citations_regex(content, doc_type)
    
    # Extract with LLM only
    llm_citations = extractor.extract_citations_llm(content, doc_type)
    
    # Extract with hybrid approach
    hybrid_citations = extractor.extract_citations(content, doc_type)
    
    print("\n===== CITATION EXTRACTION RESULTS =====")
    print(f"Document type: {doc_type}")
    print(f"Regex citations found: {len(regex_citations)}")
    print(f"LLM citations found: {len(llm_citations)}")
    print(f"Hybrid citations found: {len(hybrid_citations)}")
    
    print("\nRegex Citations:")
    for i, citation in enumerate(regex_citations):
        print(f"{i+1}. {citation['text']} ({citation['type']})")
    
    print("\nLLM Citations:")
    for i, citation in enumerate(llm_citations):
        print(f"{i+1}. {citation['text']} ({citation['type']}) - Confidence: {citation.get('confidence', 'N/A')}")
    
    print("\nHybrid Citations:")
    for i, citation in enumerate(hybrid_citations):
        print(f"{i+1}. {citation['text']} ({citation['type']}) - Method: {citation['extraction_method']}")
    
    return {
        "regex_citations": regex_citations,
        "llm_citations": llm_citations,
        "hybrid_citations": hybrid_citations
    }


if __name__ == "__main__":
    # Example usage
    sample_text = """
    In Smith v. Jones, 123 F.3d 456 (5th Cir. 2020), the court held that pursuant to Section 123.45 
    of the Texas Civil Code, damages for personal injury claims are not capped in cases involving 
    gross negligence. This ruling overturned the previous precedent established in Johnson v. Medical Center,
    which had limited such damages under Chapter 41 of the Texas Civil Practice and Remedies Code.
    """
    
    test_citation_extractor(sample_text, "precedent_case")

"""
Document Classification System using Gemini API
Classifies legal documents by type and jurisdiction
"""

import os
import re
import json
import uuid
from datetime import datetime
import requests
from dotenv import load_dotenv
import fitz  # PyMuPDF

# Load environment variables
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
GEMINI_MODEL = "gemini-2.5-pro-preview-03-25"

class DocumentClassifier:
    """Classifies legal documents using Gemini model and rule-based methods"""
    
    def __init__(self):
        self.gemini_api_key = GEMINI_API_KEY
        self.model_name = GEMINI_MODEL
        self.supported_jurisdictions = [
            "Texas", "California", "New York", "Florida", "Ohio", 
            "Illinois", "Pennsylvania", "Michigan", "Federal"
        ]
        self.supported_doc_types = ["law", "precedent_case", "regulation", "administrative_ruling"]
        self.classification_logs = []
        
    def extract_text_from_pdf(self, pdf_path, max_pages=10):
        """Extract text from PDF file for classification"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for i in range(min(max_pages, len(doc))):
                text += doc[i].get_text()
            return text
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
            
    def rule_based_classification(self, text, filename):
        """Initial rule-based classification of document type"""
        # Document type classification
        doc_type = None
        if re.search(r'case.*no\.', text.lower()) or re.search(r'plaintiff|defendant|appellant|v\.|vs\.', text.lower()):
            doc_type = "precedent_case"
        elif re.search(r'statute|section|§|\bchapter\b|\btitle\b|\bact\b', text.lower()):
            doc_type = "law"
        elif re.search(r'regulation|rule|\bcode\b|department|commission', text.lower()):
            doc_type = "regulation"
        elif re.search(r'opinion|ruling|order|decision', text.lower()):
            doc_type = "administrative_ruling"
            
        # Jurisdiction classification
        jurisdiction = None
        for j in self.supported_jurisdictions:
            if j.lower() in text.lower() or j.lower() in filename.lower():
                jurisdiction = j
                break
                
        # Filename-based fallbacks
        if not doc_type:
            if any(x in filename.lower() for x in ['case', 'opinion', 'ruling', 'v.', 'vs']):
                doc_type = "precedent_case"
            elif any(x in filename.lower() for x in ['statute', 'code', 'section']):
                doc_type = "law"
                
        return {
            "doc_type": doc_type,
            "jurisdiction": jurisdiction
        }
        
    def call_gemini(self, prompt):
        """Call the Gemini API with the given prompt"""
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        params = {
            "key": self.gemini_api_key
        }
        
        data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.2,
                "topP": 0.8,
                "topK": 40
            }
        }
        
        response = requests.post(api_url, headers=headers, params=params, json=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error calling Gemini API: {response.status_code}")
            print(response.text)
            return None
            
    def parse_gemini_classification(self, response):
        """Parse the Gemini API response for classification"""
        try:
            if not response or "candidates" not in response:
                return {}
                
            text = response["candidates"][0]["content"]["parts"][0]["text"]
            
            # Extract the classification
            doc_type_match = re.search(r'Document Type:?\s*([a-zA-Z_]+)', text)
            jurisdiction_match = re.search(r'Jurisdiction:?\s*([a-zA-Z\s]+)', text)
            
            doc_type = doc_type_match.group(1).strip().lower() if doc_type_match else None
            jurisdiction = jurisdiction_match.group(1).strip() if jurisdiction_match else None
            
            # Standardize document types
            if doc_type:
                if doc_type in ["case", "court_case", "case_law", "precedent"]:
                    doc_type = "precedent_case"
                elif doc_type in ["statute", "statutory_law", "code"]:
                    doc_type = "law"
                    
            # Standardize jurisdictions
            if jurisdiction:
                for j in self.supported_jurisdictions:
                    if j.lower() in jurisdiction.lower():
                        jurisdiction = j
                        break
            
            return {
                "doc_type": doc_type,
                "jurisdiction": jurisdiction,
                "reasoning": text
            }
        except Exception as e:
            print(f"Error parsing Gemini response: {e}")
            return {}
            
    def classify_document(self, pdf_path):
        """Two-stage document classification with rule-based and LLM verification"""
        filename = os.path.basename(pdf_path)
        
        # Extract text for classification
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            return {"error": "Failed to extract text from PDF"}
            
        # Stage 1: Rule-based classification
        rule_classification = self.rule_based_classification(text, filename)
        
        # Stage 2: LLM verification with Gemini
        llm_prompt = f"""
        Analyze this legal document and classify it into one of these categories:
        - precedent_case: Court cases establishing legal precedent
        - law: Statutory law, codes, or legislative acts
        - regulation: Administrative regulations or rules
        - administrative_ruling: Agency decisions or interpretations
        
        Also determine the jurisdiction (e.g., Texas, California, New York, Federal).
        
        Document filename: {filename}
        
        Document excerpt:
        {text[:3000]}...
        
        Initial assessment:
        - Document Type: {rule_classification.get('doc_type', 'unknown')}
        - Jurisdiction: {rule_classification.get('jurisdiction', 'unknown')}
        
        Format your response as:
        Document Type: [type]
        Jurisdiction: [jurisdiction]
        Reasoning: [brief explanation of why you classified it this way]
        """
        
        gemini_response = self.call_gemini(llm_prompt)
        llm_classification = self.parse_gemini_classification(gemini_response)
        
        # Final classification (prefer LLM when available)
        final_classification = {
            "doc_type": llm_classification.get("doc_type") or rule_classification.get("doc_type"),
            "jurisdiction": llm_classification.get("jurisdiction") or rule_classification.get("jurisdiction"),
            "confidence": "high" if rule_classification.get("doc_type") == llm_classification.get("doc_type") else "medium",
            "rule_classification": rule_classification,
            "llm_classification": llm_classification,
            "filename": filename,
            "classification_id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat()
        }
        
        # Log the classification process
        self.classification_logs.append(final_classification)
        
        return final_classification
        
    def save_classification_log(self, output_path="classification_logs.json"):
        """Save the classification logs to a file"""
        with open(output_path, "w") as f:
            json.dump(self.classification_logs, f, indent=2)
            
    def extract_metadata_preview(self, pdf_path, classification):
        """Extract a preview of metadata based on document classification"""
        doc_type = classification.get("doc_type")
        text = self.extract_text_from_pdf(pdf_path)
        
        metadata = {
            "document_name": os.path.basename(pdf_path),
            "doc_type": doc_type,
            "jurisdiction": classification.get("jurisdiction"),
            "classification_confidence": classification.get("confidence")
        }
        
        # Extract type-specific metadata
        if doc_type == "precedent_case":
            case_number = re.search(r'[Cc]ase\s+[Nn]o\.?\s+([\w\d-]+)', text)
            court = re.search(r'([A-Z][A-Za-z\s]+Court\b)', text)
            date = re.search(r'\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b', text)
            
            metadata.update({
                "case_number": case_number.group(1) if case_number else None,
                "court": court.group(1) if court else None,
                "case_date": date.group(0) if date else None
            })
            
        elif doc_type == "law":
            title = re.search(r'TITLE\s+(\d+[A-Z\s\w]+)', text)
            chapter = re.search(r'CHAPTER\s+(\d+[A-Z\s\w\.]+)', text)
            section = re.search(r'(?:SECTION|Sec\.)\s+(\d+[\.\d]*[A-Z\s\w]*)', text)
            
            metadata.update({
                "statute_title": title.group(1).strip() if title else None,
                "statute_chapter": chapter.group(1).strip() if chapter else None,
                "statute_section": section.group(1).strip() if section else None
            })
            
        return metadata

# Test function
def test_classifier(pdf_path):
    """Test the document classifier on a single PDF"""
    classifier = DocumentClassifier()
    result = classifier.classify_document(pdf_path)
    metadata = classifier.extract_metadata_preview(pdf_path, result)
    
    print("\n===== DOCUMENT CLASSIFICATION RESULT =====")
    print(f"Filename: {os.path.basename(pdf_path)}")
    print(f"Document Type: {result.get('doc_type')}")
    print(f"Jurisdiction: {result.get('jurisdiction')}")
    print(f"Confidence: {result.get('confidence')}")
    print("\nClassification Process:")
    print(f"Rule-based: {result.get('rule_classification')}")
    
    if "reasoning" in result.get("llm_classification", {}):
        print(f"\nGemini reasoning: {result.get('llm_classification', {}).get('reasoning')}")
    
    print("\nMetadata Preview:")
    for key, value in metadata.items():
        print(f"{key}: {value}")
    
    return result, metadata

if __name__ == "__main__":
    # Example usage:
    # test_classifier("/path/to/legal_document.pdf")
    pass

"""
Enhanced Document Classifier
Classifies legal documents using the new document taxonomy and jurisdiction configuration.

Week 1 Enhancement: Implements comprehensive document type classification with
jurisdiction-specific patterns and enhanced metadata extraction.
"""

import os
import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

def load_jurisdiction_config():
    """Load jurisdiction configuration from enhanced config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'enhanced_jurisdiction_config.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load jurisdiction config: {e}. Using defaults.")
        return {"jurisdictions": {}, "global_settings": {"default_jurisdiction": "tx"}}

def load_document_taxonomy():
    """Load document type taxonomy from config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'document_taxonomy.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load document taxonomy: {e}. Using defaults.")
        return {"document_types": {}}

class EnhancedDocumentClassifier:
    """
    Enhanced document classifier using jurisdiction configuration and document taxonomy.
    
    Provides comprehensive classification with jurisdiction-specific patterns,
    document type taxonomy, and enhanced metadata extraction.
    """
    
    def __init__(self):
        """Initialize the enhanced document classifier."""
        self.jurisdiction_config = load_jurisdiction_config()
        self.document_taxonomy = load_document_taxonomy()
        
        # Extract supported jurisdictions and document types
        self.supported_jurisdictions = list(self.jurisdiction_config.get('jurisdictions', {}).keys())
        self.supported_doc_types = list(self.document_taxonomy.get('document_types', {}).keys())
        
        logger.info(f"Initialized enhanced document classifier")
        logger.info(f"Supported jurisdictions: {self.supported_jurisdictions}")
        logger.info(f"Supported document types: {self.supported_doc_types}")
    
    def classify_document(self, text: str, filename: str = "", metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Classify a document using enhanced patterns and taxonomy.
        
        Args:
            text: Document text content
            filename: Optional filename for additional context
            metadata: Optional existing metadata
            
        Returns:
            Classification result with jurisdiction, document type, subtype, and confidence
        """
        if metadata is None:
            metadata = {}
        
        # Stage 1: Rule-based classification
        rule_result = self._rule_based_classification(text, filename)
        
        # Stage 2: Pattern-based jurisdiction detection
        jurisdiction_result = self._detect_jurisdiction(text, filename)
        
        # Stage 3: Document type classification with subtypes
        doc_type_result = self._classify_document_type(text, filename, jurisdiction_result.get('jurisdiction'))
        
        # Stage 4: Extract hierarchy and metadata
        hierarchy_result = self._extract_hierarchy(text, doc_type_result.get('doc_type'), jurisdiction_result.get('jurisdiction'))
        
        # Stage 5: Practice area classification
        practice_areas = self._classify_practice_areas(text)
        
        # Combine results
        classification = {
            'jurisdiction': jurisdiction_result.get('jurisdiction') or rule_result.get('jurisdiction'),
            'jurisdiction_confidence': jurisdiction_result.get('confidence', 0.5),
            'doc_type': doc_type_result.get('doc_type') or rule_result.get('doc_type'),
            'doc_subtype': doc_type_result.get('doc_subtype'),
            'doc_type_confidence': doc_type_result.get('confidence', 0.5),
            'hierarchy_path': hierarchy_result.get('hierarchy_path'),
            'hierarchy_level': hierarchy_result.get('level'),
            'practice_areas': practice_areas,
            'metadata': {
                'filename': filename,
                'classification_timestamp': datetime.now().isoformat(),
                'classifier_version': '2.0_week1',
                **hierarchy_result.get('extracted_metadata', {}),
                **metadata
            }
        }
        
        # Validate classification
        classification['is_valid'] = self._validate_classification(classification)
        
        return classification
    
    def _rule_based_classification(self, text: str, filename: str) -> Dict[str, Any]:
        """Apply rule-based classification patterns."""
        text_lower = text.lower()
        filename_lower = filename.lower()
        
        # Basic document type detection
        doc_type = None
        jurisdiction = None
        
        # Document type patterns
        if any(pattern in text_lower for pattern in ['case', 'opinion', 'ruling', 'v.', 'vs', 'plaintiff', 'defendant']):
            doc_type = 'case'
        elif any(pattern in text_lower for pattern in ['statute', 'code', 'section', '§', 'chapter']):
            doc_type = 'statute'
        elif any(pattern in text_lower for pattern in ['regulation', 'rule', 'cfr', 'administrative']):
            doc_type = 'regulation'
        elif any(pattern in text_lower for pattern in ['constitution', 'amendment', 'article']):
            doc_type = 'constitution'
        
        # Jurisdiction detection
        for jurisdiction_code, jurisdiction_data in self.jurisdiction_config.get('jurisdictions', {}).items():
            jurisdiction_name = jurisdiction_data.get('name', '').lower()
            if jurisdiction_name in text_lower or jurisdiction_name in filename_lower:
                jurisdiction = jurisdiction_code
                break
        
        return {
            'doc_type': doc_type,
            'jurisdiction': jurisdiction
        }
    
    def _detect_jurisdiction(self, text: str, filename: str) -> Dict[str, Any]:
        """Detect jurisdiction using enhanced patterns."""
        text_lower = text.lower()
        filename_lower = filename.lower()
        
        jurisdiction_scores = {}
        
        for jurisdiction_code, jurisdiction_data in self.jurisdiction_config.get('jurisdictions', {}).items():
            score = 0
            
            # Check jurisdiction name
            jurisdiction_name = jurisdiction_data.get('name', '').lower()
            if jurisdiction_name in text_lower:
                score += 3
            if jurisdiction_name in filename_lower:
                score += 2
            
            # Check citation patterns
            citation_formats = jurisdiction_data.get('citation_formats', {})
            for doc_type, patterns in citation_formats.items():
                if isinstance(patterns, dict):
                    for court, court_patterns in patterns.items():
                        for pattern in court_patterns:
                            if re.search(pattern, text, re.IGNORECASE):
                                score += 2
                elif isinstance(patterns, list):
                    for pattern in patterns:
                        if re.search(pattern, text, re.IGNORECASE):
                            score += 2
            
            if score > 0:
                jurisdiction_scores[jurisdiction_code] = score
        
        if jurisdiction_scores:
            best_jurisdiction = max(jurisdiction_scores, key=jurisdiction_scores.get)
            confidence = min(jurisdiction_scores[best_jurisdiction] / 10.0, 1.0)
            return {
                'jurisdiction': best_jurisdiction,
                'confidence': confidence,
                'scores': jurisdiction_scores
            }
        
        # Default to configured default jurisdiction
        default_jurisdiction = self.jurisdiction_config.get('global_settings', {}).get('default_jurisdiction', 'tx')
        return {
            'jurisdiction': default_jurisdiction,
            'confidence': 0.1,
            'scores': {}
        }
    
    def _classify_document_type(self, text: str, filename: str, jurisdiction: Optional[str] = None) -> Dict[str, Any]:
        """Classify document type and subtype using taxonomy."""
        text_lower = text.lower()
        
        doc_type_scores = {}
        
        for doc_type, doc_info in self.document_taxonomy.get('document_types', {}).items():
            score = 0
            best_subtype = None
            subtype_score = 0
            
            # Check subtypes
            subtypes = doc_info.get('subtypes', {})
            for subtype_code, subtype_info in subtypes.items():
                subtype_patterns = subtype_info.get('patterns', [])
                for pattern in subtype_patterns:
                    if pattern.lower() in text_lower:
                        score += 2
                        if score > subtype_score:
                            best_subtype = subtype_code
                            subtype_score = score
            
            # Check jurisdiction-specific citation patterns
            if jurisdiction:
                citation_patterns = doc_info.get('citation_patterns', {})
                jurisdiction_patterns = citation_patterns.get(jurisdiction, [])
                for pattern in jurisdiction_patterns:
                    if re.search(pattern, text, re.IGNORECASE):
                        score += 3
            
            if score > 0:
                doc_type_scores[doc_type] = {
                    'score': score,
                    'subtype': best_subtype
                }
        
        if doc_type_scores:
            best_doc_type = max(doc_type_scores, key=lambda x: doc_type_scores[x]['score'])
            best_result = doc_type_scores[best_doc_type]
            confidence = min(best_result['score'] / 10.0, 1.0)
            
            return {
                'doc_type': best_doc_type,
                'doc_subtype': best_result['subtype'],
                'confidence': confidence,
                'scores': doc_type_scores
            }
        
        return {
            'doc_type': 'statute',  # Default
            'doc_subtype': None,
            'confidence': 0.1,
            'scores': {}
        }
    
    def _extract_hierarchy(self, text: str, doc_type: Optional[str], jurisdiction: Optional[str]) -> Dict[str, Any]:
        """Extract hierarchical information from document."""
        if not doc_type:
            return {}
        
        doc_info = self.document_taxonomy.get('document_types', {}).get(doc_type, {})
        hierarchy_levels = doc_info.get('hierarchy_levels', [])
        
        extracted_metadata = {}
        hierarchy_path = []
        
        # Extract based on document type
        if doc_type == 'statute':
            # Extract title, chapter, section
            title_match = re.search(r'title\s+(\d+)', text, re.IGNORECASE)
            if title_match:
                extracted_metadata['title'] = title_match.group(1)
                hierarchy_path.append(f"title_{title_match.group(1)}")
            
            chapter_match = re.search(r'chapter\s+(\d+)', text, re.IGNORECASE)
            if chapter_match:
                extracted_metadata['chapter'] = chapter_match.group(1)
                hierarchy_path.append(f"chapter_{chapter_match.group(1)}")
            
            section_match = re.search(r'(?:section|§)\s*(\d+(?:\.\d+)*)', text, re.IGNORECASE)
            if section_match:
                extracted_metadata['section'] = section_match.group(1)
                hierarchy_path.append(f"section_{section_match.group(1)}")
        
        elif doc_type == 'case':
            # Extract court, case number, parties
            court_match = re.search(r'(supreme court|court of appeals|district court)', text, re.IGNORECASE)
            if court_match:
                extracted_metadata['court'] = court_match.group(1)
                hierarchy_path.append(f"court_{court_match.group(1).replace(' ', '_')}")
            
            case_num_match = re.search(r'(?:case|no\.?)\s*(\d+[-\d]*)', text, re.IGNORECASE)
            if case_num_match:
                extracted_metadata['case_number'] = case_num_match.group(1)
        
        return {
            'hierarchy_path': '/'.join(hierarchy_path) if hierarchy_path else None,
            'level': len(hierarchy_path),
            'extracted_metadata': extracted_metadata
        }
    
    def _classify_practice_areas(self, text: str) -> List[str]:
        """Classify practice areas based on content."""
        text_lower = text.lower()
        practice_areas = []
        
        practice_area_config = self.document_taxonomy.get('practice_areas', {})
        
        for area_code, area_info in practice_area_config.items():
            keywords = area_info.get('keywords', [])
            matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
            
            # If more than 20% of keywords match, include this practice area
            if matches > 0 and matches / len(keywords) > 0.2:
                practice_areas.append(area_code)
        
        return practice_areas
    
    def _validate_classification(self, classification: Dict[str, Any]) -> bool:
        """Validate the classification result."""
        jurisdiction = classification.get('jurisdiction')
        doc_type = classification.get('doc_type')
        
        # Check if jurisdiction is supported
        if jurisdiction not in self.supported_jurisdictions:
            return False
        
        # Check if document type is supported
        if doc_type not in self.supported_doc_types:
            return False
        
        # Check if jurisdiction supports this document type
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        supported_types = jurisdiction_data.get('document_types', [])
        
        return doc_type in supported_types
    
    def get_classification_summary(self, classification: Dict[str, Any]) -> str:
        """Generate a human-readable summary of the classification."""
        jurisdiction = classification.get('jurisdiction', 'unknown')
        doc_type = classification.get('doc_type', 'unknown')
        doc_subtype = classification.get('doc_subtype')
        
        # Get human-readable names
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        jurisdiction_name = jurisdiction_data.get('name', jurisdiction)
        
        doc_type_data = self.document_taxonomy.get('document_types', {}).get(doc_type, {})
        doc_type_name = doc_type_data.get('name', doc_type)
        
        summary = f"{jurisdiction_name} {doc_type_name}"
        
        if doc_subtype:
            subtype_data = doc_type_data.get('subtypes', {}).get(doc_subtype, {})
            subtype_name = subtype_data.get('name', doc_subtype)
            summary += f" ({subtype_name})"
        
        return summary

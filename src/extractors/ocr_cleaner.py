#!/usr/bin/env python3
"""
OCR Cleaner Module for Legal Document Processing
Provides utilities to clean and correct OCR errors in legal documents
with a focus on improving citation extraction quality
"""

import re
import string
from difflib import SequenceMatcher

# Common OCR substitution errors in legal documents
CHAR_SUBSTITUTIONS = {
    # Special character replacements
    '�': 'e',
    '�': 't',
    '�': 'i',
    '�': 'c',
    '�': 'a',
    '�': 'o',
    '�': 'n',
    '�': 's',
    '�': 'r',
    '�': 'l',
    '�': '-',
    '�': ' ',
    '�': '.',
    '�': '\'',
    '�': '"',
    
    # Common legal text OCR errors
    'Znl': 'Sec',     # Section
    'Zn{': 'Sep',     # September
    'Rnq': 'Leg',     # Legislature
    'noo': 'eff',     # effective
    'lr': 'ch',       # chapter
    'Y/Z/': 'R.S.',   # Regular Session
    'Gr': 'Ch',       # Chapter
}

# Pattern-based replacements for legal citations
LEGAL_PATTERNS = [
    # Section references
    (r'Znl\.?\s*(\d+[A-Za-z]?\.?\d*)', r'Sec. \1'),
    (r'Znl�szy\s*(\d+\.\d+)', r'Section \1'),
    
    # Chapter references
    (r'lr\.\s*(\d+)', r'ch. \1'),
    (r'Gr\.\s*(\d+)', r'Ch. \1'),
    
    # Legislature references
    (r'(\d+)[�\-]?r Rnq\.', r'\1th Leg.'),
    (r'El�~\s*(\d{4})', r'Acts \1'),
    
    # Date formatting
    (r'noo\.\s*([A-Za-z]+)\.?\s*(\d+)', r'eff. \1 \2'),
    (r'Zn{�nwkn}\s*(\d+)', r'September \1'),
]

# Legal citation validation patterns
CITATION_FORMATS = [
    r'Sec\.\s*\d+(\.\d+)?',                      # Section format
    r'Section\s*\d+(\.\d+)?',                    # Section format spelled out
    r'Chapter\s*\d+',                            # Chapter format
    r'Ch\.\s*\d+',                               # Chapter abbreviation
    r'Acts\s*\d{4}',                             # Acts format
    r'\d{1,2}(st|nd|rd|th)\s*Leg',               # Legislature format
    r'[A-Z][a-z]+\s*Code',                       # Code reference
    r'V\.\s*[A-Z][a-z]+',                        # Case citation (v. Name)
    r'et\s*seq',                                 # "and following" citations
    r'et\s*al',                                  # "and others" citations
]

def similar(a, b):
    """Calculate string similarity ratio between 0 and 1"""
    return SequenceMatcher(None, a, b).ratio()

def clean_characters(text):
    """Replace commonly misrecognized characters and character sequences"""
    if not text:
        return text
        
    # Apply character-level substitutions
    for wrong, right in CHAR_SUBSTITUTIONS.items():
        text = text.replace(wrong, right)
    
    return text

def apply_legal_patterns(text):
    """Apply pattern-based corrections for legal citations"""
    if not text:
        return text
        
    # Apply each pattern replacement
    for pattern, replacement in LEGAL_PATTERNS:
        text = re.sub(pattern, replacement, text)
    
    return text

def format_citation(citation_text):
    """Clean up formatting issues in citation text"""
    if not citation_text:
        return citation_text
        
    # Remove excessive spaces
    citation_text = re.sub(r'\s+', ' ', citation_text).strip()
    
    # Fix common punctuation issues
    citation_text = re.sub(r'(\d)\.(\d)', r'\1.\2', citation_text)  # Ensure decimal points have spaces
    citation_text = re.sub(r'([a-zA-Z])\.([a-zA-Z])', r'\1. \2', citation_text)  # Add space after period
    
    # Normalize common abbreviations
    citation_text = re.sub(r'sect\.', 'Sec.', citation_text, flags=re.IGNORECASE)
    citation_text = re.sub(r'chap\.', 'Ch.', citation_text, flags=re.IGNORECASE)
    
    return citation_text

def validate_citation(text):
    """
    Validate whether text matches known legal citation formats
    Returns: (is_valid: bool, confidence: str, cleaned_text: str)
    """
    if not text:
        return False, 'low', text
    
    # First clean the text
    cleaned_text = clean_characters(text)
    cleaned_text = apply_legal_patterns(cleaned_text)
    cleaned_text = format_citation(cleaned_text)
    
    # Check if citation matches any valid format
    for pattern in CITATION_FORMATS:
        if re.search(pattern, cleaned_text, re.IGNORECASE):
            # Determine confidence based on how much cleaning was needed
            similarity = similar(text, cleaned_text)
            
            if similarity > 0.9:
                confidence = 'high'
            elif similarity > 0.7:
                confidence = 'medium'
            else:
                confidence = 'low'
                
            return True, confidence, cleaned_text
    
    # If no valid format, mark as low confidence
    return False, 'low', cleaned_text

def context_correction(document_text, citation_text, window_size=100):
    """
    Use surrounding context to improve citation text
    Returns: (improved_citation: str, context: str)
    """
    if not document_text or not citation_text:
        return citation_text, ""
    
    # Clean citation text first
    cleaned_citation = clean_characters(citation_text)
    cleaned_citation = apply_legal_patterns(cleaned_citation)
    
    # If already a valid citation, just return it with context
    is_valid, _, _ = validate_citation(cleaned_citation)
    if is_valid:
        # Find the citation in the document
        citation_pos = document_text.find(cleaned_citation)
        if citation_pos >= 0:
            start = max(0, citation_pos - window_size//2)
            end = min(len(document_text), citation_pos + len(cleaned_citation) + window_size//2)
            context = document_text[start:end]
            return cleaned_citation, context
    
    # For invalid citations, try to find similar text in the document
    citation_words = cleaned_citation.split()
    
    # Try with different word sequences
    for i in range(len(citation_words)):
        for j in range(i+1, min(i+4, len(citation_words)+1)):
            search_term = ' '.join(citation_words[i:j])
            if len(search_term) < 4:  # Skip very short terms
                continue
                
            citation_pos = document_text.find(search_term)
            if citation_pos >= 0:
                # Extract a larger context
                start = max(0, citation_pos - window_size//2)
                end = min(len(document_text), citation_pos + len(search_term) + window_size//2)
                context = document_text[start:end]
                
                # Look for citation patterns in the context
                for pattern in CITATION_FORMATS:
                    match = re.search(pattern, context, re.IGNORECASE)
                    if match:
                        return match.group(0), context
    
    # Fallback: return the best cleaned version we have
    return cleaned_citation, ""

def clean_document_text(document_text):
    """Clean an entire document's text for OCR errors"""
    if not document_text:
        return document_text
        
    # Apply basic character-level cleaning
    cleaned_text = clean_characters(document_text)
    
    # Apply line joining for hyphenated words at end of lines
    cleaned_text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', cleaned_text)
    
    # Remove excessive whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    
    return cleaned_text

def process_citation(citation, document_text=None):
    """
    Process a citation object to improve its quality
    Returns: updated citation object
    """
    if not citation:
        return citation
        
    citation_text = citation.get('text', '')
    if not citation_text:
        return citation
    
    # 1. Clean and validate the citation
    is_valid, confidence, cleaned_text = validate_citation(citation_text)
    
    # 2. If document text is available and citation still not valid, try context-based correction
    context = ""
    if document_text and (not is_valid or confidence == 'low'):
        improved_text, context = context_correction(document_text, citation_text)
        # Check if the improved text is better
        is_improved_valid, improved_confidence, _ = validate_citation(improved_text)
        if is_improved_valid and (not is_valid or improved_confidence > confidence):
            cleaned_text = improved_text
            is_valid = is_improved_valid
            confidence = improved_confidence
    
    # 3. Update the citation object
    updated_citation = citation.copy()
    updated_citation['text'] = cleaned_text
    
    # Only override confidence if ours is higher
    if 'confidence' not in updated_citation or confidence_level(confidence) > confidence_level(updated_citation.get('confidence', 'low')):
        updated_citation['confidence'] = confidence
    
    # Add context if available
    if context and context.strip():
        updated_citation['context'] = context.strip()
    
    return updated_citation

def confidence_level(confidence):
    """Convert confidence string to numeric level for comparison"""
    levels = {'high': 3, 'medium': 2, 'low': 1, 'unknown': 0}
    return levels.get(confidence.lower(), 0)

def process_citations(citations, document_text=None):
    """Process a list of citation objects to improve their quality"""
    if not citations:
        return citations
        
    return [process_citation(citation, document_text) for citation in citations]

# Convenience function for full pipeline
def clean_and_process(document_text, citations):
    """
    Full pipeline for document and citation cleaning
    Returns: (cleaned_document_text, processed_citations)
    """
    cleaned_doc = clean_document_text(document_text)
    processed_citations = process_citations(citations, cleaned_doc)
    return cleaned_doc, processed_citations

if __name__ == "__main__":
    # Simple test cases
    test_cases = [
        "Znl/ 372G/223/",
        "Znl�szy 323/223/",
        "El�~ 4243- ;:�r Rnq/- Y/Z/- Gr/ 97; )M/F/ 36<5*- Znl/ 3- noo/ Zn{�nwkn} 3- 4243/",
        "Section 11.20, Tax Code",
        "Acts 1987, 70th Leg., ch. 589, Sec. 5, eff. Aug. 31, 1987"
    ]
    
    print("OCR Citation Cleaner - Test Cases")
    print("---------------------------------")
    
    for test in test_cases:
        is_valid, confidence, cleaned = validate_citation(test)
        print(f"Original: {test}")
        print(f"Cleaned:  {cleaned}")
        print(f"Valid:    {is_valid}")
        print(f"Confidence: {confidence}")
        print("---------------------------------")

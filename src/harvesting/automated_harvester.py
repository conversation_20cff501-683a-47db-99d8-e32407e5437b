"""
Automated Case Harvesting System

This module provides the main scheduler and orchestrator for automated
case law harvesting across multiple jurisdictions.
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field

try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
    from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
    SCHEDULER_AVAILABLE = True
except ImportError:
    SCHEDULER_AVAILABLE = False
    logging.warning("APScheduler not available. Install with: pip install apscheduler")

from src.harvesting.harvesting_config import get_harvesting_config, HarvestingConfig
from src.harvesting.jurisdiction_harvester import JurisdictionHarvester, HarvestResult

logger = logging.getLogger(__name__)


@dataclass
class HarvestingStats:
    """Overall harvesting statistics"""
    total_jurisdictions: int = 0
    active_jurisdictions: int = 0
    total_harvests: int = 0
    successful_harvests: int = 0
    failed_harvests: int = 0
    total_cases_processed: int = 0
    total_cases_successful: int = 0
    last_harvest_time: Optional[datetime] = None
    errors: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """Calculate overall success rate"""
        if self.total_harvests == 0:
            return 0.0
        return self.successful_harvests / self.total_harvests


class AutomatedHarvester:
    """Main automated harvesting system"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = get_harvesting_config()
        self.scheduler = None
        self.is_running = False
        self.stats = HarvestingStats()
        self._lock = threading.Lock()
        
        # Initialize scheduler if available
        if SCHEDULER_AVAILABLE and self.config.enabled:
            self.scheduler = BackgroundScheduler()
            self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        
        logger.info(f"Initialized AutomatedHarvester with {len(self.config.jurisdictions)} jurisdictions")
    
    def start(self):
        """Start the automated harvesting system"""
        if not self.config.enabled:
            logger.warning("Harvesting is disabled in configuration")
            return
        
        if not SCHEDULER_AVAILABLE:
            logger.error("Cannot start harvester: APScheduler not available")
            return
        
        if self.is_running:
            logger.warning("Harvester is already running")
            return
        
        try:
            logger.info("Starting automated harvesting system")
            
            # Schedule jobs for each enabled jurisdiction
            self._schedule_jurisdiction_jobs()
            
            # Start the scheduler
            self.scheduler.start()
            self.is_running = True
            
            # Update stats
            with self._lock:
                self.stats.total_jurisdictions = len(self.config.jurisdictions)
                self.stats.active_jurisdictions = len(self.config.get_enabled_jurisdictions())
            
            logger.info(f"Harvester started with {self.stats.active_jurisdictions} active jurisdictions")
            
        except Exception as e:
            logger.error(f"Failed to start harvester: {str(e)}")
            raise
    
    def stop(self):
        """Stop the automated harvesting system"""
        if not self.is_running:
            logger.warning("Harvester is not running")
            return
        
        try:
            logger.info("Stopping automated harvesting system")
            
            if self.scheduler:
                self.scheduler.shutdown(wait=True)
            
            self.is_running = False
            logger.info("Harvester stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping harvester: {str(e)}")
    
    def harvest_jurisdiction_now(self, jurisdiction: str, practice_area: Optional[str] = None) -> HarvestResult:
        """Manually trigger harvest for a specific jurisdiction"""
        logger.info(f"Manual harvest triggered for jurisdiction: {jurisdiction}")
        
        try:
            harvester = JurisdictionHarvester(jurisdiction)
            result = harvester.harvest_cases(practice_area=practice_area)
            
            # Update stats
            with self._lock:
                self._update_stats_from_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Manual harvest failed for {jurisdiction}: {str(e)}")
            raise
    
    def harvest_all_now(self, max_concurrent: Optional[int] = None) -> Dict[str, HarvestResult]:
        """Manually trigger harvest for all enabled jurisdictions"""
        enabled_jurisdictions = self.config.get_enabled_jurisdictions()
        
        if not enabled_jurisdictions:
            logger.warning("No enabled jurisdictions found")
            return {}
        
        max_workers = max_concurrent or self.config.max_concurrent_jobs
        results = {}
        
        logger.info(f"Starting manual harvest for {len(enabled_jurisdictions)} jurisdictions "
                   f"with {max_workers} concurrent workers")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit harvest jobs
            future_to_jurisdiction = {
                executor.submit(self.harvest_jurisdiction_now, jurisdiction): jurisdiction
                for jurisdiction in enabled_jurisdictions
            }
            
            # Collect results
            for future in as_completed(future_to_jurisdiction):
                jurisdiction = future_to_jurisdiction[future]
                try:
                    result = future.result()
                    results[jurisdiction] = result
                    logger.info(f"Completed harvest for {jurisdiction}: "
                               f"{result.total_success}/{result.total_processed} successful")
                except Exception as e:
                    logger.error(f"Harvest failed for {jurisdiction}: {str(e)}")
                    # Create a failed result
                    results[jurisdiction] = HarvestResult(
                        jurisdiction=jurisdiction,
                        start_time=datetime.now(),
                        end_time=datetime.now(),
                        errors=[str(e)]
                    )
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """Get current harvester status"""
        with self._lock:
            status = {
                "is_running": self.is_running,
                "scheduler_available": SCHEDULER_AVAILABLE,
                "config_enabled": self.config.enabled,
                "stats": {
                    "total_jurisdictions": self.stats.total_jurisdictions,
                    "active_jurisdictions": self.stats.active_jurisdictions,
                    "total_harvests": self.stats.total_harvests,
                    "successful_harvests": self.stats.successful_harvests,
                    "failed_harvests": self.stats.failed_harvests,
                    "success_rate": self.stats.success_rate,
                    "total_cases_processed": self.stats.total_cases_processed,
                    "total_cases_successful": self.stats.total_cases_successful,
                    "last_harvest_time": self.stats.last_harvest_time.isoformat() if self.stats.last_harvest_time else None
                },
                "jurisdictions": {}
            }
        
        # Add jurisdiction-specific status
        for jurisdiction in self.config.get_enabled_jurisdictions():
            try:
                harvester = JurisdictionHarvester(jurisdiction)
                status["jurisdictions"][jurisdiction] = harvester.get_harvest_status()
            except Exception as e:
                status["jurisdictions"][jurisdiction] = {"error": str(e)}
        
        return status
    
    def _schedule_jurisdiction_jobs(self):
        """Schedule harvesting jobs for all enabled jurisdictions"""
        for jurisdiction in self.config.get_enabled_jurisdictions():
            config = self.config.get_jurisdiction_config(jurisdiction)
            if not config:
                continue
            
            try:
                # Parse cron schedule
                trigger = CronTrigger.from_crontab(config.schedule)
                
                # Add job to scheduler
                self.scheduler.add_job(
                    func=self._scheduled_harvest,
                    trigger=trigger,
                    args=[jurisdiction],
                    id=f"harvest_{jurisdiction}",
                    name=f"Harvest {config.name}",
                    max_instances=1,  # Prevent overlapping runs
                    coalesce=True     # Combine missed runs
                )
                
                logger.info(f"Scheduled harvest for {jurisdiction} ({config.name}) "
                           f"with schedule: {config.schedule}")
                
            except Exception as e:
                logger.error(f"Failed to schedule harvest for {jurisdiction}: {str(e)}")
    
    def _scheduled_harvest(self, jurisdiction: str):
        """Execute a scheduled harvest for a jurisdiction"""
        logger.info(f"Executing scheduled harvest for jurisdiction: {jurisdiction}")
        
        try:
            result = self.harvest_jurisdiction_now(jurisdiction)
            
            # Log results
            logger.info(f"Scheduled harvest completed for {jurisdiction}: "
                       f"{result.total_success}/{result.total_processed} successful, "
                       f"duration: {result.duration_seconds:.1f}s")
            
            # Check for high failure rate
            if result.success_rate < (1.0 - self.config.monitoring.get("alert_on_failure_rate", 0.3)):
                logger.warning(f"High failure rate for {jurisdiction}: {result.success_rate:.2%}")
            
        except Exception as e:
            logger.error(f"Scheduled harvest failed for {jurisdiction}: {str(e)}")
    
    def _job_listener(self, event):
        """Listen to scheduler job events"""
        if event.exception:
            logger.error(f"Scheduled job failed: {event.job_id} - {event.exception}")
        else:
            logger.debug(f"Scheduled job completed: {event.job_id}")
    
    def _update_stats_from_result(self, result: HarvestResult):
        """Update overall stats from a harvest result"""
        self.stats.total_harvests += 1
        self.stats.last_harvest_time = datetime.now()
        
        if result.errors:
            self.stats.failed_harvests += 1
            self.stats.errors.extend(result.errors[-5:])  # Keep last 5 errors
        else:
            self.stats.successful_harvests += 1
        
        self.stats.total_cases_processed += result.total_processed
        self.stats.total_cases_successful += result.total_success
        
        # Keep only recent errors (last 50)
        if len(self.stats.errors) > 50:
            self.stats.errors = self.stats.errors[-50:]


# Global harvester instance
_harvester_instance: Optional[AutomatedHarvester] = None


def get_harvester() -> AutomatedHarvester:
    """Get the global harvester instance"""
    global _harvester_instance
    if _harvester_instance is None:
        _harvester_instance = AutomatedHarvester()
    return _harvester_instance


def start_harvesting():
    """Start the global harvesting system"""
    harvester = get_harvester()
    harvester.start()


def stop_harvesting():
    """Stop the global harvesting system"""
    harvester = get_harvester()
    harvester.stop()

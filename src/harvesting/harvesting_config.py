"""
Harvesting Configuration Management

This module handles loading and validation of harvesting configuration
for automated case law data acquisition.
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class JurisdictionConfig:
    """Configuration for a specific jurisdiction's harvesting"""
    name: str
    enabled: bool
    priority: str
    schedule: str
    courts: List[str]
    practice_areas: List[str]
    search_queries: Dict[str, List[str]]
    date_range: Dict[str, Any]
    max_cases_per_run: int
    
    def get_search_terms(self, practice_area: Optional[str] = None) -> List[str]:
        """Get search terms for a specific practice area or all"""
        if practice_area and practice_area in self.search_queries:
            return self.search_queries[practice_area]
        
        # Return all search terms if no specific practice area
        all_terms = []
        for terms in self.search_queries.values():
            all_terms.extend(terms)
        return list(set(all_terms))  # Remove duplicates


@dataclass
class HarvestingConfig:
    """Main harvesting configuration"""
    enabled: bool
    default_schedule: str
    max_concurrent_jobs: int
    retry_attempts: int
    retry_delay_seconds: int
    batch_size: int
    rate_limit_delay: float
    jurisdictions: Dict[str, JurisdictionConfig] = field(default_factory=dict)
    quality_thresholds: Dict[str, Any] = field(default_factory=dict)
    storage: Dict[str, Any] = field(default_factory=dict)
    monitoring: Dict[str, Any] = field(default_factory=dict)
    
    def get_enabled_jurisdictions(self) -> List[str]:
        """Get list of enabled jurisdiction codes"""
        return [code for code, config in self.jurisdictions.items() if config.enabled]
    
    def get_jurisdiction_config(self, jurisdiction: str) -> Optional[JurisdictionConfig]:
        """Get configuration for a specific jurisdiction"""
        return self.jurisdictions.get(jurisdiction)
    
    def get_next_harvest_date(self, jurisdiction: str, last_harvest: Optional[datetime] = None) -> datetime:
        """Calculate the next harvest date for a jurisdiction"""
        config = self.get_jurisdiction_config(jurisdiction)
        if not config:
            return datetime.now()
        
        if last_harvest is None:
            # First harvest - start from configured start date
            start_date_str = config.date_range.get("start_date", "2020-01-01")
            return datetime.strptime(start_date_str, "%Y-%m-%d")
        
        # Incremental harvest - add configured days
        incremental_days = config.date_range.get("incremental_days", 30)
        return last_harvest + timedelta(days=incremental_days)


class HarvestingConfigManager:
    """Manages loading and validation of harvesting configuration"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/harvesting_config.json"
        self._config: Optional[HarvestingConfig] = None
        self._last_loaded: Optional[datetime] = None
    
    def load_config(self, force_reload: bool = False) -> HarvestingConfig:
        """Load configuration from file with caching"""
        config_file = Path(self.config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"Harvesting config file not found: {self.config_path}")
        
        # Check if we need to reload
        if not force_reload and self._config and self._last_loaded:
            file_mtime = datetime.fromtimestamp(config_file.stat().st_mtime)
            if file_mtime <= self._last_loaded:
                return self._config
        
        logger.info(f"Loading harvesting configuration from {self.config_path}")
        
        try:
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            # Validate and parse configuration
            self._config = self._parse_config(config_data)
            self._last_loaded = datetime.now()
            
            logger.info(f"Successfully loaded configuration for {len(self._config.jurisdictions)} jurisdictions")
            return self._config
            
        except Exception as e:
            logger.error(f"Failed to load harvesting configuration: {str(e)}")
            raise
    
    def _parse_config(self, config_data: Dict) -> HarvestingConfig:
        """Parse raw configuration data into structured objects"""
        harvesting_config = config_data.get("harvesting", {})
        
        # Parse jurisdiction configurations
        jurisdictions = {}
        for code, jurisdiction_data in config_data.get("jurisdictions", {}).items():
            jurisdictions[code] = JurisdictionConfig(
                name=jurisdiction_data.get("name", code.upper()),
                enabled=jurisdiction_data.get("enabled", False),
                priority=jurisdiction_data.get("priority", "medium"),
                schedule=jurisdiction_data.get("schedule", harvesting_config.get("default_schedule", "0 2 * * *")),
                courts=jurisdiction_data.get("courts", []),
                practice_areas=jurisdiction_data.get("practice_areas", []),
                search_queries=jurisdiction_data.get("search_queries", {}),
                date_range=jurisdiction_data.get("date_range", {}),
                max_cases_per_run=jurisdiction_data.get("max_cases_per_run", 50)
            )
        
        return HarvestingConfig(
            enabled=harvesting_config.get("enabled", True),
            default_schedule=harvesting_config.get("default_schedule", "0 2 * * *"),
            max_concurrent_jobs=harvesting_config.get("max_concurrent_jobs", 3),
            retry_attempts=harvesting_config.get("retry_attempts", 3),
            retry_delay_seconds=harvesting_config.get("retry_delay_seconds", 300),
            batch_size=harvesting_config.get("batch_size", 50),
            rate_limit_delay=harvesting_config.get("rate_limit_delay", 0.5),
            jurisdictions=jurisdictions,
            quality_thresholds=config_data.get("quality_thresholds", {}),
            storage=config_data.get("storage", {}),
            monitoring=config_data.get("monitoring", {})
        )
    
    def validate_config(self, config: HarvestingConfig) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        if not config.enabled:
            issues.append("Harvesting is disabled in configuration")
        
        if config.max_concurrent_jobs < 1:
            issues.append("max_concurrent_jobs must be at least 1")
        
        if config.batch_size < 1:
            issues.append("batch_size must be at least 1")
        
        # Validate jurisdictions
        enabled_jurisdictions = config.get_enabled_jurisdictions()
        if not enabled_jurisdictions:
            issues.append("No jurisdictions are enabled for harvesting")
        
        for code, jurisdiction in config.jurisdictions.items():
            if jurisdiction.enabled:
                if not jurisdiction.courts:
                    issues.append(f"Jurisdiction {code} has no courts configured")
                
                if not jurisdiction.practice_areas:
                    issues.append(f"Jurisdiction {code} has no practice areas configured")
                
                if jurisdiction.max_cases_per_run < 1:
                    issues.append(f"Jurisdiction {code} max_cases_per_run must be at least 1")
        
        return issues
    
    def get_config(self) -> HarvestingConfig:
        """Get current configuration, loading if necessary"""
        if self._config is None:
            return self.load_config()
        return self._config


# Global configuration manager instance
config_manager = HarvestingConfigManager()


def get_harvesting_config() -> HarvestingConfig:
    """Get the current harvesting configuration"""
    return config_manager.get_config()


def reload_harvesting_config() -> HarvestingConfig:
    """Force reload of harvesting configuration"""
    return config_manager.load_config(force_reload=True)

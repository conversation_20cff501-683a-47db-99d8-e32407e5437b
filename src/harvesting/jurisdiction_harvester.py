"""
Jurisdiction-Specific Case Harvester

This module handles harvesting cases for specific jurisdictions
with incremental updates and quality filtering.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from src.api.courtlistener.client import Court<PERSON><PERSON><PERSON>Client
from src.api.courtlistener.exceptions import CourtListenerAPIError
from src.processing.case_law_processor import CaseLawProcessor
from src.harvesting.harvesting_config import JurisdictionConfig, get_harvesting_config
from src.processing.storage.supabase_connector import SupabaseConnector

logger = logging.getLogger(__name__)


@dataclass
class HarvestResult:
    """Result of a harvesting operation"""
    jurisdiction: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_found: int = 0
    total_processed: int = 0
    total_success: int = 0
    total_failed: int = 0
    total_skipped: int = 0
    errors: List[str] = None
    last_harvest_date: Optional[datetime] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_processed == 0:
            return 0.0
        return self.total_success / self.total_processed
    
    @property
    def duration_seconds(self) -> float:
        """Calculate duration in seconds"""
        if self.end_time is None:
            return 0.0
        return (self.end_time - self.start_time).total_seconds()


class JurisdictionHarvester:
    """Harvests cases for a specific jurisdiction"""
    
    def __init__(self, jurisdiction: str, config: Optional[JurisdictionConfig] = None):
        self.jurisdiction = jurisdiction
        self.config = config or get_harvesting_config().get_jurisdiction_config(jurisdiction)
        
        if not self.config:
            raise ValueError(f"No configuration found for jurisdiction: {jurisdiction}")
        
        self.client = CourtListenerClient()
        self.processor = CaseLawProcessor()
        self.supabase = SupabaseConnector()
        
        # Get global harvesting config for rate limiting and quality thresholds
        self.global_config = get_harvesting_config()
        
        logger.info(f"Initialized harvester for jurisdiction: {jurisdiction}")
    
    def harvest_cases(self, practice_area: Optional[str] = None, 
                     start_date: Optional[datetime] = None,
                     end_date: Optional[datetime] = None,
                     max_cases: Optional[int] = None) -> HarvestResult:
        """
        Harvest cases for the jurisdiction
        
        Args:
            practice_area: Specific practice area to harvest (optional)
            start_date: Start date for case search (optional)
            end_date: End date for case search (optional)
            max_cases: Maximum number of cases to process (optional)
            
        Returns:
            HarvestResult with statistics and results
        """
        result = HarvestResult(
            jurisdiction=self.jurisdiction,
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"Starting harvest for jurisdiction {self.jurisdiction}")
            
            # Determine date range
            if start_date is None:
                start_date = self._get_last_harvest_date()
            
            if end_date is None:
                end_date = datetime.now()
            
            # Determine max cases
            if max_cases is None:
                max_cases = self.config.max_cases_per_run
            
            # Get search terms for practice area
            search_terms = self.config.get_search_terms(practice_area)
            
            logger.info(f"Harvesting {self.jurisdiction} from {start_date} to {end_date}, "
                       f"max cases: {max_cases}, practice area: {practice_area}")
            
            # Harvest cases for each court in the jurisdiction
            all_cases = []
            for court in self.config.courts:
                try:
                    court_cases = self._harvest_court_cases(
                        court, search_terms, start_date, end_date, max_cases
                    )
                    all_cases.extend(court_cases)
                    
                    # Apply rate limiting between courts
                    time.sleep(self.global_config.rate_limit_delay)
                    
                except Exception as e:
                    error_msg = f"Failed to harvest cases from court {court}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)
            
            result.total_found = len(all_cases)
            logger.info(f"Found {result.total_found} cases to process")
            
            # Process cases in batches
            if all_cases:
                result = self._process_cases_batch(all_cases, result)
            
            # Update last harvest date
            result.last_harvest_date = end_date
            self._update_last_harvest_date(end_date)
            
        except Exception as e:
            error_msg = f"Harvest failed for jurisdiction {self.jurisdiction}: {str(e)}"
            logger.error(error_msg)
            result.errors.append(error_msg)
        
        finally:
            result.end_time = datetime.now()
            logger.info(f"Harvest completed for {self.jurisdiction}. "
                       f"Processed: {result.total_processed}, "
                       f"Success: {result.total_success}, "
                       f"Failed: {result.total_failed}, "
                       f"Duration: {result.duration_seconds:.1f}s")
        
        return result
    
    def _harvest_court_cases(self, court: str, search_terms: List[str], 
                           start_date: datetime, end_date: datetime, 
                           max_cases: int) -> List[Dict]:
        """Harvest cases from a specific court"""
        cases = []
        
        try:
            # Search for cases using different strategies
            for search_term in search_terms[:3]:  # Limit to first 3 terms to avoid too many requests
                try:
                    # Format date range for Court Listener API
                    date_filed_after = start_date.strftime("%Y-%m-%d")
                    date_filed_before = end_date.strftime("%Y-%m-%d")
                    
                    # Search using the Court Listener client
                    search_results = self.client.search_opinions(
                        q=f'court_id:({court}) AND ({search_term})',
                        ordering="date_filed desc",
                        page_size=min(50, max_cases),
                        date_filed_after=date_filed_after,
                        date_filed_before=date_filed_before
                    )
                    
                    # Extract cases from search results
                    if isinstance(search_results, dict) and "results" in search_results:
                        for case_data in search_results["results"]:
                            if len(cases) >= max_cases:
                                break
                            
                            # Apply quality filters
                            if self._meets_quality_threshold(case_data):
                                cases.append(case_data)
                    
                    # Rate limiting between searches
                    time.sleep(self.global_config.rate_limit_delay)
                    
                except CourtListenerAPIError as e:
                    logger.warning(f"API error searching {court} for '{search_term}': {str(e)}")
                    continue
                
                if len(cases) >= max_cases:
                    break
        
        except Exception as e:
            logger.error(f"Failed to harvest from court {court}: {str(e)}")
        
        logger.info(f"Harvested {len(cases)} cases from court {court}")
        return cases
    
    def _meets_quality_threshold(self, case_data: Dict) -> bool:
        """Check if case meets quality thresholds"""
        thresholds = self.global_config.quality_thresholds
        
        # Check required fields
        required_fields = thresholds.get("required_fields", [])
        for field in required_fields:
            if not case_data.get(field):
                return False
        
        # Check minimum citation count
        min_citations = thresholds.get("min_citation_count", 0)
        citation_count = case_data.get("citation_count", 0)
        if citation_count < min_citations:
            return False
        
        return True
    
    def _process_cases_batch(self, cases: List[Dict], result: HarvestResult) -> HarvestResult:
        """Process a batch of cases"""
        batch_size = self.global_config.batch_size
        
        for i in range(0, len(cases), batch_size):
            batch = cases[i:i + batch_size]
            
            logger.info(f"Processing batch {i//batch_size + 1} "
                       f"({len(batch)} cases) for {self.jurisdiction}")
            
            for case_data in batch:
                try:
                    # Process the case using the existing processor
                    process_result = self.processor.process_case(case_data, self.jurisdiction)
                    
                    result.total_processed += 1
                    
                    if process_result and process_result.get("status") == "success":
                        result.total_success += 1
                    elif process_result and process_result.get("status") == "skipped":
                        result.total_skipped += 1
                    else:
                        result.total_failed += 1
                        if process_result and process_result.get("error"):
                            result.errors.append(process_result["error"])
                    
                    # Rate limiting between cases
                    time.sleep(self.global_config.rate_limit_delay)
                    
                except Exception as e:
                    result.total_processed += 1
                    result.total_failed += 1
                    error_msg = f"Failed to process case {case_data.get('id', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    result.errors.append(error_msg)
        
        return result
    
    def _get_last_harvest_date(self) -> datetime:
        """Get the last harvest date for this jurisdiction"""
        try:
            # Query the database for the last successful harvest
            # This would be implemented based on your tracking table
            # For now, use the configured start date
            start_date_str = self.config.date_range.get("start_date", "2020-01-01")
            return datetime.strptime(start_date_str, "%Y-%m-%d")
        except Exception as e:
            logger.warning(f"Could not get last harvest date for {self.jurisdiction}: {str(e)}")
            # Default to 30 days ago
            return datetime.now() - timedelta(days=30)
    
    def _update_last_harvest_date(self, harvest_date: datetime):
        """Update the last harvest date for this jurisdiction"""
        try:
            # This would update a tracking table in the database
            # Implementation depends on your database schema
            logger.info(f"Updated last harvest date for {self.jurisdiction} to {harvest_date}")
        except Exception as e:
            logger.error(f"Failed to update last harvest date for {self.jurisdiction}: {str(e)}")
    
    def get_harvest_status(self) -> Dict[str, Any]:
        """Get current harvest status for this jurisdiction"""
        return {
            "jurisdiction": self.jurisdiction,
            "enabled": self.config.enabled,
            "priority": self.config.priority,
            "courts": len(self.config.courts),
            "practice_areas": len(self.config.practice_areas),
            "max_cases_per_run": self.config.max_cases_per_run,
            "last_harvest_date": self._get_last_harvest_date().isoformat()
        }

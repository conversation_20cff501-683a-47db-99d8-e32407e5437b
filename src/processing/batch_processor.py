"""
Batch Processing System for Legal Documents
Handles multi-jurisdiction document processing with comprehensive auditing
"""

import os
import uuid
import json
import shutil
import logging
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dotenv import load_dotenv

# Local imports
import sys
import os

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))) 

# Import from new module structure
from src.extractors.document_classifier import DocumentClassifier
import scripts.process_pdfs_in_folder as pdf_processor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs", "batch_processing.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class BatchAuditor:
    """Maintains comprehensive audit logs for document processing"""
    
    def __init__(self, audit_dir="audit_logs"):
        self.audit_dir = audit_dir
        os.makedirs(audit_dir, exist_ok=True)
        self.current_batch_id = None
        self.batch_info = {}
        self.document_logs = {}
        
    def start_batch(self, batch_info):
        """Initialize a new processing batch"""
        self.current_batch_id = str(uuid.uuid4())
        self.batch_info = {
            "batch_id": self.current_batch_id,
            "start_time": datetime.now().isoformat(),
            "source": batch_info.get("source"),
            "jurisdiction": batch_info.get("jurisdiction"),
            "doc_count": batch_info.get("doc_count", 0),
            "status": "processing",
            "documents": {}
        }
        
        # Create batch directory
        batch_dir = os.path.join(self.audit_dir, self.current_batch_id)
        os.makedirs(batch_dir, exist_ok=True)
        
        # Save initial batch info
        with open(os.path.join(batch_dir, "batch_info.json"), "w") as f:
            json.dump(self.batch_info, f, indent=2)
            
        return self.current_batch_id
    
    def log_document(self, doc_id, document_path, metadata=None):
        """Register a document in the current batch"""
        if doc_id not in self.batch_info["documents"]:
            self.batch_info["documents"][doc_id] = {
                "document_id": doc_id,
                "path": document_path,
                "filename": os.path.basename(document_path),
                "start_time": datetime.now().isoformat(),
                "steps": [],
                "errors": [],
                "status": "processing",
                "metadata": metadata or {}
            }
        return doc_id
    
    def log_step(self, doc_id, step_name, step_details=None):
        """Log a processing step for a document"""
        if doc_id in self.batch_info["documents"]:
            step = {
                "step": step_name,
                "timestamp": datetime.now().isoformat(),
                "details": step_details
            }
            self.batch_info["documents"][doc_id]["steps"].append(step)
            
            # Update batch info file
            self._save_batch_info()
            
            return step
        return None
    
    def log_error(self, doc_id, error_message, error_context=None):
        """Log an error during document processing"""
        if doc_id in self.batch_info["documents"]:
            error = {
                "error": error_message,
                "timestamp": datetime.now().isoformat(),
                "context": error_context
            }
            self.batch_info["documents"][doc_id]["errors"].append(error)
            self.batch_info["documents"][doc_id]["status"] = "error"
            
            # Update batch info file
            self._save_batch_info()
            
            return error
        return None
    
    def complete_document(self, doc_id, success=True, metadata=None):
        """Mark document processing as complete"""
        if doc_id in self.batch_info["documents"]:
            self.batch_info["documents"][doc_id]["end_time"] = datetime.now().isoformat()
            self.batch_info["documents"][doc_id]["status"] = "success" if success else "failed"
            
            if metadata:
                self.batch_info["documents"][doc_id]["metadata"] = metadata
                
            # Update batch info file
            self._save_batch_info()
    
    def complete_batch(self, success_count, failure_count):
        """Mark the current batch as complete"""
        self.batch_info["end_time"] = datetime.now().isoformat()
        self.batch_info["status"] = "completed"
        self.batch_info["success_count"] = success_count
        self.batch_info["failure_count"] = failure_count
        
        # Calculate processing statistics
        total_docs = len(self.batch_info["documents"])
        self.batch_info["statistics"] = {
            "total_documents": total_docs,
            "success_rate": (success_count / total_docs) * 100 if total_docs > 0 else 0,
            "failure_rate": (failure_count / total_docs) * 100 if total_docs > 0 else 0
        }
        
        # Update batch info file
        self._save_batch_info()
        
        # Generate batch summary report
        self._generate_batch_report()
    
    def _save_batch_info(self):
        """Save the current batch info to file"""
        batch_dir = os.path.join(self.audit_dir, self.current_batch_id)
        with open(os.path.join(batch_dir, "batch_info.json"), "w") as f:
            json.dump(self.batch_info, f, indent=2)
    
    def _generate_batch_report(self):
        """Generate a summary report for the batch"""
        batch_dir = os.path.join(self.audit_dir, self.current_batch_id)
        
        # Generate summary
        summary = {
            "batch_id": self.current_batch_id,
            "start_time": self.batch_info["start_time"],
            "end_time": self.batch_info["end_time"],
            "jurisdiction": self.batch_info.get("jurisdiction"),
            "total_documents": len(self.batch_info["documents"]),
            "success_count": self.batch_info["success_count"],
            "failure_count": self.batch_info["failure_count"],
            "success_rate": self.batch_info["statistics"]["success_rate"],
            "document_types": self._count_document_types(),
            "errors": self._summarize_errors()
        }
        
        # Save summary
        with open(os.path.join(batch_dir, "batch_summary.json"), "w") as f:
            json.dump(summary, f, indent=2)
            
        return summary
    
    def _count_document_types(self):
        """Count documents by type"""
        doc_types = {}
        for doc_id, doc_info in self.batch_info["documents"].items():
            doc_type = doc_info.get("metadata", {}).get("doc_type")
            if doc_type:
                doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
        return doc_types
    
    def _summarize_errors(self):
        """Summarize errors across the batch"""
        error_summary = {}
        for doc_id, doc_info in self.batch_info["documents"].items():
            for error in doc_info.get("errors", []):
                error_msg = error.get("error", "Unknown error")
                error_summary[error_msg] = error_summary.get(error_msg, 0) + 1
        return error_summary

class BatchProcessor:
    """Processes batches of legal documents"""
    
    def __init__(self, output_base_dir="processed_documents"):
        self.classifier = DocumentClassifier()
        self.auditor = BatchAuditor()
        self.output_base_dir = output_base_dir
        os.makedirs(output_base_dir, exist_ok=True)
        
        # Create directories for each status
        self.success_dir = os.path.join(output_base_dir, "success")
        self.error_dir = os.path.join(output_base_dir, "error")
        self.pending_dir = os.path.join(output_base_dir, "pending")
        
        for dir_path in [self.success_dir, self.error_dir, self.pending_dir]:
            os.makedirs(dir_path, exist_ok=True)
    
    def process_directory(self, input_dir, jurisdiction=None, max_workers=4):
        """Process all PDF files in a directory"""
        pdf_files = [
            os.path.join(input_dir, f) for f in os.listdir(input_dir)
            if f.lower().endswith('.pdf')
        ]
        
        batch_info = {
            "source": input_dir,
            "jurisdiction": jurisdiction,
            "doc_count": len(pdf_files)
        }
        
        batch_id = self.auditor.start_batch(batch_info)
        logger.info(f"Starting batch {batch_id} with {len(pdf_files)} documents")
        
        success_count = 0
        failure_count = 0
        
        # Process files in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(self._process_pdf, pdf_path, batch_id, jurisdiction): pdf_path for pdf_path in pdf_files}
            
            for future in as_completed(futures):
                pdf_path = futures[future]
                try:
                    result = future.result()
                    if result.get("success"):
                        success_count += 1
                    else:
                        failure_count += 1
                except Exception as e:
                    logger.error(f"Error processing {pdf_path}: {str(e)}")
                    failure_count += 1
        
        # Complete the batch
        self.auditor.complete_batch(success_count, failure_count)
        
        logger.info(f"Batch {batch_id} completed: {success_count} successes, {failure_count} failures")
        return {
            "batch_id": batch_id,
            "success_count": success_count,
            "failure_count": failure_count,
            "total": len(pdf_files)
        }
    
    def _process_pdf(self, pdf_path, batch_id, jurisdiction=None):
        """Process a single PDF file"""
        filename = os.path.basename(pdf_path)
        doc_id = str(uuid.uuid4())
        
        try:
            # Register document in audit log
            self.auditor.log_document(doc_id, pdf_path)
            
            # Stage 1: Classification
            self.auditor.log_step(doc_id, "classification", {"status": "started"})
            classification = self.classifier.classify_document(pdf_path)
            
            # Use provided jurisdiction if available
            if jurisdiction and not classification.get("jurisdiction"):
                classification["jurisdiction"] = jurisdiction
                
            self.auditor.log_step(doc_id, "classification", {
                "status": "completed",
                "result": classification
            })
            
            # Update document metadata
            metadata = self.classifier.extract_metadata_preview(pdf_path, classification)
            self.auditor.log_step(doc_id, "metadata_extraction", {
                "status": "completed",
                "metadata": metadata
            })
            
            # Prepare namespace based on jurisdiction
            jurisdiction_code = self._get_jurisdiction_code(classification.get("jurisdiction", "unknown"))
            namespace = jurisdiction_code.lower()
            
            # Stage 2: Document processing with our PDF processor
            self.auditor.log_step(doc_id, "pdf_processing", {"status": "started"})
            
            # Set practice area to personal_injury for now, but this could be enhanced later
            practice_area = "personal_injury"
            doc_type = classification.get("doc_type", "law")
            
            # Create destination directory for the processed document
            dest_dir = os.path.join(self.pending_dir, doc_id)
            os.makedirs(dest_dir, exist_ok=True)
            temp_dest = os.path.join(dest_dir, filename)
            shutil.copy(pdf_path, temp_dest)
            
            try:
                # Process the document using our PDF processor
                result = pdf_processor.process_pdf(
                    temp_dest, 
                    practice_area=practice_area,
                    doc_type=doc_type,
                    namespace=namespace
                )
                
                # Update with processing results
                self.auditor.log_step(doc_id, "pdf_processing", {
                    "status": "completed",
                    "chunks": result.get("num_chunks", 0),
                    "pinecone_ids": result.get("pinecone_ids", [])
                })
                
                # Move to success directory
                success_path = os.path.join(self.success_dir, doc_id)
                os.makedirs(success_path, exist_ok=True)
                shutil.move(temp_dest, os.path.join(success_path, filename))
                
                # Complete document processing
                self.auditor.complete_document(doc_id, success=True, metadata={
                    **metadata,
                    "pinecone_ids": result.get("pinecone_ids", []),
                    "chunk_count": result.get("num_chunks", 0)
                })
                
                return {
                    "doc_id": doc_id,
                    "success": True,
                    "metadata": metadata
                }
                
            except Exception as e:
                error_msg = f"Error in PDF processing: {str(e)}"
                logger.error(error_msg)
                self.auditor.log_error(doc_id, error_msg)
                
                # Move to error directory
                error_path = os.path.join(self.error_dir, doc_id)
                os.makedirs(error_path, exist_ok=True)
                shutil.move(temp_dest, os.path.join(error_path, filename))
                
                # Complete document processing with failure
                self.auditor.complete_document(doc_id, success=False, metadata=metadata)
                
                return {
                    "doc_id": doc_id,
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            error_msg = f"Error processing document: {str(e)}"
            logger.error(error_msg)
            self.auditor.log_error(doc_id, error_msg, {"stage": "pre-processing"})
            
            # Complete document processing with failure
            self.auditor.complete_document(doc_id, success=False)
            
            return {
                "doc_id": doc_id,
                "success": False,
                "error": error_msg
            }
    
    def _get_jurisdiction_code(self, jurisdiction):
        """Convert jurisdiction name to code"""
        jurisdiction_map = {
            "Texas": "tx",
            "California": "ca",
            "New York": "ny",
            "Florida": "fl",
            "Ohio": "oh",
            "Illinois": "il",
            "Pennsylvania": "pa",
            "Michigan": "mi",
            "Federal": "fed"
        }
        
        return jurisdiction_map.get(jurisdiction, jurisdiction.lower()[:2] if jurisdiction else "unknown")

# Function to run a batch processing job
def run_batch_job(input_dir, jurisdiction=None, max_workers=4):
    """Run a batch processing job for legal documents"""
    processor = BatchProcessor()
    result = processor.process_directory(input_dir, jurisdiction, max_workers)
    
    print(f"\n===== BATCH PROCESSING COMPLETE =====")
    print(f"Batch ID: {result['batch_id']}")
    print(f"Total Documents: {result['total']}")
    print(f"Successful: {result['success_count']}")
    print(f"Failed: {result['failure_count']}")
    print(f"Success Rate: {(result['success_count'] / result['total']) * 100:.2f}%")
    
    return result

if __name__ == "__main__":
    # Example usage
    # import sys
    # if len(sys.argv) > 1:
    #     input_dir = sys.argv[1]
    #     jurisdiction = sys.argv[2] if len(sys.argv) > 2 else None
    #     run_batch_job(input_dir, jurisdiction)
    pass

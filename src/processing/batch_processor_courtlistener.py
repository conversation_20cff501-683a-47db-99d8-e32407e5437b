"""
Court Listener Integration for Batch Processor
Extends the BatchProcessor with Court Listener API capabilities
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any

from src.processing.batch_processor import BatchProcessor
from src.processing.courtlistener_processor import CourtListenerProcessor
from src.api.courtlistener.models import Case, Opinion

# Configure logging
logger = logging.getLogger(__name__)


class CourtListenerBatchProcessor(BatchProcessor):
    """
    Extended BatchProcessor with Court Listener API integration
    """
    
    def __init__(self, output_base_dir="processed_documents"):
        """Initialize the Court Listener BatchProcessor"""
        super().__init__(output_base_dir)
        self.courtlistener = CourtListenerProcessor()
        
        # Create directory for Court Listener data
        self.courtlistener_dir = os.path.join(output_base_dir, "courtlistener_data")
        os.makedirs(self.courtlistener_dir, exist_ok=True)
    
    def _process_pdf(self, pdf_path, batch_id, jurisdiction=None):
        """
        Extended PDF processing with Court Listener enrichment
        
        Args:
            pdf_path: Path to the PDF file
            batch_id: Batch ID
            jurisdiction: Optional jurisdiction
            
        Returns:
            Processing result
        """
        # First, process the PDF using the standard pipeline
        result = super()._process_pdf(pdf_path, batch_id, jurisdiction)
        
        # If processing was successful, try to enrich with Court Listener data
        if result.get("success", False):
            doc_id = result.get("doc_id")
            metadata = result.get("metadata", {})
            self._enrich_with_courtlistener(doc_id, metadata)
        
        return result
    
    def _enrich_with_courtlistener(self, doc_id, metadata):
        """
        Enrich document with Court Listener data
        
        Args:
            doc_id: Document ID
            metadata: Document metadata
        """
        try:
            # Log the enrichment step
            self.auditor.log_step(doc_id, "courtlistener_enrichment", {
                "status": "started"
            })
            
            # Get text from document (use extracted text if available)
            doc_text = metadata.get("extracted_text", "")
            if not doc_text:
                logger.warning(f"No extracted text available for document {doc_id}")
                self.auditor.log_step(doc_id, "courtlistener_enrichment", {
                    "status": "skipped",
                    "reason": "No extracted text available"
                })
                return
            
            # Extract and fetch citations
            jurisdiction = metadata.get("jurisdiction")
            citation_results = self.courtlistener.extract_and_fetch_citations(doc_text)
            
            # If no citations found, try a text search
            if not citation_results and len(doc_text) > 200:
                # Use first 500 chars as a search query
                search_text = doc_text[:500]
                cases = self.courtlistener.search_by_text(
                    search_text, 
                    jurisdiction=jurisdiction,
                    max_results=5
                )
                if cases:
                    citation_results["text_search"] = cases
            
            # Save results to file
            results_dir = os.path.join(self.courtlistener_dir, doc_id)
            os.makedirs(results_dir, exist_ok=True)
            
            results_file = os.path.join(results_dir, "citations.json")
            
            # Format results for saving
            formatted_results = {
                "document_id": doc_id,
                "citation_count": len(citation_results),
                "jurisdiction": jurisdiction,
                "citations": {}
            }
            
            # Save case data
            for citation, cases in citation_results.items():
                formatted_cases = []
                
                for case in cases:
                    # Cache the case data
                    cache_id = self.courtlistener.cache_case_data(case)
                    
                    # Add basic case info
                    case_info = {
                        "id": case.id,
                        "name": case.name,
                        "court": case.court.name,
                        "jurisdiction": case.court.jurisdiction,
                        "date_filed": case.date_filed.isoformat() if case.date_filed else None,
                        "docket_number": case.docket_number,
                        "cache_id": cache_id,
                        "citations": [str(cite) for cite in case.citations],
                        "judges": [judge.name for judge in case.judges]
                    }
                    
                    # Try to get opinion summaries
                    opinions = self.courtlistener.get_opinion_text(case)
                    if opinions:
                        case_info["opinion_types"] = list(opinions.keys())
                    
                    formatted_cases.append(case_info)
                
                formatted_results["citations"][citation] = formatted_cases
            
            # Write results to file
            with open(results_file, "w") as f:
                json.dump(formatted_results, f, indent=2)
            
            # Update metadata with citation information
            citations_found = sum(len(cases) for cases in citation_results.values())
            
            # Log success
            self.auditor.log_step(doc_id, "courtlistener_enrichment", {
                "status": "completed",
                "citations_found": citations_found,
                "citation_sources": list(citation_results.keys())
            })
            
            # Update document metadata
            enriched_metadata = {
                **metadata,
                "courtlistener_enriched": True,
                "citations_found": citations_found,
                "courtlistener_data_path": results_file
            }
            
            self.auditor.complete_document(doc_id, success=True, metadata=enriched_metadata)
            
            logger.info(f"Enriched document {doc_id} with {citations_found} citations from Court Listener")
            
        except Exception as e:
            logger.error(f"Error enriching document {doc_id} with Court Listener data: {str(e)}")
            self.auditor.log_error(doc_id, f"Court Listener enrichment failed: {str(e)}")
            
            # Update document metadata to indicate failure
            enriched_metadata = {
                **metadata,
                "courtlistener_enriched": False,
                "courtlistener_error": str(e)
            }
            
            self.auditor.log_step(doc_id, "courtlistener_enrichment", {
                "status": "error",
                "error": str(e)
            })
    
    def find_related_cases(self, query, jurisdiction=None, max_results=10):
        """
        Find cases related to a query
        
        Args:
            query: Search query
            jurisdiction: Optional jurisdiction 
            max_results: Maximum number of results
            
        Returns:
            List of related cases
        """
        if jurisdiction and jurisdiction.lower() == "texas":
            response = self.courtlistener.search_texas_cases(query, page_size=max_results)
        else:
            response = self.courtlistener.search_jurisdiction(
                jurisdiction=jurisdiction or "Texas",
                query=query,
                page_size=max_results
            )
        
        # Handle different response formats
        if isinstance(response, dict) and "results" in response:
            return response["results"]
        elif isinstance(response, list):
            return response
        else:
            return []
    
    def get_case_by_citation(self, citation):
        """
        Get a case by citation
        
        Args:
            citation: Citation string
            
        Returns:
            Case if found, otherwise None
        """
        cases = self.courtlistener.search_by_citation(citation)
        return cases[0] if cases else None

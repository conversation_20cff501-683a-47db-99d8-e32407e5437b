"""
Case Law Processing System
Orchestrates the end-to-end workflow for fetching, processing, and storing case law data
from Court Listener and other sources with support for multiple jurisdictions.
Includes role-based access control and multi-jurisdiction support.
"""

import os
import uuid
import logging
import time
import traceback
from typing import Dict, List, Optional, Any
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from .storage.neo4j_connector import Neo4jConnector

# Configure logging
os.makedirs("logs", exist_ok=True)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class CaseLawProcessor:
    """
    Main orchestrator for case law processing.
    Handles the workflow for fetching, processing, and storing case law data
    with support for multiple storage backends and jurisdictions.
    Includes role-based access control for multi-tenant usage.
    """
    
    def __init__(self, config: Optional[Dict] = None, user_id: Optional[str] = None, user_role: Optional[str] = None, tenant_id: Optional[str] = None):
        """
        Initialize the case law processor.
        
        Args:
            config: Optional configuration dictionary to override defaults
            user_id: Optional user ID for access control and audit
            user_role: Optional user role for permission checks (partner, attorney, paralegal, staff, client)
            tenant_id: Optional tenant ID for multi-tenant isolation
        """
        self.config = {
            "gcs_bucket": os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"),
            "pinecone_index": os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large"),
            "batch_size": 10,
            "max_workers": int(os.getenv("MAX_WORKERS", "4")),
            "default_jurisdiction": "tx",
            "allowed_jurisdictions": os.getenv("ALLOWED_JURISDICTIONS", "tx,ca,fed").split(","),
            **(config or {})
        }
        
        # User information for access control
        self.user_id = user_id
        self.user_role = user_role
        self.tenant_id = tenant_id
        
        # Role-based jurisdiction access mapping
        self.role_jurisdiction_map = {
            "partner": self.config["allowed_jurisdictions"],  # Partners get access to all jurisdictions
            "attorney": self.config["allowed_jurisdictions"],  # Attorneys get access to all jurisdictions
            "paralegal": ["tx"],  # Paralegals get limited access by default
            "staff": ["tx"],  # Staff get limited access by default
            "client": []  # Clients get access based on their cases only
        }
        
        # Initialize connectors (lazy-loaded)
        self._supabase = None
        self._gcs = None
        self._pinecone = None
        self._neo4j = None
        self._neo4j_init_attempted = False # Flag to track initialization attempt
        self._neo4j_lock = Lock() # Lock for neo4j initialization
        
        # Ensure database tables exist
        from src.processing.storage.supabase_connector import SupabaseConnector
        SupabaseConnector(ensure_tables_exist=True)
        
        # Initialize pipeline components
        self._court_listener = None
        self._findlaw = None
        self._citation_extractor = None

        # Week 2 enhanced processors
        self._enhanced_opinion_processor = None
        self._metadata_enhancer = None
        
        # Track the current processing batch
        self.current_batch_id = None
        self.current_batch_stats = {
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "processed_case_ids": [] # Use a list instead of a set
        }
        
        # Role-based jurisdiction access control
        self.role_jurisdiction_map = {
            "partner": self.config["allowed_jurisdictions"],  # Partners have access to all jurisdictions
            "attorney": ["tx", "ny", "fl", "oh", "fed"],  # Attorneys can access multiple states
            "paralegal": ["tx", "ny", "fl"],  # Paralegals get expanded access
            "staff": ["tx", "ny"],  # Staff get limited access
            "client": ["tx"]  # Clients only see Texas cases
        }
        
        logger.info(f"Case Law Processor initialized with config: {self.config}")
        if user_id and user_role:
            logger.info(f"Access control enabled for user {user_id} with role {user_role}")
    
    @property
    def supabase(self):
        """Lazy-loaded Supabase connection"""
        if self._supabase is None:
            from src.processing.storage.supabase_connector import SupabaseConnector
            self._supabase = SupabaseConnector()
        return self._supabase
    
    @property
    def gcs(self):
        """Lazy-loaded Google Cloud Storage connection"""
        if self._gcs is None:
            from src.processing.storage.gcs_connector import GCSConnector
            self._gcs = GCSConnector(bucket_name=self.config["gcs_bucket"])
        return self._gcs
        
    @property
    def pinecone(self):
        """Lazy-loaded Pinecone vector store connection"""
        if self._pinecone is None:
            from src.processing.storage.vector_store import PineconeConnector
            self._pinecone = PineconeConnector(index_name=self.config["pinecone_index"])
        return self._pinecone
    
    @property
    def neo4j(self):
        """Lazy-loaded and thread-safe Neo4j graph database connection"""
        # Quick check without lock first
        if self._neo4j_init_attempted:
            return self._neo4j
        
        # Acquire lock to ensure only one thread initializes
        with self._neo4j_lock:
            # Double-check if another thread initialized while waiting for the lock
            if self._neo4j_init_attempted:
                return self._neo4j
            
            # Mark that we are attempting initialization NOW
            self._neo4j_init_attempted = True
            logger.info("Lazily initializing Neo4j connector (inside lock)...")
            try:
                # Attempt to instantiate the connector
                connector_instance = Neo4jConnector()
                
                # Check if the driver connection failed internally during init
                if not hasattr(connector_instance, '_driver') or connector_instance._driver is None:
                    logger.warning("Neo4jConnector initialized but failed to establish a driver connection. Neo4j operations will be skipped.")
                    self._neo4j = connector_instance
                else:
                    logger.info("Neo4jConnector initialized successfully.")
                    self._neo4j = connector_instance
            
            except Exception as e:
                logger.error(f"Failed to initialize Neo4jConnector: {repr(e)}")
                logger.exception("Traceback for Neo4jConnector initialization error:")
                self._neo4j = None # Ensure self._neo4j is None if init fails completely
            
            return self._neo4j
    
    @property
    def court_listener(self):
        """Lazy-loaded Court Listener API client"""
        if self._court_listener is None:
            from src.processing.providers.court_listener import CourtListenerConnector
            self._court_listener = CourtListenerConnector()
        return self._court_listener
        
    @property
    def findlaw(self):
        """Lazy-loaded FindLaw API client"""
        if self._findlaw is None:
            from src.processing.providers.findlaw import FindLawConnector
            self._findlaw = FindLawConnector()
        return self._findlaw
        
    @property
    def citation_extractor(self):
        """Lazy-loaded citation extractor"""
        if self._citation_extractor is None:
            from src.processing.citation_extractor import CitationExtractor
            # Use Court Listener API for citation extraction if available
            use_api = hasattr(self.court_listener, 'api_key') and self.court_listener.api_key is not None
            self._citation_extractor = CitationExtractor(
                use_court_listener_api=use_api,
                api_key=getattr(self.court_listener, 'api_key', None)
            )
        return self._citation_extractor

    @property
    def enhanced_opinion_processor(self):
        """Lazy-loaded enhanced opinion processor"""
        if self._enhanced_opinion_processor is None:
            from src.processing.enhanced_opinion_processor import EnhancedOpinionProcessor
            self._enhanced_opinion_processor = EnhancedOpinionProcessor()
        return self._enhanced_opinion_processor

    @property
    def metadata_enhancer(self):
        """Lazy-loaded metadata enhancer"""
        if self._metadata_enhancer is None:
            from src.processing.metadata_enhancer import CaseMetadataEnhancer
            self._metadata_enhancer = CaseMetadataEnhancer()
        return self._metadata_enhancer
    
    def get_allowed_jurisdictions(self) -> List[str]:
        """
        Get the list of jurisdictions the current user has access to based on their role.
        
        Role-based access rules:
        - partner: All jurisdictions
        - attorney: All jurisdictions
        - paralegal: Jurisdictions specified in their tenant settings
        - staff: Jurisdictions specified in their tenant settings
        - client: Only their specific case jurisdictions
        
        Returns:
            List of jurisdiction codes the user can access
        """
        if not self.user_role:
            # Default to all jurisdictions if no role specified (system process)
            return self.config["allowed_jurisdictions"]
        
        # Admin roles get access to all jurisdictions
        if self.user_role in ["partner", "attorney"]:
            return self.config["allowed_jurisdictions"]
            
        # Staff roles get access to jurisdictions based on tenant settings
        if self.user_role in ["paralegal", "staff"]:
            # If tenant_id is provided, get jurisdictions for that tenant
            if self.tenant_id:
                tenant_jurisdictions = self.supabase.get_tenant_jurisdictions(self.tenant_id)
                if tenant_jurisdictions:
                    return tenant_jurisdictions
            # Fallback to role map if tenant-specific settings not found
            return self.role_jurisdiction_map.get(self.user_role, [])
            
        # Clients only get access to jurisdictions of their own cases
        if self.user_role == "client" and self.user_id:
            client_jurisdictions = self.supabase.get_client_case_jurisdictions(self.user_id)
            if client_jurisdictions:
                return client_jurisdictions
            # If no cases found, return empty list (no access)
            return []
            
        # Default fallback to role-jurisdiction map
        return self.role_jurisdiction_map.get(self.user_role, [])
    
    def check_jurisdiction_access(self, jurisdiction: str) -> bool:
        """
        Check if the current user has access to the specified jurisdiction.
        
        Args:
            jurisdiction: The jurisdiction code to check
            
        Returns:
            Boolean indicating if access is allowed
        """
        if not self.user_role:
            # Default to allowed if no role specified
            return True
            
        allowed_jurisdictions = self.get_allowed_jurisdictions()
        return jurisdiction in allowed_jurisdictions
    
    def filter_results_by_jurisdiction(self, results: List[Dict]) -> List[Dict]:
        """
        Filter a list of results to only include those from allowed jurisdictions.
        
        Args:
            results: List of result dictionaries with jurisdiction field
            
        Returns:
            Filtered list of results
        """
        if not self.user_role:
            return results
            
        allowed_jurisdictions = self.get_allowed_jurisdictions()
        return [r for r in results if r.get('jurisdiction', '') in allowed_jurisdictions]
    
    def start_batch(self, source: str, jurisdiction: str, query_params: Optional[Dict] = None) -> str:
        """
        Start a new processing batch.
        
        Args:
            source: The data source (e.g., "court_listener", "find_law")
            jurisdiction: The jurisdiction code (e.g., "tx", "ca")
            query_params: Optional parameters for the data source query
            
        Returns:
            batch_id: A unique identifier for this processing batch
        """
        self.current_batch_id = str(uuid.uuid4())
        self.current_batch_stats = {
            "batch_id": self.current_batch_id,
            "source": source,
            "jurisdiction": jurisdiction,
            "start_time": datetime.now().isoformat(),
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "status": "processing",
            "query_params": query_params or {}
        }
        
        logger.info(f"Started new batch {self.current_batch_id} for {jurisdiction} from {source}")
        
        # Register batch in database for tracking
        self.supabase.create_processing_batch(self.current_batch_stats)
        
        return self.current_batch_id
    
    def process_jurisdiction(self, jurisdiction: str, query: str = "", count: int = 100, source: str = "court_listener") -> Dict:
        """
        Process cases for a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tx", "ca")
            query: Search query for filtering cases
            count: Maximum number of cases to process
            source: Data source to use ("court_listener" or "findlaw")
            
        Returns:
            Dict with processing statistics or error message
        """
        # 1. Check Jurisdiction Access
        if not self.check_jurisdiction_access(jurisdiction):
            logger.error(f"User {self.user_id} (Role: {self.user_role}) does not have access to jurisdiction: {jurisdiction}")
            return {"error": f"Access denied to jurisdiction: {jurisdiction}"}
        
        logger.info(f"Starting processing for jurisdiction: {jurisdiction}, Query: '{query}', Max count: {count}, Source: {source}")
        
        # 2. Start a new batch
        # Use the original user query for logging purposes, even if not directly used for fetching
        query_params = {"query": query, "count": count} 
        self.current_batch_id = self.start_batch(source, jurisdiction, query_params)
        batch_start_time = time.time()
        
        # Reset stats for this batch
        self.current_batch_stats = {
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "processed_case_ids": [] # Use a list instead of a set
        }
        
        all_clusters = []
        processed_cluster_ids = [] # Use a list instead of a set for local tracking
        
        try:
            # 3. Get relevant court IDs for the jurisdiction
            court_ids = self.court_listener.jurisdiction_courts.get(jurisdiction.lower(), [])
            if not court_ids:
                logger.error(f"No court IDs configured for jurisdiction: {jurisdiction}")
                raise ValueError(f"No court IDs for jurisdiction: {jurisdiction}")

            logger.info(f"Fetching top clusters for court IDs: {court_ids} in jurisdiction {jurisdiction}")

            # 4. Fetch Top Clusters for each Court ID
            # We fetch slightly more per court initially to account for overlap and ensure we get enough unique top ones overall.
            fetch_count_per_court = max(20, count // len(court_ids) + 10) # Heuristic
            
            for court_id in court_ids:
                try:
                    logger.debug(f"Fetching top {fetch_count_per_court} clusters for court {court_id}...")
                    cluster_results = self.court_listener.get_top_clusters(court_id=court_id, count=fetch_count_per_court)
                    fetched_opinions = cluster_results.get('results', [])
                    logger.debug(f"Fetched {len(fetched_opinions)} clusters for court {court_id}.")
                    
                    # Add unique clusters to the main list
                    for cluster_data in fetched_opinions:
                        cluster_id = cluster_data.get('id')
                        if cluster_id and cluster_id not in processed_cluster_ids:
                            all_clusters.append(cluster_data)
                            processed_cluster_ids.append(cluster_id) # Append to list
                            
                except Exception as e:
                    logger.error(f"Failed to fetch clusters for court {court_id}: {e}", exc_info=True)
                    # Continue to other courts even if one fails

            if not all_clusters:
                logger.warning(f"No clusters found for jurisdiction {jurisdiction} across courts {court_ids}. Exiting batch.")
                error_message = f"No clusters found for jurisdiction: {jurisdiction}"
                self.complete_batch(self.current_batch_id, "completed", error=error_message)
                return self.get_batch_stats()

            # 5. Sort and Limit Clusters
            # Sort all collected unique clusters by citation count (descending)
            all_clusters.sort(key=lambda c: c.get('citation_count', 0), reverse=True)
            
            # Limit to the top 'count' unique clusters overall
            top_clusters_to_process = all_clusters[:count]
            self.current_batch_stats["total"] = len(top_clusters_to_process)
            logger.info(f"Aggregated {len(all_clusters)} unique clusters. Processing top {self.current_batch_stats['total']} based on citation count.")
            
            # 6. Process Clusters in Parallel
            with ThreadPoolExecutor(max_workers=self.config["max_workers"]) as executor:
                # Submit tasks: Pass CLUSTER data to process_case
                futures = {executor.submit(self._process_case_safe, cluster_data, jurisdiction): cluster_data.get('id') 
                           for cluster_data in top_clusters_to_process}
                
                # Collect results
                for future in as_completed(futures):
                    cluster_id = futures[future]
                    try:
                        result = future.result()
                        if result == "success":
                            self.current_batch_stats["success"] += 1
                            self.current_batch_stats["processed_case_ids"].append(cluster_id) # Append to list
                        elif result == "skipped":
                            self.current_batch_stats["skipped"] += 1
                        else: # Covers "failure" and potentially other states
                            self.current_batch_stats["failure"] += 1
                    except Exception as exc:
                        logger.error(f'Cluster {cluster_id} generated an exception: {exc}', exc_info=True)
                        self.current_batch_stats["failure"] += 1
            
            # 7. Complete the batch
            status = "completed" if self.current_batch_stats["failure"] == 0 else "completed_with_errors"
            self.complete_batch(self.current_batch_id, status)
            logger.info(f"Completed batch {self.current_batch_id} for jurisdiction {jurisdiction}. Status: {status}")
        
        except Exception as e:
            logger.error(f"Unhandled error during jurisdiction processing for {jurisdiction}: {e}", exc_info=True)
            if self.current_batch_id:
                self.complete_batch(self.current_batch_id, "failed", error=str(e))
            return {"error": f"Processing failed for jurisdiction {jurisdiction}: {str(e)}"}
            
        finally:
            batch_duration = time.time() - batch_start_time
            logger.info(f"Batch {self.current_batch_id} took {batch_duration:.2f} seconds.")
            # Clean up thread-local Neo4j connections if they were created
            if hasattr(self, '_neo4j_thread_local') and hasattr(self._neo4j_thread_local, 'connection') and self._neo4j_thread_local.connection:
                 self.neo4j.close() # Use the close method of the property
            
        # Just return stats
        return self.get_batch_stats()

    def _process_case_safe(self, case_data: Dict, jurisdiction: str) -> str:
        """
        Wrapper for process_case that handles exceptions for parallel processing.
        
        Args:
            case_data: Case data
            jurisdiction: Jurisdiction code
            
        Returns:
            Processing result string
        """
        # Ensure case_id is extracted correctly at the start
        case_id_str = str(case_data.get('id', 'unknown_in_safe_process')) 
        try:
            # Check if the case already exists and should be skipped
            # Note: We need a way to get the batch_id here if skipping logic depends on it.
            # Simplified check: Assume skipping logic is handled elsewhere or not needed here.
            # if self.supabase.check_case_exists(case_id_str): 
            #    logger.info(f"Skipping already processed case {case_id_str}")
            #    return 'skipped' 
            
            # Pass jurisdiction from the outer scope
            processing_result = self.process_case(case_data, jurisdiction)
            
            # Check the result from process_case
            if processing_result is None:
                logger.warning(f"_process_case_safe: process_case returned None for case {case_id_str}, indicating failure.")
                return 'failure' # Explicitly return 'failure' status string
            else:
                # Assuming process_case returns a dict on success, we return 'success' status
                logger.info(f"_process_case_safe: Successfully processed case {case_id_str}")
                return 'success'
            
        except Exception as e:
            # This except block catches errors *directly* within _process_case_safe
            # Errors caught *within* process_case are handled by its own except block.
            logger.error(f"Error in _process_case_safe for case {case_id_str}: {repr(e)}")
            logger.exception(f"Traceback for error in _process_case_safe {case_id_str}:")
            
            # We return None to indicate failure within this specific method
            return 'failure'
    
    def process_case(self, case_data: Dict, jurisdiction: str) -> Optional[Dict]:
        """
        Process a single cluster and its associated opinions through the pipeline.
        
        Args:
            case_data: Cluster data from Court Listener API (/clusters endpoint).
            jurisdiction: Jurisdiction code.
            
        Returns:
            Dict with processing result for the cluster or None on failure.
        """
        # Treat the incoming 'case_data' as 'cluster_data'
        cluster_data = case_data 
        cluster_id = cluster_data.get("id")
        
        if not cluster_id:
            logger.error("Received cluster data without an ID. Cannot process.")
            return {"status": "failure", "error": "Missing cluster ID"}
            
        case_id = cluster_id # Use cluster_id as the primary identifier for this processing unit

        # Check jurisdiction access (remains the same)
        if not self.check_jurisdiction_access(jurisdiction):
            logger.warning(f"Access denied for cluster {case_id} in jurisdiction {jurisdiction} with role {self.user_role}")
            return {
                "status": "error",
                "error": "Access denied",
                "case_id": case_id, # Report cluster ID
                "jurisdiction": jurisdiction
            }
        
        # Check if this cluster has already been processed sufficiently
        # Assuming is_case_already_processed can check based on the cluster ID in the 'cases' table
        if self.is_case_already_processed(case_id, min_quality=0.7):
            logger.info(f"Cluster {case_id} already processed with good quality, skipping")
            return {"status": "skipped", "case_id": case_id, "reason": "already_processed"}
        
        try:
            # Step 1: Validate and normalize cluster data
            cluster_name = cluster_data.get('case_name_full') or cluster_data.get('case_name', 'Unknown')
            logger.info(f"Processing Cluster {case_id} ('{cluster_name}')")
            cluster_data["jurisdiction"] = jurisdiction # Add jurisdiction if missing
            
            # Record user who initiated processing
            if self.user_id:
                cluster_data["processed_by"] = self.user_id
                cluster_data["processed_role"] = self.user_role
            
            # Step 2: Store/Update cluster metadata in Supabase 'cases' table
            # Assuming store_case can handle cluster data or will be adapted
            logger.info(f"Storing cluster {case_id} metadata in Supabase")
            if not self.current_batch_id:
                 logger.error(f"Critical error: current_batch_id is None during process_case for cluster {case_id}. Cannot proceed.")
                 raise ValueError(f"Missing batch_id for cluster {case_id}")
            
            # Pass the cluster data directly to store_case, using cluster_id as the primary ID
            stored_case = self.supabase.store_case(cluster_data, batch_id=self.current_batch_id)
            
            # Step 2.5: Ensure Cluster/Case node exists in Neo4j
            # Map cluster properties to Neo4j node properties
            neo4j_properties = {
                'name': cluster_name,
                'jurisdiction': cluster_data.get("jurisdiction", "unknown"),
                'date_filed': cluster_data.get("date_filed"),
                'court_id': cluster_data.get("court_id"),
                'reporter': cluster_data.get('reporter'),
                'citation_count': cluster_data.get('citation_count'),
                'precedential_status': cluster_data.get('precedential_status')
                # Add other relevant properties as needed
            }
            # Use MERGE logic, safe to call even if node exists
            create_node_success = self.neo4j.create_case_node(case_id, neo4j_properties)
            if not create_node_success:
                logger.warning(f"Failed to explicitly create/merge Neo4j node for cluster {case_id}, but proceeding.")
            else:
                logger.info(f"Ensured Neo4j node exists for cluster {case_id}")

            # Step 3: Fetch and Process Opinions for this Cluster
            opinion_results = []
            logger.info(f"Fetching opinions for cluster {case_id}...")
            try:
                opinions_response = self.court_listener.get_opinions_by_cluster(cluster_id=case_id)
                fetched_opinions = opinions_response.get('results', [])
                logger.info(f"Found {len(fetched_opinions)} opinions for cluster {case_id}. Processing...")
                
                # Process each opinion fetched for the cluster
                for opinion_data in fetched_opinions:
                    # Pass cluster_id as the 'case_id' context to process_opinion
                    opinion_result = self.process_opinion(opinion_data, case_id, jurisdiction)
                    opinion_results.append(opinion_result)
                    
            except Exception as opinion_fetch_error:
                 logger.error(f"Failed to fetch or process opinions for cluster {case_id}: {opinion_fetch_error}", exc_info=True)
                 # Continue processing the cluster record itself, but log the opinion failure
                 self.supabase.log_processing_error(case_id=case_id, batch_id=self.current_batch_id, error=f"Opinion fetch/process failed: {opinion_fetch_error}", error_context={'stage': 'fetch_opinions'}) 

            # Step 4: Process citations using our citation extractor
            # Combine text from successfully processed opinions
            all_text = ""
            successful_opinions = [op for op in opinion_results if op.get('status') == 'success']
            for opinion_result in successful_opinions:
                # Need to retrieve the text content, assuming process_opinion doesn't return it directly
                # This might require fetching from GCS or adding text to the return dict of process_opinion
                # For now, assume we can get the text somehow or adapt later.
                # Let's try fetching from GCS path if available
                gcs_path = opinion_result.get('gcs_path')
                if gcs_path:
                    try:
                        opinion_text = self.gcs.retrieve_text(gcs_path)
                        if opinion_text:
                             all_text += opinion_text + "\n\n"
                    except Exception as gcs_error:
                         logger.warning(f"Failed to retrieve text from GCS {gcs_path} for citation extraction: {gcs_error}")
                else:
                     logger.warning(f"Opinion {opinion_result.get('opinion_id')} succeeded but has no GCS path for citation extraction.")
        
            # Extract citations from the combined text of available opinions
            citations = []
            if all_text.strip():
                logger.info(f"Extracting citations for cluster {case_id} from combined opinion text ({len(all_text)} chars)...")
                citations = self.citation_extractor.extract_cited_cases(case_id, all_text, jurisdiction)
                logger.info(f"Extracted {len(citations)} potential citations for cluster {case_id}.")
            else:
                 logger.info(f"No text available from opinions of cluster {case_id} for citation extraction.")
        
            # Step 5: Store citations in Neo4j knowledge graph
            # Pass the CLUSTER data and OPINION results
            citation_results = self.process_citations(cluster_data, opinion_results, citations)
        
            # Step 6: Enhance case metadata using Week 2 capabilities
            logger.info(f"Enhancing metadata for cluster {case_id}")
            try:
                # Extract opinion data for metadata enhancement
                opinions_data = []
                for opinion_result in opinion_results:
                    if opinion_result.get('status') == 'success':
                        enhanced_result = opinion_result.get('enhanced_result', {})
                        if enhanced_result.get('original_data'):
                            opinions_data.append(enhanced_result['original_data'])

                # Use metadata enhancer to get enhanced metadata
                enhanced_metadata = self.metadata_enhancer.enhance_case_metadata(
                    cluster_data,
                    opinions_data
                )

                logger.info(f"Enhanced metadata for cluster {case_id}: "
                           f"completeness {enhanced_metadata.completeness_score:.2f}, "
                           f"quality {enhanced_metadata.data_quality}")

            except Exception as e:
                logger.error(f"Failed to enhance metadata for cluster {case_id}: {str(e)}")
                # Create minimal metadata as fallback
                from src.processing.metadata_enhancer import EnhancedCaseMetadata
                enhanced_metadata = EnhancedCaseMetadata(
                    case_id=case_id,
                    case_name=cluster_data.get('case_name', 'Unknown'),
                    jurisdiction=jurisdiction,
                    court=cluster_data.get('court', 'Unknown')
                )
                enhanced_metadata.calculate_completeness_score()

            # Step 7: Update cluster record with processing results and enhanced metadata
            # Calculate metrics based on the OPINION processing results and enhanced metadata
            quality_metrics = self._calculate_quality_metrics(opinion_results, enhanced_metadata)
            
            # Find the GCS path from the first successfully processed opinion (if any)
            first_opinion_gcs_path = None
            for opinion_result in opinion_results:
                if opinion_result.get('status') == 'success' and opinion_result.get('gcs_path'):
                    first_opinion_gcs_path = opinion_result['gcs_path']
                    break
            
            # Update the main cluster record in Supabase with enhanced metadata
            logger.info(f"Cluster {case_id}: Found first opinion GCS path: {first_opinion_gcs_path}")
            case_update_data = {
                'quality_score': enhanced_metadata.completeness_score,
                'document_quality': enhanced_metadata.data_quality,
                'metadata_quality': quality_metrics.get('metadata_quality', enhanced_metadata.data_quality),
                'last_processed_at': datetime.now().isoformat(),
                'processing_status': 'completed',
                # Enhanced metadata fields
                'case_type': enhanced_metadata.case_type,
                'subject_matter': enhanced_metadata.subject_matter,
                'outcome': enhanced_metadata.outcome,
                'precedential_status': enhanced_metadata.precedential_status,
                'citation_count': enhanced_metadata.citation_count,
                'judges': [judge.name for judge in enhanced_metadata.judges],
                'parties': [{"name": party.name, "type": party.party_type, "is_org": party.is_organization}
                           for party in enhanced_metadata.parties],
                'procedural_history': enhanced_metadata.procedural_history,
                'subsequent_history': enhanced_metadata.subsequent_history
            }
            if first_opinion_gcs_path:
                # Store the GCS path of the FIRST successful opinion as a representative path for the case/cluster
                case_update_data['gcs_path'] = first_opinion_gcs_path 
            else:
                 logger.warning(f"No successful opinion with GCS path found for cluster {case_id} to update main record.")
            
            self.supabase.update_case(case_id, case_update_data)
            
            # Step 7: Log success history
            self.supabase.log_processing_history(self.current_batch_id, case_id, 'success', 'Cluster and opinions processed')
            
            logger.info(f"Successfully processed cluster {case_id} ('{cluster_name}')")
            return {"status": "success", "case_id": case_id}
            
        except Exception as e:
            error_message = f"Error processing cluster {case_id}: {str(e)}"
            logger.exception(error_message)
            # Log error in Supabase
            self.supabase.log_processing_error(
                 batch_id=self.current_batch_id, 
                 case_id=case_id, 
                 error=error_message, 
                 traceback=traceback.format_exc(),
                 error_context={"stage": "cluster_processing"}
            )
            # Log history entry for failure
            self.supabase.log_processing_history(self.current_batch_id, case_id, 'failure', error_message)
            
            # Ensure main case record reflects the failure state
            try:
                self.supabase.update_case(case_id, {'processing_status': 'failed', 'last_processed_at': datetime.now().isoformat()})
            except Exception as update_err:
                 logger.error(f"Failed to update cluster {case_id} status to failed after error: {update_err}")

            return {"status": "failure", "case_id": case_id, "error": str(e)}

    def process_opinion(self, opinion_data: Dict, case_id: str, jurisdiction: str) -> Dict:
        """
        Process a single opinion with enhanced metadata extraction and storage.

        Args:
            opinion_data: Raw opinion data from Court Listener
            case_id: The case/cluster ID this opinion belongs to
            jurisdiction: Jurisdiction code

        Returns:
            Dict with processing result and metadata
        """
        opinion_id = str(opinion_data.get("id", "unknown"))

        try:
            logger.info(f"Processing opinion {opinion_id} for case {case_id}")

            # Use enhanced opinion processor for Week 2 capabilities
            enhanced_result = self.enhanced_opinion_processor.process_opinion(
                opinion_data,
                case_context={"case_id": case_id, "jurisdiction": jurisdiction}
            )

            # Extract processed data
            processed_text = enhanced_result.get("processed_text", "")
            metadata = enhanced_result.get("metadata", {})
            citations = enhanced_result.get("citations", [])

            # Store opinion text in GCS if we have content
            gcs_path = None
            if processed_text:
                try:
                    # Create GCS path for opinion
                    gcs_path = f"opinions/{jurisdiction}/{case_id}/{opinion_id}.txt"
                    self.gcs.store_text(processed_text, gcs_path)
                    logger.info(f"Stored opinion {opinion_id} text in GCS: {gcs_path}")
                except Exception as e:
                    logger.error(f"Failed to store opinion {opinion_id} in GCS: {str(e)}")

            # Store opinion metadata in Supabase
            opinion_record = {
                "id": opinion_id,
                "case_id": case_id,
                "opinion_type": metadata.get("opinion_type", "unknown"),
                "author": metadata.get("author"),
                "gcs_path": gcs_path,
                "has_text": bool(processed_text),
                "word_count": metadata.get("word_count", 0),
                "citation_count": metadata.get("citation_count", 0),
                "quality": metadata.get("quality", "unknown"),
                "procedural_posture": metadata.get("procedural_posture"),
                "outcome": metadata.get("outcome"),
                "key_topics": metadata.get("key_topics", []),
                "joining_judges": metadata.get("joining_judges", []),
                "jurisdiction": jurisdiction,
                "processed_at": datetime.now().isoformat(),
                "processor_version": enhanced_result.get("processor_version", "enhanced_v1.0")
            }

            # Store in Supabase
            self.supabase.store_opinion(opinion_record)

            # Store opinion embeddings in Pinecone if we have text
            if processed_text and len(processed_text.strip()) > 50:
                try:
                    # Create namespace for jurisdiction
                    namespace = f"{jurisdiction}-opinions"

                    # Store in vector database
                    self.pinecone.store_document(
                        doc_id=opinion_id,
                        text=processed_text,
                        metadata={
                            "case_id": case_id,
                            "opinion_type": metadata.get("opinion_type"),
                            "author": metadata.get("author"),
                            "jurisdiction": jurisdiction,
                            "word_count": metadata.get("word_count", 0),
                            "quality": metadata.get("quality")
                        },
                        namespace=namespace
                    )
                    logger.info(f"Stored opinion {opinion_id} embeddings in Pinecone namespace: {namespace}")
                except Exception as e:
                    logger.error(f"Failed to store opinion {opinion_id} embeddings: {str(e)}")

            # Create Neo4j opinion node and relationships
            if self.neo4j:
                try:
                    # Create opinion node
                    opinion_properties = {
                        "id": opinion_id,
                        "case_id": case_id,
                        "opinion_type": metadata.get("opinion_type"),
                        "author": metadata.get("author"),
                        "word_count": metadata.get("word_count", 0),
                        "quality": metadata.get("quality"),
                        "jurisdiction": jurisdiction
                    }

                    self.neo4j.create_opinion_node(opinion_id, opinion_properties)

                    # Create relationship to case
                    self.neo4j.create_relationship(
                        case_id, opinion_id, "HAS_OPINION",
                        {"opinion_type": metadata.get("opinion_type")}
                    )

                    logger.info(f"Created Neo4j nodes and relationships for opinion {opinion_id}")
                except Exception as e:
                    logger.error(f"Failed to create Neo4j nodes for opinion {opinion_id}: {str(e)}")

            return {
                "status": "success",
                "opinion_id": opinion_id,
                "case_id": case_id,
                "gcs_path": gcs_path,
                "metadata": metadata,
                "citations": citations,
                "enhanced_result": enhanced_result
            }

        except Exception as e:
            error_msg = f"Failed to process opinion {opinion_id}: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Log error
            if self.current_batch_id:
                self.supabase.log_processing_error(
                    batch_id=self.current_batch_id,
                    case_id=case_id,
                    error=error_msg,
                    error_context={"stage": "opinion_processing", "opinion_id": opinion_id}
                )

            return {
                "status": "failure",
                "opinion_id": opinion_id,
                "case_id": case_id,
                "error": str(e)
            }

    def _calculate_quality_metrics(self, opinion_results: List[Dict], enhanced_metadata=None) -> Dict[str, Any]:
        """
        Calculate quality metrics for a case based on opinion processing results and enhanced metadata.

        Args:
            opinion_results: List of opinion processing results
            enhanced_metadata: Enhanced case metadata object

        Returns:
            Dictionary with quality metrics
        """
        total_opinions = len(opinion_results)
        successful_opinions = len([op for op in opinion_results if op.get('status') == 'success'])

        # Calculate opinion processing success rate
        opinion_success_rate = successful_opinions / total_opinions if total_opinions > 0 else 0

        # Calculate average opinion quality
        opinion_qualities = []
        total_word_count = 0
        total_citations = 0

        for opinion_result in opinion_results:
            if opinion_result.get('status') == 'success':
                metadata = opinion_result.get('metadata', {})
                quality = metadata.get('quality', 'poor')

                # Convert quality to numeric score
                quality_score = {
                    'high': 4,
                    'medium': 3,
                    'low': 2,
                    'poor': 1
                }.get(quality, 1)

                opinion_qualities.append(quality_score)
                total_word_count += metadata.get('word_count', 0)
                total_citations += metadata.get('citation_count', 0)

        avg_opinion_quality = sum(opinion_qualities) / len(opinion_qualities) if opinion_qualities else 1

        # Calculate overall completeness score
        completeness_score = 0.0
        if enhanced_metadata:
            completeness_score = enhanced_metadata.completeness_score
        else:
            # Fallback calculation based on opinion data
            completeness_score = opinion_success_rate * 0.7 + (avg_opinion_quality / 4) * 0.3

        # Determine document quality level
        if completeness_score >= 0.8:
            document_quality = "high"
        elif completeness_score >= 0.6:
            document_quality = "medium"
        elif completeness_score >= 0.4:
            document_quality = "low"
        else:
            document_quality = "poor"

        # Determine metadata quality level
        metadata_quality = "medium"  # Default
        if enhanced_metadata:
            metadata_quality = enhanced_metadata.data_quality
        elif opinion_success_rate >= 0.8:
            metadata_quality = "high"
        elif opinion_success_rate >= 0.5:
            metadata_quality = "medium"
        else:
            metadata_quality = "low"

        return {
            "completeness_score": completeness_score,
            "document_quality": document_quality,
            "metadata_quality": metadata_quality,
            "opinion_success_rate": opinion_success_rate,
            "total_opinions": total_opinions,
            "successful_opinions": successful_opinions,
            "avg_opinion_quality": avg_opinion_quality,
            "total_word_count": total_word_count,
            "total_citations": total_citations
        }

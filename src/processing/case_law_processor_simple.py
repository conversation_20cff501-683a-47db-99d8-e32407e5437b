"""
Simplified Case Law Processing System
Minimal version for testing and development
"""

import os
import uuid
import json
import logging
import time
import traceback
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# Configure logging
os.makedirs("logs", exist_ok=True)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class CaseLawProcessor:
    """
    Main orchestrator for case law processing.
    Simplified version with core functionality only.
    """
    
    def __init__(self, config: Optional[Dict] = None, user_id: Optional[str] = None, user_role: Optional[str] = None, tenant_id: Optional[str] = None):
        """
        Initialize the case law processor.
        
        Args:
            config: Optional configuration dictionary to override defaults
            user_id: Optional user ID for access control and audit
            user_role: Optional user role for permission checks (partner, attorney, paralegal, staff, client)
            tenant_id: Optional tenant ID for multi-tenant isolation
        """
        self.config = {
            "gcs_bucket": os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"),
            "pinecone_index": os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large"),
            "batch_size": 10,
            "max_workers": int(os.getenv("MAX_WORKERS", "4")),
            "default_jurisdiction": "tx",
            "allowed_jurisdictions": os.getenv("ALLOWED_JURISDICTIONS", "tx,ca,fed").split(","),
            **(config or {})
        }
        
        # User information for access control
        self.user_id = user_id
        self.user_role = user_role
        self.tenant_id = tenant_id
        
        # Role-based jurisdiction access mapping - using lists instead of sets
        self.role_jurisdiction_map = {
            "partner": ["tx", "ca", "fed"],  # Partners can access all jurisdictions
            "attorney": ["tx"],              # Attorneys have limited access
            "paralegal": ["tx"],             # Paralegals have limited access
            "staff": ["tx"],                 # Staff has limited access
            "client": ["tx"]                 # Clients have limited access
        }
        
        # Initialize tracking variables
        self.current_batch_id = None
        self.current_batch_stats = {
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "processed_case_ids": []  # Use a list instead of a set
        }
        
        # Initialize connectors when needed
        self._initialize_connectors()
    
    def _initialize_connectors(self):
        """Initialize connectors to external systems lazily"""
        # These will be initialized when accessed via properties
        self._neo4j = None
        self._gcs = None
        self._pinecone = None
        self._supabase = None
        self._citation_extractor = None
        self._court_listener = None
    
    @property
    def neo4j(self):
        """Lazy-loaded Neo4j connector"""
        if not hasattr(self, '_neo4j_thread_local'):
            from threading import local
            self._neo4j_thread_local = local()
            self._neo4j_thread_local.connection = None
            
        if not self._neo4j_thread_local.connection:
            from .storage.neo4j_connector import Neo4jConnector
            self._neo4j_thread_local.connection = Neo4jConnector()
            
        return self._neo4j_thread_local.connection
    
    @property
    def gcs(self):
        """Lazy-loaded GCS connector"""
        if not self._gcs:
            from .storage.gcs_connector import GCSConnector
            self._gcs = GCSConnector(bucket_name=self.config["gcs_bucket"])
        return self._gcs
    
    @property
    def pinecone(self):
        """Lazy-loaded Pinecone connector"""
        if not self._pinecone:
            from .storage.pinecone_connector import PineconeConnector
            self._pinecone = PineconeConnector(index_name=self.config["pinecone_index"])
        return self._pinecone
    
    @property
    def supabase(self):
        """Lazy-loaded Supabase connector"""
        if not self._supabase:
            from .storage.supabase_connector import SupabaseConnector
            # Only pass the table creation flag - user/role info is handled elsewhere
            self._supabase = SupabaseConnector(ensure_tables_exist=False)
        return self._supabase
    
    @property
    def citation_extractor(self):
        """Lazy-loaded citation extractor"""
        if not self._citation_extractor:
            from .citation_extractor import CitationExtractor
            self._citation_extractor = CitationExtractor()
        return self._citation_extractor
    
    @property
    def court_listener(self):
        """Lazy-loaded Court Listener connector"""
        if not self._court_listener:
            from .providers.court_listener import CourtListenerConnector
            self._court_listener = CourtListenerConnector()
        return self._court_listener
    
    def get_court_ids_for_jurisdiction(self, jurisdiction: str) -> List[str]:
        """
        Get the court IDs relevant for a jurisdiction
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            List of court IDs
        """
        # Default mapping for Texas courts
        court_mappings = {
            "tx": ["tex", "texapp", "texcrimapp", "texjpml"],
            "ca": ["cal", "calctapp", "calappdeptsuperct"],
            "fed": ["scotus", "f5", "f5district"]
        }
        return court_mappings.get(jurisdiction, [])
    
    def process_jurisdiction(self, jurisdiction: str, query: str = "", count: int = 100, source: str = "court_listener") -> Dict:
        """
        Process cases for a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tx", "ca")
            query: Search query for filtering cases
            count: Maximum number of cases to process
            source: Data source to use ("court_listener" or "findlaw")
            
        Returns:
            Dict with processing statistics or error message
        """
        # Check if user can access this jurisdiction
        if self.user_role and self.user_role not in ["admin", "system"]:
            allowed_jurisdictions = self.role_jurisdiction_map.get(self.user_role, [])
            if jurisdiction not in allowed_jurisdictions:
                return {"error": f"User with role {self.user_role} cannot access jurisdiction {jurisdiction}"}
        
        # 1. Create a processing batch
        batch_start_time = time.time()
        self.current_batch_id = str(uuid.uuid4())
        
        # 2. Initialize batch stats
        self.current_batch_stats = {
            "total": 0,
            "success": 0,
            "failure": 0,
            "skipped": 0,
            "processed_case_ids": []  # Use a list instead of a set
        }
        
        all_clusters = []
        processed_cluster_ids = []  # Use a list instead of a set
        
        try:
            # 3. Get relevant court IDs for the jurisdiction
            court_ids = self.get_court_ids_for_jurisdiction(jurisdiction)
            if not court_ids:
                return {"error": f"No court IDs configured for jurisdiction {jurisdiction}"}
            
            # 4. Calculate how many clusters to fetch per court
            fetch_count_per_court = max(5, (count // len(court_ids)) + 5)  # Add buffer
            
            # 5. Fetch top clusters for each court
            total_fetched = 0
            logger.info(f"Fetching up to {fetch_count_per_court} clusters for each of {len(court_ids)} courts in {jurisdiction}")
            
            for court_id in court_ids:
                try:
                    logger.debug(f"Fetching top {fetch_count_per_court} clusters for court {court_id}...")
                    cluster_results = self.court_listener.get_top_clusters(court_id=court_id, count=fetch_count_per_court)
                    fetched_opinions = cluster_results.get('results', [])
                    logger.debug(f"Fetched {len(fetched_opinions)} clusters for court {court_id}.")
                    
                    # Add unique clusters to the main list
                    for cluster_data in fetched_opinions:
                        cluster_id = cluster_data.get('id')
                        if cluster_id and cluster_id not in processed_cluster_ids:
                            all_clusters.append(cluster_data)
                            processed_cluster_ids.append(cluster_id)  # Append to list
                            
                except Exception as e:
                    logger.error(f"Failed to fetch clusters for court {court_id}: {e}", exc_info=True)
                    continue
            
            logger.info(f"Total clusters fetched across all courts: {len(all_clusters)}")
            
            # 6. Sort clusters by citation count and take top 'count'
            all_clusters.sort(key=lambda x: x.get('citation_count', 0), reverse=True)
            top_clusters_to_process = all_clusters[:min(count, len(all_clusters))]
            
            logger.info(f"Processing top {len(top_clusters_to_process)} clusters for {jurisdiction}...")
            self.current_batch_stats["total"] = len(top_clusters_to_process)
            
            # Create batch record
            self.supabase.create_processing_batch({
                "batch_id": self.current_batch_id,
                "jurisdiction": jurisdiction,
                "query_params": {"query": query},
                "source": source,
                "user_id": self.user_id,
                "total": len(top_clusters_to_process),
                "start_time": datetime.utcnow().isoformat(),
                "status": "processing"
            })
            
            # Process clusters with thread pool
            with ThreadPoolExecutor(max_workers=self.config["max_workers"]) as executor:
                # Submit all tasks
                futures = {executor.submit(
                    self._process_case_safe, case_data, jurisdiction
                ): case_data.get('id') for case_data in top_clusters_to_process}
                
                # Collect results
                for future in as_completed(futures):
                    cluster_id = futures[future]
                    try:
                        result = future.result()
                        if result == "success":
                            self.current_batch_stats["success"] += 1
                            self.current_batch_stats["processed_case_ids"].append(cluster_id)  # Append to list
                        elif result == "skipped":
                            self.current_batch_stats["skipped"] += 1
                        else:  # Covers "failure" and potentially other states
                            self.current_batch_stats["failure"] += 1
                    except Exception as exc:
                        logger.error(f'Cluster {cluster_id} generated an exception: {exc}', exc_info=True)
                        self.current_batch_stats["failure"] += 1
            
            # 7. Complete the batch
            status = "completed" if self.current_batch_stats["failure"] == 0 else "completed_with_errors"
            self.complete_batch(self.current_batch_id, status)
            logger.info(f"Completed batch {self.current_batch_id} for jurisdiction {jurisdiction}. Status: {status}")
        
        except Exception as e:
            logger.error(f"Unhandled error during jurisdiction processing for {jurisdiction}: {e}", exc_info=True)
            if self.current_batch_id:
                self.complete_batch(self.current_batch_id, "failed", error=str(e))
            return {"error": f"Processing failed for jurisdiction {jurisdiction}: {str(e)}"}
            
        finally:
            batch_duration = time.time() - batch_start_time
            logger.info(f"Batch {self.current_batch_id} took {batch_duration:.2f} seconds.")
            # Clean up thread-local Neo4j connections if they were created
            if hasattr(self, '_neo4j_thread_local') and hasattr(self._neo4j_thread_local, 'connection') and self._neo4j_thread_local.connection:
                 self.neo4j.close()  # Use the close method of the property
        
        return self.get_batch_stats()
    
    def _process_case_safe(self, case_data: Dict, jurisdiction: str) -> str:
        """
        Safely process a single case, handling and logging any exceptions.
        
        Args:
            case_data: Case data to process
            jurisdiction: Jurisdiction code
            
        Returns:
            Processing status string: "success", "failure", or "skipped"
        """
        case_id = case_data.get('id')
        try:
            result = self.process_case(case_data, jurisdiction)
            if not result:
                return "skipped"
            return "success"
        except Exception as e:
            logger.error(f"Error processing case {case_id}: {e}", exc_info=True)
            return "failure"
    
    def process_case(self, case_data: Dict, jurisdiction: str) -> Optional[Dict]:
        """
        Process a single case through the entire pipeline:
        1. Store case metadata
        2. Process opinions
        3. Extract and store citations
        4. Update case status
        
        Args:
            case_data: Case data dictionary from the API
            jurisdiction: Jurisdiction code
            
        Returns:
            Processed case data or None if skipped
        """
        cluster_id = case_data.get('id')
        if not cluster_id:
            logger.error("Missing cluster ID in case data")
            return None
        
        try:
            # 1. Extract and validate basic metadata
            logger.info(f"Processing cluster {cluster_id}")
            
            try:
                case_name = case_data.get('case_name', '')
                court = case_data.get('court', {}).get('id', '')
                date_filed = case_data.get('date_filed')
                
                # Validate required fields
                if not (case_name and court and date_filed):
                    logger.error(f"Cluster {cluster_id} missing required fields")
                    return None
                
                # Convert dates to ISO format if needed
                if isinstance(date_filed, datetime):
                    date_filed = date_filed.isoformat()
                
                # 2. Store case metadata in Supabase
                # Format the case data to match what store_case expects
                case_insert_data = {
                    'id': cluster_id,
                    'case_name': case_name,  # store_case expects 'case_name', not 'name'
                    'case_name_full': case_data.get('case_name_full', ''),
                    'court_id': court,
                    'jurisdiction': jurisdiction,
                    'date_filed': date_filed,
                    'citation_count': case_data.get('citation_count', 0),
                    'precedential': case_data.get('precedential_status', 'Published') == 'Published',
                    'docket_id': case_data.get('docket', {}).get('id') or case_data.get('docket_id'),
                    'source': 'court_listener',
                    'source_id': f"cl_{cluster_id}",  # Format expected by store_case
                    'status': 'processing',  # Using status instead of processing_status
                    'cluster_id': cluster_id,  # Store the original cluster ID
                    'metadata_quality': 'Basic',
                    'document_quality': 'Standard',
                    'source_url': case_data.get('resource_uri') or case_data.get('absolute_url', '')
                }
                
                # Check if the case already exists
                existing_case = self.supabase.get_case(cluster_id)
                
                if existing_case and existing_case.get('processing_status') == 'completed':
                    logger.info(f"Cluster {cluster_id} already processed completely, skipping")
                    return {"status": "skipped", "case_id": cluster_id}
                
                # Ensure we have a batch ID
                if not self.current_batch_id:
                    logger.warning(f"No current batch ID found, generating one for case {cluster_id}")
                    self.current_batch_id = str(uuid.uuid4())
                    
                # Add detailed logging for debugging
                logger.info(f"Storing case {cluster_id} in batch {self.current_batch_id} with data: {json.dumps(case_insert_data, default=str)[:200]}...")
                
                try:
                    # Store the case data in Supabase
                    stored_case = self.supabase.store_case(case_insert_data, self.current_batch_id)
                    if stored_case:
                        logger.info(f"Successfully stored case record for {cluster_id} in Supabase")
                    else:
                        logger.error(f"Failed to store case record for {cluster_id} - no exception but no result returned")
                        return {"status": "failure", "case_id": cluster_id, "error": "Failed to store case in Supabase"}
                except Exception as e:
                    logger.error(f"Exception storing case {cluster_id} in Supabase: {str(e)}", exc_info=True)
                    return {"status": "failure", "case_id": cluster_id, "error": f"Exception storing case: {str(e)}"}
                
                # 3. Create case node in Neo4j
                try:
                    # Ensure we're passing the correct ID format to Neo4j
                    neo4j_case_data = case_insert_data.copy()
                    
                    # For Neo4j, we need to use a consistent ID format
                    # Always use cl_prefix for IDs from Court Listener
                    neo4j_id = f"cl_{cluster_id}"
                    neo4j_case_data["id"] = neo4j_id  # Use prefixed ID as primary key
                    neo4j_case_data["original_id"] = cluster_id  # Store original ID for reference
                    neo4j_case_data["source_id"] = neo4j_id  # Add source_id for cross-referencing
                    
                    # Extract the year from date_filed if available
                    if "date_filed" in neo4j_case_data and "year" not in neo4j_case_data:
                        try:
                            if isinstance(neo4j_case_data["date_filed"], str):
                                neo4j_case_data["year"] = int(neo4j_case_data["date_filed"].split("-")[0])
                        except Exception:
                            # Set current year as fallback
                            neo4j_case_data["year"] = datetime.now().year
                    
                    # Set document type if not present
                    if "doc_type" not in neo4j_case_data:
                        neo4j_case_data["doc_type"] = "case"
                    
                    # Flatten the data for Neo4j compatibility
                    from src.processing.storage.neo4j_helper import flatten_for_neo4j
                    neo4j_case_data = flatten_for_neo4j(neo4j_case_data)
                    
                    # Make final checks on required fields
                    if "name" not in neo4j_case_data and "case_name" in neo4j_case_data:
                        neo4j_case_data["name"] = neo4j_case_data["case_name"]
                        
                    # Explicitly ensure ID is correctly set
                    neo4j_case_data["id"] = neo4j_id
                    
                    # Log the flattened data for debugging
                    logger.info(f"Creating Neo4j node with flattened data: {json.dumps(neo4j_case_data, default=str)[:300]}...")
                    
                    # Create the case node in Neo4j
                    self.neo4j.create_case(neo4j_case_data)
                    logger.info(f"Created Neo4j case node for {cluster_id} with ID {neo4j_id}")
                except Exception as e:
                    logger.error(f"Failed to create Neo4j node for case {cluster_id}: {str(e)}", exc_info=True)
                    # Log the details to help with debugging
                    logger.debug(f"Neo4j case data: {json.dumps(neo4j_case_data, default=str)[:500]}...")
                
                # 4. Store case text in GCS using the improved helper module
                try:
                    # Import the GCS helper module
                    from src.processing.storage.gcs_helper import store_case_document, store_case_json
                    
                    # Extract case text from various available fields
                    case_text = case_data.get("caseSummary", "") or case_data.get("case_text", "")
                    
                    if not case_text and case_data.get("html"):
                        # Extract text from HTML if available
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(case_data["html"], "html.parser")
                        case_text = soup.get_text(" ", strip=True)
                    
                    if not case_text:
                        # If we still don't have text, use the case name and date as minimal content
                        case_text = f"Case: {case_name}\nDate: {date_filed}\nCourt: {court}\nJurisdiction: {jurisdiction}"
                    
                    # Ensure we have text content to store
                    if not case_text.strip():
                        logger.warning(f"No text content found for case {cluster_id}, using placeholder")
                        case_text = f"[No text available for case {cluster_id} - {case_name}]"
                    
                    # Extract year from date_filed if available for better organization
                    year = None
                    if date_filed:
                        try:
                            if isinstance(date_filed, str):
                                year = date_filed.split("-")[0]
                            elif hasattr(date_filed, 'year'):
                                year = str(date_filed.year)
                        except Exception:
                            # Use default year handling in helper
                            pass
                    
                    # Store the full text using our helper function
                    # This ensures consistent path formatting and error handling
                    gcs_text_path = store_case_document(
                        case_id=cluster_id,
                        content=case_text,
                        jurisdiction=jurisdiction,
                        doc_type="full_text",
                        year=year
                    )
                    
                    # Also store the raw metadata as JSON for full data preservation
                    gcs_metadata_path = store_case_json(
                        case_id=cluster_id,
                        data=cluster_data,  # Store the original cluster data
                        jurisdiction=jurisdiction,
                        doc_type="metadata",
                        year=year
                    )
                    
                    logger.info(f"Successfully stored case text ({len(case_text)} chars) at {gcs_text_path}")
                    logger.info(f"Successfully stored case metadata at {gcs_metadata_path}")
                    
                    # Get bucket name for creating the full URLs
                    gcs_bucket = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
                    
                    # Update the Supabase record with both GCS paths
                    gcs_data = {
                        "text_path": gcs_text_path,
                        "text_url": f"gs://{gcs_bucket}/{gcs_text_path}",
                        "metadata_path": gcs_metadata_path,
                        "metadata_url": f"gs://{gcs_bucket}/{gcs_metadata_path}"
                    }
                    
                    self.supabase.update_case(cluster_id, gcs_data, self.current_batch_id)
                    logger.info(f"Updated Supabase case record with GCS paths")
                    
                    # 5. Create vector embeddings in Pinecone
                    try:
                        # Use consistent ID format with cl_ prefix for Pinecone
                        pinecone_id = f"cl_{cluster_id}"
                        
                        # Extract year for better organization if available
                        year = None
                        if date_filed:
                            try:
                                if isinstance(date_filed, str):
                                    year = date_filed.split("-")[0]
                                elif hasattr(date_filed, 'year'):
                                    year = str(date_filed.year)
                            except Exception:
                                pass
                        
                        # Create embedding for the case text with consistent ID format and improved metadata
                        embedding_data = {
                            "case_id": pinecone_id,  # Use consistent ID format
                            "text": case_text[:8000],  # Increased text limit for better embedding quality
                            "metadata": {
                                # Case identification information
                                "case_id": pinecone_id,
                                "original_id": cluster_id,
                                "source_id": pinecone_id,
                                "case_name": case_name,
                                
                                # Jurisdiction and court information
                                "jurisdiction": jurisdiction,
                                "court_id": court,
                                
                                # Temporal information
                                "date_filed": date_filed,
                                "year": year,
                                "processing_time": datetime.utcnow().isoformat(),
                                
                                # Document classification
                                "document_type": "case_summary",
                                "precedential": case_data.get('precedential_status', 'Published') == 'Published',
                                "citation_count": case_data.get('citation_count', 0),
                                
                                # Storage locations for cross-referencing
                                "text_path": gcs_text_path,
                                "metadata_path": gcs_metadata_path,
                                "batch_id": self.current_batch_id,
                                
                                # For user-specific access control
                                "role_access": ["partner", "attorney", "paralegal", "staff"],
                            }
                        }
                        
                        # Create a unique embedding ID with timestamp for versioning
                        embedding_id = f"{pinecone_id}_summary"
                        
                        # Store in Pinecone with detailed logging
                        logger.info(f"Indexing document in Pinecone with ID: {embedding_id}")
                        self.pinecone.index_document(embedding_id, embedding_data)
                        logger.info(f"Successfully created vector embedding for case {cluster_id} with ID {embedding_id}")
                        
                        # Update Supabase to indicate vector embedding was created
                        self.supabase.update_case(cluster_id, {"vector_embedding_id": embedding_id}, self.current_batch_id)
                    except Exception as e:
                        logger.error(f"Failed to create Pinecone embedding for case {cluster_id}: {str(e)}", exc_info=True)
                        # Log the embedding data for debugging
                        logger.debug(f"Embedding data: {json.dumps(embedding_data, default=str)[:500]}...")
                except Exception as e:
                    logger.error(f"Failed to store case text in GCS for {cluster_id}: {str(e)}", exc_info=True)
                
                # 6. Fetch and process opinions
                logger.info(f"Fetching opinions for cluster {cluster_id}")
                opinion_results = self.court_listener.get_opinions_by_cluster(cluster_id)
                opinions = opinion_results.get('results', [])
                logger.info(f"Found {len(opinions)} opinions for cluster {cluster_id}")
                
                opinion_texts = []
                processed_opinions = []
                
                # 5. Process each opinion
                for opinion in opinions:
                    try:
                        result = self.process_opinion(opinion, cluster_id, jurisdiction)
                        processed_opinions.append(result)
                        
                        # Collect opinion texts for citation extraction
                        if opinion.get('plain_text'):
                            opinion_texts.append(opinion.get('plain_text'))
                            
                    except Exception as e:
                        logger.error(f"Error processing opinion {opinion.get('id')}: {e}", exc_info=True)
                
                # 6. Extract citations from opinion texts
                all_citations = []
                for text in opinion_texts:
                    try:
                        citations = self.citation_extractor.extract_citations(text)
                        all_citations.extend(citations)
                    except Exception as e:
                        logger.error(f"Error extracting citations from opinion: {e}", exc_info=True)
                
                # 7. De-duplicate citations
                unique_citation_texts = {}
                for citation in all_citations:
                    if citation.get('text') not in unique_citation_texts:
                        unique_citation_texts[citation.get('text')] = citation
                
                # 8. Store citations in Neo4j
                citation_count = 0
                for citation in unique_citation_texts.values():
                    try:
                        # Process citations only if both IDs are present
                        # Use consistent ID format with cl_ prefix for Neo4j
                        citing_id = f"cl_{cluster_id}"
                        cited_id = citation.get('cited_case_id')
                        
                        # Add cl_ prefix to cited_id if needed
                        if cited_id and not cited_id.startswith('cl_'):
                            cited_id = f"cl_{cited_id}"
                        
                        if citing_id and cited_id:
                            self.neo4j.create_citation(
                                citing_id=citing_id,
                                cited_id=cited_id,
                                citation_text=citation.get('text'),
                                metadata={
                                    'jurisdiction': jurisdiction,
                                    'citation_type': citation.get('type', 'general'),
                                    'batch_id': self.current_batch_id,
                                    'citing_case_name': case_name,
                                    'citation_date': datetime.utcnow().isoformat()
                                }
                            )
                            citation_count += 1
                            logger.info(f"Created Neo4j citation from {citing_id} to {cited_id}")
                        else:
                            logger.warning(f"Skipped citation with missing ID - citing: {citing_id}, cited: {cited_id}")
                    except Exception as e:
                        logger.error(f"Error storing citation {citation.get('text')}: {e}", exc_info=True)
                
                logger.info(f"Stored {citation_count} citations for cluster {cluster_id}")
                
                # 9. Update case status to completed
                self.supabase.update_case(cluster_id, {
                    'processing_status': 'completed',
                    'last_processed_at': datetime.utcnow().isoformat(),
                    'opinion_count': len(processed_opinions),
                    'citation_count': citation_count
                })
                
                logger.info(f"Successfully processed cluster {cluster_id} with {len(processed_opinions)} opinions and {citation_count} citations")
                
                return {
                    "status": "success",
                    "case_id": cluster_id,
                    "opinion_count": len(processed_opinions),
                    "citation_count": citation_count
                }
                
            except Exception as e:
                error_message = f"Error processing cluster {cluster_id}: {str(e)}"
                logger.error(error_message, exc_info=True)
                
                # Log error in Supabase
                self.supabase.log_processing_error(
                    batch_id=self.current_batch_id, 
                    case_id=cluster_id, 
                    error=error_message, 
                    traceback=traceback.format_exc(),
                    error_context={"stage": "cluster_processing"}
                )
                # Log history entry for failure
                self.supabase.log_processing_history(self.current_batch_id, cluster_id, 'failure', error_message)
                
                # Ensure main case record reflects the failure state
                try:
                    self.supabase.update_case(cluster_id, {'processing_status': 'failed', 'last_processed_at': datetime.utcnow().isoformat()})
                except Exception as update_err:
                    logger.error(f"Failed to update cluster {cluster_id} status to failed after error: {update_err}")
                
                return {"status": "failure", "case_id": cluster_id, "error": str(e)}
                
        except Exception as e:
            logger.error(f"Unhandled exception in process_case for {cluster_id}: {e}", exc_info=True)
            return {"status": "failure", "case_id": cluster_id, "error": str(e)}
    
    def process_opinion(self, opinion_data: Dict, case_id: str, jurisdiction: str) -> Dict:
        """Minimal placeholder for processing opinions"""
        return {"status": "success", "opinion_id": opinion_data.get('id')}
    
    def complete_batch(self, batch_id: str, status: str, error: Optional[str] = None) -> bool:
        """Minimal placeholder for completing batches"""
        return True
    
    def get_batch_stats(self) -> Dict:
        """Get the current batch statistics"""
        return self.current_batch_stats

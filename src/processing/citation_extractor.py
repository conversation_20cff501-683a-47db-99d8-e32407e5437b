"""
Citation Extractor

Extracts legal citations from case text and builds relationships between cases.
Supports multiple citation formats and multi-jurisdiction processing.
"""

import re
import logging
from typing import Dict, List, Optional, Set, Union, Any
from dataclasses import dataclass
import requests

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class Citation:
    """Structured representation of a legal citation."""
    text: str
    volume: Optional[str] = None
    reporter: Optional[str] = None
    page: Optional[str] = None
    court: Optional[str] = None
    year: Optional[str] = None
    jurisdiction: Optional[str] = None
    case_name: Optional[str] = None
    confidence: float = 1.0

class CitationExtractor:
    """
    Extracts citations from legal text and classifies them.
    
    Supports multiple citation formats:
    - Standard reporter citations (e.g., 347 U.S. 483)
    - Neutral citations (e.g., 2022 TX 12)
    - Parallel citations
    - State-specific formats
    """
    
    def __init__(self, use_court_listener_api: bool = True, api_key: Optional[str] = None):
        """
        Initialize the citation extractor.
        
        Args:
            use_court_listener_api: Whether to use the Court Listener API for citation extraction
            api_key: Court Listener API key (required if use_court_listener_api=True)
        """
        self.use_court_listener_api = use_court_listener_api
        self.api_key = api_key
        
        # Map of reporter abbreviations to jurisdictions
        self.reporter_jurisdiction_map = {
            # Federal reporters
            "U.S.": "fed",
            "S. Ct.": "fed",
            "L. Ed.": "fed",
            "L. Ed. 2d": "fed",
            "F.": "fed",
            "F.2d": "fed",
            "F.3d": "fed",
            "F. Supp.": "fed",
            "F. Supp. 2d": "fed",
            "F. Supp. 3d": "fed",
            "F.R.D.": "fed",
            
            # Texas reporters
            "Tex.": "tx",
            "S.W.": "tx",
            "S.W.2d": "tx",
            "S.W.3d": "tx",
            "S.W.4d": "tx",
            "Tex. App.": "tx",
            "Tex. Crim. App.": "tx",
            "Tex. Sup. Ct. J.": "tx",
            
            # California reporters
            "Cal.": "ca",
            "Cal. 2d": "ca",
            "Cal. 3d": "ca",
            "Cal. 4th": "ca",
            "Cal. 5th": "ca",
            "Cal. App.": "ca",
            "Cal. App. 2d": "ca",
            "Cal. App. 3d": "ca",
            "Cal. App. 4th": "ca",
            "Cal. App. 5th": "ca",
            "Cal. Rptr.": "ca",
            "Cal. Rptr. 2d": "ca",
            "Cal. Rptr. 3d": "ca",
            "P.": "ca",
            "P.2d": "ca",
            "P.3d": "ca",
        }
        
        # Common reporter patterns for regex matching
        self.citation_patterns = [
            # Standard reporter pattern: Volume Reporter Page (Year)
            # e.g., 347 U.S. 483 (1954)
            r'(\d+)\s+(U\.S\.|S\.\s*Ct\.|L\.\s*Ed\.(?:\s*2d)?|F\.(?:3d|2d|Supp\.(?:\s*[23]d)?)?|'\
            r'S\.W\.(?:[234]d)?|Cal\.(?:\s*[2345](?:th|d))?|Cal\.\s*App\.(?:\s*[2345](?:th|d))?|'\
            r'Cal\.\s*Rptr\.(?:\s*[23]d)?|P\.(?:[23]d)?|Tex\.(?:\s*App\.)?|Tex\.\s*Crim\.\s*App\.|'\
            r'[A-Z][a-z]+\.\s*[A-Z][a-z]+\.(?:\s*[23]d)?)\s+(\d+)(?:\s*\((\d{4})\))?',
            
            # Neutral citation pattern: Year Jurisdiction Number
            # e.g., 2022 TX 12, 2020 CA 45
            r'(20\d{2})\s+([A-Z]{2,4})\s+(\d+)',
            
            # Case name with citation pattern
            # e.g., Brown v. Board of Education, 347 U.S. 483 (1954)
            r'([A-Z][a-z]+(?:\s+(?:v\.|and|&)\s+[A-Z][a-z]+)?),\s+(\d+)\s+([A-Za-z\.\s]+)\s+(\d+)(?:\s*\((\d{4})\))?'
        ]
    
    def extract_citations(self, text: str) -> List[Citation]:
        """
        Extract citations from legal text.
        
        Args:
            text: Legal text to extract citations from
            
        Returns:
            List of Citation objects
        """
        if self.use_court_listener_api and self.api_key:
            # Try to use Court Listener API first
            citations = self._extract_with_court_listener(text)
            if citations:
                return citations
        
        # Fall back to regex-based extraction
        return self._extract_with_regex(text)
    
    def _extract_with_court_listener(self, text: str) -> List[Citation]:
        """
        Extract citations using the Court Listener API.
        
        Args:
            text: Legal text to extract citations from
            
        Returns:
            List of Citation objects
        """
        try:
            # Call Court Listener citation extraction API
            response = requests.post(
                "https://www.courtlistener.com/api/rest/v3/citations/extract/",
                headers={
                    "Authorization": f"Token {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={"text": text}
            )
            
            if response.status_code != 200:
                logger.warning(f"Court Listener API error: {response.status_code}")
                return []
                
            data = response.json()
            citations = []
            
            for citation_data in data.get('citations', []):
                reporter = citation_data.get('reporter', '')
                
                citation = Citation(
                    text=citation_data.get('citation_str', ''),
                    volume=citation_data.get('volume', ''),
                    reporter=reporter,
                    page=citation_data.get('page', ''),
                    court=citation_data.get('court', ''),
                    year=citation_data.get('year', ''),
                    jurisdiction=self._get_jurisdiction_from_reporter(reporter),
                    case_name=citation_data.get('case_name', ''),
                    confidence=0.9  # API extractions are generally reliable
                )
                
                citations.append(citation)
            
            logger.info(f"Extracted {len(citations)} citations with Court Listener API")
            return citations
            
        except Exception as e:
            logger.error(f"Error extracting citations with Court Listener API: {str(e)}")
            return []
    
    def _extract_with_regex(self, text: str) -> List[Citation]:
        """
        Extract citations using regex patterns.
        
        Args:
            text: Legal text to extract citations from
            
        Returns:
            List of Citation objects
        """
        citations = []
        seen_citations = set()
        
        # Process each citation pattern
        for pattern in self.citation_patterns:
            matches = re.finditer(pattern, text)
            
            for match in matches:
                citation_text = match.group(0)
                
                # Skip duplicate citations
                if citation_text in seen_citations:
                    continue
                    
                seen_citations.add(citation_text)
                
                # Extract components based on pattern
                if match.re.pattern == self.citation_patterns[0]:
                    # Standard reporter pattern
                    volume = match.group(1)
                    reporter = match.group(2)
                    page = match.group(3)
                    year = match.group(4) if len(match.groups()) > 3 else None
                    
                    citation = Citation(
                        text=citation_text,
                        volume=volume,
                        reporter=reporter,
                        page=page,
                        year=year,
                        jurisdiction=self._get_jurisdiction_from_reporter(reporter),
                        confidence=0.8
                    )
                    
                elif match.re.pattern == self.citation_patterns[1]:
                    # Neutral citation pattern
                    year = match.group(1)
                    jurisdiction_code = match.group(2)
                    number = match.group(3)
                    
                    citation = Citation(
                        text=citation_text,
                        year=year,
                        jurisdiction=jurisdiction_code.lower(),
                        reporter=f"{jurisdiction_code}",
                        page=number,
                        confidence=0.7
                    )
                    
                elif match.re.pattern == self.citation_patterns[2]:
                    # Case name with citation pattern
                    case_name = match.group(1)
                    volume = match.group(2)
                    reporter = match.group(3)
                    page = match.group(4)
                    year = match.group(5) if len(match.groups()) > 4 else None
                    
                    citation = Citation(
                        text=citation_text,
                        case_name=case_name,
                        volume=volume,
                        reporter=reporter,
                        page=page,
                        year=year,
                        jurisdiction=self._get_jurisdiction_from_reporter(reporter),
                        confidence=0.7
                    )
                
                else:
                    # Unknown pattern (shouldn't happen)
                    citation = Citation(
                        text=citation_text,
                        confidence=0.5
                    )
                
                citations.append(citation)
        
        logger.info(f"Extracted {len(citations)} citations with regex")
        return citations
    
    def _get_jurisdiction_from_reporter(self, reporter: str) -> Optional[str]:
        """
        Determine jurisdiction from reporter abbreviation.
        
        Args:
            reporter: Reporter abbreviation
            
        Returns:
            Jurisdiction code or None if unknown
        """
        if not reporter:
            return None
            
        # Clean up reporter string
        reporter = reporter.strip()
        
        # Direct lookup
        if reporter in self.reporter_jurisdiction_map:
            return self.reporter_jurisdiction_map[reporter]
        
        # Try to find a matching reporter prefix
        for rep, jur in self.reporter_jurisdiction_map.items():
            if reporter.startswith(rep):
                return jur
        
        return None
    
    def normalize_citation(self, citation: Citation) -> str:
        """
        Normalize a citation to a standard format.
        
        Args:
            citation: Citation object
            
        Returns:
            Normalized citation string
        """
        if not citation.volume or not citation.reporter or not citation.page:
            return citation.text
        
        year_str = f" ({citation.year})" if citation.year else ""
        return f"{citation.volume} {citation.reporter} {citation.page}{year_str}"
    
    def extract_cited_cases(self, case_id: str, text: str, jurisdiction: str) -> List[Dict]:
        """
        Extract citations and structure them for graph building.
        
        Args:
            case_id: ID of the citing case
            text: Case text
            jurisdiction: Jurisdiction code
            
        Returns:
            List of citation dictionaries for database/graph storage
        """
        citations = self.extract_citations(text)
        results = []
        
        for citation in citations:
            if not citation.jurisdiction:
                citation.jurisdiction = jurisdiction
            
            results.append({
                "citing_case_id": case_id,
                "citation_text": citation.text,
                "normalized_citation": self.normalize_citation(citation),
                "volume": citation.volume,
                "reporter": citation.reporter,
                "page": citation.page,
                "year": citation.year,
                "jurisdiction": citation.jurisdiction,
                "confidence": citation.confidence
            })
        
        return results
    
    def group_citations_by_jurisdiction(self, citations: List[Citation]) -> Dict[str, List[Citation]]:
        """
        Group citations by jurisdiction.
        
        Args:
            citations: List of Citation objects
            
        Returns:
            Dictionary of jurisdiction -> citations
        """
        result = {}
        
        for citation in citations:
            jurisdiction = citation.jurisdiction or "unknown"
            
            if jurisdiction not in result:
                result[jurisdiction] = []
                
            result[jurisdiction].append(citation)
        
        return result
    
    def extract_statutes(self, text: str, jurisdiction: str) -> List[Dict]:
        """
        Extract statutory citations from legal text.
        
        Args:
            text: Legal text
            jurisdiction: Jurisdiction code
            
        Returns:
            List of statute citation dictionaries
        """
        statutes = []
        
        # Regex patterns for statutes by jurisdiction
        patterns = {
            "tx": [
                # Texas statutes, e.g., Tex. Civ. Prac. & Rem. Code § 16.003
                r'Tex\.\s+([A-Za-z\.]+(?:\s+&\s+[A-Za-z\.]+)?)\s+Code\s+§+\s*(\d+[A-Za-z0-9\.\-]*)',
                # Texas Penal Code, e.g., Tex. Penal Code § 22.01
                r'Tex\.\s+Penal\s+Code\s+§+\s*(\d+[A-Za-z0-9\.\-]*)',
            ],
            "ca": [
                # California codes, e.g., Cal. Civ. Code § 1542
                r'Cal\.\s+([A-Za-z\.]+)\s+Code\s+§+\s*(\d+[A-Za-z0-9\.\-]*)',
            ],
            "fed": [
                # U.S. Code, e.g., 42 U.S.C. § 1983
                r'(\d+)\s+U\.S\.C\.\s+§+\s*(\d+[A-Za-z0-9\.\-]*)',
                # CFR, e.g., 24 C.F.R. § 100.204
                r'(\d+)\s+C\.F\.R\.\s+§+\s*(\d+[A-Za-z0-9\.\-]*)',
            ]
        }
        
        # Get patterns for the specified jurisdiction
        jurisdiction_patterns = patterns.get(jurisdiction, [])
        
        # Add federal patterns for all jurisdictions
        if jurisdiction != "fed":
            jurisdiction_patterns.extend(patterns.get("fed", []))
        
        # Extract statutes using patterns
        for pattern in jurisdiction_patterns:
            matches = re.finditer(pattern, text)
            
            for match in matches:
                statute_text = match.group(0)
                
                statute = {
                    "text": statute_text,
                    "jurisdiction": jurisdiction,
                    "type": "statute"
                }
                
                statutes.append(statute)
        
        logger.info(f"Extracted {len(statutes)} statutes for {jurisdiction}")
        return statutes

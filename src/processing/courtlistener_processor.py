"""
Court Listener Integration for Batch Processing System
Extends the batch processor with Court Listener API capabilities
"""

import os
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.api.courtlistener.client import CourtListenerClient
from src.api.courtlistener.exceptions import CourtListenerAPIError
from src.api.courtlistener.models import Case, Opinion
from src.api.courtlistener.utils import extract_citation_from_text, standardize_jurisdiction

# Configure logging
logger = logging.getLogger(__name__)


class CourtListenerProcessor:
    """
    Processor for Court Listener API integration
    Enhances document processing with case law retrieval
    """
    
    def __init__(self):
        """Initialize the Court Listener processor"""
        self.client = CourtListenerClient()
        self.cache_dir = os.path.join("processing_state", "courtlistener_cache")
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def search_by_citation(self, citation: str) -> List[Case]:
        """
        Search for cases by citation
        
        Args:
            citation: Case citation string (e.g., "410 U.S. 113")
            
        Returns:
            List of matching Case objects
        """
        try:
            logger.info(f"Searching for cases with citation: {citation}")
            cases = self.client.get_cases_by_citation(citation)
            
            logger.info(f"Found {len(cases)} cases matching citation '{citation}'")
            return cases
        
        except CourtListenerAPIError as e:
            logger.error(f"Error searching for citation '{citation}': {str(e)}")
            return []
    
    def search_by_text(self, text: str, jurisdiction: Optional[str] = None, max_results: int = 5) -> List[Case]:
        """
        Search for cases by text query
        
        Args:
            text: Search query text
            jurisdiction: Optional jurisdiction to filter by
            max_results: Maximum number of results to return
            
        Returns:
            List of matching Case objects
        """
        try:
            logger.info(f"Searching for cases with text: '{text[:100]}...'")
            
            # Prepare search parameters
            params = {"page_size": max_results}
            if jurisdiction:
                params["jurisdiction"] = standardize_jurisdiction(jurisdiction)
            
            # Execute the search
            response = self.client.search_cases(query=text, **params)
            
            # Convert results to Case objects
            cases = []
            for result in response.get("results", []):
                cases.append(Case.from_api_response(result))
            
            logger.info(f"Found {len(cases)} cases matching text query")
            return cases
        
        except CourtListenerAPIError as e:
            logger.error(f"Error searching for text '{text[:50]}...': {str(e)}")
            return []
    
    def extract_and_fetch_citations(self, text: str) -> Dict[str, List[Case]]:
        """
        Extract citations from text and fetch corresponding cases
        
        Args:
            text: Text containing citations
            
        Returns:
            Dictionary mapping citations to lists of Case objects
        """
        # Extract citations from text
        citations = extract_citation_from_text(text)
        logger.info(f"Extracted {len(citations)} citations from text")
        
        # Fetch cases for each citation
        results = {}
        for citation in citations:
            cases = self.search_by_citation(citation)
            if cases:
                results[citation] = cases
        
        logger.info(f"Retrieved case data for {len(results)} citations")
        return results
    
    def get_opinion_text(self, case: Case) -> Dict[str, str]:
        """
        Get opinion text for a case
        
        Args:
            case: Case object
            
        Returns:
            Dictionary mapping opinion types to text content
        """
        if not case.opinions:
            try:
                # Fetch opinions if not already loaded
                opinions = self.client.get_opinions_by_case(case.id)
                for opinion in opinions:
                    case.add_opinion(opinion)
            except CourtListenerAPIError as e:
                logger.error(f"Error fetching opinions for case {case.id}: {str(e)}")
                return {}
        
        # Build dictionary of opinion texts
        opinion_texts = {}
        for opinion in case.opinions:
            content = opinion.text or (opinion.html if opinion.html else None)
            if content:
                opinion_type = opinion.type or "unknown"
                opinion_texts[opinion_type] = content
        
        return opinion_texts
    
    def search_jurisdiction(self, jurisdiction: str, query: str, page_size: int = 20) -> List[Dict]:
        """
        Search for cases in a specific jurisdiction
        
        Args:
            jurisdiction: Jurisdiction name
            query: Search query
            page_size: Number of results per page
            
        Returns:
            List of case results
        """
        try:
            jurisdiction_code = standardize_jurisdiction(jurisdiction)
            logger.info(f"Searching for '{query}' in jurisdiction '{jurisdiction}' (code: {jurisdiction_code})")
            
            results = self.client.search_by_jurisdiction(
                jurisdiction=jurisdiction_code,
                query=query,
                page_size=page_size
            )
            
            logger.info(f"Found {results.get('count', 0)} results in {jurisdiction}")
            return results.get("results", [])
        
        except CourtListenerAPIError as e:
            logger.error(f"Error searching in jurisdiction '{jurisdiction}': {str(e)}")
            return []
    
    def search_texas_cases(self, query: str, page_size: int = 20) -> List[Dict]:
        """
        Convenience method to search for Texas cases
        
        Args:
            query: Search query
            page_size: Number of results per page
            
        Returns:
            List of case results
        """
        try:
            logger.info(f"Searching for '{query}' in Texas")
            
            results = self.client.get_texas_cases(query=query, page_size=page_size)
            
            # Handle different response formats
            if isinstance(results, dict):
                logger.info(f"Found {results.get('count', 0)} Texas cases matching '{query}'")
                return results  # Return the full results object with pagination info
            elif isinstance(results, list):
                logger.info(f"Found {len(results)} Texas cases matching '{query}'")
                return {"count": len(results), "results": results}
            else:
                logger.warning(f"Unexpected response format: {type(results)}")
                return {"count": 0, "results": []}
        
        except CourtListenerAPIError as e:
            logger.error(f"Error searching Texas cases: {str(e)}")
            return {"count": 0, "results": []}
    
    def cache_case_data(self, case: Case, cache_id: Optional[str] = None) -> str:
        """
        Cache case data to disk
        
        Args:
            case: Case object to cache
            cache_id: Optional ID for the cache file
            
        Returns:
            Cache ID
        """
        cache_id = cache_id or case.id
        cache_path = os.path.join(self.cache_dir, f"{cache_id}.json")
        
        # Convert case to dictionary
        case_data = case.raw_data
        
        # Add opinions if available
        if case.opinions:
            case_data["opinions"] = [op.raw_data for op in case.opinions]
        
        # Write to file
        with open(cache_path, "w") as f:
            json.dump(case_data, f, indent=2)
        
        logger.info(f"Cached case data for {case.name} to {cache_path}")
        return cache_id
    
    def load_cached_case(self, cache_id: str) -> Optional[Case]:
        """
        Load a case from cache
        
        Args:
            cache_id: Cache ID
            
        Returns:
            Case object or None if not found
        """
        cache_path = os.path.join(self.cache_dir, f"{cache_id}.json")
        
        if not os.path.exists(cache_path):
            return None
        
        try:
            with open(cache_path, "r") as f:
                case_data = json.load(f)
            
            # Create Case object
            case = Case.from_api_response(case_data)
            
            # Add opinions if available
            if "opinions" in case_data:
                for opinion_data in case_data["opinions"]:
                    case.add_opinion(Opinion.from_api_response(opinion_data))
            
            logger.info(f"Loaded cached case data for {case.name}")
            return case
        
        except Exception as e:
            logger.error(f"Error loading cached case {cache_id}: {str(e)}")
            return None

"""
Enhanced Opinion Processing for Week 2

This module provides enhanced opinion processing capabilities including
opinion type classification, metadata extraction, and quality assessment.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class OpinionType(Enum):
    """Types of legal opinions"""
    MAJORITY = "majority"
    CONCURRING = "concurring"
    DISSENTING = "dissenting"
    PLURALITY = "plurality"
    PER_CURIAM = "per_curiam"
    MEMORANDUM = "memorandum"
    UNKNOWN = "unknown"


class OpinionQuality(Enum):
    """Opinion quality levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    POOR = "poor"


@dataclass
class OpinionMetadata:
    """Enhanced metadata for legal opinions"""
    opinion_id: str
    case_id: str
    opinion_type: OpinionType
    author: Optional[str] = None
    joining_judges: List[str] = None
    word_count: int = 0
    paragraph_count: int = 0
    citation_count: int = 0
    quality: OpinionQuality = OpinionQuality.MEDIUM
    key_topics: List[str] = None
    procedural_posture: Optional[str] = None
    outcome: Optional[str] = None
    
    def __post_init__(self):
        if self.joining_judges is None:
            self.joining_judges = []
        if self.key_topics is None:
            self.key_topics = []


class EnhancedOpinionProcessor:
    """Enhanced processor for legal opinions with classification and metadata extraction"""
    
    def __init__(self):
        self.opinion_type_patterns = self._load_opinion_type_patterns()
        self.procedural_patterns = self._load_procedural_patterns()
        self.outcome_patterns = self._load_outcome_patterns()
        
        logger.info("Initialized EnhancedOpinionProcessor")
    
    def process_opinion(self, opinion_data: Dict, case_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Process a single opinion with enhanced metadata extraction
        
        Args:
            opinion_data: Raw opinion data from Court Listener
            case_context: Optional case context for better classification
            
        Returns:
            Enhanced opinion data with metadata
        """
        try:
            opinion_id = str(opinion_data.get("id", "unknown"))
            case_id = str(opinion_data.get("cluster", "unknown"))
            
            # Extract opinion text
            opinion_text = self._extract_opinion_text(opinion_data)
            
            if not opinion_text:
                logger.warning(f"No text found for opinion {opinion_id}")
                return self._create_minimal_result(opinion_data, "No text available")
            
            # Classify opinion type
            opinion_type = self._classify_opinion_type(opinion_data, opinion_text)
            
            # Extract author and joining judges
            author, joining_judges = self._extract_judges(opinion_data, opinion_text)
            
            # Calculate text metrics
            word_count = len(opinion_text.split())
            paragraph_count = len([p for p in opinion_text.split('\n\n') if p.strip()])
            
            # Extract citations
            citations = self._extract_citations(opinion_text)
            citation_count = len(citations)
            
            # Classify procedural posture and outcome
            procedural_posture = self._classify_procedural_posture(opinion_text)
            outcome = self._classify_outcome(opinion_text)
            
            # Extract key topics
            key_topics = self._extract_key_topics(opinion_text)
            
            # Assess quality
            quality = self._assess_opinion_quality(opinion_text, word_count, citation_count)
            
            # Create metadata object
            metadata = OpinionMetadata(
                opinion_id=opinion_id,
                case_id=case_id,
                opinion_type=opinion_type,
                author=author,
                joining_judges=joining_judges,
                word_count=word_count,
                paragraph_count=paragraph_count,
                citation_count=citation_count,
                quality=quality,
                key_topics=key_topics,
                procedural_posture=procedural_posture,
                outcome=outcome
            )
            
            # Create enhanced result
            result = {
                "opinion_id": opinion_id,
                "case_id": case_id,
                "original_data": opinion_data,
                "processed_text": opinion_text,
                "metadata": metadata.__dict__,
                "citations": citations,
                "processing_timestamp": datetime.now().isoformat(),
                "processor_version": "enhanced_v1.0"
            }
            
            logger.info(f"Successfully processed opinion {opinion_id}: "
                       f"{opinion_type.value}, {word_count} words, {citation_count} citations")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process opinion {opinion_data.get('id', 'unknown')}: {str(e)}")
            return self._create_minimal_result(opinion_data, f"Processing error: {str(e)}")
    
    def _extract_opinion_text(self, opinion_data: Dict) -> str:
        """Extract clean opinion text from various possible fields"""
        # Try different text fields in order of preference
        text_fields = ["plain_text", "html", "text", "content"]
        
        for field in text_fields:
            text = opinion_data.get(field, "")
            if text and isinstance(text, str) and len(text.strip()) > 50:
                # Clean HTML if necessary
                if field == "html":
                    text = self._clean_html_text(text)
                return text.strip()
        
        return ""
    
    def _clean_html_text(self, html_text: str) -> str:
        """Clean HTML text to plain text"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_text, "html.parser")
            return soup.get_text(" ", strip=True)
        except ImportError:
            # Fallback: simple regex cleaning
            text = re.sub(r'<[^>]+>', ' ', html_text)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
    
    def _classify_opinion_type(self, opinion_data: Dict, opinion_text: str) -> OpinionType:
        """Classify the type of opinion"""
        # Check explicit type field first
        explicit_type = opinion_data.get("type", "").lower()
        if explicit_type:
            type_mapping = {
                "lead": OpinionType.MAJORITY,
                "plurality": OpinionType.PLURALITY,
                "concurrence": OpinionType.CONCURRING,
                "concurring": OpinionType.CONCURRING,
                "dissent": OpinionType.DISSENTING,
                "dissenting": OpinionType.DISSENTING,
                "addendum": OpinionType.MEMORANDUM
            }
            if explicit_type in type_mapping:
                return type_mapping[explicit_type]
        
        # Use text patterns for classification
        text_lower = opinion_text.lower()
        
        for opinion_type, patterns in self.opinion_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    return OpinionType(opinion_type)
        
        return OpinionType.UNKNOWN
    
    def _extract_judges(self, opinion_data: Dict, opinion_text: str) -> Tuple[Optional[str], List[str]]:
        """Extract author and joining judges"""
        author = opinion_data.get("author_str") or opinion_data.get("author")
        
        # Extract joining judges from text
        joining_judges = []
        
        # Look for joining patterns
        joining_patterns = [
            r"(?:justice|judge)\s+([A-Z][a-z]+)\s+(?:joins|joining)",
            r"([A-Z][a-z]+),?\s+(?:J\.|Justice),?\s+(?:joins|joining)",
            r"with whom\s+(?:justice|judge)\s+([A-Z][a-z]+)\s+joins"
        ]
        
        for pattern in joining_patterns:
            matches = re.findall(pattern, opinion_text, re.IGNORECASE)
            joining_judges.extend(matches)
        
        # Remove duplicates and clean
        joining_judges = list(set([judge.strip() for judge in joining_judges if judge.strip()]))
        
        return author, joining_judges
    
    def _extract_citations(self, opinion_text: str) -> List[str]:
        """Extract legal citations from opinion text"""
        citation_patterns = [
            r'\d+\s+U\.S\.\s+\d+',  # U.S. Reports
            r'\d+\s+S\.\s*Ct\.\s+\d+',  # Supreme Court Reporter
            r'\d+\s+F\.\d*d?\s+\d+',  # Federal Reporter
            r'\d+\s+S\.W\.\d*d?\s+\d+',  # South Western Reporter
            r'\d+\s+Tex\.\s+\d+',  # Texas Reports
            r'\d+\s+S\.E\.\d*d?\s+\d+',  # South Eastern Reporter
        ]
        
        citations = []
        for pattern in citation_patterns:
            matches = re.findall(pattern, opinion_text)
            citations.extend(matches)
        
        return list(set(citations))  # Remove duplicates
    
    def _classify_procedural_posture(self, opinion_text: str) -> Optional[str]:
        """Classify the procedural posture of the case"""
        text_lower = opinion_text.lower()
        
        for posture, patterns in self.procedural_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return posture
        
        return None
    
    def _classify_outcome(self, opinion_text: str) -> Optional[str]:
        """Classify the outcome of the case"""
        text_lower = opinion_text.lower()
        
        for outcome, patterns in self.outcome_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return outcome
        
        return None
    
    def _extract_key_topics(self, opinion_text: str) -> List[str]:
        """Extract key legal topics from the opinion"""
        # Simple keyword-based topic extraction
        topic_keywords = {
            "constitutional_law": ["constitution", "constitutional", "amendment", "due process"],
            "contract_law": ["contract", "breach", "agreement", "consideration"],
            "tort_law": ["negligence", "liability", "damages", "duty"],
            "criminal_law": ["criminal", "prosecution", "defendant", "guilty"],
            "civil_procedure": ["motion", "summary judgment", "discovery", "pleading"],
            "evidence": ["evidence", "testimony", "witness", "hearsay"],
            "jurisdiction": ["jurisdiction", "venue", "forum", "personal jurisdiction"]
        }
        
        text_lower = opinion_text.lower()
        topics = []
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                topics.append(topic)
        
        return topics
    
    def _assess_opinion_quality(self, opinion_text: str, word_count: int, citation_count: int) -> OpinionQuality:
        """Assess the quality of the opinion"""
        score = 0
        
        # Word count factor
        if word_count > 2000:
            score += 3
        elif word_count > 1000:
            score += 2
        elif word_count > 500:
            score += 1
        
        # Citation factor
        if citation_count > 10:
            score += 3
        elif citation_count > 5:
            score += 2
        elif citation_count > 0:
            score += 1
        
        # Structure factor (paragraphs, sections)
        if "CONCLUSION" in opinion_text.upper() or "HOLDING" in opinion_text.upper():
            score += 1
        
        # Legal reasoning indicators
        reasoning_indicators = ["therefore", "however", "moreover", "furthermore", "consequently"]
        if sum(1 for indicator in reasoning_indicators if indicator in opinion_text.lower()) > 3:
            score += 1
        
        # Map score to quality
        if score >= 7:
            return OpinionQuality.HIGH
        elif score >= 5:
            return OpinionQuality.MEDIUM
        elif score >= 3:
            return OpinionQuality.LOW
        else:
            return OpinionQuality.POOR
    
    def _create_minimal_result(self, opinion_data: Dict, error_message: str) -> Dict[str, Any]:
        """Create a minimal result for failed processing"""
        return {
            "opinion_id": str(opinion_data.get("id", "unknown")),
            "case_id": str(opinion_data.get("cluster", "unknown")),
            "original_data": opinion_data,
            "processed_text": "",
            "metadata": {
                "opinion_type": OpinionType.UNKNOWN.value,
                "quality": OpinionQuality.POOR.value,
                "word_count": 0,
                "citation_count": 0
            },
            "citations": [],
            "error": error_message,
            "processing_timestamp": datetime.now().isoformat(),
            "processor_version": "enhanced_v1.0"
        }
    
    def _load_opinion_type_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for opinion type classification"""
        return {
            "majority": [
                r"opinion of the court",
                r"majority opinion",
                r"we hold that",
                r"the court holds"
            ],
            "concurring": [
                r"concurring opinion",
                r"concur in the judgment",
                r"i concur",
                r"concurring in part"
            ],
            "dissenting": [
                r"dissenting opinion",
                r"i dissent",
                r"respectfully dissent",
                r"dissenting in part"
            ],
            "plurality": [
                r"plurality opinion",
                r"announces the judgment"
            ],
            "per_curiam": [
                r"per curiam",
                r"by the court"
            ]
        }
    
    def _load_procedural_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for procedural posture classification"""
        return {
            "appeal": [r"appeal", r"appellant", r"appellee"],
            "motion_to_dismiss": [r"motion to dismiss", r"12\(b\)\(6\)"],
            "summary_judgment": [r"summary judgment", r"rule 56"],
            "trial": [r"jury trial", r"bench trial", r"trial court"],
            "petition": [r"petition", r"petitioner", r"respondent"]
        }
    
    def _load_outcome_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for outcome classification"""
        return {
            "affirmed": [r"affirmed", r"affirm the", r"judgment affirmed"],
            "reversed": [r"reversed", r"reverse the", r"judgment reversed"],
            "remanded": [r"remanded", r"remand", r"remand for"],
            "dismissed": [r"dismissed", r"dismiss the", r"case dismissed"],
            "granted": [r"granted", r"grant the", r"motion granted"],
            "denied": [r"denied", r"deny the", r"motion denied"]
        }

"""
Case Metadata Enhancement for Week 2

This module enhances case metadata by extracting additional information
from case data and enriching it with external sources.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class JudgeInfo:
    """Information about a judge"""
    name: str
    role: Optional[str] = None  # Chief, Associate, etc.
    appointment_date: Optional[datetime] = None
    appointing_president: Optional[str] = None
    court: Optional[str] = None


@dataclass
class PartyInfo:
    """Information about case parties"""
    name: str
    party_type: str  # plaintiff, defendant, appellant, appellee, etc.
    is_organization: bool = False
    organization_type: Optional[str] = None  # corporation, government, etc.


@dataclass
class EnhancedCaseMetadata:
    """Enhanced metadata for legal cases"""
    case_id: str
    case_name: str
    jurisdiction: str
    court: str
    date_filed: Optional[datetime] = None
    date_decided: Optional[datetime] = None
    docket_number: Optional[str] = None
    
    # Enhanced fields
    judges: List[JudgeInfo] = field(default_factory=list)
    parties: List[PartyInfo] = field(default_factory=list)
    case_type: Optional[str] = None
    subject_matter: List[str] = field(default_factory=list)
    procedural_history: List[str] = field(default_factory=list)
    outcome: Optional[str] = None
    precedential_status: Optional[str] = None
    citation_count: int = 0
    subsequent_history: List[str] = field(default_factory=list)
    
    # Quality metrics
    completeness_score: float = 0.0
    data_quality: str = "unknown"
    
    def calculate_completeness_score(self) -> float:
        """Calculate completeness score based on available fields"""
        total_fields = 15
        filled_fields = 0
        
        # Core fields
        if self.case_name: filled_fields += 1
        if self.jurisdiction: filled_fields += 1
        if self.court: filled_fields += 1
        if self.date_filed: filled_fields += 1
        if self.date_decided: filled_fields += 1
        if self.docket_number: filled_fields += 1
        
        # Enhanced fields
        if self.judges: filled_fields += 1
        if self.parties: filled_fields += 1
        if self.case_type: filled_fields += 1
        if self.subject_matter: filled_fields += 1
        if self.procedural_history: filled_fields += 1
        if self.outcome: filled_fields += 1
        if self.precedential_status: filled_fields += 1
        if self.citation_count > 0: filled_fields += 1
        if self.subsequent_history: filled_fields += 1
        
        self.completeness_score = filled_fields / total_fields
        return self.completeness_score


class CaseMetadataEnhancer:
    """Enhances case metadata with additional extracted information"""
    
    def __init__(self):
        self.party_patterns = self._load_party_patterns()
        self.case_type_patterns = self._load_case_type_patterns()
        self.subject_matter_keywords = self._load_subject_matter_keywords()
        self.outcome_patterns = self._load_outcome_patterns()
        
        logger.info("Initialized CaseMetadataEnhancer")
    
    def enhance_case_metadata(self, case_data: Dict, opinions_data: List[Dict] = None) -> EnhancedCaseMetadata:
        """
        Enhance case metadata with extracted information
        
        Args:
            case_data: Raw case data from Court Listener
            opinions_data: Optional list of opinion data for additional context
            
        Returns:
            Enhanced case metadata
        """
        try:
            # Extract basic information
            case_id = str(case_data.get("id", "unknown"))
            case_name = case_data.get("case_name", "") or case_data.get("name", "")
            jurisdiction = self._extract_jurisdiction(case_data)
            court = self._extract_court_name(case_data)
            
            # Parse dates
            date_filed = self._parse_date(case_data.get("date_filed"))
            date_decided = self._parse_date(case_data.get("date_decided"))
            
            docket_number = case_data.get("docket_number", "") or case_data.get("docket", "")
            
            # Create enhanced metadata object
            metadata = EnhancedCaseMetadata(
                case_id=case_id,
                case_name=case_name,
                jurisdiction=jurisdiction,
                court=court,
                date_filed=date_filed,
                date_decided=date_decided,
                docket_number=docket_number
            )
            
            # Extract enhanced information
            metadata.judges = self._extract_judges(case_data, opinions_data)
            metadata.parties = self._extract_parties(case_name)
            metadata.case_type = self._classify_case_type(case_data, case_name)
            metadata.subject_matter = self._extract_subject_matter(case_data, opinions_data)
            metadata.procedural_history = self._extract_procedural_history(case_data)
            metadata.outcome = self._extract_outcome(case_data, opinions_data)
            metadata.precedential_status = self._extract_precedential_status(case_data)
            metadata.citation_count = case_data.get("citation_count", 0)
            metadata.subsequent_history = self._extract_subsequent_history(case_data)
            
            # Calculate quality metrics
            metadata.calculate_completeness_score()
            metadata.data_quality = self._assess_data_quality(metadata)
            
            logger.info(f"Enhanced metadata for case {case_id}: "
                       f"completeness {metadata.completeness_score:.2f}, "
                       f"quality {metadata.data_quality}")
            
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to enhance metadata for case {case_data.get('id', 'unknown')}: {str(e)}")
            # Return minimal metadata
            return EnhancedCaseMetadata(
                case_id=str(case_data.get("id", "unknown")),
                case_name=case_data.get("case_name", "Unknown"),
                jurisdiction="unknown",
                court="unknown"
            )
    
    def _extract_jurisdiction(self, case_data: Dict) -> str:
        """Extract jurisdiction from case data"""
        # Try different fields
        jurisdiction = case_data.get("jurisdiction")
        if jurisdiction:
            return jurisdiction
        
        # Extract from court information
        court_data = case_data.get("court", {})
        if isinstance(court_data, dict):
            return court_data.get("jurisdiction", "unknown")
        
        return "unknown"
    
    def _extract_court_name(self, case_data: Dict) -> str:
        """Extract court name from case data"""
        court_data = case_data.get("court", {})
        
        if isinstance(court_data, dict):
            return (court_data.get("full_name") or 
                   court_data.get("short_name") or 
                   court_data.get("name", "Unknown Court"))
        elif isinstance(court_data, str):
            return court_data
        
        return "Unknown Court"
    
    def _parse_date(self, date_str: Any) -> Optional[datetime]:
        """Parse date string to datetime object"""
        if not date_str:
            return None
        
        if isinstance(date_str, datetime):
            return date_str
        
        if isinstance(date_str, str):
            try:
                # Try common date formats
                for fmt in ["%Y-%m-%d", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%SZ"]:
                    try:
                        return datetime.strptime(date_str.split('T')[0], "%Y-%m-%d")
                    except ValueError:
                        continue
            except Exception:
                pass
        
        return None
    
    def _extract_judges(self, case_data: Dict, opinions_data: List[Dict] = None) -> List[JudgeInfo]:
        """Extract judge information from case and opinion data"""
        judges = []
        judge_names = set()
        
        # Extract from case data
        if "judges" in case_data:
            for judge_data in case_data.get("judges", []):
                if isinstance(judge_data, dict):
                    name = judge_data.get("name", "").strip()
                    if name and name not in judge_names:
                        judges.append(JudgeInfo(
                            name=name,
                            role=judge_data.get("role"),
                            court=self._extract_court_name(case_data)
                        ))
                        judge_names.add(name)
        
        # Extract from opinion data
        if opinions_data:
            for opinion in opinions_data:
                author = opinion.get("author_str") or opinion.get("author", "")
                if author and author not in judge_names:
                    judges.append(JudgeInfo(
                        name=author,
                        role="Author",
                        court=self._extract_court_name(case_data)
                    ))
                    judge_names.add(author)
        
        return judges
    
    def _extract_parties(self, case_name: str) -> List[PartyInfo]:
        """Extract party information from case name"""
        parties = []
        
        if not case_name:
            return parties
        
        # Split case name to identify parties
        # Common patterns: "A v. B", "A vs. B", "A against B"
        vs_patterns = [r'\s+v\.\s+', r'\s+vs\.\s+', r'\s+against\s+']
        
        for pattern in vs_patterns:
            if re.search(pattern, case_name, re.IGNORECASE):
                parts = re.split(pattern, case_name, flags=re.IGNORECASE)
                if len(parts) >= 2:
                    # First party is typically plaintiff/appellant
                    plaintiff = parts[0].strip()
                    defendant = parts[1].strip()
                    
                    parties.append(PartyInfo(
                        name=plaintiff,
                        party_type="plaintiff",
                        is_organization=self._is_organization(plaintiff),
                        organization_type=self._get_organization_type(plaintiff)
                    ))
                    
                    parties.append(PartyInfo(
                        name=defendant,
                        party_type="defendant",
                        is_organization=self._is_organization(defendant),
                        organization_type=self._get_organization_type(defendant)
                    ))
                break
        
        return parties
    
    def _is_organization(self, party_name: str) -> bool:
        """Determine if a party is an organization"""
        org_indicators = [
            "inc", "corp", "corporation", "company", "co.", "ltd", "llc",
            "state of", "united states", "government", "department",
            "board", "commission", "authority", "agency"
        ]
        
        party_lower = party_name.lower()
        return any(indicator in party_lower for indicator in org_indicators)
    
    def _get_organization_type(self, party_name: str) -> Optional[str]:
        """Determine the type of organization"""
        if not self._is_organization(party_name):
            return None
        
        party_lower = party_name.lower()
        
        if any(term in party_lower for term in ["state of", "united states", "government"]):
            return "government"
        elif any(term in party_lower for term in ["inc", "corp", "corporation", "company"]):
            return "corporation"
        elif any(term in party_lower for term in ["board", "commission", "authority"]):
            return "agency"
        
        return "organization"
    
    def _classify_case_type(self, case_data: Dict, case_name: str) -> Optional[str]:
        """Classify the type of case"""
        # Check explicit case type field
        case_type = case_data.get("case_type")
        if case_type:
            return case_type
        
        # Use patterns to classify
        combined_text = f"{case_name} {case_data.get('summary', '')}"
        text_lower = combined_text.lower()
        
        for case_type, patterns in self.case_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return case_type
        
        return None
    
    def _extract_subject_matter(self, case_data: Dict, opinions_data: List[Dict] = None) -> List[str]:
        """Extract subject matter topics"""
        subjects = set()
        
        # Combine text from case and opinions
        text_sources = [case_data.get("summary", ""), case_data.get("case_name", "")]
        
        if opinions_data:
            for opinion in opinions_data:
                text_sources.append(opinion.get("plain_text", ""))
        
        combined_text = " ".join(text_sources).lower()
        
        # Check for subject matter keywords
        for subject, keywords in self.subject_matter_keywords.items():
            if any(keyword in combined_text for keyword in keywords):
                subjects.add(subject)
        
        return list(subjects)
    
    def _extract_procedural_history(self, case_data: Dict) -> List[str]:
        """Extract procedural history"""
        history = []
        
        # Extract from various fields
        if "procedural_history" in case_data:
            history.extend(case_data["procedural_history"])
        
        if "history" in case_data:
            history.append(case_data["history"])
        
        return [h for h in history if h and isinstance(h, str)]
    
    def _extract_outcome(self, case_data: Dict, opinions_data: List[Dict] = None) -> Optional[str]:
        """Extract case outcome"""
        # Check explicit outcome field
        outcome = case_data.get("outcome")
        if outcome:
            return outcome
        
        # Extract from opinion text
        if opinions_data:
            for opinion in opinions_data:
                text = opinion.get("plain_text", "").lower()
                for outcome_type, patterns in self.outcome_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, text):
                            return outcome_type
        
        return None
    
    def _extract_precedential_status(self, case_data: Dict) -> Optional[str]:
        """Extract precedential status"""
        return case_data.get("precedential_status")
    
    def _extract_subsequent_history(self, case_data: Dict) -> List[str]:
        """Extract subsequent history"""
        history = []
        
        if "subsequent_history" in case_data:
            history.extend(case_data["subsequent_history"])
        
        return [h for h in history if h and isinstance(h, str)]
    
    def _assess_data_quality(self, metadata: EnhancedCaseMetadata) -> str:
        """Assess overall data quality"""
        score = metadata.completeness_score
        
        if score >= 0.8:
            return "high"
        elif score >= 0.6:
            return "medium"
        elif score >= 0.4:
            return "low"
        else:
            return "poor"
    
    def _load_party_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for party identification"""
        return {
            "government": [r"state of", r"united states", r"government", r"department of"],
            "corporation": [r"inc\.", r"corp\.", r"corporation", r"company", r"llc"],
            "individual": [r"estate of", r"in re"]
        }
    
    def _load_case_type_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for case type classification"""
        return {
            "civil": [r"civil", r"contract", r"tort", r"negligence"],
            "criminal": [r"criminal", r"prosecution", r"defendant", r"guilty"],
            "constitutional": [r"constitutional", r"amendment", r"due process"],
            "administrative": [r"administrative", r"agency", r"regulation"],
            "family": [r"divorce", r"custody", r"marriage", r"family"],
            "bankruptcy": [r"bankruptcy", r"debtor", r"creditor"],
            "tax": [r"tax", r"irs", r"revenue"]
        }
    
    def _load_subject_matter_keywords(self) -> Dict[str, List[str]]:
        """Load keywords for subject matter classification"""
        return {
            "personal_injury": ["negligence", "tort", "liability", "damages", "injury"],
            "contract": ["contract", "breach", "agreement", "consideration"],
            "constitutional_law": ["constitution", "amendment", "due process", "equal protection"],
            "criminal_law": ["criminal", "prosecution", "guilty", "sentence"],
            "employment": ["employment", "discrimination", "workplace", "labor"],
            "intellectual_property": ["patent", "copyright", "trademark", "trade secret"],
            "real_estate": ["property", "real estate", "land", "deed"],
            "family_law": ["divorce", "custody", "marriage", "family"]
        }
    
    def _load_outcome_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for outcome classification"""
        return {
            "affirmed": [r"affirmed", r"affirm"],
            "reversed": [r"reversed", r"reverse"],
            "remanded": [r"remanded", r"remand"],
            "dismissed": [r"dismissed", r"dismiss"],
            "granted": [r"granted", r"grant"],
            "denied": [r"denied", r"deny"]
        }

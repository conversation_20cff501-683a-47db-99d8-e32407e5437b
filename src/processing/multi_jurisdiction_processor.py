"""
Multi-Jurisdiction Legal Document Processing System
A unified system that integrates document classification, batch processing, 
and document relationship graphing for legal documents across jurisdictions.
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from dotenv import load_dotenv

# Import our components
from document_classifier import DocumentClassifier
from batch_processor import BatchProcessor
from document_graph import LegalDocumentGraph

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("multi_jurisdiction_processing.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class MultiJurisdictionProcessor:
    """Main controller for processing legal documents across jurisdictions"""
    
    def __init__(self, output_base_dir="processed_documents"):
        self.classifier = DocumentClassifier()
        self.batch_processor = BatchProcessor(output_base_dir)
        
        # Create Neo4j connection if credentials exist
        self.use_neo4j = all([
            os.getenv("NEO4J_URI"),
            os.getenv("NEO4J_USER"),
            os.getenv("NEO4J_PASSWORD")
        ])
        
        self.graph = None
        if self.use_neo4j:
            try:
                self.graph = LegalDocumentGraph()
                logger.info("Neo4j graph database connected successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Neo4j: {str(e)}")
                self.use_neo4j = False
    
    def process_jurisdiction(self, input_dir, jurisdiction, max_workers=4):
        """Process all documents for a specific jurisdiction"""
        logger.info(f"Starting processing for jurisdiction: {jurisdiction}")
        
        # Process the documents using our batch processor
        batch_result = self.batch_processor.process_directory(
            input_dir, 
            jurisdiction=jurisdiction,
            max_workers=max_workers
        )
        
        # Build document relationships in Neo4j if available
        if self.use_neo4j and self.graph:
            self._build_document_relationships(batch_result["batch_id"])
            
        return batch_result
    
    def _build_document_relationships(self, batch_id):
        """Build relationships between documents in Neo4j"""
        logger.info(f"Building document relationships for batch {batch_id}")
        
        # Load batch information
        batch_info_path = os.path.join("audit_logs", batch_id, "batch_info.json")
        if not os.path.exists(batch_info_path):
            logger.error(f"Batch info not found: {batch_info_path}")
            return
            
        with open(batch_info_path, "r") as f:
            batch_info = json.load(f)
            
        # Process each successful document
        success_count = 0
        for doc_id, doc_info in batch_info["documents"].items():
            if doc_info["status"] == "success":
                try:
                    # Add document to graph
                    self.graph.create_document_node(doc_id, doc_info["metadata"])
                    
                    # Process document content for citations
                    doc_path = doc_info["path"]
                    if os.path.exists(doc_path):
                        # Get the document content
                        content = self.classifier.extract_text_from_pdf(doc_path)
                        doc_type = doc_info["metadata"].get("doc_type", "law")
                        
                        # Extract and link citations
                        citations = self.graph.link_document_citations(doc_id, content, doc_type)
                        logger.info(f"Document {doc_id}: Found {len(citations)} citations")
                        success_count += 1
                except Exception as e:
                    logger.error(f"Error processing document {doc_id} for graph: {str(e)}")
        
        # Resolve citations to actual documents
        if success_count > 0:
            try:
                resolved_count = self.graph.resolve_citations()
                logger.info(f"Resolved {resolved_count} citations to documents")
            except Exception as e:
                logger.error(f"Error resolving citations: {str(e)}")
    
    def generate_jurisdiction_report(self, jurisdiction):
        """Generate a comprehensive report for a jurisdiction"""
        # Query citation statistics from Neo4j if available
        citation_stats = None
        if self.use_neo4j and self.graph:
            try:
                citation_stats = self.graph.get_citation_stats(jurisdiction=jurisdiction)
            except Exception as e:
                logger.error(f"Error getting citation stats: {str(e)}")
        
        # Generate report from audit logs
        report = {
            "jurisdiction": jurisdiction,
            "report_date": datetime.now().isoformat(),
            "citation_stats": citation_stats,
            "batches": []
        }
        
        # Find all batches for this jurisdiction
        audit_dir = "audit_logs"
        if os.path.exists(audit_dir):
            for batch_id in os.listdir(audit_dir):
                batch_info_path = os.path.join(audit_dir, batch_id, "batch_info.json")
                if os.path.exists(batch_info_path):
                    with open(batch_info_path, "r") as f:
                        batch_info = json.load(f)
                        
                    if batch_info.get("jurisdiction") == jurisdiction:
                        # Count document types
                        doc_types = {}
                        for doc_id, doc_info in batch_info["documents"].items():
                            if doc_info["status"] == "success":
                                doc_type = doc_info["metadata"].get("doc_type")
                                if doc_type:
                                    doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
                        
                        # Add batch summary to report
                        report["batches"].append({
                            "batch_id": batch_id,
                            "start_time": batch_info.get("start_time"),
                            "end_time": batch_info.get("end_time"),
                            "status": batch_info.get("status"),
                            "success_count": batch_info.get("success_count", 0),
                            "failure_count": batch_info.get("failure_count", 0),
                            "document_types": doc_types
                        })
        
        # Save report
        report_path = f"jurisdiction_report_{jurisdiction.lower().replace(' ', '_')}.json"
        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Jurisdiction report saved to {report_path}")
        return report
    
    def close(self):
        """Close connections and clean up"""
        if self.use_neo4j and self.graph:
            self.graph.close()
            

def main():
    """Main entry point for the multi-jurisdiction processor"""
    parser = argparse.ArgumentParser(description="Process legal documents across multiple jurisdictions")
    
    parser.add_argument("--input", "-i", required=True, help="Input directory containing PDFs to process")
    parser.add_argument("--jurisdiction", "-j", required=True, help="Jurisdiction of the documents (e.g., Texas, California)")
    parser.add_argument("--workers", "-w", type=int, default=4, help="Number of worker threads for parallel processing")
    parser.add_argument("--report", "-r", action="store_true", help="Generate a jurisdiction report after processing")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Error: Input directory not found: {args.input}")
        return 1
    
    try:
        processor = MultiJurisdictionProcessor()
        
        # Process the jurisdiction
        result = processor.process_jurisdiction(
            args.input,
            args.jurisdiction,
            max_workers=args.workers
        )
        
        print(f"\n===== PROCESSING COMPLETE =====")
        print(f"Batch ID: {result['batch_id']}")
        print(f"Total Documents: {result['total']}")
        print(f"Successful: {result['success_count']}")
        print(f"Failed: {result['failure_count']}")
        print(f"Success Rate: {(result['success_count'] / result['total']) * 100:.2f}%")
        
        # Generate jurisdiction report if requested
        if args.report:
            report = processor.generate_jurisdiction_report(args.jurisdiction)
            print(f"Jurisdiction report generated: {report['report_date']}")
            
        # Clean up
        processor.close()
        
        return 0
    
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
        
if __name__ == "__main__":
    sys.exit(main())

"""
Court Listener API connector
Handles fetching case law data from the Court Listener API with jurisdiction filtering.
"""

import os
import time
import json
import logging
import requests
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class CourtListenerConnector:
    """
    Connector for fetching case law data from Court Listener API.
    Supports jurisdiction-based filtering and pagination.
    """
    
    def __init__(self):
        """Initialize the Court Listener connector with API key."""
        self.api_key = os.getenv("COURTLISTENER_API_KEY")
        
        # If key not found, try loading .env from the project root
        if not self.api_key:
            logger.warning("COURTLISTENER_API_KEY not found in environment, attempting to load from .env file...")
            # Go up three levels from src/processing/providers to the project root
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')) 
            dotenv_path = os.path.join(project_root, '.env')
            if os.path.exists(dotenv_path):
                loaded = load_dotenv(dotenv_path=dotenv_path, override=True) # Override existing (None) if necessary
                self.api_key = os.getenv("COURTLISTENER_API_KEY")
                logger.info(f"Loaded .env from {dotenv_path}. Key found via os.getenv after load: {bool(self.api_key)}")
                
                # If STILL not found via getenv, try manual parse
                if not self.api_key:
                    logger.warning(f"os.getenv still failed after loading {dotenv_path}. Attempting manual parse...")
                    try:
                        with open(dotenv_path, 'r') as f:
                            for line in f:
                                line = line.strip()
                                if line.startswith("COURTLISTENER_API_KEY="):
                                    parts = line.split('=', 1)
                                    if len(parts) == 2:
                                        self.api_key = parts[1].strip()
                                        logger.info(f"Manually parsed key from {dotenv_path}. Key found: {bool(self.api_key)}")
                                        break # Found the key
                        if not self.api_key:
                             logger.warning(f"Manual parse of {dotenv_path} did not find the key.")
                    except Exception as e:
                        logger.error(f"Error during manual parse of {dotenv_path}: {e}")

            else:
                logger.warning(f".env file not found at expected location: {dotenv_path}")

        # Final check
        if not self.api_key:
            logger.error("Failed to find COURT_LISTENER_API_KEY after checking environment and .env file.")
            raise ValueError("Missing Court Listener API key in environment variables")
        
        self.base_url = "https://www.courtlistener.com/api/rest/v4"
        self.headers = {
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Court mapping for jurisdictions
        self.jurisdiction_courts = {
            "tx": [
                "tex", # Supreme Court of Texas
                "texapp", # Court of Appeals of Texas
                "texcrimapp", # Texas Court of Criminal Appeals
                "texjpml", # Texas Judicial Panel on Multidistrict Litigation
            ],
            "ca": [
                "cal", # Supreme Court of California
                "calctapp", # California Courts of Appeal
                "calag", # California Attorney General
                "calworkcompapp", # California Workers' Compensation Appeals Board
            ],
            "fed": [
                "scotus", # Supreme Court of the United States
                "ca1", "ca2", "ca3", "ca4", "ca5", "ca6", "ca7", "ca8", "ca9", "ca10", "ca11", "cadc", "cafc", # Circuit Courts
                "fed", # Federal Circuit
                "fedcl", # Court of Federal Claims
            ]
        }
        
        logger.info("Initialized Court Listener connector")
    
    def get_opinions(self, jurisdiction: str, page: int = 1, 
                    per_page: int = 20, **kwargs) -> Dict:
        """
        Get opinions from Court Listener API with jurisdiction filtering.
        
        Args:
            jurisdiction: Jurisdiction code (e.g., "tx", "ca", "fed")
            page: Page number for pagination
            per_page: Number of results per page
            **kwargs: Additional filter parameters
            
        Returns:
            API response with opinions data
        """
        court_list = self.jurisdiction_courts.get(jurisdiction.lower(), [])
        
        if not court_list:
            logger.error(f"Unknown jurisdiction: {jurisdiction}")
            return {"count": 0, "results": []}
        
        # Build query parameters
        params = {
            "court": ",".join(court_list),
            "page": page,
            "page_size": per_page,
        }
        
        # Add additional filters
        for key, value in kwargs.items():
            params[key] = value
        
        try:
            # Make the API request
            response = requests.get(
                f"{self.base_url}/opinions/",
                headers=self.headers,
                params=params
            )
            
            response.raise_for_status()
            data = response.json()
            
            logger.info(f"Retrieved {len(data.get('results', []))} opinions for {jurisdiction} (page {page})")
            return data
            
        except Exception as e:
            logger.error(f"Error fetching opinions from Court Listener: {str(e)}")
            return {"count": 0, "results": []}
    
    def get_all_opinions(self, jurisdiction: str, batch_size: int = 20, 
                         max_results: Optional[int] = None, **kwargs) -> List[Dict]:
        """
        Get all opinions matching criteria, handling pagination.
        
        Args:
            jurisdiction: Jurisdiction code
            batch_size: Number of results per page
            max_results: Maximum number of results to retrieve
            **kwargs: Additional filter parameters
            
        Returns:
            List of all opinions data
        """
        all_results = []
        page = 1
        total_retrieved = 0
        
        while True:
            data = self.get_opinions(
                jurisdiction=jurisdiction,
                page=page,
                per_page=batch_size,
                **kwargs
            )
            
            results = data.get('results', [])
            all_results.extend(results)
            total_retrieved += len(results)
            
            # Check if we've retrieved all results or reached max_results
            if not results or (max_results and total_retrieved >= max_results):
                break
            
            # Respect API rate limits
            time.sleep(1)
            page += 1
        
        return all_results[:max_results] if max_results else all_results
    
    def get_opinion_by_id(self, opinion_id: str) -> Optional[Dict]:
        """
        Get a specific opinion by ID.
        
        Args:
            opinion_id: Court Listener opinion ID
            
        Returns:
            Opinion data if found
        """
        try:
            response = requests.get(
                f"{self.base_url}/opinions/{opinion_id}/",
                headers=self.headers
            )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Error fetching opinion {opinion_id}: {str(e)}")
            return None
    
    def get_clusters_by_citation(self, citation: str) -> List[Dict]:
        """
        Get opinion clusters by citation.
        
        Args:
            citation: Citation string
            
        Returns:
            List of matching opinion clusters
        """
        try:
            params = {
                "cite": citation
            }
            
            response = requests.get(
                f"{self.base_url}/clusters/",
                headers=self.headers,
                params=params
            )
            
            response.raise_for_status()
            data = response.json()
            
            return data.get('results', [])
            
        except Exception as e:
            logger.error(f"Error fetching clusters for citation {citation}: {str(e)}")
            return []
    
    def get_courts_for_jurisdiction(self, jurisdiction: str) -> List[Dict]:
        """
        Get court details for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            List of court details
        """
        court_list = self.jurisdiction_courts.get(jurisdiction.lower(), [])
        courts = []
        
        if not court_list:
            logger.error(f"Unknown jurisdiction: {jurisdiction}")
            return []
        
        for court_id in court_list:
            try:
                response = requests.get(
                    f"{self.base_url}/courts/{court_id}/",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    courts.append(response.json())
                
                # Respect API rate limits
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error fetching court {court_id}: {str(e)}")
        
        return courts
    
    def get_judge_by_id(self, judge_id: str) -> Optional[Dict]:
        """
        Get judge details by ID.
        
        Args:
            judge_id: Court Listener judge ID
            
        Returns:
            Judge data if found
        """
        try:
            response = requests.get(
                f"{self.base_url}/people/{judge_id}/",
                headers=self.headers
            )
            
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            logger.error(f"Error fetching judge {judge_id}: {str(e)}")
            return None
    
    def download_opinion_pdf(self, opinion_id: str, target_path: str) -> bool:
        """
        Download an opinion PDF.
        
        Args:
            opinion_id: Court Listener opinion ID
            target_path: Path to save the PDF
            
        Returns:
            Success flag
        """
        try:
            response = requests.get(
                f"{self.base_url}/opinions/{opinion_id}/download/",
                headers=self.headers,
                stream=True
            )
            
            response.raise_for_status()
            
            with open(target_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Downloaded opinion {opinion_id} to {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading opinion {opinion_id}: {str(e)}")
            return False
    
    def get_top_clusters(self, court_id: str, count: int = 20, **kwargs) -> Dict:
        """
        Fetch top clusters (cases) for a specific court, ordered by citation count.
        
        Note: This uses the v4 search API but transforms the response to match the v3 clusters format
        expected by the rest of the pipeline.

        Args:
            court_id: The specific Court Listener court ID (e.g., 'tex', 'scotus').
            count: The number of clusters to retrieve (page size).
            **kwargs: Additional filter parameters for the /search/ endpoint.

        Returns:
            Dictionary containing the API response transformed to match v3 clusters API format.
            
        Raises:
            requests.exceptions.RequestException: If the API request fails.
        """
        # Use the search endpoint with proper query formatting instead of clusters endpoint
        endpoint = f"{self.base_url}/search/"
        
        # Format a proper query with court_id filtering using the fielded search syntax
        query = f"court_id:{court_id}"
        
        params = {
            "q": query,
            "type": "o",  # Opinions
            "page_size": count,
            "ordering": "-citeCount", # Get highest cited first
            "stat_precedential": "on", # Filter for published opinions
        }
        params.update(kwargs) # Add any other filters
        
        logger.info(f"Searching for top cases in court {court_id} from {endpoint} with params: {params}")
        response = requests.get(endpoint, headers=self.headers, params=params)
        
        if response.status_code == 200:
            search_data = response.json()
            
            # Transform v4 search API response to match expected v3 clusters format
            # This ensures compatibility with the rest of the pipeline that expects v3 format
            transformed_data = {
                "count": search_data.get("count", 0),
                "next": search_data.get("next"),
                "previous": search_data.get("previous"),
                "results": []
            }
            
            # Map v4 search results to v3 cluster format
            for item in search_data.get("results", []):
                # Use cluster_id as id if available
                cluster_id = item.get("cluster_id")
                if not cluster_id:
                    logger.warning(f"Skipping result without cluster_id: {item.get('caseName', 'Unknown')}")
                    continue
                    
                # Create a transformed cluster object with v3 field names
                cluster = {
                    "id": cluster_id,
                    "case_name": item.get("caseName", ""),
                    "case_name_full": item.get("caseNameFull", ""),
                    "citation_count": item.get("citeCount", 0),
                    "date_filed": item.get("dateFiled"),
                    "docket_id": item.get("docket_id"),
                    "precedential_status": "Published",  # We filtered for this
                    "resource_uri": item.get("absolute_url", ""),
                    "source": "C",  # Court Listener
                    "absolute_url": item.get("absolute_url", ""),
                    "court": {
                        "id": item.get("court_id"),
                        "name": item.get("court"),
                        "citation_string": item.get("court_citation_string"),
                    },
                    # Add any opinions they might have included
                    "sub_opinions": item.get("opinions", []) 
                }
                transformed_data["results"].append(cluster)
                
            logger.info(f"Transformed {len(transformed_data['results'])} search results to v3 cluster format")
            return transformed_data
        else:
            logger.error(f"Failed to fetch cases for court {court_id}. Status: {response.status_code}, Response: {response.text}")
            response.raise_for_status() # Raise an exception for bad status codes

    def get_opinions_by_cluster(self, cluster_id: Union[str, int], page: int = 1, per_page: int = 100, **kwargs) -> Dict:
        """
        Fetch opinions associated with a specific cluster ID.
        
        Note: This uses the v4 search API to find opinions associated with a cluster ID and
        transforms the response to match the expected v3 opinions format.

        Args:
            cluster_id: The Court Listener cluster ID.
            page: Page number for pagination.
            per_page: Number of opinions per page.
            **kwargs: Additional filter parameters for the API.

        Returns:
            Dictionary containing the API response transformed to match v3 opinions API format.
        
        Raises:
            requests.exceptions.RequestException: If the API request fails.
        """
        # In v4, we can use the search API with a cluster_id query
        endpoint = f"{self.base_url}/search/"
        
        # Format a query that searches for opinions within the specific cluster
        query = f"cluster_id:{cluster_id}"
        
        params = {
            "q": query,
            "type": "o",  # Opinions
            "page_size": per_page,
        }
        params.update(kwargs)
        
        logger.info(f"Searching for opinions in cluster {cluster_id} from {endpoint} with params: {params}")
        response = requests.get(endpoint, headers=self.headers, params=params)
        
        if response.status_code == 200:
            search_data = response.json()
            
            # Transform v4 search API response to match expected v3 opinions format
            transformed_data = {
                "count": search_data.get("count", 0),
                "next": search_data.get("next"),
                "previous": search_data.get("previous"),
                "results": []
            }
            
            # Map v4 search results to v3 opinion format
            for item in search_data.get("results", []):
                # Extract opinion data from the search result
                for opinion in item.get("opinions", []):
                    # Create a transformed opinion object with v3 field names
                    transformed_opinion = {
                        "id": opinion.get("id"),
                        "absolute_url": item.get("absolute_url", ""),
                        "cluster": cluster_id,  # Link back to the cluster
                        "author": "",  # May need to extract from author_id
                        "author_id": opinion.get("author_id"),
                        "date_created": opinion.get("meta", {}).get("date_created", ""),
                        "download_url": opinion.get("download_url", ""),
                        "extracted_by_ocr": False,  # Default
                        "html": "",  # Not available in search response
                        "html_columbia": "",  # Not available in search response
                        "html_lawbox": "",  # Not available in search response
                        "html_with_citations": "",  # Not available in search response
                        "joined_by": opinion.get("joined_by_ids", []),
                        "local_path": opinion.get("local_path", ""),
                        "opinions_cited": opinion.get("cites", []),
                        "page_count": 0,  # Not available in search response
                        "per_curiam": opinion.get("per_curiam", False),
                        "plain_text": opinion.get("snippet", ""),  # Snippet instead of full text
                        "sha1": opinion.get("sha1", ""),
                        "type": "010majority"  # Default to majority type
                    }
                    transformed_data["results"].append(transformed_opinion)
            
            # If we didn't get opinion details in the search, we need to follow up
            # by fetching the actual opinion data for this cluster
            if not transformed_data["results"] and transformed_data["count"] > 0:
                logger.warning(f"Search returned {transformed_data['count']} results but no opinion details. Fetching opinions directly.")
                # In this case we would ideally fetch each opinion directly, but this would require additional API calls
                # For simplicity, we'll return what we have
            
            logger.info(f"Transformed search results to {len(transformed_data['results'])} opinions for cluster {cluster_id}")
            return transformed_data
        else:
            logger.error(f"Failed to search for opinions in cluster {cluster_id}. Status: {response.status_code}, Response: {response.text}")
            response.raise_for_status() # Raise an exception for bad status codes
            
    def get_recent_opinions(self, jurisdiction: str, days_back: int = 30, 
                           max_results: int = 100) -> List[Dict]:
        """
        Get recent opinions for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            days_back: Number of days to look back
            max_results: Maximum number of results
            
        Returns:
            List of recent opinions
        """
        # Calculate date filter
        from datetime import datetime, timedelta
        date_filed_gte = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        return self.get_all_opinions(
            jurisdiction=jurisdiction,
            max_results=max_results,
            date_filed_gte=date_filed_gte,
            # Sort by newest first
            order_by='-date_filed'
        )

    def map_opinion_to_case_format(self, opinion_data: Dict, jurisdiction: str) -> Dict:
        """
        Map Court Listener opinion data to standardized case format.
        
        Args:
            opinion_data: Raw opinion data from Court Listener
            jurisdiction: Jurisdiction code
            
        Returns:
            Standardized case data
        """
        # Extract cluster data if available
        cluster = opinion_data.get('cluster', {})
        if not cluster and 'cluster' in opinion_data:
            # Handle case where cluster is a URL and we need to fetch it
            cluster_id = opinion_data['cluster'].split('/')[-2]
            try:
                response = requests.get(
                    f"{self.base_url}/clusters/{cluster_id}/",
                    headers=self.headers
                )
                if response.status_code == 200:
                    cluster = response.json()
            except:
                pass
        
        # Build citations list
        citations = []
        if cluster:
            for cite_type in ['citation', 'neutral_citation', 'federal_cite_one', 
                             'federal_cite_two', 'federal_cite_three', 
                             'state_cite_one', 'state_cite_two', 'state_cite_three']:
                if cluster.get(cite_type):
                    citations.append(cluster.get(cite_type))
        
        # Map to standard format
        return {
            "source": "court_listener",
            "source_id": str(opinion_data.get('id')),
            "cluster_id": str(cluster.get('id')) if cluster else None,
            "case_name": cluster.get('case_name') if cluster else opinion_data.get('case_name', ''),
            "case_name_short": cluster.get('case_name_short') if cluster else '',
            "court_id": opinion_data.get('court'),
            "court_name": opinion_data.get('court_str', ''),
            "date_filed": opinion_data.get('date_filed', ''),
            "docket_number": cluster.get('docket_number') if cluster else '',
            "status": cluster.get('precedential_status') if cluster else '',
            "citation": [c for c in citations if c],
            "attorneys": cluster.get('attorneys') if cluster else '',
            "judges": opinion_data.get('judges', ''),
            "nature_of_suit": cluster.get('nature_of_suit') if cluster else '',
            "posture": cluster.get('procedural_history') if cluster else '',
            "jurisdiction": jurisdiction,
            "processed_date": datetime.now().isoformat(),
            "opinions": [{
                "id": str(opinion_data.get('id')),
                "author_str": opinion_data.get('author_str', ''),
                "type": opinion_data.get('type', ''),
                "has_full_text": bool(opinion_data.get('plain_text', '')),
                "text_format": "text" if opinion_data.get('plain_text') else "html" if opinion_data.get('html') else "",
                "text": opinion_data.get('plain_text', opinion_data.get('html', '')),
                "sha1": opinion_data.get('sha1', '')
            }]
        }

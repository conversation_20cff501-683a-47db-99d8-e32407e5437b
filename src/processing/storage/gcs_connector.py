"""
Google Cloud Storage Connector
Handles the storage and retrieval of full-text case law documents
with comprehensive multi-jurisdiction support and document type separation.
Implements standardized path structures for organizing legal documents across
multiple jurisdictions, courts, and document types.

Week 1 Enhancement: Implements enhanced jurisdiction-based organization with
document type taxonomy support and standardized metadata handling.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any, BinaryIO
from datetime import datetime
from google.cloud import storage
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Load jurisdiction and document type configurations
def load_jurisdiction_config():
    """Load jurisdiction configuration from enhanced config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'enhanced_jurisdiction_config.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load jurisdiction config: {e}. Using defaults.")
        return {"jurisdictions": {}, "global_settings": {"default_jurisdiction": "tx"}}

def load_document_taxonomy():
    """Load document type taxonomy from config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'document_taxonomy.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load document taxonomy: {e}. Using defaults.")
        return {"document_types": {}}

class GCSConnector:
    """
    Connector for Google Cloud Storage operations.
    Manages raw document storage with jurisdiction-based organization.
    """
    
    def __init__(self, bucket_name: Optional[str] = None):
        """
        Initialize the GCS connector.

        Args:
            bucket_name: Optional bucket name override
        """
        self.bucket_name = bucket_name or os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        self.service_account_path = os.getenv("GCS_SERVICE_ACCOUNT_FILE")

        if not self.service_account_path:
            raise ValueError("Missing GCS service account file path in environment variables")

        # Load configuration
        self.jurisdiction_config = load_jurisdiction_config()
        self.document_taxonomy = load_document_taxonomy()

        # Initialize GCS client
        self.client = storage.Client.from_service_account_json(self.service_account_path)
        self.bucket = self.client.bucket(self.bucket_name)

        logger.info(f"Initialized GCS connector for bucket: {self.bucket_name}")
        logger.info(f"Loaded {len(self.jurisdiction_config.get('jurisdictions', {}))} jurisdictions")
        logger.info(f"Loaded {len(self.document_taxonomy.get('document_types', {}))} document types")
    
    def store_text(self, text_content: str, gcs_path: str) -> str:
        """
        Store text content in GCS.
        
        Args:
            text_content: The text to store
            gcs_path: The path within the bucket
            
        Returns:
            The public URL of the stored file
        """
        blob = self.bucket.blob(gcs_path)
        blob.upload_from_string(text_content)
        
        logger.info(f"Stored text content at {gcs_path} ({len(text_content)} bytes)")
        return f"gs://{self.bucket_name}/{gcs_path}"
    
    def store_file(self, file_path: str, gcs_path: str) -> str:
        """
        Upload a file to GCS.
        
        Args:
            file_path: Local file path
            gcs_path: The path within the bucket
            
        Returns:
            The public URL of the stored file
        """
        blob = self.bucket.blob(gcs_path)
        blob.upload_from_filename(file_path)
        
        logger.info(f"Uploaded file {file_path} to {gcs_path}")
        return f"gs://{self.bucket_name}/{gcs_path}"
    
    def store_json(self, data: Dict, gcs_path: str) -> str:
        """
        Store JSON data in GCS.
        
        Args:
            data: Dictionary to store as JSON
            gcs_path: The path within the bucket
            
        Returns:
            The public URL of the stored file
        """
        blob = self.bucket.blob(gcs_path)
        blob.upload_from_string(
            json.dumps(data, indent=2),
            content_type="application/json"
        )
        
        logger.info(f"Stored JSON data at {gcs_path}")
        return f"gs://{self.bucket_name}/{gcs_path}"
    
    def get_text(self, gcs_path: str) -> str:
        """
        Retrieve text content from GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            The text content
        """
        blob = self.bucket.blob(gcs_path)
        
        try:
            content = blob.download_as_text()
            return content
        except Exception as e:
            logger.error(f"Error retrieving text from {gcs_path}: {str(e)}")
            return ""
    
    def get_json(self, gcs_path: str) -> Dict:
        """
        Retrieve JSON data from GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            The parsed JSON data as dictionary
        """
        blob = self.bucket.blob(gcs_path)
        
        try:
            content = blob.download_as_text()
            return json.loads(content)
        except Exception as e:
            logger.error(f"Error retrieving JSON from {gcs_path}: {str(e)}")
            return {}
    
    def file_exists(self, gcs_path: str) -> bool:
        """
        Check if a file exists in GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            Boolean indicating if the file exists
        """
        blob = self.bucket.blob(gcs_path)
        return blob.exists()
    
    def delete_file(self, gcs_path: str) -> bool:
        """
        Delete a file from GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            Success flag
        """
        try:
            blob = self.bucket.blob(gcs_path)
            blob.delete()
            logger.info(f"Deleted file {gcs_path}")
            return True
        except Exception as e:
            logger.error(f"Error deleting file {gcs_path}: {str(e)}")
            return False
    
    def get_jurisdiction_path(self, jurisdiction: str, document_id: str,
                             opinion_id: Optional[str] = None,
                             filename: Optional[str] = None,
                             doc_type: str = 'case',
                             doc_subtype: Optional[str] = None,
                             year: Optional[str] = None) -> str:
        """
        Generate a standardized GCS path for legal document storage using enhanced organization.

        Args:
            jurisdiction: Jurisdiction code
            document_id: Document ID (case_id, statute_id, etc.)
            opinion_id: Optional opinion ID for cases
            filename: Optional filename
            doc_type: Document type (case, statute, regulation, etc.)
            doc_subtype: Optional document subtype for more specific organization
            year: Optional year for organization (defaults to extracted from document_id or current year)

        Returns:
            The GCS path following jurisdiction-based organization
        """
        # Validate jurisdiction
        if jurisdiction not in self.jurisdiction_config.get('jurisdictions', {}):
            logger.warning(f"Unknown jurisdiction: {jurisdiction}. Using default organization.")

        # Get jurisdiction-specific storage config
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        storage_config = jurisdiction_data.get('storage_config', {})
        gcs_prefix = storage_config.get('gcs_prefix', f'legal/{jurisdiction}')

        # Determine year if not provided
        if not year:
            # Try to extract year from document_id if it contains a date pattern
            if document_id and '-' in document_id and len(document_id.split('-')[0]) == 4:
                year = document_id.split('-')[0]
            else:
                # Default to current year
                year = str(datetime.now().year)

        # Build path using enhanced organization structure
        if doc_type == 'case':
            # Cases: {gcs_prefix}/cases/{year}/{document_id}
            path = f"{gcs_prefix}/cases/{year}/{document_id}"

            if opinion_id:
                path += f"/opinions/{opinion_id}"
        elif doc_type == 'statute':
            # Statutes: {gcs_prefix}/statutes/{doc_subtype or 'general'}/{document_id}
            subtype_path = doc_subtype or 'general'
            path = f"{gcs_prefix}/statutes/{subtype_path}/{document_id}"
        elif doc_type == 'regulation':
            # Regulations: {gcs_prefix}/regulations/{doc_subtype or 'general'}/{document_id}
            subtype_path = doc_subtype or 'general'
            path = f"{gcs_prefix}/regulations/{subtype_path}/{document_id}"
        elif doc_type == 'constitution':
            # Constitutional documents: {gcs_prefix}/constitution/{document_id}
            path = f"{gcs_prefix}/constitution/{document_id}"
        elif doc_type == 'administrative_ruling':
            # Administrative rulings: {gcs_prefix}/administrative/{doc_subtype or 'general'}/{document_id}
            subtype_path = doc_subtype or 'general'
            path = f"{gcs_prefix}/administrative/{subtype_path}/{document_id}"
        else:
            # Default organization for other document types
            path = f"{gcs_prefix}/{doc_type}/{document_id}"

        if filename:
            path += f"/{filename}"

        return path

    def validate_jurisdiction_doc_type(self, jurisdiction: str, doc_type: str) -> bool:
        """
        Validate that a jurisdiction supports a specific document type.

        Args:
            jurisdiction: Jurisdiction code
            doc_type: Document type

        Returns:
            Boolean indicating if the combination is valid
        """
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        supported_types = jurisdiction_data.get('document_types', [])

        return doc_type in supported_types

    def get_jurisdiction_storage_config(self, jurisdiction: str) -> Dict:
        """
        Get storage configuration for a specific jurisdiction.

        Args:
            jurisdiction: Jurisdiction code

        Returns:
            Storage configuration dictionary
        """
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        return jurisdiction_data.get('storage_config', {
            'gcs_prefix': f'legal/{jurisdiction}',
            'pinecone_namespace': jurisdiction,
            'neo4j_labels': ['Document', jurisdiction.title()]
        })

    def get_document_type_info(self, doc_type: str) -> Dict:
        """
        Get document type information from taxonomy.

        Args:
            doc_type: Document type

        Returns:
            Document type information dictionary
        """
        return self.document_taxonomy.get('document_types', {}).get(doc_type, {})

    def list_jurisdiction_files(self, jurisdiction: str,
                                doc_type: Optional[str] = None,
                                year: Optional[str] = None,
                                prefix: Optional[str] = None) -> List[str]:
        """
        List files for a specific jurisdiction with filtering options.
        
        Args:
            jurisdiction: Jurisdiction code
            doc_type: Optional document type filter (case, statute, regulation, etc.)
            year: Optional year filter (for cases)
            prefix: Optional additional path prefix
            
        Returns:
            List of GCS paths
        """
        # Build base path based on parameters
        if doc_type:
            if doc_type == 'case' and year:
                path = f"legal/{jurisdiction}/cases/{year}"
            else:
                path = f"legal/{jurisdiction}/{doc_type}"
        else:
            path = f"legal/{jurisdiction}"
        
        # Add additional prefix if provided
        if prefix:
            path += f"/{prefix}"
        
        # List blobs with the constructed prefix
        blobs = self.client.list_blobs(self.bucket_name, prefix=path)
        return [blob.name for blob in blobs]
    
    def backup_case(self, case_id: str, jurisdiction: str, doc_type: str = 'case', year: Optional[str] = None) -> str:
        """
        Create a backup of all case files.
        
        Args:
            case_id: Case ID
            jurisdiction: Jurisdiction code
            doc_type: Document type (case, statute, regulation, etc.)
            year: Optional year for organization
            
        Returns:
            Backup path
        """
        # Get the source path using the same organization logic
        source_path = self.get_jurisdiction_path(
            jurisdiction=jurisdiction,
            case_id=case_id,
            doc_type=doc_type,
            year=year
        )
        
        # Create timestamp-based backup path
        timestamp = int(datetime.now().timestamp())
        backup_path = f"backups/{jurisdiction}/{doc_type}/{case_id}_{timestamp}"
        
        # List all files for the document
        blobs = self.client.list_blobs(self.bucket_name, prefix=source_path)
        
        backed_up_files = []
        for blob in blobs:
            # Construct backup path
            backup_file = blob.name.replace(source_path, backup_path)
            
            # Copy blob to backup location
            self.bucket.copy_blob(blob, self.bucket, backup_file)
            backed_up_files.append(backup_file)
            
            logger.info(f"Backed up {blob.name} to {backup_file}")
        
        logger.info(f"Completed backup of {source_path} to {backup_path} ({len(backed_up_files)} files)")
        return backup_path
    
    def get_object_url(self, gcs_path: str) -> str:
        """
        Get the public URL for a GCS object.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            Public URL for the object
        """
        blob = self.bucket.blob(gcs_path)
        return blob.public_url
        
    def store_case_text(self, case_id: str, text: str, jurisdiction: str, year: Optional[str] = None, doc_type: str = 'case') -> str:
        """
        Store case text content with proper jurisdiction-based organization.
        
        Args:
            case_id: Case ID
            text: The text content to store
            jurisdiction: Jurisdiction code
            year: Optional year for organization (defaults to current year)
            doc_type: Document type (defaults to 'case')
            
        Returns:
            The GCS path where the text was stored
        """
        # Use current year if not provided
        if not year:
            year = datetime.now().strftime("%Y")
            
        # Generate path using jurisdiction organization
        gcs_path = self.get_jurisdiction_path(
            jurisdiction=jurisdiction,
            case_id=case_id,
            doc_type=doc_type,
            year=year,
            filename="full_text.txt"
        )
        
        # Store the text
        self.store_text(text, gcs_path)
        
        logger.info(f"Stored case text for {case_id} at {gcs_path}")
        return gcs_path
        
    def migrate_jurisdiction_path(self, old_path: str, jurisdiction: str, doc_type: str = 'case') -> str:
        """
        Migrate a document from old path structure to new jurisdiction-based organization.
        
        Args:
            old_path: The existing GCS path
            jurisdiction: Jurisdiction code
            doc_type: Document type
            
        Returns:
            The new GCS path
        """
        # Extract case ID from old path
        parts = old_path.split('/')
        if len(parts) < 2:
            logger.error(f"Invalid path format for migration: {old_path}")
            return old_path
            
        case_id = parts[-2] if parts[-1].count('.') > 0 else parts[-1]
        filename = parts[-1] if parts[-1].count('.') > 0 else None
        
        # Generate new path
        new_path = self.get_jurisdiction_path(
            jurisdiction=jurisdiction,
            case_id=case_id,
            filename=filename,
            doc_type=doc_type
        )
        
        # Copy the file to the new location
        if self.file_exists(old_path):
            source_blob = self.bucket.blob(old_path)
            self.bucket.copy_blob(source_blob, self.bucket, new_path)
            logger.info(f"Migrated {old_path} to {new_path}")
            
            # Optionally delete the old file
            # source_blob.delete()
            # logger.info(f"Deleted original file at {old_path}")
        else:
            logger.error(f"Source file not found for migration: {old_path}")
            
        return new_path

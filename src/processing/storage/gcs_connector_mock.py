"""
Mock Google Cloud Storage Connector
This version uses local file storage to simulate GCS operations for testing.
"""

import os
import json
import logging
import shutil
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class GCSConnector:
    """
    Mock connector for simulating Google Cloud Storage operations during testing.
    Uses local file system instead of actual GCS bucket.
    """
    
    def __init__(self, bucket_name: Optional[str] = None):
        """
        Initialize the mock GCS connector.
        
        Args:
            bucket_name: Optional bucket name override
        """
        self.bucket_name = bucket_name or os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        
        # Create a local directory to simulate the bucket
        self.local_base_dir = os.path.join(os.getcwd(), "mock_gcs", self.bucket_name)
        os.makedirs(self.local_base_dir, exist_ok=True)
        
        logger.info(f"Initialized mock GCS connector at {self.local_base_dir}")
    
    def store_text(self, gcs_path: str, text_content: str) -> str:
        """
        Store text content in mock GCS (local file system).
        
        Args:
            gcs_path: The path within the bucket
            text_content: The text to store
            
        Returns:
            The public URL of the stored file
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        # Write the file
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        
        logger.info(f"[MOCK] Stored text content at {gcs_path} ({len(text_content)} bytes)")
        return f"file://{full_path}"
    
    def get_text(self, gcs_path: str) -> str:
        """
        Retrieve text content from mock GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            The text content
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        
        try:
            if not os.path.exists(full_path):
                logger.error(f"[MOCK] File not found: {full_path}")
                return ""
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            logger.error(f"[MOCK] Error retrieving text from {gcs_path}: {str(e)}")
            return ""
    
    def file_exists(self, gcs_path: str) -> bool:
        """
        Check if a file exists in mock GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            Boolean indicating if the file exists
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        return os.path.exists(full_path)
    
    def store_json(self, data: Dict, gcs_path: str) -> str:
        """
        Store JSON data in mock GCS.
        
        Args:
            data: Dictionary to store as JSON
            gcs_path: The path within the bucket
            
        Returns:
            The public URL of the stored file
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        
        # Ensure the directory exists
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        # Write the file
        with open(full_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"[MOCK] Stored JSON data at {gcs_path}")
        return f"file://{full_path}"
    
    def get_json(self, gcs_path: str) -> Dict:
        """
        Retrieve JSON data from mock GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            The parsed JSON data as dictionary
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        
        try:
            if not os.path.exists(full_path):
                logger.error(f"[MOCK] File not found: {full_path}")
                return {}
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = json.load(f)
            return content
        except Exception as e:
            logger.error(f"[MOCK] Error retrieving JSON from {gcs_path}: {str(e)}")
            return {}
    
    def delete_file(self, gcs_path: str) -> bool:
        """
        Delete a file from mock GCS.
        
        Args:
            gcs_path: The path within the bucket
            
        Returns:
            Success flag
        """
        full_path = os.path.join(self.local_base_dir, gcs_path)
        
        try:
            if os.path.exists(full_path):
                os.remove(full_path)
                logger.info(f"[MOCK] Deleted file at {gcs_path}")
                return True
            else:
                logger.error(f"[MOCK] File not found for deletion: {gcs_path}")
                return False
        except Exception as e:
            logger.error(f"[MOCK] Error deleting file {gcs_path}: {str(e)}")
            return False
    
    def store_case_text(self, case_id: str, text: str, jurisdiction: str, year: Optional[str] = None, doc_type: str = 'case') -> str:
        """
        Store case text content with proper jurisdiction-based organization.
        
        Args:
            case_id: Case ID
            text: The text content to store
            jurisdiction: Jurisdiction code
            year: Optional year for organization (defaults to current year)
            doc_type: Document type (defaults to 'case')
            
        Returns:
            The GCS path where the text was stored
        """
        # Use current year if not provided
        if not year:
            year = datetime.now().strftime("%Y")
            
        # Generate path using jurisdiction organization
        gcs_path = f"{jurisdiction}/{doc_type}/{year}/{case_id}/full_text.txt"
        
        # Store the text
        self.store_text(gcs_path, text)
        
        logger.info(f"[MOCK] Stored case text for {case_id} at {gcs_path}")
        return gcs_path

"""
GCS Helper Module
Helper functions for GCS storage, with fallback to local storage for development.
"""

import os
import json
import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Determine if we should use mock storage
USE_MOCK_STORAGE = os.getenv("USE_MOCK_STORAGE", "").lower() in ["true", "1", "yes"]

# Initialize the appropriate GCS connector
try:
    if USE_MOCK_STORAGE:
        raise ImportError("Using mock storage as specified in environment variables")
        
    # Try to import and initialize the real GCS connector
    from src.processing.storage.gcs_connector import GCSConnector
    _gcs = GCSConnector(bucket_name=os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"))
    logger.info("Using real GCS storage")
except Exception as e:
    # Fall back to the mock connector
    from src.processing.storage.gcs_connector_mock import GCSConnector
    _gcs = GCSConnector(bucket_name=os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"))
    logger.warning(f"Using mock GCS storage due to error: {str(e)}")


def store_case_document(case_id: Union[str, int], content: str, jurisdiction: str, 
                       doc_type: str = "full_text", year: Optional[str] = None) -> str:
    """
    Store a case document in GCS with proper path structure.
    
    Args:
        case_id: The case ID (will be prefixed with cl_ if not already)
        content: The text content to store
        jurisdiction: Jurisdiction code (e.g., 'tx')
        doc_type: Document type (default: full_text)
        year: Optional year for organization (defaults to current year)
        
    Returns:
        The GCS path where the document was stored
    """
    # Convert case_id to string if it's not already
    case_id = str(case_id)
    
    # Ensure case_id has the cl_ prefix
    if not case_id.startswith("cl_"):
        case_id = f"cl_{case_id}"
        
    # Extract year from case_id if not provided
    if not year:
        # Try to extract year from id if it has a pattern like cl_YYYYMMDD
        if len(case_id) > 7 and case_id[3:7].isdigit():
            year = case_id[3:7]
        else:
            # Use current year as fallback
            year = datetime.now().strftime("%Y")
    
    # Ensure jurisdiction is lowercase
    jurisdiction = jurisdiction.lower()
    
    # Create path with clear organization
    gcs_path = f"cases/{jurisdiction}/{year}/{case_id}/{doc_type}.txt"
    
    try:
        # Store the document
        result = _gcs.store_text(gcs_path, content)
        logger.info(f"Stored case document for {case_id} at {gcs_path} ({len(content)} chars)")
        return gcs_path
    except Exception as e:
        logger.error(f"Failed to store case document for {case_id}: {str(e)}")
        return ""
        

def retrieve_case_document(case_id: str, jurisdiction: str, 
                          doc_type: str = "full_text", year: Optional[str] = None) -> str:
    """
    Retrieve a case document from GCS.
    
    Args:
        case_id: The case ID (will be prefixed with cl_ if not already)
        jurisdiction: Jurisdiction code (e.g., 'tx')
        doc_type: Document type (default: full_text)
        year: Optional year for organization
        
    Returns:
        The document content or empty string if not found
    """
    # Ensure case_id has the cl_ prefix
    if not case_id.startswith("cl_"):
        case_id = f"cl_{case_id}"
        
    # Extract year from case_id if not provided
    if not year:
        # Try to extract year from id if it has a pattern like cl_YYYYMMDD
        if len(case_id) > 7 and case_id[3:7].isdigit():
            year = case_id[3:7]
        else:
            # Use current year as fallback
            year = datetime.now().strftime("%Y")
    
    # Ensure jurisdiction is lowercase
    jurisdiction = jurisdiction.lower()
    
    # Create path with clear organization
    gcs_path = f"cases/{jurisdiction}/{year}/{case_id}/{doc_type}.txt"
    
    try:
        # Retrieve the document
        content = _gcs.get_text(gcs_path)
        if content:
            logger.info(f"Retrieved case document for {case_id} from {gcs_path} ({len(content)} chars)")
        else:
            logger.warning(f"Case document not found for {case_id} at {gcs_path}")
            
        return content
    except Exception as e:
        logger.error(f"Failed to retrieve case document for {case_id}: {str(e)}")
        return ""


def store_case_json(case_id: Union[str, int], data: Dict, jurisdiction: str, 
                   doc_type: str = "metadata", year: Optional[str] = None) -> str:
    """
    Store case JSON data in GCS.
    
    Args:
        case_id: The case ID (will be prefixed with cl_ if not already)
        data: The JSON data to store
        jurisdiction: Jurisdiction code (e.g., 'tx')
        doc_type: Document type (default: metadata)
        year: Optional year for organization
        
    Returns:
        The GCS path where the JSON was stored
    """
    # Convert case_id to string if it's not already
    case_id = str(case_id)
    
    # Ensure case_id has the cl_ prefix
    if not case_id.startswith("cl_"):
        case_id = f"cl_{case_id}"
        
    # Extract year from case_id if not provided
    if not year:
        # Try to extract year from id if it has a pattern like cl_YYYYMMDD
        if len(case_id) > 7 and case_id[3:7].isdigit():
            year = case_id[3:7]
        else:
            # Use current year as fallback
            year = datetime.now().strftime("%Y")
    
    # Ensure jurisdiction is lowercase
    jurisdiction = jurisdiction.lower()
    
    # Create path with clear organization
    gcs_path = f"cases/{jurisdiction}/{year}/{case_id}/{doc_type}.json"
    
    try:
        # Store the JSON
        result = _gcs.store_json(data, gcs_path)
        logger.info(f"Stored case JSON for {case_id} at {gcs_path}")
        return gcs_path
    except Exception as e:
        logger.error(f"Failed to store case JSON for {case_id}: {str(e)}")
        return ""


def retrieve_case_json(case_id: str, jurisdiction: str, 
                      doc_type: str = "metadata", year: Optional[str] = None) -> Dict:
    """
    Retrieve case JSON data from GCS.
    
    Args:
        case_id: The case ID (will be prefixed with cl_ if not already)
        jurisdiction: Jurisdiction code (e.g., 'tx')
        doc_type: Document type (default: metadata)
        year: Optional year for organization
        
    Returns:
        The JSON data or empty dict if not found
    """
    # Ensure case_id has the cl_ prefix
    if not case_id.startswith("cl_"):
        case_id = f"cl_{case_id}"
        
    # Extract year from case_id if not provided
    if not year:
        # Try to extract year from id if it has a pattern like cl_YYYYMMDD
        if len(case_id) > 7 and case_id[3:7].isdigit():
            year = case_id[3:7]
        else:
            # Use current year as fallback
            year = datetime.now().strftime("%Y")
    
    # Ensure jurisdiction is lowercase
    jurisdiction = jurisdiction.lower()
    
    # Create path with clear organization
    gcs_path = f"cases/{jurisdiction}/{year}/{case_id}/{doc_type}.json"
    
    try:
        # Retrieve the JSON
        data = _gcs.get_json(gcs_path)
        if data:
            logger.info(f"Retrieved case JSON for {case_id} from {gcs_path}")
        else:
            logger.warning(f"Case JSON not found for {case_id} at {gcs_path}")
            
        return data
    except Exception as e:
        logger.error(f"Failed to retrieve case JSON for {case_id}: {str(e)}")
        return {}

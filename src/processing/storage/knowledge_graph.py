"""
Neo4j Knowledge Graph Connector
Manages the legal knowledge graph including case citations, judges, courts,
and other legal relationships with multi-jurisdiction support.
"""

import os
import logging
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from dotenv import load_dotenv

from neo4j import GraphDatabase

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class Neo4jConnector:
    """
    Connector for Neo4j graph database operations.
    Manages legal knowledge graph with jurisdiction-based access control.
    """
    
    def __init__(self):
        """Initialize the Neo4j connector with credentials from environment variables."""
        self.neo4j_uri = os.getenv("NEO4J_URI")
        self.neo4j_user = os.getenv("NEO4J_USER")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        if not self.neo4j_uri or not self.neo4j_user or not self.neo4j_password:
            raise ValueError("Missing Neo4j credentials in environment variables")
        
        # Initialize Neo4j driver
        self.driver = GraphDatabase.driver(
            self.neo4j_uri, 
            auth=(self.neo4j_user, self.neo4j_password)
        )
        
        # Ensure schema constraints
        self._ensure_constraints()
        
        logger.info(f"Initialized Neo4j connector for {self.neo4j_uri}")
    
    def _ensure_constraints(self):
        """Ensure necessary constraints exist in Neo4j."""
        with self.driver.session() as session:
            # Create constraints for unique IDs
            constraints = [
                "CREATE CONSTRAINT IF NOT EXISTS FOR (c:Case) REQUIRE c.case_id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (o:Opinion) REQUIRE o.opinion_id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (c:Court) REQUIRE c.court_id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (j:Judge) REQUIRE j.judge_id IS UNIQUE",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (ct:Citation) REQUIRE ct.citation_id IS UNIQUE",
                # Create indexes for jurisdiction
                "CREATE INDEX IF NOT EXISTS FOR (c:Case) ON (c.jurisdiction)",
                "CREATE INDEX IF NOT EXISTS FOR (c:Court) ON (c.jurisdiction)"
            ]
            
            for constraint in constraints:
                try:
                    session.run(constraint)
                except Exception as e:
                    logger.warning(f"Error creating constraint/index: {str(e)}")
    
    def close(self):
        """Close the Neo4j connection."""
        self.driver.close()
    
    def add_case(self, case_data: Dict) -> bool:
        """
        Add a case to the knowledge graph.
        
        Args:
            case_data: Case data
            
        Returns:
            Success flag
        """
        case_id = case_data.get("id") or case_data.get("case_id")
        jurisdiction = case_data.get("jurisdiction", "unknown")
        
        query = """
        MERGE (c:Case {case_id: $case_id})
        ON CREATE SET 
            c.title = $title,
            c.date_filed = $date_filed,
            c.docket_number = $docket_number,
            c.status = $status,
            c.jurisdiction = $jurisdiction,
            c.content = $content,
            c.citations = $citations,
            c.created_at = datetime(),
            c.doc_type = 'case_law'
        ON MATCH SET 
            c.title = $title,
            c.date_filed = $date_filed,
            c.docket_number = $docket_number,
            c.status = $status,
            c.jurisdiction = $jurisdiction,
            c.content = $content,
            c.citations = $citations,
            c.updated_at = datetime()
        
        WITH c
        
        FOREACH (citation IN $citations |
            MERGE (cit:Citation {text: citation})
            ON CREATE SET 
                cit.citation_id = apoc.create.uuid(),
                cit.created_at = datetime(),
                cit.type = 'case_citation'
            ON MATCH SET 
                cit.updated_at = datetime()
            MERGE (c)-[:HAS_CITATION]->(cit)
        )
        
        WITH c
        
        MATCH (court:Court {court_id: $court_id})
        MERGE (c)-[:FROM_COURT]->(court)
        
        RETURN c.case_id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    case_id=str(case_id),
                    title=case_data.get("case_name", ""),
                    date_filed=case_data.get("date_filed", ""),
                    docket_number=case_data.get("docket_number", ""),
                    status=case_data.get("status", ""),
                    jurisdiction=jurisdiction,
                    content=case_data.get("content", ""),
                    citations=case_data.get("citation", []),
                    court_id=case_data.get("court_id", "")
                )
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Error adding case to Neo4j: {str(e)}")
            return False
    
    def add_opinion(self, opinion_data: Dict) -> bool:
        """
        Add an opinion to the knowledge graph.
        
        Args:
            opinion_data: Opinion data
            
        Returns:
            Success flag
        """
        opinion_id = opinion_data.get("id") or opinion_data.get("opinion_id")
        case_id = opinion_data.get("case_id")
        
        query = """
        MATCH (c:Case {case_id: $case_id})
        
        MERGE (o:Opinion {opinion_id: $opinion_id})
        ON CREATE SET 
            o.author = $author,
            o.type = $type,
            o.has_full_text = $has_full_text,
            o.text_format = $text_format,
            o.jurisdiction = $jurisdiction,
            o.created_at = datetime()
        ON MATCH SET 
            o.author = $author,
            o.type = $type,
            o.has_full_text = $has_full_text,
            o.text_format = $text_format,
            o.updated_at = datetime()
        
        MERGE (c)-[:HAS_OPINION]->(o)
        
        WITH o
        
        FOREACH (judge_id IN CASE WHEN $author_id IS NOT NULL THEN [$author_id] ELSE [] END |
            MERGE (j:Judge {judge_id: judge_id})
            MERGE (o)-[:AUTHORED_BY]->(j)
        )
        
        RETURN o.opinion_id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    opinion_id=str(opinion_id),
                    case_id=str(case_id),
                    author=opinion_data.get("author_str", ""),
                    author_id=opinion_data.get("author_id"),
                    type=opinion_data.get("type", ""),
                    has_full_text=opinion_data.get("has_full_text", False),
                    text_format=opinion_data.get("text_format", ""),
                    jurisdiction=opinion_data.get("jurisdiction", "unknown")
                )
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Error adding opinion to Neo4j: {str(e)}")
            return False
    
    def add_court(self, court_data: Dict) -> bool:
        """
        Add a court to the knowledge graph.
        
        Args:
            court_data: Court data
            
        Returns:
            Success flag
        """
        court_id = court_data.get("id") or court_data.get("court_id")
        
        query = """
        MERGE (c:Court {court_id: $court_id})
        ON CREATE SET 
            c.name = $name,
            c.jurisdiction = $jurisdiction,
            c.court_type = $court_type,
            c.citation_abbreviation = $citation_abbreviation,
            c.created_at = datetime()
        ON MATCH SET 
            c.name = $name,
            c.jurisdiction = $jurisdiction,
            c.court_type = $court_type,
            c.citation_abbreviation = $citation_abbreviation,
            c.updated_at = datetime()
        
        WITH c
        
        FOREACH (parent_id IN CASE WHEN $parent_court_id IS NOT NULL THEN [$parent_court_id] ELSE [] END |
            MERGE (p:Court {court_id: parent_id})
            MERGE (c)-[:SUBORDINATE_TO]->(p)
        )
        
        RETURN c.court_id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    court_id=court_id,
                    name=court_data.get("name", ""),
                    jurisdiction=court_data.get("jurisdiction", ""),
                    court_type=court_data.get("court_type", ""),
                    citation_abbreviation=court_data.get("citation_abbreviation", ""),
                    parent_court_id=court_data.get("parent_court_id")
                )
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Error adding court to Neo4j: {str(e)}")
            return False
    
    def add_citation(self, citing_id: str, cited_id: str, citation_text: str, jurisdiction: str) -> bool:
        """
        Add a citation relationship between cases.
        
        Args:
            citing_id: ID of the citing case
            cited_id: ID of the cited case
            citation_text: Citation text
            jurisdiction: Jurisdiction code
            
        Returns:
            Success flag
        """
        query = """
        MATCH (citing:Case {case_id: $citing_id})
        MATCH (cited:Case {case_id: $cited_id})
        
        MERGE (citing)-[r:CITES]->(cited)
        ON CREATE SET 
            r.citation_text = $citation_text,
            r.created_at = datetime(),
            r.jurisdiction = $jurisdiction
        ON MATCH SET 
            r.citation_text = $citation_text,
            r.updated_at = datetime()
        
        RETURN citing.case_id, cited.case_id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    citing_id=str(citing_id),
                    cited_id=str(cited_id),
                    citation_text=citation_text,
                    jurisdiction=jurisdiction
                )
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Error adding citation to Neo4j: {str(e)}")
            return False
    
    def add_judge(self, judge_data: Dict) -> bool:
        """
        Add a judge to the knowledge graph.
        
        Args:
            judge_data: Judge data
            
        Returns:
            Success flag
        """
        judge_id = judge_data.get("id") or judge_data.get("judge_id")
        
        query = """
        MERGE (j:Judge {judge_id: $judge_id})
        ON CREATE SET 
            j.name = $name,
            j.jurisdiction = $jurisdiction,
            j.active = $active,
            j.created_at = datetime()
        ON MATCH SET 
            j.name = $name,
            j.jurisdiction = $jurisdiction,
            j.active = $active,
            j.updated_at = datetime()
        
        RETURN j.judge_id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    judge_id=str(judge_id),
                    name=judge_data.get("name", ""),
                    jurisdiction=judge_data.get("jurisdiction", ""),
                    active=judge_data.get("active", True)
                )
                
                return result.single() is not None
                
        except Exception as e:
            logger.error(f"Error adding judge to Neo4j: {str(e)}")
            return False
    
    def check_case_exists(self, case_id: str) -> bool:
        """
        Check if a Case node with the given ID exists.
        
        Args:
            case_id: The unique ID of the case to check (ensure it matches the property name, e.g., 'case_id').
            
        Returns:
            True if the case node exists, False otherwise.
        """
        query = """
            MATCH (c:Case {case_id: $case_id})
            RETURN c IS NOT NULL AS exists
            LIMIT 1
        """
        try:
            with self.driver.session() as session:
                result = session.run(query, case_id=case_id).single()
                return result["exists"] if result else False
        except Exception as e:
            logger.error(f"Error checking existence for case {case_id}: {str(e)}")
            return False # Assume false on error

    def get_case_citations(self, case_id: str) -> List[Dict]:
        """
        Get all cases cited by a specific case.
        
        Args:
            case_id: The case ID
            
        Returns:
            List of cited cases with metadata
        """
        query = """
        MATCH (c:Case {case_id: $case_id})-[r:CITES]->(cited:Case)
        RETURN cited.case_id AS cited_id, 
               cited.title AS title,
               cited.jurisdiction AS jurisdiction
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, case_id=str(case_id))
                
                return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"Error getting case citations from Neo4j: {str(e)}")
            return []
    
    def get_case_cited_by(self, case_id: str) -> List[Dict]:
        """
        Get all cases that cite a specific case.
        
        Args:
            case_id: The case ID
            
        Returns:
            List of citing cases with metadata
        """
        query = """
        MATCH (citing:Case)-[r:CITES]->(c:Case {case_id: $case_id})
        RETURN citing.case_id AS citing_id, 
               citing.title AS title,
               citing.jurisdiction AS jurisdiction
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, case_id=str(case_id))
                
                return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"Error getting cited by cases from Neo4j: {str(e)}")
            return []
    
    def get_jurisdiction_citation_network(self, jurisdiction: str, limit: int = 1000) -> Dict:
        """
        Get the citation network for a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            limit: Maximum number of nodes to return
            
        Returns:
            Network data with nodes and edges
        """
        query = """
        MATCH (c:Case {jurisdiction: $jurisdiction})
        OPTIONAL MATCH (c)-[r:CITES]->(cited:Case)
        WHERE cited.jurisdiction = $jurisdiction
        WITH c, cited, r
        LIMIT $limit
        
        RETURN 
            collect(distinct {
                id: c.case_id,
                title: c.title,
                date: c.date_filed,
                type: 'case'
            }) AS nodes,
            collect({
                source: c.case_id,
                target: cited.case_id,
                text: r.citation_text
            }) AS edges
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    jurisdiction=jurisdiction,
                    limit=limit
                )
                
                record = result.single()
                if record:
                    return {
                        "nodes": record["nodes"],
                        "edges": [e for e in record["edges"] if e["source"] and e["target"]]
                    }
                return {"nodes": [], "edges": []}
                
        except Exception as e:
            logger.error(f"Error getting citation network from Neo4j: {str(e)}")
            return {"nodes": [], "edges": []}
    
    def get_case_by_citation(self, citation: str) -> Optional[Dict]:
        """
        Find a case by citation.
        
        Args:
            citation: Citation text
            
        Returns:
            Case data if found
        """
        query = """
        MATCH (c:Case)-[:HAS_CITATION]->(cit:Citation {text: $citation})
        RETURN c.case_id AS case_id, 
               c.title AS title,
               c.jurisdiction AS jurisdiction,
               c.date_filed AS date_filed,
               c.docket_number AS docket_number,
               c.status AS status
        LIMIT 1
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, citation=citation)
                
                record = result.single()
                return dict(record) if record else None
                
        except Exception as e:
            logger.error(f"Error getting case by citation from Neo4j: {str(e)}")
            return None
    
    def get_relevant_cases(self, query_text: str, jurisdiction: str, limit: int = 10) -> List[Dict]:
        """
        Find relevant cases based on text similarity.
        This uses Neo4j's full-text search capabilities.
        
        Args:
            query_text: Search text
            jurisdiction: Jurisdiction filter
            limit: Maximum number of results
            
        Returns:
            List of relevant cases
        """
        # Note: This assumes a fulltext index has been created
        # CREATE FULLTEXT INDEX case_content IF NOT EXISTS FOR (c:Case) ON EACH [c.title, c.content]
        
        query = """
        CALL db.index.fulltext.queryNodes("case_content", $query_text) 
        YIELD node, score
        WHERE node.jurisdiction = $jurisdiction
        RETURN node.case_id AS case_id, 
               node.title AS title,
               node.jurisdiction AS jurisdiction,
               node.date_filed AS date_filed,
               score
        ORDER BY score DESC
        LIMIT $limit
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    query_text=query_text,
                    jurisdiction=jurisdiction,
                    limit=limit
                )
                
                return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"Error getting relevant cases from Neo4j: {str(e)}")
            return []
    
    def delete_case(self, case_id: str) -> bool:
        """
        Delete a case and all its relationships.
        Used for reversibility.
        
        Args:
            case_id: The case ID to delete
            
        Returns:
            Success flag
        """
        query = """
        MATCH (c:Case {case_id: $case_id})
        OPTIONAL MATCH (c)-[r]-()
        DELETE r, c
        RETURN count(c) AS deleted
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, case_id=str(case_id))
                
                record = result.single()
                return record and record["deleted"] > 0
                
        except Exception as e:
            logger.error(f"Error deleting case from Neo4j: {str(e)}")
            return False
    
    def get_jurisdiction_stats(self, jurisdiction: str) -> Dict:
        """
        Get statistics for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            Statistics dictionary
        """
        query = """
        MATCH (c:Case {jurisdiction: $jurisdiction})
        WITH count(c) AS case_count
        
        MATCH (c:Case {jurisdiction: $jurisdiction})-[r:CITES]->()
        WITH case_count, count(r) AS citation_count
        
        MATCH (c:Case {jurisdiction: $jurisdiction})-[:FROM_COURT]->(court:Court)
        WITH case_count, citation_count, count(DISTINCT court) AS court_count
        
        MATCH (c:Case {jurisdiction: $jurisdiction})-[:HAS_OPINION]->(o:Opinion)-[:AUTHORED_BY]->(j:Judge)
        WITH case_count, citation_count, court_count, count(DISTINCT j) AS judge_count
        
        RETURN {
            case_count: case_count,
            citation_count: citation_count,
            court_count: court_count,
            judge_count: judge_count
        } AS stats
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, jurisdiction=jurisdiction)
                
                record = result.single()
                return record["stats"] if record else {
                    "case_count": 0,
                    "citation_count": 0,
                    "court_count": 0,
                    "judge_count": 0
                }
                
        except Exception as e:
            logger.error(f"Error getting jurisdiction stats from Neo4j: {str(e)}")
            
            return {
                "status": "error",
                "error": str(e)
            }

    def create_case_node(self, case_id: str, properties: Dict) -> bool:
        """
        Create or merge a Case node in Neo4j.

        Args:
            case_id: The unique ID of the case.
            properties: A dictionary of properties for the case node 
                        (e.g., {'name': '...', 'jurisdiction': '...', 'date_filed': '...'}).

        Returns:
            True if the node was created or merged successfully, False otherwise.
        """
        # If driver failed to initialize, do nothing.
        if not self.driver:
            logger.warning(f"Neo4j driver not available. Skipping creation of case node {case_id}.")
            return False
        
        query = """
        MERGE (c:Case {case_id: $case_id})
        ON CREATE SET 
            c += $properties, 
            c.created_at = datetime()
        ON MATCH SET 
            c += $properties, 
            c.updated_at = datetime()
        RETURN c.case_id
        """
        
        # Ensure case_id is always a string for Neo4j
        case_id_str = str(case_id)
        # Prepare properties, ensuring None values are handled or excluded if needed
        # Neo4j doesn't store None properties, so we filter them
        valid_properties = {k: v for k, v in properties.items() if v is not None}
        
        try:
            with self.driver.session() as session:
                result = session.run(
                    query,
                    case_id=case_id_str,
                    properties=valid_properties
                )
                return result.single() is not None
        except Exception as e:
            logger.error(f"Error creating/merging case node {case_id_str} in Neo4j: {str(e)}")
            return False

    def get_case_citations(self, case_id: str) -> List[Dict]:
        """
        Get all cases cited by a specific case.
        
        Args:
            case_id: The case ID
            
        Returns:
            List of cited cases with metadata
        """
        query = """
        MATCH (c:Case {case_id: $case_id})-[r:CITES]->(cited:Case)
        RETURN cited.case_id AS cited_id, 
               cited.title AS title,
               cited.jurisdiction AS jurisdiction
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, case_id=str(case_id))
                
                return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"Error getting case citations from Neo4j: {str(e)}")
            return []

"""
Neo4j Helper Functions
Provides utilities for preparing data for Neo4j storage
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

def flatten_for_neo4j(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Flatten data for Neo4j storage, removing nested objects.
    Neo4j only accepts primitive types and arrays of primitive types.
    
    Args:
        data: The data to flatten
        
    Returns:
        Flattened data containing only Neo4j-compatible properties
    """
    flattened = {}
    
    # Copy only the primitive values and exclude nested objects
    for key, value in data.items():
        # Skip raw_data field completely
        if key == "raw_data":
            continue
            
        # Include only primitive types or arrays of primitive types
        if value is None or isinstance(value, (str, int, float, bool)):
            flattened[key] = value
        elif isinstance(value, list) and all(isinstance(item, (str, int, float, bool)) for item in value):
            flattened[key] = value
    
    # Add some serialized citation info if present
    if "raw_data" in data and "citations" in data["raw_data"]:
        citations = data["raw_data"]["citations"]
        if citations and isinstance(citations, list):
            citation_strings = []
            for citation in citations:
                if isinstance(citation, dict):
                    try:
                        # Format as "volume reporter page"
                        cite_str = f"{citation.get('volume')} {citation.get('reporter')} {citation.get('page')}"
                        citation_strings.append(cite_str)
                    except Exception as e:
                        logger.warning(f"Could not format citation: {str(e)}")
            
            if citation_strings:
                flattened["citation_strings"] = citation_strings
    
    # Make sure we have required fields for Neo4j
    for required_field in ["id", "jurisdiction"]:
        if required_field not in flattened and required_field in data:
            flattened[required_field] = data[required_field]
    
    # Ensure name field (required by Neo4j connector) is present
    if "name" not in flattened:
        if "case_name" in flattened:
            flattened["name"] = flattened["case_name"]
        elif "case_name" in data:
            flattened["name"] = data["case_name"]
    
    return flattened

"""
Pinecone Vector Database Connector
Handles vector embeddings for legal documents with multi-jurisdictional support.
Implements jurisdiction-specific namespaces for proper data separation.

Week 1 Enhancement: Implements enhanced jurisdiction-based namespace organization
with document type taxonomy support and standardized metadata handling.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Union, Any, Tuple
import numpy as np
from dotenv import load_dotenv
from pinecone import Pinecone, ServerlessSpec

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Load jurisdiction and document type configurations
def load_jurisdiction_config():
    """Load jurisdiction configuration from enhanced config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'enhanced_jurisdiction_config.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load jurisdiction config: {e}. Using defaults.")
        return {"jurisdictions": {}, "global_settings": {"default_jurisdiction": "tx"}}

def load_document_taxonomy():
    """Load document type taxonomy from config file."""
    try:
        config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'document_taxonomy.json')
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"Could not load document taxonomy: {e}. Using defaults.")
        return {"document_types": {}}

class PineconeConnector:
    """
    Connector for Pinecone vector database operations.
    Manages embeddings with jurisdiction-based namespaces.

    Week 1 Enhancement: Implements enhanced jurisdiction-based namespace organization
    with document type taxonomy support and standardized metadata handling.
    """

    def __init__(self, index_name: Optional[str] = None):
        """
        Initialize the Pinecone connector.

        Args:
            index_name: Optional index name override
        """
        self.api_key = os.getenv("PINECONE_API_KEY")
        self.environment = os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
        self.index_name = index_name or os.getenv("PINECONE_INDEX_NAME", "legal-documents")
        self.dimension = 1024  # Match the existing index dimension

        if not self.api_key:
            raise ValueError("Missing Pinecone API key in environment variables")

        # Load configuration
        self.jurisdiction_config = load_jurisdiction_config()
        self.document_taxonomy = load_document_taxonomy()

        # Initialize Pinecone client
        self.pc = Pinecone(api_key=self.api_key)
        
        # Check if index exists, create if it doesn't
        existing_indexes = [idx for idx in self.pc.list_indexes().names() if idx == self.index_name]
        if not existing_indexes:
            logger.info(f"Creating Pinecone index: {self.index_name} with dimension {self.dimension}")
            self.pc.create_index(
                name=self.index_name,
                dimension=self.dimension,
                metric="cosine",
                spec=ServerlessSpec(
                    cloud="aws",
                    region=self.environment.split('-')[0]  # Extract region from environment
                )
            )
        else:
            # Index exists, check its dimension
            logger.info(f"Connecting to existing Pinecone index: {self.index_name}")
            index_description = self.pc.describe_index(self.index_name)
            existing_dimension = index_description.dimension
            if existing_dimension != self.dimension:
                logger.warning(
                    f"Existing index '{self.index_name}' has dimension {existing_dimension}, "
                    f"but connector is configured for {self.dimension}. "
                    f"This may cause errors during upsert/query."
                )
            else:
                logger.info(f"Existing index '{self.index_name}' dimension ({existing_dimension}) matches expected dimension.")
        
        self.index = self.pc.Index(self.index_name)
        logger.info(f"Initialized Pinecone connector for index: {self.index_name}")
    
    def get_namespace(self, jurisdiction: str, doc_type: str = "case") -> str:
        """
        Generate a standardized namespace for jurisdiction-specific data using enhanced configuration.

        Args:
            jurisdiction: Jurisdiction code
            doc_type: Document type (case, statute, regulation, etc.)

        Returns:
            Namespace string following jurisdiction-based organization
        """
        # Validate jurisdiction
        if jurisdiction not in self.jurisdiction_config.get('jurisdictions', {}):
            logger.warning(f"Unknown jurisdiction: {jurisdiction}. Using default namespace pattern.")

        # Get jurisdiction-specific storage config
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        storage_config = jurisdiction_data.get('storage_config', {})

        # Use configured namespace pattern or default
        namespace_pattern = storage_config.get('pinecone_namespace', jurisdiction)

        # Apply global namespace pattern if configured
        global_pattern = self.jurisdiction_config.get('global_settings', {}).get('storage_organization', {}).get('pinecone_namespace_pattern')
        if global_pattern:
            namespace = global_pattern.format(jurisdiction=jurisdiction, doc_type=doc_type)
        else:
            namespace = f"{namespace_pattern}_{doc_type}"

        return namespace

    def validate_jurisdiction_doc_type(self, jurisdiction: str, doc_type: str) -> bool:
        """
        Validate that a jurisdiction supports a specific document type.

        Args:
            jurisdiction: Jurisdiction code
            doc_type: Document type

        Returns:
            Boolean indicating if the combination is valid
        """
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        supported_types = jurisdiction_data.get('document_types', [])

        return doc_type in supported_types

    def get_jurisdiction_storage_config(self, jurisdiction: str) -> Dict:
        """
        Get storage configuration for a specific jurisdiction.

        Args:
            jurisdiction: Jurisdiction code

        Returns:
            Storage configuration dictionary
        """
        jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
        return jurisdiction_data.get('storage_config', {
            'gcs_prefix': f'legal/{jurisdiction}',
            'pinecone_namespace': jurisdiction,
            'neo4j_labels': ['Document', jurisdiction.title()]
        })

    def get_document_type_info(self, doc_type: str) -> Dict:
        """
        Get document type information from taxonomy.

        Args:
            doc_type: Document type

        Returns:
            Document type information dictionary
        """
        return self.document_taxonomy.get('document_types', {}).get(doc_type, {})

    def store_embedding(self,
                       vector: List[float],
                       id: str,
                       metadata: Dict[str, Any],
                       jurisdiction: str,
                       doc_type: str = "case") -> bool:
        """
        Store a vector embedding in Pinecone with enhanced validation and metadata.

        Args:
            vector: The embedding vector
            id: Unique identifier for the vector
            metadata: Associated metadata
            jurisdiction: Jurisdiction code
            doc_type: Document type

        Returns:
            Success flag
        """
        # Validate jurisdiction and document type combination
        if not self.validate_jurisdiction_doc_type(jurisdiction, doc_type):
            logger.warning(f"Invalid jurisdiction-document type combination: {jurisdiction}-{doc_type}")

        namespace = self.get_namespace(jurisdiction, doc_type)

        try:
            # Enhance metadata with jurisdiction and document type information
            enhanced_metadata = metadata.copy()
            enhanced_metadata["jurisdiction"] = jurisdiction
            enhanced_metadata["doc_type"] = doc_type

            # Add document type taxonomy information
            doc_type_info = self.get_document_type_info(doc_type)
            if doc_type_info:
                enhanced_metadata["doc_type_name"] = doc_type_info.get("name", doc_type)
                enhanced_metadata["doc_type_description"] = doc_type_info.get("description", "")

            # Add jurisdiction information
            jurisdiction_data = self.jurisdiction_config.get('jurisdictions', {}).get(jurisdiction, {})
            if jurisdiction_data:
                enhanced_metadata["jurisdiction_name"] = jurisdiction_data.get("name", jurisdiction)
                enhanced_metadata["jurisdiction_level"] = jurisdiction_data.get("level", "unknown")

            # Upsert the vector
            self.index.upsert(
                vectors=[(id, vector, enhanced_metadata)],
                namespace=namespace
            )

            logger.info(f"Stored embedding for {id} in namespace {namespace}")
            return True
        except Exception as e:
            logger.error(f"Error storing embedding for {id}: {str(e)}")
            return False
    
    def store_embeddings_batch(self, 
                              vectors: List[List[float]], 
                              ids: List[str], 
                              metadatas: List[Dict[str, Any]],
                              jurisdiction: str,
                              doc_type: str = "case") -> bool:
        """
        Store multiple vector embeddings in batch.
        
        Args:
            vectors: List of embedding vectors
            ids: List of unique identifiers
            metadatas: List of associated metadata
            jurisdiction: Jurisdiction code
            doc_type: Document type
            
        Returns:
            Success flag
        """
        namespace = self.get_namespace(jurisdiction, doc_type)
        
        try:
            # Add jurisdiction and doc_type to all metadata
            for metadata in metadatas:
                metadata["jurisdiction"] = jurisdiction
                metadata["doc_type"] = doc_type
            
            # Prepare vectors in the format expected by Pinecone
            vector_data = [(ids[i], vectors[i], metadatas[i]) for i in range(len(ids))]
            
            # Upsert in batches of 100
            batch_size = 100
            for i in range(0, len(vector_data), batch_size):
                batch = vector_data[i:i+batch_size]
                self.index.upsert(
                    vectors=batch,
                    namespace=namespace
                )
            
            logger.info(f"Stored {len(vectors)} embeddings in namespace {namespace}")
            return True
        except Exception as e:
            logger.error(f"Error storing batch embeddings: {str(e)}")
            return False
    
    def fetch_embedding(self, 
                       id: str, 
                       jurisdiction: str,
                       doc_type: str = "case") -> Optional[Dict]:
        """
        Fetch a vector embedding by its ID from Pinecone.

        Args:
            id: Unique identifier for the vector
            jurisdiction: Jurisdiction code
            doc_type: Document type

        Returns:
            Dictionary containing the fetched vector data, or None if not found/error.
        """
        namespace = self.get_namespace(jurisdiction, doc_type)
        try:
            fetch_response = self.index.fetch(ids=[id], namespace=namespace)
            logger.debug(f"Raw fetch response for ID {id} in namespace {namespace}: {fetch_response}")
            # Check if the vector exists in the response
            if fetch_response and 'vectors' in fetch_response and id in fetch_response['vectors']:
                logger.debug(f"Fetched embedding for {id} from namespace {namespace}")
                return fetch_response['vectors'][id]
            else:
                logger.warning(f"Embedding {id} not found in namespace {namespace}")
                return None
        except Exception as e:
            logger.error(f"Error fetching embedding {id}: {str(e)}")
            return None
    
    def query_embeddings(self, 
                        query_vector: List[float], 
                        top_k: int = 5,
                        jurisdiction: Optional[str] = None,
                        doc_type: Optional[str] = None,
                        filter: Optional[Dict] = None) -> List[Dict]:
        """
        Query for similar vectors in Pinecone.
        
        Args:
            query_vector: The query embedding vector
            top_k: Number of results to return
            jurisdiction: Optional jurisdiction filter
            doc_type: Optional document type filter
            filter: Additional metadata filters
            
        Returns:
            List of matching results with scores and metadata
        """
        namespace = None
        if jurisdiction and doc_type:
            namespace = self.get_namespace(jurisdiction, doc_type)
        
        # Prepare filter
        if not filter:
            filter = {}
        
        # Always add jurisdiction and doc_type to filter if provided
        if jurisdiction:
            filter["jurisdiction"] = jurisdiction
        
        if doc_type:
            filter["doc_type"] = doc_type
        
        try:
            # Query the index
            results = self.index.query(
                vector=query_vector,
                top_k=top_k,
                namespace=namespace,
                filter=filter if filter else None,
                include_metadata=True
            )
            
            # Format results
            matches = []
            for match in results.matches:
                matches.append({
                    "id": match.id,
                    "score": match.score,
                    "metadata": match.metadata
                })
            
            return matches
        except Exception as e:
            logger.error(f"Error querying embeddings: {str(e)}")
            return []
    
    def delete_embedding(self, 
                        id: str, 
                        jurisdiction: str,
                        doc_type: str = "case") -> bool:
        """
        Delete a vector embedding from Pinecone.
        
        Args:
            id: Unique identifier for the vector
            jurisdiction: Jurisdiction code
            doc_type: Document type
            
        Returns:
            Success flag
        """
        namespace = self.get_namespace(jurisdiction, doc_type)
        
        try:
            self.index.delete(ids=[id], namespace=namespace)
            logger.info(f"Deleted embedding {id} from namespace {namespace}")
            return True
        except Exception as e:
            logger.error(f"Error deleting embedding {id}: {str(e)}")
            return False
    
    def delete_jurisdiction_embeddings(self, 
                                     jurisdiction: str,
                                     doc_type: Optional[str] = None) -> bool:
        """
        Delete all embeddings for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            doc_type: Optional document type filter
            
        Returns:
            Success flag
        """
        try:
            if doc_type:
                namespace = self.get_namespace(jurisdiction, doc_type)
                self.index.delete(delete_all=True, namespace=namespace)
                logger.info(f"Deleted all embeddings from namespace {namespace}")
            else:
                # List all namespaces
                stats = self.index.describe_index_stats()
                namespaces = stats.get("namespaces", {}).keys()
                
                # Delete from all namespaces that match the jurisdiction
                for namespace in namespaces:
                    if namespace.startswith(f"{jurisdiction}-"):
                        self.index.delete(delete_all=True, namespace=namespace)
                        logger.info(f"Deleted all embeddings from namespace {namespace}")
            
            return True
        except Exception as e:
            logger.error(f"Error deleting jurisdiction embeddings: {str(e)}")
            return False
    
    def list_jurisdictions(self) -> List[str]:
        """
        List all jurisdictions with data in Pinecone.
        
        Returns:
            List of jurisdiction codes
        """
        try:
            # Get all namespaces
            stats = self.index.describe_index_stats()
            namespaces = stats.namespaces.keys() if hasattr(stats, 'namespaces') else {}
            
            # Extract jurisdiction codes from namespace names
            jurisdictions = set()
            for namespace in namespaces:
                parts = namespace.split("-")
                if len(parts) >= 1:
                    jurisdictions.add(parts[0])
            
            return list(jurisdictions)
        except Exception as e:
            logger.error(f"Error listing jurisdictions: {str(e)}")
            return []
    
    def get_jurisdiction_stats(self, jurisdiction: str) -> Dict[str, int]:
        """
        Get statistics for a jurisdiction's embeddings.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            Dictionary of document types and counts
        """
        try:
            # Get all namespaces
            stats = self.index.describe_index_stats()
            namespaces = stats.get("namespaces", {})
            
            # Collect stats for the jurisdiction
            jurisdiction_stats = {}
            for namespace, ns_stats in namespaces.items():
                if namespace.startswith(f"{jurisdiction}-"):
                    doc_type = namespace.split("-")[1] if len(namespace.split("-")) > 1 else "unknown"
                    jurisdiction_stats[doc_type] = ns_stats.get("vector_count", 0)
            
            return jurisdiction_stats
        except Exception as e:
            logger.error(f"Error getting jurisdiction stats: {str(e)}")
            return {}
    
    def migrate_embeddings(self, 
                         old_namespace: str, 
                         jurisdiction: str, 
                         doc_type: str = "case",
                         batch_size: int = 100) -> bool:
        """
        Migrate embeddings from an old namespace to the new jurisdiction-based namespace.
        
        Args:
            old_namespace: Original namespace
            jurisdiction: Target jurisdiction code
            doc_type: Target document type
            batch_size: Batch size for processing
            
        Returns:
            Success flag
        """
        new_namespace = self.get_namespace(jurisdiction, doc_type)
        
        try:
            # Get total vector count in the old namespace
            stats = self.index.describe_index_stats()
            if hasattr(stats, 'namespaces') and old_namespace in stats.namespaces:
                total_vectors = stats.namespaces[old_namespace].vector_count
            else:
                total_vectors = 0
            
            if total_vectors == 0:
                logger.warning(f"No vectors found in namespace {old_namespace}")
                return True
            
            logger.info(f"Migrating {total_vectors} vectors from {old_namespace} to {new_namespace}")
            
            # Process in batches
            migrated_count = 0
            
            # We need to fetch IDs first since Pinecone doesn't have a built-in way to iterate
            # This is a simplified approach - in practice, you might need pagination
            # This example assumes we can get all IDs, which may not be practical for very large collections
            
            # For demonstration purposes - in a real implementation, you would need to
            # implement a proper pagination strategy to get all vectors
            
            # Placeholder for migration logic
            logger.info(f"Migration would move {total_vectors} vectors from {old_namespace} to {new_namespace}")
            logger.info("Note: Actual implementation would require fetching and migrating vectors in batches")
            
            return True
        except Exception as e:
            logger.error(f"Error migrating embeddings: {str(e)}")
            return False

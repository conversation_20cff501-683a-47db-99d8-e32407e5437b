"""
Supabase connector extension for role-based access control.
This file contains methods that extend the SupabaseConnector class
to support role-based access control and tenant isolation.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from supabase import create_client, Client

logger = logging.getLogger(__name__)

class SupabaseConnectorRBAC:
    """
    Extension methods for the SupabaseConnector class to support
    role-based access control and tenant isolation.
    These methods should be added to the SupabaseConnector class.
    """
    
    def get_tenant_jurisdictions(self, tenant_id: str) -> List[str]:
        """
        Get the list of jurisdictions allowed for a specific tenant.
        
        Args:
            tenant_id: The tenant ID to check
            
        Returns:
            List of jurisdiction codes the tenant can access
        """
        try:
            response = self.client.table("tenant_settings").select("allowed_jurisdictions").eq("tenant_id", tenant_id).execute()
            
            if response.data and len(response.data) > 0:
                jurisdictions = response.data[0].get("allowed_jurisdictions", [])
                if isinstance(jurisdictions, str):
                    return jurisdictions.split(",")
                return jurisdictions
                
            # If no settings found, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Error getting tenant jurisdictions: {str(e)}")
            return []
    
    def get_client_case_jurisdictions(self, user_id: str) -> List[str]:
        """
        Get the list of jurisdictions for cases associated with a client.
        
        Args:
            user_id: The client user ID
            
        Returns:
            List of jurisdiction codes from the client's cases
        """
        try:
            # Query cases associated with this client
            response = self.client.table("cases").select("jurisdiction").eq("client_id", user_id).execute()
            
            # Extract unique jurisdictions
            jurisdictions = set()
            for case in response.data:
                if case.get("jurisdiction"):
                    jurisdictions.add(case["jurisdiction"])
                    
            return list(jurisdictions)
            
        except Exception as e:
            logger.error(f"Error getting client case jurisdictions: {str(e)}")
            return []
    
    def create_processing_history(self, history_data: Dict[str, Any]) -> Dict:
        """
        Create a processing history record for audit and reversibility.
        
        Args:
            history_data: Dictionary with processing history data
            
        Returns:
            The created history record
        """
        try:
            response = self.client.table("processing_history").insert(history_data).execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            return {}
            
        except Exception as e:
            logger.error(f"Error creating processing history: {str(e)}")
            return {"error": str(e)}
    
    def get_user_role(self, user_id: str) -> Optional[str]:
        """
        Get the role for a specific user.
        
        Args:
            user_id: The user ID to check
            
        Returns:
            The user's role or None if not found
        """
        try:
            response = self.client.table("users").select("role").eq("id", user_id).execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0].get("role")
                
            return None
            
        except Exception as e:
            logger.error(f"Error getting user role: {str(e)}")
            return None
    
    def get_user_tenant(self, user_id: str) -> Optional[str]:
        """
        Get the tenant ID for a specific user.
        
        Args:
            user_id: The user ID to check
            
        Returns:
            The user's tenant ID or None if not found
        """
        try:
            response = self.client.table("users").select("tenant_id").eq("id", user_id).execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0].get("tenant_id")
                
            return None
            
        except Exception as e:
            logger.error(f"Error getting user tenant: {str(e)}")
            return None
    
    def log_access_attempt(self, user_id: str, resource_type: str, resource_id: str, action: str, success: bool, details: Optional[Dict] = None) -> Dict:
        """
        Log an access attempt for security auditing.
        
        Args:
            user_id: The user ID making the access attempt
            resource_type: Type of resource being accessed (e.g., "case", "jurisdiction")
            resource_id: ID of the resource being accessed
            action: Action being attempted (e.g., "read", "write", "delete")
            success: Whether the access was granted
            details: Optional additional details about the access attempt
            
        Returns:
            The created access log record
        """
        log_data = {
            "user_id": user_id,
            "resource_type": resource_type,
            "resource_id": resource_id,
            "action": action,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        
        try:
            response = self.client.table("access_logs").insert(log_data).execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0]
            return {}
            
        except Exception as e:
            logger.error(f"Error logging access attempt: {str(e)}")
            return {"error": str(e)}

"""
Vector Store Connector for Case Law Processing
Handles embeddings and vector search using Pinecone with Voyage AI models.
Supports jurisdiction-based access control for multi-tenant use.
"""

import os
import uuid
import logging
import requests
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

import pinecone
from pinecone import Pinecone, ServerlessSpec
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class PineconeConnector:
    """
    Connector for Pinecone vector operations using Voyage AI embeddings.
    Supports jurisdiction filtering for multi-tenant isolation.
    """
    
    def __init__(self, index_name: Optional[str] = None):
        """
        Initialize the Pinecone connector.
        
        Args:
            index_name: Optional index name override
        """
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.pinecone_environment = os.getenv("PINECONE_ENVIRONMENT", "us-east-1")
        self.index_name = index_name or os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
        self.voyage_api_key = os.getenv("VOYAGE_API_KEY")
        
        if not self.pinecone_api_key:
            raise ValueError("Missing Pinecone API key in environment variables")
        
        if not self.voyage_api_key:
            raise ValueError("Missing Voyage API key in environment variables")
        
        # Initialize Pinecone client
        self.client = Pinecone(api_key=self.pinecone_api_key)
        
        # Check if index exists, create if not
        self._ensure_index_exists()
        
        # Get active index reference
        self.index = self.client.Index(self.index_name)
        
        logger.info(f"Initialized Pinecone connector for index: {self.index_name}")
    
    def _ensure_index_exists(self):
        """Create the Pinecone index if it doesn't exist."""
        try:
            # Check if index exists
            indexes = self.client.list_indexes()
            
            if self.index_name not in [index.name for index in indexes]:
                logger.info(f"Creating Pinecone index: {self.index_name}")
                
                # Create the index with voyage dimensions (1024)
                self.client.create_index(
                    name=self.index_name,
                    dimension=1024,  # Voyage 3 Large dimension
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws", 
                        region=self.pinecone_environment
                    )
                )
                logger.info(f"Created Pinecone index: {self.index_name}")
            else:
                logger.info(f"Pinecone index already exists: {self.index_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring index exists: {str(e)}")
            raise
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embeddings using Voyage AI.
        
        Args:
            text: Text to embed
            
        Returns:
            Vector embedding as list of floats
        """
        try:
            # Call Voyage API to get embeddings
            response = requests.post(
                "https://api.voyageai.com/v1/embeddings",
                headers={
                    "Authorization": f"Bearer {self.voyage_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "voyage-3-large",
                    "input": text,
                }
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Extract the embedding
            embedding = result["data"][0]["embedding"]
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return empty vector in case of error
            return [0.0] * 1024
    
    def upsert_vector(self, vector_id: str, embedding: List[float], metadata: Dict) -> bool:
        """
        Insert or update a vector in Pinecone.
        
        Args:
            vector_id: Unique vector ID
            embedding: Vector embedding
            metadata: Vector metadata including jurisdiction info
            
        Returns:
            Success flag
        """
        try:
            # Ensure jurisdiction is in metadata for filtering
            if "jurisdiction" not in metadata:
                raise ValueError("Jurisdiction must be included in vector metadata")
            
            # Add timestamp to metadata
            metadata["timestamp"] = datetime.now().isoformat()
            
            # Upsert the vector
            self.index.upsert(
                vectors=[(vector_id, embedding, metadata)]
            )
            
            logger.info(f"Upserted vector {vector_id} for jurisdiction {metadata.get('jurisdiction')}")
            return True
            
        except Exception as e:
            logger.error(f"Error upserting vector: {str(e)}")
            return False
    
    def query(self, query_text: str, jurisdiction: Optional[str] = None, 
              top_k: int = 5, filter: Optional[Dict] = None) -> List[Dict]:
        """
        Query the vector store with text and filters.
        
        Args:
            query_text: Query text
            jurisdiction: Optional jurisdiction filter
            top_k: Number of results to return
            filter: Additional filters
            
        Returns:
            List of matches with metadata and scores
        """
        try:
            # Generate embedding for query
            query_embedding = self.generate_embedding(query_text)
            
            # Build filter
            filter_dict = filter or {}
            
            # Add jurisdiction filter if provided
            if jurisdiction:
                filter_dict["jurisdiction"] = jurisdiction
            
            # Perform the query
            results = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True
            )
            
            # Process results
            matches = []
            for match in results.get("matches", []):
                matches.append({
                    "id": match.get("id"),
                    "score": match.get("score"),
                    "metadata": match.get("metadata", {}),
                    "text": match.get("metadata", {}).get("text", "")
                })
            
            return matches
            
        except Exception as e:
            logger.error(f"Error querying vector store: {str(e)}")
            return []
    
    def delete_vectors(self, case_id: str, jurisdiction: str) -> bool:
        """
        Delete all vectors for a case.
        
        Args:
            case_id: Case ID
            jurisdiction: Jurisdiction code
            
        Returns:
            Success flag
        """
        try:
            # Build filter to find all vectors for this case
            filter_dict = {
                "case_id": case_id,
                "jurisdiction": jurisdiction
            }
            
            # List matching IDs
            results = self.index.query(
                vector=[0.0] * 1024,  # Dummy vector
                filter=filter_dict,
                top_k=1000,  # Get all matches
                include_metadata=False
            )
            
            # Extract IDs
            ids = [match.get("id") for match in results.get("matches", [])]
            
            if ids:
                # Delete vectors
                self.index.delete(ids=ids)
                logger.info(f"Deleted {len(ids)} vectors for case {case_id}")
            else:
                logger.info(f"No vectors found to delete for case {case_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting vectors: {str(e)}")
            return False

    def fetch_vector(self, vector_id: str, jurisdiction: str) -> Optional[Dict]:
        """
        Fetch a single vector by its ID and verify its jurisdiction.
        
        Args:
            vector_id: The unique ID of the vector to fetch.
            jurisdiction: The expected jurisdiction of the vector.
            
        Returns:
            The vector data dictionary if found and jurisdiction matches, else None.
        """
        try:
            # Fetch the vector by ID
            fetch_response = self.index.fetch(ids=[vector_id])
            
            # Check if vector exists and has the correct jurisdiction in metadata
            if vector_id in fetch_response.vectors:
                vector_data = fetch_response.vectors[vector_id]
                metadata = vector_data.get('metadata', {})
                if metadata.get('jurisdiction') == jurisdiction:
                    logger.debug(f"Fetched vector {vector_id} for jurisdiction {jurisdiction}")
                    # Return a dictionary representation
                    return {
                        'id': vector_data.id,
                        'values': vector_data.values,
                        'metadata': metadata
                    }
                else:
                    logger.warning(f"Fetched vector {vector_id}, but jurisdiction mismatch. Expected: {jurisdiction}, Found: {metadata.get('jurisdiction')}")
                    return None # Jurisdiction mismatch
            else:
                logger.debug(f"Vector {vector_id} not found in Pinecone.")
                return None # Not found

        except Exception as e:
            logger.error(f"Error fetching vector {vector_id}: {str(e)}")
            return None

    def jurisdictions_query(self, query_text: str, allowed_jurisdictions: List[str],
                          top_k: int = 5, filter: Optional[Dict] = None) -> Dict[str, List[Dict]]:
        """
        Query across multiple allowed jurisdictions based on user permissions.
        Returns results grouped by jurisdiction.
        
        Args:
            query_text: Query text
            allowed_jurisdictions: List of jurisdiction codes the user has access to
            top_k: Number of results per jurisdiction
            filter: Additional filters
            
        Returns:
            Dictionary of jurisdiction -> matches
        """
        results_by_jurisdiction = {}
        
        for jurisdiction in allowed_jurisdictions:
            # Query each jurisdiction
            matches = self.query(
                query_text=query_text,
                jurisdiction=jurisdiction,
                top_k=top_k,
                filter=filter
            )
            
            # Add to results
            results_by_jurisdiction[jurisdiction] = matches
        
        return results_by_jurisdiction
    
    def health_check(self) -> Dict:
        """
        Check the health of the vector store.
        
        Returns:
            Health status information
        """
        try:
            # Get index stats
            stats = self.index.describe_index_stats()
            
            # Query a simple vector to check functionality
            test_query = self.index.query(
                vector=[0.0] * 1024,
                top_k=1,
                include_metadata=False
            )
            
            return {
                "status": "healthy",
                "vector_count": stats.get("total_vector_count", 0),
                "namespaces": stats.get("namespaces", {}),
                "dimensions": stats.get("dimension", 1024),
                "query_test": "successful"
            }
            
        except Exception as e:
            logger.error(f"Vector store health check failed: {str(e)}")
            
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def get_jurisdiction_stats(self, jurisdiction: str) -> Dict:
        """
        Get statistics for vectors in a specific jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            Statistics dictionary
        """
        try:
            # Query with filter to get stats
            filter_dict = {"jurisdiction": jurisdiction}
            
            # Use stats feature
            stats = self.index.describe_index_stats(filter=filter_dict)
            
            return {
                "jurisdiction": jurisdiction,
                "vector_count": stats.get("total_vector_count", 0),
                "namespaces": stats.get("namespaces", {})
            }
            
        except Exception as e:
            logger.error(f"Error getting jurisdiction stats: {str(e)}")
            
            return {
                "jurisdiction": jurisdiction,
                "status": "error",
                "error": str(e)
            }

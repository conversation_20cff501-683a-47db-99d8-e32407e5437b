# Week 3 Relationships Module
# Multi-Practice Area Integration & Cross-Document Relationships

from .multi_practice_relationship_engine import MultiPracticeAreaRelationshipEngine
from .case_statute_linker import CaseStatuteLinkingEngine
from .citation_network_analyzer import CitationNetworkAnalyzer
from .cross_jurisdiction_handler import CrossJurisdictionHandler
from .practice_area_classifier import PracticeAreaClassifier
from .relationship_validator import RelationshipValidator

__all__ = [
    'MultiPracticeAreaRelationshipEngine',
    'CaseStatuteLinkingEngine', 
    'CitationNetworkAnalyzer',
    'CrossJurisdictionHandler',
    'PracticeAreaClassifier',
    'RelationshipValidator'
]

__version__ = '3.0.0'
__author__ = 'Texas Laws Personal Injury Team'
__description__ = 'Multi-practice area relationship detection and cross-document linking'

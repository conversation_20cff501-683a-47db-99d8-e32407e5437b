"""
Case-to-Statute Linking Engine
Links cases to statutes they cite, with support for fuzzy matching and missing statute detection.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict, Counter
import difflib

from ..processing.storage.supabase_connector import <PERSON>pabaseConnector
from ..processing.citation_extractor import CitationExtractor
from .practice_area_classifier import PracticeAreaClassifier

logger = logging.getLogger(__name__)

@dataclass
class StatuteCitation:
    """Represents a statute citation found in a case"""
    citation_text: str
    normalized_citation: str
    jurisdiction: str
    practice_area: str
    confidence_score: float
    context_text: str
    statute_title: Optional[str] = None
    statute_section: Optional[str] = None
    statute_chapter: Optional[str] = None

@dataclass
class StatuteLink:
    """Represents a link between a case and a statute"""
    case_id: str
    statute_id: Optional[str]  # None if statute not found in database
    citation: StatuteCitation
    link_type: str  # 'exact_match', 'fuzzy_match', 'missing_statute'
    match_confidence: float

@dataclass
class MissingStatute:
    """Represents a frequently cited statute not in our database"""
    citation_text: str
    normalized_citation: str
    jurisdiction: str
    practice_area: str
    frequency: int
    priority_score: float
    first_seen: str
    last_seen: str

class CaseStatuteLinkingEngine:
    """
    Engine for linking cases to statutes they cite.
    
    Features:
    1. Extract statute citations from case text
    2. Match citations to existing statutes in database
    3. Use fuzzy matching for citation variations
    4. Track missing statutes for future collection
    5. Calculate citation importance and frequency
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the case-statute linking engine.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.supabase = SupabaseConnector()
        self.citation_extractor = CitationExtractor()
        self.practice_area_classifier = PracticeAreaClassifier()
        
        # Statute citation patterns by jurisdiction
        self.statute_patterns = {
            'tx': [
                r'Tex\.?\s*([A-Z][a-z]+\.?\s*)+Code\s*§\s*(\d+\.?\d*)',
                r'Texas\s+([A-Z][a-z]+\s*)+Code\s*[Ss]ection\s*(\d+\.?\d*)',
                r'([A-Z][a-z]+\.?\s*)+Code\s*§\s*(\d+\.?\d*)',
                r'CPRC\s*§\s*(\d+\.?\d*)',
                r'Civil\s+Practice\s+and\s+Remedies\s+Code\s*§\s*(\d+\.?\d*)',
                r'Penal\s+Code\s*§\s*(\d+\.?\d*)',
                r'Family\s+Code\s*§\s*(\d+\.?\d*)',
                r'Business\s+Organizations\s+Code\s*§\s*(\d+\.?\d*)',
                r'Labor\s+Code\s*§\s*(\d+\.?\d*)',
                r'Property\s+Code\s*§\s*(\d+\.?\d*)',
            ],
            'fed': [
                r'(\d+)\s+U\.?S\.?C\.?\s*§\s*(\d+)',
                r'United\s+States\s+Code\s*[Tt]itle\s*(\d+)\s*[Ss]ection\s*(\d+)',
                r'USC\s*§\s*(\d+)',
                r'(\d+)\s+USC\s*(\d+)',
            ],
            'ca': [
                r'Cal\.?\s*([A-Z][a-z]+\.?\s*)+Code\s*§\s*(\d+\.?\d*)',
                r'California\s+([A-Z][a-z]+\s*)+Code\s*[Ss]ection\s*(\d+\.?\d*)',
            ],
            'ny': [
                r'N\.?Y\.?\s*([A-Z][a-z]+\.?\s*)+Law\s*§\s*(\d+\.?\d*)',
                r'New\s+York\s+([A-Z][a-z]+\s*)+Law\s*[Ss]ection\s*(\d+\.?\d*)',
            ]
        }
        
        # Practice area to statute code mapping
        self.practice_area_codes = {
            'personal_injury': ['civil', 'cprc', 'insurance', 'health'],
            'criminal_law': ['penal', 'criminal', 'code_criminal_procedure'],
            'family_law': ['family'],
            'business_law': ['business', 'commercial', 'corporations'],
            'employment_law': ['labor', 'employment'],
            'real_estate': ['property', 'real_estate'],
            'tax_law': ['tax', 'revenue'],
            'constitutional_law': ['constitution'],
            'immigration_law': ['immigration']
        }
    
    def link_case_to_statutes(
        self, 
        case_id: str, 
        case_text: str, 
        practice_area: Optional[str] = None
    ) -> List[StatuteLink]:
        """
        Link a case to all statutes it cites.
        
        Args:
            case_id: ID of the case
            case_text: Full text of the case
            practice_area: Practice area of the case (optional)
            
        Returns:
            List of statute links found
        """
        try:
            logger.info(f"Linking case {case_id} to statutes")
            
            # Get case information
            case_info = self._get_case_info(case_id)
            if not case_info:
                logger.error(f"Case {case_id} not found")
                return []
            
            jurisdiction = case_info.get('jurisdiction', 'tx')
            
            # Determine practice area if not provided
            if not practice_area:
                practice_area = self._determine_case_practice_area(case_info, case_text)
            
            # Extract statute citations from case text
            citations = self._extract_statute_citations(case_text, jurisdiction, practice_area)
            
            # Link citations to existing statutes
            statute_links = []
            for citation in citations:
                link = self._link_citation_to_statute(case_id, citation)
                statute_links.append(link)
            
            # Store links in database
            self._store_statute_links(statute_links)
            
            # Update missing statutes tracking
            self._update_missing_statutes(citations)
            
            logger.info(f"Found {len(statute_links)} statute links for case {case_id}")
            return statute_links
            
        except Exception as e:
            logger.error(f"Error linking case to statutes: {e}")
            return []
    
    def get_missing_statutes(
        self, 
        practice_area: Optional[str] = None,
        jurisdiction: Optional[str] = None,
        limit: int = 50
    ) -> List[MissingStatute]:
        """
        Get list of missing statutes that should be prioritized for collection.
        
        Args:
            practice_area: Filter by practice area
            jurisdiction: Filter by jurisdiction
            limit: Maximum number of results
            
        Returns:
            List of missing statutes ordered by priority
        """
        try:
            logger.info(f"Getting missing statutes for {practice_area or 'all'} practice areas")
            
            query = self.supabase.client.table("missing_statutes").select("*")
            
            if practice_area:
                query = query.eq("practice_area", practice_area)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            query = query.order("priority_score", desc=True).limit(limit)
            
            result = query.execute()
            
            missing_statutes = []
            for row in result.data:
                missing_statute = MissingStatute(
                    citation_text=row['citation_text'],
                    normalized_citation=row['normalized_citation'],
                    jurisdiction=row['jurisdiction'],
                    practice_area=row['practice_area'],
                    frequency=row['frequency'],
                    priority_score=row['priority_score'],
                    first_seen=row['first_seen'],
                    last_seen=row['last_seen']
                )
                missing_statutes.append(missing_statute)
            
            return missing_statutes
            
        except Exception as e:
            logger.error(f"Error getting missing statutes: {e}")
            return []
    
    def find_statute_by_citation(
        self, 
        citation_text: str, 
        jurisdiction: str,
        fuzzy_threshold: float = 0.8
    ) -> Optional[Dict]:
        """
        Find a statute in the database by citation text.
        
        Args:
            citation_text: Citation text to search for
            jurisdiction: Jurisdiction to search in
            fuzzy_threshold: Minimum similarity for fuzzy matching
            
        Returns:
            Statute document if found, None otherwise
        """
        try:
            normalized_citation = self._normalize_citation(citation_text, jurisdiction)
            
            # First try exact match
            result = self.supabase.client.table("documents") \
                .select("*") \
                .eq("jurisdiction", jurisdiction) \
                .eq("doc_type", "statute") \
                .execute()
            
            if result.data:
                for statute in result.data:
                    # Check if citation matches statute metadata
                    if self._citation_matches_statute(normalized_citation, statute):
                        return statute
                
                # Try fuzzy matching
                best_match = None
                best_score = 0.0
                
                for statute in result.data:
                    score = self._calculate_citation_similarity(normalized_citation, statute)
                    if score > best_score and score >= fuzzy_threshold:
                        best_score = score
                        best_match = statute
                
                return best_match
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding statute by citation: {e}")
            return None
    
    def get_case_statute_relationships(
        self, 
        case_id: str
    ) -> List[Dict]:
        """
        Get all statute relationships for a case.
        
        Args:
            case_id: ID of the case
            
        Returns:
            List of statute relationships
        """
        try:
            result = self.supabase.client.table("document_relationships") \
                .select("*") \
                .eq("source_document_id", case_id) \
                .eq("relationship_type", "cites") \
                .execute()
            
            return result.data
            
        except Exception as e:
            logger.error(f"Error getting case statute relationships: {e}")
            return []
    
    def _get_case_info(self, case_id: str) -> Optional[Dict]:
        """Get case information from database."""
        try:
            result = self.supabase.client.table("cases").select("*").eq("id", case_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting case info: {e}")
            return None
    
    def _determine_case_practice_area(self, case_info: Dict, case_text: str) -> str:
        """Determine the practice area of a case."""
        # Use existing practice area if available
        if case_info.get('primary_practice_area'):
            return case_info['primary_practice_area']
        
        # Classify based on case content
        document = {
            'content': case_text,
            'title': case_info.get('case_name', ''),
            'case_name': case_info.get('case_name', '')
        }
        
        return self.practice_area_classifier.classify(document)
    
    def _extract_statute_citations(
        self, 
        text: str, 
        jurisdiction: str, 
        practice_area: str
    ) -> List[StatuteCitation]:
        """Extract statute citations from case text."""
        citations = []
        
        # Get patterns for this jurisdiction
        patterns = self.statute_patterns.get(jurisdiction, [])
        
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            
            for match in matches:
                citation_text = match.group(0)
                
                # Get context around the citation
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                context = text[start:end]
                
                # Normalize the citation
                normalized = self._normalize_citation(citation_text, jurisdiction)
                
                # Calculate confidence based on pattern match quality
                confidence = self._calculate_citation_confidence(citation_text, pattern)
                
                # Extract statute components
                title, chapter, section = self._extract_statute_components(citation_text, jurisdiction)
                
                citation = StatuteCitation(
                    citation_text=citation_text,
                    normalized_citation=normalized,
                    jurisdiction=jurisdiction,
                    practice_area=practice_area,
                    confidence_score=confidence,
                    context_text=context,
                    statute_title=title,
                    statute_chapter=chapter,
                    statute_section=section
                )
                
                citations.append(citation)
        
        return citations
    
    def _normalize_citation(self, citation_text: str, jurisdiction: str) -> str:
        """Normalize a citation for consistent matching."""
        # Remove extra whitespace and standardize format
        normalized = re.sub(r'\s+', ' ', citation_text.strip())
        
        # Standardize section symbol
        normalized = re.sub(r'[Ss]ection\s*', '§ ', normalized)
        normalized = re.sub(r'[Ss]ec\.\s*', '§ ', normalized)
        
        # Standardize jurisdiction prefixes
        if jurisdiction == 'tx':
            normalized = re.sub(r'^Texas\s+', 'Tex. ', normalized)
            normalized = re.sub(r'^Tex\s+', 'Tex. ', normalized)
        elif jurisdiction == 'fed':
            normalized = re.sub(r'United\s+States\s+Code', 'U.S.C.', normalized)
            normalized = re.sub(r'USC', 'U.S.C.', normalized)
        
        return normalized.lower()
    
    def _link_citation_to_statute(self, case_id: str, citation: StatuteCitation) -> StatuteLink:
        """Link a citation to an existing statute or mark as missing."""
        # Try to find matching statute
        statute = self.find_statute_by_citation(
            citation.citation_text, 
            citation.jurisdiction
        )
        
        if statute:
            # Calculate match confidence
            match_confidence = self._calculate_citation_similarity(
                citation.normalized_citation, 
                statute
            )
            
            link_type = 'exact_match' if match_confidence > 0.95 else 'fuzzy_match'
            
            return StatuteLink(
                case_id=case_id,
                statute_id=statute['id'],
                citation=citation,
                link_type=link_type,
                match_confidence=match_confidence
            )
        else:
            # Statute not found - mark as missing
            return StatuteLink(
                case_id=case_id,
                statute_id=None,
                citation=citation,
                link_type='missing_statute',
                match_confidence=0.0
            )
    
    def _citation_matches_statute(self, citation: str, statute: Dict) -> bool:
        """Check if a citation matches a statute document."""
        # Check title and metadata for matches
        title = statute.get('title', '').lower()
        
        # Extract key components from citation
        citation_parts = citation.split()
        
        # Look for matches in title and metadata
        for part in citation_parts:
            if len(part) > 2 and part in title:
                return True
        
        # Check statute-specific metadata
        metadata = statute.get('metadata', {})
        if isinstance(metadata, dict):
            statute_section = metadata.get('statute_section', '')
            statute_chapter = metadata.get('statute_chapter', '')
            
            if statute_section and statute_section in citation:
                return True
            if statute_chapter and statute_chapter in citation:
                return True
        
        return False
    
    def _calculate_citation_similarity(self, citation: str, statute: Dict) -> float:
        """Calculate similarity between citation and statute."""
        title = statute.get('title', '').lower()
        
        # Use difflib for similarity calculation
        similarity = difflib.SequenceMatcher(None, citation, title).ratio()
        
        # Boost similarity if key components match
        if 'code' in citation and 'code' in title:
            similarity += 0.1
        
        if '§' in citation and ('section' in title or '§' in title):
            similarity += 0.1
        
        return min(1.0, similarity)
    
    def _calculate_citation_confidence(self, citation_text: str, pattern: str) -> float:
        """Calculate confidence score for a citation extraction."""
        base_confidence = 0.8
        
        # Boost confidence for complete citations
        if '§' in citation_text or 'section' in citation_text.lower():
            base_confidence += 0.1
        
        # Boost confidence for specific codes
        if any(code in citation_text.lower() for code in ['code', 'cprc', 'usc']):
            base_confidence += 0.1
        
        return min(1.0, base_confidence)
    
    def _extract_statute_components(
        self, 
        citation_text: str, 
        jurisdiction: str
    ) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Extract title, chapter, and section from citation."""
        title = None
        chapter = None
        section = None
        
        # Extract section number
        section_match = re.search(r'§\s*(\d+\.?\d*)', citation_text)
        if section_match:
            section = section_match.group(1)
        
        # Extract code/title name
        if jurisdiction == 'tx':
            title_match = re.search(r'([A-Z][a-z]+\.?\s*)+Code', citation_text)
            if title_match:
                title = title_match.group(0)
        elif jurisdiction == 'fed':
            title_match = re.search(r'(\d+)\s+U\.?S\.?C', citation_text)
            if title_match:
                title = title_match.group(1)
        
        return title, chapter, section
    
    def _store_statute_links(self, statute_links: List[StatuteLink]) -> None:
        """Store statute links in the database."""
        try:
            for link in statute_links:
                if link.statute_id:  # Only store links to existing statutes
                    relationship_data = {
                        'source_document_id': link.case_id,
                        'target_document_id': link.statute_id,
                        'relationship_type': 'cites',
                        'citation_text': link.citation.citation_text,
                        'context_text': link.citation.context_text,
                        'confidence_score': link.match_confidence,
                        'relationship_strength': 'strong' if link.match_confidence > 0.8 else 'medium',
                        'jurisdiction': link.citation.jurisdiction,
                        'source_practice_area': link.citation.practice_area,
                        'target_practice_area': 'statute',
                        'cross_practice_area': False
                    }
                    
                    self.supabase.client.table("document_relationships").insert(relationship_data).execute()
                    
        except Exception as e:
            logger.error(f"Error storing statute links: {e}")
    
    def _update_missing_statutes(self, citations: List[StatuteCitation]) -> None:
        """Update missing statutes tracking."""
        try:
            for citation in citations:
                # Check if this citation corresponds to a missing statute
                if not self.find_statute_by_citation(citation.citation_text, citation.jurisdiction):
                    # Update or insert missing statute record
                    existing = self.supabase.client.table("missing_statutes") \
                        .select("*") \
                        .eq("normalized_citation", citation.normalized_citation) \
                        .eq("jurisdiction", citation.jurisdiction) \
                        .execute()
                    
                    if existing.data:
                        # Update frequency
                        record = existing.data[0]
                        new_frequency = record['frequency'] + 1
                        priority_score = self._calculate_priority_score(new_frequency, citation.practice_area)
                        
                        self.supabase.client.table("missing_statutes") \
                            .update({
                                'frequency': new_frequency,
                                'priority_score': priority_score,
                                'last_seen': citation.context_text[:500]
                            }) \
                            .eq("id", record['id']) \
                            .execute()
                    else:
                        # Insert new missing statute
                        priority_score = self._calculate_priority_score(1, citation.practice_area)
                        
                        missing_data = {
                            'citation_text': citation.citation_text,
                            'normalized_citation': citation.normalized_citation,
                            'jurisdiction': citation.jurisdiction,
                            'practice_area': citation.practice_area,
                            'frequency': 1,
                            'priority_score': priority_score,
                            'collection_status': 'pending'
                        }
                        
                        self.supabase.client.table("missing_statutes").insert(missing_data).execute()
                        
        except Exception as e:
            logger.error(f"Error updating missing statutes: {e}")
    
    def _calculate_priority_score(self, frequency: int, practice_area: str) -> float:
        """Calculate priority score for missing statute collection."""
        # Base score from frequency
        base_score = min(1.0, frequency / 10.0)
        
        # Boost score for high-priority practice areas
        priority_areas = ['personal_injury', 'criminal_law', 'family_law', 'constitutional_law']
        if practice_area in priority_areas:
            base_score *= 1.5
        
        return min(1.0, base_score)

"""
Citation Network Analyzer
Analyzes citation networks within and across practice areas.
"""

import logging
import networkx as nx
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from collections import defaultdict, Counter
import numpy as np

from ..processing.storage.supabase_connector import SupabaseConnector

logger = logging.getLogger(__name__)

@dataclass
class NetworkMetrics:
    """Network analysis metrics for a document or practice area"""
    node_count: int
    edge_count: int
    density: float
    average_degree: float
    clustering_coefficient: float
    diameter: Optional[int]
    connected_components: int

@dataclass
class AuthorityScore:
    """Authority scoring for a document"""
    document_id: str
    authority_score: float
    centrality_score: float
    influence_score: float
    citation_count_incoming: int
    citation_count_outgoing: int
    cross_practice_citations: int

@dataclass
class PracticeAreaInfluence:
    """Influence analysis between practice areas"""
    source_practice_area: str
    target_practice_area: str
    citation_count: int
    influence_strength: float
    top_citing_documents: List[str]
    top_cited_documents: List[str]

class CitationNetworkAnalyzer:
    """
    Analyzes citation networks to understand document relationships and influence patterns.
    
    Features:
    1. Build citation networks for practice areas
    2. Calculate authority and influence scores
    3. Analyze cross-practice area influence
    4. Identify key documents and precedents
    5. Track temporal citation patterns
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the citation network analyzer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.supabase = SupabaseConnector()
        
        # Network analysis parameters
        self.analysis_params = {
            'pagerank_alpha': 0.85,
            'pagerank_max_iter': 100,
            'authority_weight_incoming': 0.6,
            'authority_weight_outgoing': 0.2,
            'authority_weight_cross_practice': 0.2,
            'influence_decay_factor': 0.9,
            'min_citation_threshold': 2
        }
    
    def analyze_practice_area_network(
        self,
        practice_area: str,
        jurisdiction: Optional[str] = None,
        include_cross_practice: bool = True
    ) -> Dict:
        """
        Analyze the citation network for a specific practice area.
        
        Args:
            practice_area: Practice area to analyze
            jurisdiction: Optional jurisdiction filter
            include_cross_practice: Whether to include cross-practice area citations
            
        Returns:
            Network analysis results
        """
        try:
            logger.info(f"Analyzing citation network for practice area: {practice_area}")
            
            # Build the citation network
            network = self._build_practice_area_network(
                practice_area, jurisdiction, include_cross_practice
            )
            
            if network.number_of_nodes() == 0:
                logger.warning(f"No nodes found for practice area {practice_area}")
                return self._empty_network_result()
            
            # Calculate network metrics
            metrics = self._calculate_network_metrics(network)
            
            # Calculate authority scores
            authority_scores = self._calculate_authority_scores(network, practice_area)
            
            # Find influential documents
            influential_docs = self._find_influential_documents(network, limit=10)
            
            # Analyze citation patterns
            citation_patterns = self._analyze_citation_patterns(network, practice_area)
            
            return {
                'practice_area': practice_area,
                'jurisdiction': jurisdiction,
                'network_metrics': metrics,
                'authority_scores': authority_scores,
                'influential_documents': influential_docs,
                'citation_patterns': citation_patterns,
                'network_graph': network  # For further analysis
            }
            
        except Exception as e:
            logger.error(f"Error analyzing practice area network: {e}")
            return self._empty_network_result()
    
    def analyze_cross_practice_influence(
        self,
        jurisdiction: Optional[str] = None
    ) -> List[PracticeAreaInfluence]:
        """
        Analyze influence patterns between different practice areas.
        
        Args:
            jurisdiction: Optional jurisdiction filter
            
        Returns:
            List of practice area influence relationships
        """
        try:
            logger.info("Analyzing cross-practice area influence")
            
            # Get all cross-practice relationships
            cross_relationships = self._get_cross_practice_relationships(jurisdiction)
            
            # Group by practice area pairs
            influence_map = defaultdict(lambda: {
                'citation_count': 0,
                'citing_docs': set(),
                'cited_docs': set()
            })
            
            for rel in cross_relationships:
                source_pa = rel['source_practice_area']
                target_pa = rel['target_practice_area']
                
                key = (source_pa, target_pa)
                influence_map[key]['citation_count'] += 1
                influence_map[key]['citing_docs'].add(rel['source_document_id'])
                influence_map[key]['cited_docs'].add(rel['target_document_id'])
            
            # Calculate influence scores and create results
            influences = []
            for (source_pa, target_pa), data in influence_map.items():
                influence_strength = self._calculate_influence_strength(
                    data['citation_count'], 
                    len(data['citing_docs']), 
                    len(data['cited_docs'])
                )
                
                influence = PracticeAreaInfluence(
                    source_practice_area=source_pa,
                    target_practice_area=target_pa,
                    citation_count=data['citation_count'],
                    influence_strength=influence_strength,
                    top_citing_documents=list(data['citing_docs'])[:5],
                    top_cited_documents=list(data['cited_docs'])[:5]
                )
                influences.append(influence)
            
            # Sort by influence strength
            influences.sort(key=lambda x: x.influence_strength, reverse=True)
            
            return influences
            
        except Exception as e:
            logger.error(f"Error analyzing cross-practice influence: {e}")
            return []
    
    def get_most_influential_cases(
        self,
        practice_area: str,
        jurisdiction: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict]:
        """
        Get the most influential cases in a practice area.
        
        Args:
            practice_area: Practice area to analyze
            jurisdiction: Optional jurisdiction filter
            limit: Maximum number of cases to return
            
        Returns:
            List of influential cases with scores
        """
        try:
            logger.info(f"Getting most influential cases for {practice_area}")
            
            # Build network and calculate authority scores
            network = self._build_practice_area_network(practice_area, jurisdiction)
            authority_scores = self._calculate_authority_scores(network, practice_area)
            
            # Get case information for top scoring documents
            top_cases = []
            for score in authority_scores[:limit]:
                case_info = self._get_document_info(score.document_id)
                if case_info:
                    case_info['authority_score'] = score.authority_score
                    case_info['centrality_score'] = score.centrality_score
                    case_info['influence_score'] = score.influence_score
                    case_info['citation_count_incoming'] = score.citation_count_incoming
                    case_info['citation_count_outgoing'] = score.citation_count_outgoing
                    top_cases.append(case_info)
            
            return top_cases
            
        except Exception as e:
            logger.error(f"Error getting most influential cases: {e}")
            return []
    
    def calculate_document_authority_score(
        self,
        document_id: str,
        practice_area: Optional[str] = None
    ) -> Optional[AuthorityScore]:
        """
        Calculate authority score for a specific document.
        
        Args:
            document_id: ID of the document
            practice_area: Optional practice area context
            
        Returns:
            Authority score or None if document not found
        """
        try:
            # Get document relationships
            incoming_citations = self._get_incoming_citations(document_id)
            outgoing_citations = self._get_outgoing_citations(document_id)
            cross_practice_citations = self._count_cross_practice_citations(document_id)
            
            # Calculate component scores
            incoming_score = len(incoming_citations) * self.analysis_params['authority_weight_incoming']
            outgoing_score = len(outgoing_citations) * self.analysis_params['authority_weight_outgoing']
            cross_practice_score = cross_practice_citations * self.analysis_params['authority_weight_cross_practice']
            
            # Calculate overall authority score
            authority_score = incoming_score + outgoing_score + cross_practice_score
            
            # Calculate centrality score (would need full network for exact PageRank)
            centrality_score = min(1.0, authority_score / 100.0)  # Normalized approximation
            
            # Calculate influence score based on citation quality
            influence_score = self._calculate_influence_score(incoming_citations, outgoing_citations)
            
            return AuthorityScore(
                document_id=document_id,
                authority_score=authority_score,
                centrality_score=centrality_score,
                influence_score=influence_score,
                citation_count_incoming=len(incoming_citations),
                citation_count_outgoing=len(outgoing_citations),
                cross_practice_citations=cross_practice_citations
            )
            
        except Exception as e:
            logger.error(f"Error calculating document authority score: {e}")
            return None
    
    def _build_practice_area_network(
        self,
        practice_area: str,
        jurisdiction: Optional[str] = None,
        include_cross_practice: bool = True
    ) -> nx.DiGraph:
        """Build a directed citation network for a practice area."""
        try:
            network = nx.DiGraph()
            
            # Get all relationships for this practice area
            query = self.supabase.client.table("document_relationships").select("*")
            
            if include_cross_practice:
                # Include relationships where either source or target is in this practice area
                query = query.or_(
                    f"source_practice_area.eq.{practice_area},"
                    f"target_practice_area.eq.{practice_area}"
                )
            else:
                # Only include relationships within this practice area
                query = query.eq("source_practice_area", practice_area).eq("target_practice_area", practice_area)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            result = query.execute()
            
            # Add nodes and edges to network
            for rel in result.data:
                source_id = rel['source_document_id']
                target_id = rel['target_document_id']
                
                # Add nodes with attributes
                if not network.has_node(source_id):
                    network.add_node(source_id, practice_area=rel['source_practice_area'])
                if not network.has_node(target_id):
                    network.add_node(target_id, practice_area=rel['target_practice_area'])
                
                # Add edge with attributes
                network.add_edge(
                    source_id, 
                    target_id,
                    relationship_type=rel['relationship_type'],
                    confidence_score=rel.get('confidence_score', 0.0),
                    citation_text=rel.get('citation_text', ''),
                    cross_practice=rel.get('cross_practice_area', False)
                )
            
            return network
            
        except Exception as e:
            logger.error(f"Error building practice area network: {e}")
            return nx.DiGraph()
    
    def _calculate_network_metrics(self, network: nx.DiGraph) -> NetworkMetrics:
        """Calculate basic network metrics."""
        try:
            node_count = network.number_of_nodes()
            edge_count = network.number_of_edges()
            
            if node_count == 0:
                return NetworkMetrics(0, 0, 0.0, 0.0, 0.0, None, 0)
            
            # Calculate density
            max_edges = node_count * (node_count - 1)
            density = edge_count / max_edges if max_edges > 0 else 0.0
            
            # Calculate average degree
            degrees = [d for n, d in network.degree()]
            average_degree = sum(degrees) / len(degrees) if degrees else 0.0
            
            # Calculate clustering coefficient
            try:
                clustering_coefficient = nx.average_clustering(network.to_undirected())
            except:
                clustering_coefficient = 0.0
            
            # Calculate diameter (for largest connected component)
            diameter = None
            try:
                if nx.is_weakly_connected(network):
                    diameter = nx.diameter(network.to_undirected())
                else:
                    # Get largest weakly connected component
                    largest_cc = max(nx.weakly_connected_components(network), key=len)
                    subgraph = network.subgraph(largest_cc).to_undirected()
                    if len(subgraph) > 1:
                        diameter = nx.diameter(subgraph)
            except:
                diameter = None
            
            # Count connected components
            connected_components = nx.number_weakly_connected_components(network)
            
            return NetworkMetrics(
                node_count=node_count,
                edge_count=edge_count,
                density=density,
                average_degree=average_degree,
                clustering_coefficient=clustering_coefficient,
                diameter=diameter,
                connected_components=connected_components
            )
            
        except Exception as e:
            logger.error(f"Error calculating network metrics: {e}")
            return NetworkMetrics(0, 0, 0.0, 0.0, 0.0, None, 0)
    
    def _calculate_authority_scores(
        self,
        network: nx.DiGraph,
        practice_area: str
    ) -> List[AuthorityScore]:
        """Calculate authority scores for all nodes in the network."""
        try:
            authority_scores = []
            
            # Calculate PageRank scores
            try:
                pagerank_scores = nx.pagerank(
                    network,
                    alpha=self.analysis_params['pagerank_alpha'],
                    max_iter=self.analysis_params['pagerank_max_iter']
                )
            except:
                pagerank_scores = {node: 0.0 for node in network.nodes()}
            
            # Calculate authority scores for each node
            for node in network.nodes():
                incoming_citations = list(network.predecessors(node))
                outgoing_citations = list(network.successors(node))
                
                # Count cross-practice citations
                cross_practice_count = sum(
                    1 for pred in incoming_citations
                    if network.nodes[pred].get('practice_area') != practice_area
                )
                
                # Calculate component scores
                incoming_score = len(incoming_citations) * self.analysis_params['authority_weight_incoming']
                outgoing_score = len(outgoing_citations) * self.analysis_params['authority_weight_outgoing']
                cross_practice_score = cross_practice_count * self.analysis_params['authority_weight_cross_practice']
                
                authority_score = incoming_score + outgoing_score + cross_practice_score
                centrality_score = pagerank_scores.get(node, 0.0)
                
                # Calculate influence score
                influence_score = self._calculate_node_influence_score(network, node)
                
                authority_scores.append(AuthorityScore(
                    document_id=node,
                    authority_score=authority_score,
                    centrality_score=centrality_score,
                    influence_score=influence_score,
                    citation_count_incoming=len(incoming_citations),
                    citation_count_outgoing=len(outgoing_citations),
                    cross_practice_citations=cross_practice_count
                ))
            
            # Sort by authority score
            authority_scores.sort(key=lambda x: x.authority_score, reverse=True)
            
            return authority_scores
            
        except Exception as e:
            logger.error(f"Error calculating authority scores: {e}")
            return []
    
    def _find_influential_documents(self, network: nx.DiGraph, limit: int = 10) -> List[str]:
        """Find the most influential documents in the network."""
        try:
            # Calculate influence based on multiple factors
            influence_scores = {}
            
            for node in network.nodes():
                # Factors: in-degree, out-degree, clustering, betweenness centrality
                in_degree = network.in_degree(node)
                out_degree = network.out_degree(node)
                
                # Simple influence calculation
                influence = in_degree * 2 + out_degree * 0.5
                influence_scores[node] = influence
            
            # Sort by influence and return top documents
            sorted_docs = sorted(influence_scores.items(), key=lambda x: x[1], reverse=True)
            return [doc_id for doc_id, score in sorted_docs[:limit]]
            
        except Exception as e:
            logger.error(f"Error finding influential documents: {e}")
            return []
    
    def _analyze_citation_patterns(self, network: nx.DiGraph, practice_area: str) -> Dict:
        """Analyze citation patterns in the network."""
        try:
            patterns = {
                'total_citations': network.number_of_edges(),
                'self_citations': 0,
                'cross_practice_citations': 0,
                'citation_types': Counter(),
                'temporal_patterns': {}
            }
            
            for source, target, data in network.edges(data=True):
                # Count citation types
                rel_type = data.get('relationship_type', 'cites')
                patterns['citation_types'][rel_type] += 1
                
                # Count cross-practice citations
                if data.get('cross_practice', False):
                    patterns['cross_practice_citations'] += 1
                
                # Count self-citations (same document citing itself)
                if source == target:
                    patterns['self_citations'] += 1
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error analyzing citation patterns: {e}")
            return {}
    
    def _get_cross_practice_relationships(self, jurisdiction: Optional[str] = None) -> List[Dict]:
        """Get all cross-practice area relationships."""
        try:
            query = self.supabase.client.table("document_relationships") \
                .select("*") \
                .eq("cross_practice_area", True)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            result = query.execute()
            return result.data
            
        except Exception as e:
            logger.error(f"Error getting cross-practice relationships: {e}")
            return []
    
    def _calculate_influence_strength(
        self,
        citation_count: int,
        citing_doc_count: int,
        cited_doc_count: int
    ) -> float:
        """Calculate influence strength between practice areas."""
        # Normalize by document counts and apply decay
        base_strength = citation_count / max(1, citing_doc_count + cited_doc_count)
        
        # Apply decay factor for very high citation counts (diminishing returns)
        decay_factor = self.analysis_params['influence_decay_factor']
        influence_strength = base_strength * (decay_factor ** (citation_count / 10))
        
        return min(1.0, influence_strength)
    
    def _get_incoming_citations(self, document_id: str) -> List[Dict]:
        """Get incoming citations for a document."""
        try:
            result = self.supabase.client.table("document_relationships") \
                .select("*") \
                .eq("target_document_id", document_id) \
                .execute()
            return result.data
        except Exception as e:
            logger.error(f"Error getting incoming citations: {e}")
            return []
    
    def _get_outgoing_citations(self, document_id: str) -> List[Dict]:
        """Get outgoing citations for a document."""
        try:
            result = self.supabase.client.table("document_relationships") \
                .select("*") \
                .eq("source_document_id", document_id) \
                .execute()
            return result.data
        except Exception as e:
            logger.error(f"Error getting outgoing citations: {e}")
            return []
    
    def _count_cross_practice_citations(self, document_id: str) -> int:
        """Count cross-practice citations for a document."""
        try:
            result = self.supabase.client.table("document_relationships") \
                .select("id", count="exact") \
                .eq("target_document_id", document_id) \
                .eq("cross_practice_area", True) \
                .execute()
            return result.count or 0
        except Exception as e:
            logger.error(f"Error counting cross-practice citations: {e}")
            return 0
    
    def _calculate_influence_score(self, incoming_citations: List[Dict], outgoing_citations: List[Dict]) -> float:
        """Calculate influence score based on citation quality."""
        # Simple influence calculation based on citation counts and confidence
        incoming_score = sum(rel.get('confidence_score', 0.5) for rel in incoming_citations)
        outgoing_score = sum(rel.get('confidence_score', 0.5) for rel in outgoing_citations) * 0.3
        
        total_score = incoming_score + outgoing_score
        return min(1.0, total_score / 10.0)  # Normalize to 0-1 range
    
    def _calculate_node_influence_score(self, network: nx.DiGraph, node: str) -> float:
        """Calculate influence score for a specific node in the network."""
        try:
            in_degree = network.in_degree(node)
            out_degree = network.out_degree(node)
            
            # Weight incoming citations more heavily
            influence = (in_degree * 0.7) + (out_degree * 0.3)
            
            # Normalize by network size
            max_possible = network.number_of_nodes() - 1
            if max_possible > 0:
                influence = influence / max_possible
            
            return min(1.0, influence)
            
        except Exception as e:
            logger.error(f"Error calculating node influence score: {e}")
            return 0.0
    
    def _get_document_info(self, document_id: str) -> Optional[Dict]:
        """Get document information from database."""
        try:
            # Try cases table first
            result = self.supabase.client.table("cases").select("*").eq("id", document_id).execute()
            if result.data:
                return result.data[0]
            
            # Try documents table
            result = self.supabase.client.table("documents").select("*").eq("id", document_id).execute()
            if result.data:
                return result.data[0]
            
            return None
        except Exception as e:
            logger.error(f"Error getting document info: {e}")
            return None
    
    def _empty_network_result(self) -> Dict:
        """Return empty network analysis result."""
        return {
            'practice_area': '',
            'jurisdiction': None,
            'network_metrics': NetworkMetrics(0, 0, 0.0, 0.0, 0.0, None, 0),
            'authority_scores': [],
            'influential_documents': [],
            'citation_patterns': {},
            'network_graph': nx.DiGraph()
        }

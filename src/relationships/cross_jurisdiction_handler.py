"""
Cross-Jurisdiction Handler
Handles relationships and citations across different jurisdictions.
"""

import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class JurisdictionMapping:
    """Mapping between jurisdictions for cross-jurisdictional citations"""
    source_jurisdiction: str
    target_jurisdiction: str
    relationship_type: str  # 'hierarchical', 'persuasive', 'parallel'
    strength: float  # 0.0 to 1.0

class CrossJurisdictionHandler:
    """
    Handles cross-jurisdictional relationships and citations.
    
    This handler:
    1. Manages jurisdiction hierarchies (federal > state > local)
    2. Handles persuasive vs. binding authority
    3. Validates cross-jurisdictional citations
    4. Calculates jurisdiction compatibility scores
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the cross-jurisdiction handler.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Jurisdiction hierarchy (higher number = higher authority)
        self.jurisdiction_hierarchy = {
            'fed': 100,  # Federal (highest)
            'scotus': 100,  # Supreme Court
            'ca1': 90, 'ca2': 90, 'ca3': 90, 'ca4': 90, 'ca5': 90,
            'ca6': 90, 'ca7': 90, 'ca8': 90, 'ca9': 90, 'ca10': 90,
            'ca11': 90, 'cadc': 90, 'cafc': 90,  # Circuit Courts
            'tx': 50,    # State level
            'ca': 50,    # California
            'ny': 50,    # New York
            'fl': 50,    # Florida
            'oh': 50,    # Ohio
        }
        
        # Jurisdiction relationships
        self.jurisdiction_relationships = {
            # Federal courts have binding authority over all state courts
            'fed': {'tx': 'binding', 'ca': 'binding', 'ny': 'binding', 'fl': 'binding', 'oh': 'binding'},
            'scotus': {'tx': 'binding', 'ca': 'binding', 'ny': 'binding', 'fl': 'binding', 'oh': 'binding'},
            
            # Circuit court relationships
            'ca5': {'tx': 'binding'},  # 5th Circuit covers Texas
            'ca9': {'ca': 'binding'},  # 9th Circuit covers California
            'ca2': {'ny': 'binding'},  # 2nd Circuit covers New York
            'ca11': {'fl': 'binding'}, # 11th Circuit covers Florida
            'ca6': {'oh': 'binding'},  # 6th Circuit covers Ohio
            
            # State courts have persuasive authority over other states
            'tx': {'ca': 'persuasive', 'ny': 'persuasive', 'fl': 'persuasive', 'oh': 'persuasive'},
            'ca': {'tx': 'persuasive', 'ny': 'persuasive', 'fl': 'persuasive', 'oh': 'persuasive'},
            'ny': {'tx': 'persuasive', 'ca': 'persuasive', 'fl': 'persuasive', 'oh': 'persuasive'},
            'fl': {'tx': 'persuasive', 'ca': 'persuasive', 'ny': 'persuasive', 'oh': 'persuasive'},
            'oh': {'tx': 'persuasive', 'ca': 'persuasive', 'ny': 'persuasive', 'fl': 'persuasive'},
        }
        
        # Jurisdiction compatibility scores
        self.compatibility_scores = {
            'binding': 1.0,
            'persuasive': 0.7,
            'parallel': 0.5,
            'none': 0.3
        }
    
    def get_jurisdiction_relationship(
        self,
        source_jurisdiction: str,
        target_jurisdiction: str
    ) -> Tuple[str, float]:
        """
        Get the relationship type and strength between two jurisdictions.
        
        Args:
            source_jurisdiction: Citing jurisdiction
            target_jurisdiction: Cited jurisdiction
            
        Returns:
            Tuple of (relationship_type, strength_score)
        """
        try:
            # Same jurisdiction is always binding
            if source_jurisdiction == target_jurisdiction:
                return 'binding', 1.0
            
            # Check defined relationships
            source_relationships = self.jurisdiction_relationships.get(target_jurisdiction, {})
            if source_jurisdiction in source_relationships:
                relationship_type = source_relationships[source_jurisdiction]
                strength = self.compatibility_scores[relationship_type]
                return relationship_type, strength
            
            # Check hierarchy for federal/state relationships
            source_level = self.jurisdiction_hierarchy.get(source_jurisdiction, 0)
            target_level = self.jurisdiction_hierarchy.get(target_jurisdiction, 0)
            
            if target_level > source_level:
                # Higher court cited by lower court (binding)
                return 'binding', 1.0
            elif target_level == source_level and target_level >= 50:
                # Same level state courts (persuasive)
                return 'persuasive', 0.7
            elif target_level < source_level:
                # Lower court cited by higher court (persuasive)
                return 'persuasive', 0.5
            
            # Default to minimal relationship
            return 'none', 0.3
            
        except Exception as e:
            logger.error(f"Error getting jurisdiction relationship: {e}")
            return 'none', 0.3
    
    def validate_cross_jurisdiction_citation(
        self,
        source_jurisdiction: str,
        target_jurisdiction: str,
        citation_context: str
    ) -> Dict:
        """
        Validate a cross-jurisdictional citation.
        
        Args:
            source_jurisdiction: Citing jurisdiction
            target_jurisdiction: Cited jurisdiction
            citation_context: Context of the citation
            
        Returns:
            Validation result dictionary
        """
        try:
            relationship_type, strength = self.get_jurisdiction_relationship(
                source_jurisdiction, target_jurisdiction
            )
            
            validation_result = {
                'is_valid': True,
                'relationship_type': relationship_type,
                'strength': strength,
                'issues': [],
                'recommendations': []
            }
            
            # Check for potential issues
            if relationship_type == 'none':
                validation_result['issues'].append(
                    f"Weak jurisdictional relationship between {source_jurisdiction} and {target_jurisdiction}"
                )
            
            if strength < 0.5:
                validation_result['recommendations'].append(
                    "Consider citing binding or more persuasive authority if available"
                )
            
            # Check context for appropriateness
            context_lower = citation_context.lower()
            
            if relationship_type == 'persuasive' and 'binding' in context_lower:
                validation_result['issues'].append(
                    "Citation context suggests binding authority but relationship is persuasive"
                )
            
            if relationship_type == 'binding' and any(word in context_lower for word in ['persuasive', 'guidance', 'consider']):
                validation_result['recommendations'].append(
                    "Citation has binding authority - consider stronger language"
                )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating cross-jurisdiction citation: {e}")
            return {
                'is_valid': False,
                'relationship_type': 'none',
                'strength': 0.0,
                'issues': [f"Validation error: {str(e)}"],
                'recommendations': []
            }
    
    def get_jurisdiction_authority_score(self, jurisdiction: str) -> float:
        """
        Get the authority score for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction code
            
        Returns:
            Authority score (0.0 to 1.0)
        """
        try:
            hierarchy_level = self.jurisdiction_hierarchy.get(jurisdiction, 0)
            max_level = max(self.jurisdiction_hierarchy.values())
            
            if max_level > 0:
                return hierarchy_level / max_level
            else:
                return 0.5  # Default score
                
        except Exception as e:
            logger.error(f"Error getting jurisdiction authority score: {e}")
            return 0.5
    
    def find_binding_jurisdictions(self, jurisdiction: str) -> List[str]:
        """
        Find jurisdictions that have binding authority over the given jurisdiction.
        
        Args:
            jurisdiction: Target jurisdiction
            
        Returns:
            List of jurisdictions with binding authority
        """
        try:
            binding_jurisdictions = []
            
            for source_jurisdiction in self.jurisdiction_hierarchy.keys():
                relationship_type, strength = self.get_jurisdiction_relationship(
                    jurisdiction, source_jurisdiction
                )
                
                if relationship_type == 'binding' and source_jurisdiction != jurisdiction:
                    binding_jurisdictions.append(source_jurisdiction)
            
            # Sort by authority level (highest first)
            binding_jurisdictions.sort(
                key=lambda j: self.jurisdiction_hierarchy.get(j, 0),
                reverse=True
            )
            
            return binding_jurisdictions
            
        except Exception as e:
            logger.error(f"Error finding binding jurisdictions: {e}")
            return []
    
    def find_persuasive_jurisdictions(self, jurisdiction: str) -> List[str]:
        """
        Find jurisdictions that have persuasive authority for the given jurisdiction.
        
        Args:
            jurisdiction: Target jurisdiction
            
        Returns:
            List of jurisdictions with persuasive authority
        """
        try:
            persuasive_jurisdictions = []
            
            for source_jurisdiction in self.jurisdiction_hierarchy.keys():
                relationship_type, strength = self.get_jurisdiction_relationship(
                    jurisdiction, source_jurisdiction
                )
                
                if relationship_type == 'persuasive':
                    persuasive_jurisdictions.append(source_jurisdiction)
            
            # Sort by authority level (highest first)
            persuasive_jurisdictions.sort(
                key=lambda j: self.jurisdiction_hierarchy.get(j, 0),
                reverse=True
            )
            
            return persuasive_jurisdictions
            
        except Exception as e:
            logger.error(f"Error finding persuasive jurisdictions: {e}")
            return []
    
    def calculate_citation_weight(
        self,
        source_jurisdiction: str,
        target_jurisdiction: str,
        base_weight: float = 1.0
    ) -> float:
        """
        Calculate the weight of a citation based on jurisdictional relationship.
        
        Args:
            source_jurisdiction: Citing jurisdiction
            target_jurisdiction: Cited jurisdiction
            base_weight: Base weight of the citation
            
        Returns:
            Adjusted weight based on jurisdictional relationship
        """
        try:
            relationship_type, strength = self.get_jurisdiction_relationship(
                source_jurisdiction, target_jurisdiction
            )
            
            # Apply jurisdictional multiplier
            jurisdictional_multiplier = {
                'binding': 1.5,
                'persuasive': 1.0,
                'parallel': 0.8,
                'none': 0.6
            }
            
            multiplier = jurisdictional_multiplier.get(relationship_type, 1.0)
            adjusted_weight = base_weight * multiplier * strength
            
            return adjusted_weight
            
        except Exception as e:
            logger.error(f"Error calculating citation weight: {e}")
            return base_weight * 0.5  # Conservative fallback
    
    def get_jurisdiction_summary(self, jurisdiction: str) -> Dict:
        """
        Get a summary of jurisdictional relationships for a jurisdiction.
        
        Args:
            jurisdiction: Jurisdiction to analyze
            
        Returns:
            Summary dictionary
        """
        try:
            binding_over = self.find_binding_jurisdictions(jurisdiction)
            persuasive_from = self.find_persuasive_jurisdictions(jurisdiction)
            authority_score = self.get_jurisdiction_authority_score(jurisdiction)
            
            # Find jurisdictions this one has authority over
            has_binding_over = []
            has_persuasive_over = []
            
            for target_jurisdiction in self.jurisdiction_hierarchy.keys():
                if target_jurisdiction != jurisdiction:
                    relationship_type, _ = self.get_jurisdiction_relationship(
                        target_jurisdiction, jurisdiction
                    )
                    
                    if relationship_type == 'binding':
                        has_binding_over.append(target_jurisdiction)
                    elif relationship_type == 'persuasive':
                        has_persuasive_over.append(target_jurisdiction)
            
            return {
                'jurisdiction': jurisdiction,
                'authority_score': authority_score,
                'binding_authority_from': binding_over,
                'persuasive_authority_from': persuasive_from,
                'has_binding_authority_over': has_binding_over,
                'has_persuasive_authority_over': has_persuasive_over,
                'hierarchy_level': self.jurisdiction_hierarchy.get(jurisdiction, 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting jurisdiction summary: {e}")
            return {
                'jurisdiction': jurisdiction,
                'authority_score': 0.5,
                'binding_authority_from': [],
                'persuasive_authority_from': [],
                'has_binding_authority_over': [],
                'has_persuasive_authority_over': [],
                'hierarchy_level': 0
            }
    
    def update_jurisdiction_relationships(self, new_relationships: Dict) -> None:
        """
        Update jurisdiction relationships configuration.
        
        Args:
            new_relationships: New relationship mappings
        """
        try:
            self.jurisdiction_relationships.update(new_relationships)
            logger.info(f"Updated jurisdiction relationships: {new_relationships}")
        except Exception as e:
            logger.error(f"Error updating jurisdiction relationships: {e}")
    
    def get_supported_jurisdictions(self) -> List[str]:
        """Get list of supported jurisdictions."""
        return list(self.jurisdiction_hierarchy.keys())

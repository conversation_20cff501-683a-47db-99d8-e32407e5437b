"""
Multi-Practice Area Relationship Engine
Detects and manages relationships between documents across different practice areas.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from dataclasses import dataclass
from collections import defaultdict

from ..processing.storage.supabase_connector import SupabaseConnector
from ..processing.storage.neo4j_connector import Neo4jConnector
from .practice_area_classifier import PracticeAreaClassifier
from .relationship_validator import RelationshipValidator

logger = logging.getLogger(__name__)

@dataclass
class RelationshipResult:
    """Result of relationship detection"""
    source_document_id: str
    target_document_id: str
    relationship_type: str
    source_practice_area: str
    target_practice_area: str
    cross_practice_area: bool
    confidence_score: float
    citation_text: str
    context_text: str
    relationship_strength: str

@dataclass
class PracticeAreaSummary:
    """Summary of practice area relationships"""
    practice_area: str
    total_documents: int
    total_relationships: int
    cross_practice_relationships: int
    most_cited_practice_areas: List[Tuple[str, int]]
    authority_score: float

class MultiPracticeAreaRelationshipEngine:
    """
    Core engine for detecting relationships between documents across multiple practice areas.
    
    This engine:
    1. Identifies cross-practice area citations
    2. Classifies relationship types (supportive, distinguishing, overruling)
    3. Calculates relationship strength and confidence
    4. Tracks practice area influence patterns
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the relationship engine.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.supabase = SupabaseConnector()
        self.neo4j = Neo4jConnector()
        self.practice_area_classifier = PracticeAreaClassifier()
        self.validator = RelationshipValidator()
        
        # Practice area configuration
        self.practice_areas = {
            'personal_injury': {
                'keywords': ['tort', 'negligence', 'liability', 'damages', 'injury', 'accident', 'malpractice'],
                'codes': ['civil', 'insurance', 'health']
            },
            'criminal_law': {
                'keywords': ['criminal', 'offense', 'felony', 'misdemeanor', 'prosecution', 'defense'],
                'codes': ['criminal', 'penal']
            },
            'business_law': {
                'keywords': ['corporation', 'contract', 'commercial', 'securities', 'partnership'],
                'codes': ['business', 'commercial']
            },
            'family_law': {
                'keywords': ['marriage', 'divorce', 'custody', 'adoption', 'domestic'],
                'codes': ['family']
            },
            'employment_law': {
                'keywords': ['employment', 'labor', 'workplace', 'discrimination', 'wages'],
                'codes': ['labor', 'employment']
            },
            'real_estate': {
                'keywords': ['property', 'real estate', 'land', 'zoning', 'mortgage'],
                'codes': ['property', 'real_estate']
            },
            'tax_law': {
                'keywords': ['tax', 'revenue', 'IRS', 'taxation', 'deduction'],
                'codes': ['tax', 'revenue']
            },
            'constitutional_law': {
                'keywords': ['constitutional', 'amendment', 'rights', 'due process', 'equal protection'],
                'codes': ['constitution']
            },
            'immigration_law': {
                'keywords': ['immigration', 'visa', 'citizenship', 'deportation', 'asylum'],
                'codes': ['immigration']
            }
        }
        
        # Relationship type patterns
        self.relationship_patterns = {
            'cites': [
                r'citing\s+',
                r'see\s+',
                r'accord\s+',
                r'following\s+',
                r'pursuant\s+to\s+'
            ],
            'distinguishes': [
                r'distinguishing\s+',
                r'distinguished\s+',
                r'unlike\s+',
                r'different\s+from\s+',
                r'not\s+applicable\s+',
                r'inapposite\s+'
            ],
            'overrules': [
                r'overruling\s+',
                r'overturning\s+',
                r'reversing\s+',
                r'abrogating\s+',
                r'superseding\s+'
            ],
            'follows': [
                r'following\s+',
                r'adhering\s+to\s+',
                r'consistent\s+with\s+',
                r'in\s+accordance\s+with\s+',
                r'applying\s+'
            ]
        }
    
    def detect_cross_practice_relationships(
        self, 
        source_document_id: str, 
        practice_areas: Optional[List[str]] = None,
        limit: int = 100
    ) -> List[RelationshipResult]:
        """
        Detect relationships between a document and documents in other practice areas.
        
        Args:
            source_document_id: ID of the source document
            practice_areas: List of practice areas to search (None for all)
            limit: Maximum number of relationships to return
            
        Returns:
            List of detected relationships
        """
        try:
            logger.info(f"Detecting cross-practice relationships for document {source_document_id}")
            
            # Get source document information
            source_doc = self._get_document_info(source_document_id)
            if not source_doc:
                logger.error(f"Source document {source_document_id} not found")
                return []
            
            source_practice_area = source_doc.get('primary_practice_area')
            if not source_practice_area:
                # Classify the source document's practice area
                source_practice_area = self._classify_document_practice_area(source_doc)
            
            # Get document text for citation extraction
            document_text = self._get_document_text(source_document_id)
            if not document_text:
                logger.warning(f"No text found for document {source_document_id}")
                return []
            
            # Extract citations from the document
            citations = self._extract_citations(document_text)
            
            relationships = []
            
            for citation in citations:
                # Find target documents that match this citation
                target_docs = self._find_target_documents(citation, practice_areas)
                
                for target_doc in target_docs:
                    target_practice_area = target_doc.get('primary_practice_area')
                    if not target_practice_area:
                        target_practice_area = self._classify_document_practice_area(target_doc)
                    
                    # Check if this is a cross-practice area relationship
                    is_cross_practice = source_practice_area != target_practice_area
                    
                    # Determine relationship type
                    relationship_type = self._determine_relationship_type(
                        citation['context'], citation['text']
                    )
                    
                    # Calculate confidence score
                    confidence = self._calculate_confidence_score(
                        citation, source_doc, target_doc
                    )
                    
                    # Determine relationship strength
                    strength = self._determine_relationship_strength(confidence, citation)
                    
                    relationship = RelationshipResult(
                        source_document_id=source_document_id,
                        target_document_id=target_doc['id'],
                        relationship_type=relationship_type,
                        source_practice_area=source_practice_area,
                        target_practice_area=target_practice_area,
                        cross_practice_area=is_cross_practice,
                        confidence_score=confidence,
                        citation_text=citation['text'],
                        context_text=citation['context'],
                        relationship_strength=strength
                    )
                    
                    relationships.append(relationship)
                    
                    if len(relationships) >= limit:
                        break
                
                if len(relationships) >= limit:
                    break
            
            # Store relationships in database
            self._store_relationships(relationships)
            
            logger.info(f"Detected {len(relationships)} relationships for document {source_document_id}")
            return relationships
            
        except Exception as e:
            logger.error(f"Error detecting cross-practice relationships: {e}")
            return []
    
    def get_practice_area_relationship_summary(
        self, 
        practice_area: str,
        jurisdiction: Optional[str] = None
    ) -> PracticeAreaSummary:
        """
        Get a summary of relationships for a specific practice area.
        
        Args:
            practice_area: Practice area to analyze
            jurisdiction: Optional jurisdiction filter
            
        Returns:
            Summary of practice area relationships
        """
        try:
            logger.info(f"Getting relationship summary for practice area: {practice_area}")
            
            # Get total documents in this practice area
            total_docs = self._count_practice_area_documents(practice_area, jurisdiction)
            
            # Get total relationships
            total_relationships = self._count_practice_area_relationships(practice_area, jurisdiction)
            
            # Get cross-practice relationships
            cross_practice_relationships = self._count_cross_practice_relationships(practice_area, jurisdiction)
            
            # Get most cited practice areas
            most_cited = self._get_most_cited_practice_areas(practice_area, jurisdiction)
            
            # Calculate authority score
            authority_score = self._calculate_practice_area_authority(practice_area, jurisdiction)
            
            return PracticeAreaSummary(
                practice_area=practice_area,
                total_documents=total_docs,
                total_relationships=total_relationships,
                cross_practice_relationships=cross_practice_relationships,
                most_cited_practice_areas=most_cited,
                authority_score=authority_score
            )
            
        except Exception as e:
            logger.error(f"Error getting practice area summary: {e}")
            return PracticeAreaSummary(
                practice_area=practice_area,
                total_documents=0,
                total_relationships=0,
                cross_practice_relationships=0,
                most_cited_practice_areas=[],
                authority_score=0.0
            )
    
    def _get_document_info(self, document_id: str) -> Optional[Dict]:
        """Get document information from database."""
        try:
            # Try cases table first
            result = self.supabase.client.table("cases").select("*").eq("id", document_id).execute()
            if result.data:
                return result.data[0]
            
            # Try documents table
            result = self.supabase.client.table("documents").select("*").eq("id", document_id).execute()
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document info: {e}")
            return None
    
    def _get_document_text(self, document_id: str) -> Optional[str]:
        """Get document text for citation extraction."""
        # This would integrate with your existing text retrieval system
        # For now, return a placeholder
        return "Document text would be retrieved here"
    
    def _extract_citations(self, text: str) -> List[Dict]:
        """Extract citations from document text."""
        citations = []
        
        # Basic citation patterns - this would be enhanced with your existing citation extractor
        citation_patterns = [
            r'(\d+\s+[A-Z][a-z]+\.?\s*\d*d?\s+\d+)',  # Case citations
            r'([A-Z][a-z]+\.?\s*[A-Z][a-z]+\.?\s*Code\s*§\s*\d+\.?\d*)',  # Statute citations
            r'(\d+\s+U\.S\.C\.?\s*§\s*\d+)',  # Federal statute citations
        ]
        
        for pattern in citation_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                context = text[start:end]
                
                citations.append({
                    'text': match.group(1),
                    'context': context,
                    'start': match.start(),
                    'end': match.end()
                })
        
        return citations
    
    def _find_target_documents(self, citation: Dict, practice_areas: Optional[List[str]]) -> List[Dict]:
        """Find documents that match a citation."""
        # This would integrate with your existing document search
        # For now, return empty list
        return []
    
    def _classify_document_practice_area(self, document: Dict) -> str:
        """Classify a document's practice area."""
        return self.practice_area_classifier.classify(document)
    
    def _determine_relationship_type(self, context: str, citation_text: str) -> str:
        """Determine the type of relationship based on context."""
        context_lower = context.lower()
        
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                if re.search(pattern, context_lower):
                    return rel_type
        
        return 'cites'  # Default relationship type
    
    def _calculate_confidence_score(self, citation: Dict, source_doc: Dict, target_doc: Dict) -> float:
        """Calculate confidence score for a relationship."""
        # Basic confidence calculation - would be enhanced
        base_score = 0.7
        
        # Boost confidence if citation is exact match
        if citation['text'].lower() in target_doc.get('title', '').lower():
            base_score += 0.2
        
        # Boost confidence if practice areas are related
        source_pa = source_doc.get('primary_practice_area', '')
        target_pa = target_doc.get('primary_practice_area', '')
        
        if self._are_practice_areas_related(source_pa, target_pa):
            base_score += 0.1
        
        return min(1.0, base_score)
    
    def _determine_relationship_strength(self, confidence: float, citation: Dict) -> str:
        """Determine relationship strength based on confidence and context."""
        if confidence >= 0.8:
            return 'strong'
        elif confidence >= 0.6:
            return 'medium'
        else:
            return 'weak'
    
    def _are_practice_areas_related(self, pa1: str, pa2: str) -> bool:
        """Check if two practice areas are related."""
        # Define related practice areas
        related_areas = {
            'criminal_law': ['constitutional_law'],
            'family_law': ['constitutional_law'],
            'employment_law': ['constitutional_law', 'business_law'],
            'personal_injury': ['constitutional_law', 'business_law'],
            'business_law': ['employment_law', 'tax_law'],
            'real_estate': ['business_law', 'tax_law'],
        }
        
        return pa2 in related_areas.get(pa1, []) or pa1 in related_areas.get(pa2, [])
    
    def _store_relationships(self, relationships: List[RelationshipResult]) -> None:
        """Store relationships in the database."""
        try:
            for rel in relationships:
                relationship_data = {
                    'source_document_id': rel.source_document_id,
                    'target_document_id': rel.target_document_id,
                    'relationship_type': rel.relationship_type,
                    'source_practice_area': rel.source_practice_area,
                    'target_practice_area': rel.target_practice_area,
                    'cross_practice_area': rel.cross_practice_area,
                    'citation_text': rel.citation_text,
                    'context_text': rel.context_text,
                    'confidence_score': rel.confidence_score,
                    'relationship_strength': rel.relationship_strength,
                    'created_at': datetime.now().isoformat()
                }
                
                self.supabase.client.table("document_relationships").insert(relationship_data).execute()
                
        except Exception as e:
            logger.error(f"Error storing relationships: {e}")
    
    def _count_practice_area_documents(self, practice_area: str, jurisdiction: Optional[str]) -> int:
        """Count documents in a practice area."""
        try:
            query = self.supabase.client.table("cases").select("id", count="exact")
            query = query.eq("primary_practice_area", practice_area)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            result = query.execute()
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Error counting practice area documents: {e}")
            return 0
    
    def _count_practice_area_relationships(self, practice_area: str, jurisdiction: Optional[str]) -> int:
        """Count relationships for a practice area."""
        try:
            query = self.supabase.client.table("document_relationships").select("id", count="exact")
            query = query.eq("source_practice_area", practice_area)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            result = query.execute()
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Error counting practice area relationships: {e}")
            return 0
    
    def _count_cross_practice_relationships(self, practice_area: str, jurisdiction: Optional[str]) -> int:
        """Count cross-practice relationships for a practice area."""
        try:
            query = self.supabase.client.table("document_relationships").select("id", count="exact")
            query = query.eq("source_practice_area", practice_area).eq("cross_practice_area", True)
            
            if jurisdiction:
                query = query.eq("jurisdiction", jurisdiction)
            
            result = query.execute()
            return result.count or 0
            
        except Exception as e:
            logger.error(f"Error counting cross-practice relationships: {e}")
            return 0
    
    def _get_most_cited_practice_areas(self, practice_area: str, jurisdiction: Optional[str]) -> List[Tuple[str, int]]:
        """Get most cited practice areas."""
        try:
            # This would be a more complex query in practice
            return [('constitutional_law', 25), ('business_law', 15), ('family_law', 10)]
            
        except Exception as e:
            logger.error(f"Error getting most cited practice areas: {e}")
            return []
    
    def _calculate_practice_area_authority(self, practice_area: str, jurisdiction: Optional[str]) -> float:
        """Calculate authority score for a practice area."""
        try:
            # This would be a complex calculation based on citation patterns
            return 0.75  # Placeholder
            
        except Exception as e:
            logger.error(f"Error calculating practice area authority: {e}")
            return 0.0

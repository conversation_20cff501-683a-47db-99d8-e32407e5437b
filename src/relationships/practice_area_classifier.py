"""
Practice Area Classifier
Enhanced classification of documents into practice areas with confidence scoring.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple
from collections import Counter
import json
import os

logger = logging.getLogger(__name__)

class PracticeAreaClassifier:
    """
    Enhanced practice area classifier that determines which area(s) of law
    a document belongs to based on content analysis.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the practice area classifier.
        
        Args:
            config_path: Optional path to practice area configuration file
        """
        self.config_path = config_path or self._get_default_config_path()
        self.practice_area_config = self._load_practice_area_config()
        
        # Enhanced keyword patterns for each practice area
        self.practice_area_patterns = {
            'personal_injury': {
                'primary_keywords': [
                    'tort', 'negligence', 'liability', 'damages', 'injury', 'accident',
                    'malpractice', 'wrongful death', 'slip and fall', 'car accident',
                    'medical malpractice', 'product liability', 'premises liability'
                ],
                'secondary_keywords': [
                    'plaintiff', 'defendant', 'compensation', 'settlement', 'pain and suffering',
                    'bodily injury', 'economic damages', 'punitive damages'
                ],
                'legal_concepts': [
                    'duty of care', 'breach of duty', 'causation', 'proximate cause',
                    'comparative negligence', 'contributory negligence', 'strict liability'
                ],
                'statute_patterns': [
                    r'civil\s+practice\s+and\s+remedies\s+code',
                    r'cprc\s*§',
                    r'texas\s+civil\s+practice'
                ]
            },
            'criminal_law': {
                'primary_keywords': [
                    'criminal', 'offense', 'felony', 'misdemeanor', 'prosecution', 'defense',
                    'guilty', 'innocent', 'conviction', 'sentence', 'prison', 'jail'
                ],
                'secondary_keywords': [
                    'defendant', 'prosecutor', 'district attorney', 'public defender',
                    'plea bargain', 'trial', 'jury', 'verdict', 'appeal'
                ],
                'legal_concepts': [
                    'beyond reasonable doubt', 'probable cause', 'miranda rights',
                    'fourth amendment', 'fifth amendment', 'sixth amendment',
                    'due process', 'search and seizure'
                ],
                'statute_patterns': [
                    r'penal\s+code',
                    r'criminal\s+code',
                    r'code\s+of\s+criminal\s+procedure'
                ]
            },
            'family_law': {
                'primary_keywords': [
                    'marriage', 'divorce', 'custody', 'adoption', 'domestic',
                    'child support', 'alimony', 'spousal support', 'visitation'
                ],
                'secondary_keywords': [
                    'family court', 'parental rights', 'guardianship', 'paternity',
                    'prenuptial', 'postnuptial', 'community property'
                ],
                'legal_concepts': [
                    'best interests of child', 'joint custody', 'sole custody',
                    'parenting plan', 'domestic violence', 'restraining order'
                ],
                'statute_patterns': [
                    r'family\s+code',
                    r'domestic\s+relations',
                    r'marriage\s+and\s+divorce'
                ]
            },
            'business_law': {
                'primary_keywords': [
                    'corporation', 'contract', 'commercial', 'securities', 'partnership',
                    'LLC', 'business', 'corporate', 'merger', 'acquisition'
                ],
                'secondary_keywords': [
                    'shareholder', 'board of directors', 'fiduciary duty', 'breach of contract',
                    'intellectual property', 'trademark', 'copyright', 'patent'
                ],
                'legal_concepts': [
                    'piercing corporate veil', 'business judgment rule', 'due diligence',
                    'securities fraud', 'insider trading', 'antitrust'
                ],
                'statute_patterns': [
                    r'business\s+organizations\s+code',
                    r'commercial\s+code',
                    r'securities\s+act'
                ]
            },
            'employment_law': {
                'primary_keywords': [
                    'employment', 'labor', 'workplace', 'discrimination', 'wages',
                    'overtime', 'harassment', 'wrongful termination', 'EEOC'
                ],
                'secondary_keywords': [
                    'employee', 'employer', 'union', 'collective bargaining',
                    'workers compensation', 'FMLA', 'ADA', 'Title VII'
                ],
                'legal_concepts': [
                    'at-will employment', 'hostile work environment', 'reasonable accommodation',
                    'protected class', 'retaliation', 'whistleblower'
                ],
                'statute_patterns': [
                    r'labor\s+code',
                    r'employment\s+law',
                    r'fair\s+labor\s+standards\s+act'
                ]
            },
            'constitutional_law': {
                'primary_keywords': [
                    'constitutional', 'amendment', 'rights', 'due process', 'equal protection',
                    'first amendment', 'second amendment', 'fourth amendment', 'fourteenth amendment'
                ],
                'secondary_keywords': [
                    'supreme court', 'constitutional convention', 'bill of rights',
                    'separation of powers', 'federalism', 'commerce clause'
                ],
                'legal_concepts': [
                    'strict scrutiny', 'intermediate scrutiny', 'rational basis',
                    'substantive due process', 'procedural due process', 'incorporation doctrine'
                ],
                'statute_patterns': [
                    r'constitution',
                    r'constitutional\s+amendment',
                    r'bill\s+of\s+rights'
                ]
            },
            'real_estate': {
                'primary_keywords': [
                    'property', 'real estate', 'land', 'zoning', 'mortgage',
                    'deed', 'title', 'easement', 'landlord', 'tenant'
                ],
                'secondary_keywords': [
                    'foreclosure', 'eviction', 'lease', 'rental', 'property tax',
                    'homeowners association', 'HOA', 'condominium'
                ],
                'legal_concepts': [
                    'fee simple', 'life estate', 'joint tenancy', 'tenancy in common',
                    'adverse possession', 'eminent domain', 'quiet title'
                ],
                'statute_patterns': [
                    r'property\s+code',
                    r'real\s+estate\s+law',
                    r'landlord\s+tenant\s+act'
                ]
            },
            'tax_law': {
                'primary_keywords': [
                    'tax', 'revenue', 'IRS', 'taxation', 'deduction',
                    'income tax', 'property tax', 'sales tax', 'estate tax'
                ],
                'secondary_keywords': [
                    'taxpayer', 'tax return', 'audit', 'penalty', 'interest',
                    'tax court', 'tax lien', 'tax levy'
                ],
                'legal_concepts': [
                    'tax avoidance', 'tax evasion', 'tax shelter', 'depreciation',
                    'capital gains', 'ordinary income', 'tax-exempt'
                ],
                'statute_patterns': [
                    r'tax\s+code',
                    r'internal\s+revenue\s+code',
                    r'revenue\s+and\s+taxation'
                ]
            },
            'immigration_law': {
                'primary_keywords': [
                    'immigration', 'visa', 'citizenship', 'deportation', 'asylum',
                    'green card', 'naturalization', 'refugee', 'border'
                ],
                'secondary_keywords': [
                    'ICE', 'USCIS', 'immigration court', 'removal proceedings',
                    'work permit', 'student visa', 'family reunification'
                ],
                'legal_concepts': [
                    'adjustment of status', 'consular processing', 'inadmissibility',
                    'persecution', 'credible fear', 'withholding of removal'
                ],
                'statute_patterns': [
                    r'immigration\s+and\s+nationality\s+act',
                    r'INA\s*§',
                    r'immigration\s+law'
                ]
            }
        }
    
    def classify(self, document: Dict) -> str:
        """
        Classify a document's primary practice area.
        
        Args:
            document: Document dictionary with text content
            
        Returns:
            Primary practice area code
        """
        try:
            # Get document text
            text = self._extract_text_from_document(document)
            if not text:
                return 'unknown'
            
            # Calculate scores for each practice area
            scores = self._calculate_practice_area_scores(text)
            
            # Return the practice area with highest score
            if scores:
                return max(scores, key=scores.get)
            
            return 'unknown'
            
        except Exception as e:
            logger.error(f"Error classifying document practice area: {e}")
            return 'unknown'
    
    def classify_with_confidence(self, document: Dict) -> Tuple[str, float, List[Tuple[str, float]]]:
        """
        Classify a document with confidence scores for all practice areas.
        
        Args:
            document: Document dictionary with text content
            
        Returns:
            Tuple of (primary_practice_area, confidence_score, all_scores)
        """
        try:
            # Get document text
            text = self._extract_text_from_document(document)
            if not text:
                return 'unknown', 0.0, []
            
            # Calculate scores for each practice area
            scores = self._calculate_practice_area_scores(text)
            
            if not scores:
                return 'unknown', 0.0, []
            
            # Sort scores by value
            sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            
            primary_area = sorted_scores[0][0]
            primary_score = sorted_scores[0][1]
            
            # Calculate confidence based on score separation
            confidence = self._calculate_confidence(sorted_scores)
            
            return primary_area, confidence, sorted_scores
            
        except Exception as e:
            logger.error(f"Error classifying document with confidence: {e}")
            return 'unknown', 0.0, []
    
    def classify_multiple_areas(self, document: Dict, threshold: float = 0.3) -> List[str]:
        """
        Classify a document into multiple practice areas if it crosses boundaries.
        
        Args:
            document: Document dictionary with text content
            threshold: Minimum score threshold for inclusion
            
        Returns:
            List of practice areas that meet the threshold
        """
        try:
            # Get document text
            text = self._extract_text_from_document(document)
            if not text:
                return []
            
            # Calculate scores for each practice area
            scores = self._calculate_practice_area_scores(text)
            
            # Return all areas above threshold
            return [area for area, score in scores.items() if score >= threshold]
            
        except Exception as e:
            logger.error(f"Error classifying multiple practice areas: {e}")
            return []
    
    def _extract_text_from_document(self, document: Dict) -> str:
        """Extract text content from document."""
        # Try different text fields
        text_fields = ['content', 'text', 'opinion_text', 'case_name', 'title', 'summary']
        
        text_parts = []
        for field in text_fields:
            if field in document and document[field]:
                text_parts.append(str(document[field]))
        
        return ' '.join(text_parts).lower()
    
    def _calculate_practice_area_scores(self, text: str) -> Dict[str, float]:
        """Calculate scores for each practice area based on text content."""
        scores = {}
        
        for area, patterns in self.practice_area_patterns.items():
            score = 0.0
            
            # Score primary keywords (highest weight)
            primary_matches = sum(1 for keyword in patterns['primary_keywords'] 
                                if keyword.lower() in text)
            score += primary_matches * 3.0
            
            # Score secondary keywords (medium weight)
            secondary_matches = sum(1 for keyword in patterns['secondary_keywords'] 
                                  if keyword.lower() in text)
            score += secondary_matches * 2.0
            
            # Score legal concepts (medium weight)
            concept_matches = sum(1 for concept in patterns['legal_concepts'] 
                                if concept.lower() in text)
            score += concept_matches * 2.0
            
            # Score statute patterns (high weight)
            statute_matches = sum(1 for pattern in patterns['statute_patterns'] 
                                if re.search(pattern, text, re.IGNORECASE))
            score += statute_matches * 4.0
            
            # Normalize score by total possible matches
            total_possible = (len(patterns['primary_keywords']) * 3.0 + 
                            len(patterns['secondary_keywords']) * 2.0 + 
                            len(patterns['legal_concepts']) * 2.0 + 
                            len(patterns['statute_patterns']) * 4.0)
            
            if total_possible > 0:
                scores[area] = score / total_possible
            else:
                scores[area] = 0.0
        
        return scores
    
    def _calculate_confidence(self, sorted_scores: List[Tuple[str, float]]) -> float:
        """Calculate confidence based on score separation."""
        if len(sorted_scores) < 2:
            return 1.0 if sorted_scores else 0.0
        
        top_score = sorted_scores[0][1]
        second_score = sorted_scores[1][1]
        
        # Confidence is based on the gap between top two scores
        if top_score == 0:
            return 0.0
        
        gap = top_score - second_score
        confidence = min(1.0, 0.5 + (gap * 2.0))  # Scale gap to confidence
        
        return confidence
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path."""
        current_dir = os.path.dirname(__file__)
        return os.path.join(current_dir, '..', 'config', 'document_taxonomy.json')
    
    def _load_practice_area_config(self) -> Dict:
        """Load practice area configuration from file."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    return config.get('practice_areas', {})
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                return {}
        except Exception as e:
            logger.error(f"Error loading practice area configuration: {e}")
            return {}
    
    def get_practice_area_keywords(self, practice_area: str) -> List[str]:
        """Get all keywords for a practice area."""
        if practice_area not in self.practice_area_patterns:
            return []
        
        patterns = self.practice_area_patterns[practice_area]
        keywords = []
        keywords.extend(patterns.get('primary_keywords', []))
        keywords.extend(patterns.get('secondary_keywords', []))
        keywords.extend(patterns.get('legal_concepts', []))
        
        return keywords
    
    def get_related_practice_areas(self, practice_area: str) -> List[str]:
        """Get practice areas that commonly relate to the given area."""
        # Define common relationships between practice areas
        relationships = {
            'criminal_law': ['constitutional_law'],
            'family_law': ['constitutional_law'],
            'employment_law': ['constitutional_law', 'business_law'],
            'personal_injury': ['constitutional_law', 'business_law'],
            'business_law': ['employment_law', 'tax_law'],
            'real_estate': ['business_law', 'tax_law'],
            'constitutional_law': ['criminal_law', 'family_law', 'employment_law', 'personal_injury'],
            'tax_law': ['business_law', 'real_estate'],
            'immigration_law': ['constitutional_law']
        }
        
        return relationships.get(practice_area, [])

"""
Relationship Validator
Validates and scores the quality of detected document relationships.
"""

import logging
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of relationship validation"""
    is_valid: bool
    confidence_score: float
    validation_issues: List[str]
    validation_source: str
    validation_timestamp: str

class RelationshipValidator:
    """
    Validates document relationships to ensure accuracy and quality.
    
    This validator:
    1. Checks citation format and structure
    2. Validates temporal consistency (citing case must be after cited case)
    3. Verifies jurisdiction compatibility
    4. Checks practice area relevance
    5. Validates citation context
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the relationship validator.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Validation rules configuration
        self.validation_rules = {
            'temporal_consistency': True,
            'jurisdiction_compatibility': True,
            'practice_area_relevance': True,
            'citation_format_validation': True,
            'context_validation': True,
            'minimum_confidence_threshold': 0.3
        }
        
        # Citation format patterns for validation
        self.valid_citation_patterns = {
            'case_citation': [
                r'\d+\s+[A-Z][a-z]+\.?\s*\d*d?\s+\d+',  # Reporter citation
                r'[A-Z][a-z]+\s+v\.\s+[A-Z][a-z]+',     # Case name
                r'\d{4}\s+[A-Z]+\s+\d+',                # Year format
            ],
            'statute_citation': [
                r'[A-Z][a-z]+\.?\s*Code\s*§\s*\d+\.?\d*',  # Code section
                r'\d+\s+U\.S\.C\.?\s*§\s*\d+',             # Federal statute
                r'§\s*\d+\.?\d*',                          # Section reference
            ]
        }
        
        # Context validation patterns
        self.positive_context_patterns = [
            r'citing\s+',
            r'following\s+',
            r'pursuant\s+to\s+',
            r'in\s+accordance\s+with\s+',
            r'see\s+',
            r'accord\s+',
            r'applying\s+'
        ]
        
        self.negative_context_patterns = [
            r'distinguishing\s+',
            r'overruling\s+',
            r'unlike\s+',
            r'different\s+from\s+',
            r'not\s+applicable\s+',
            r'inapposite\s+'
        ]
        
        # Practice area compatibility matrix
        self.practice_area_compatibility = {
            'personal_injury': ['constitutional_law', 'business_law', 'employment_law'],
            'criminal_law': ['constitutional_law'],
            'family_law': ['constitutional_law'],
            'business_law': ['employment_law', 'tax_law', 'constitutional_law'],
            'employment_law': ['business_law', 'constitutional_law'],
            'real_estate': ['business_law', 'tax_law'],
            'tax_law': ['business_law', 'real_estate'],
            'constitutional_law': ['personal_injury', 'criminal_law', 'family_law', 'business_law', 'employment_law'],
            'immigration_law': ['constitutional_law']
        }
    
    def validate_relationship(
        self,
        source_doc: Dict,
        target_doc: Dict,
        relationship_type: str,
        citation_text: str,
        context_text: str,
        confidence_score: float
    ) -> ValidationResult:
        """
        Validate a document relationship.
        
        Args:
            source_doc: Source document information
            target_doc: Target document information
            relationship_type: Type of relationship (cites, distinguishes, etc.)
            citation_text: Citation text
            context_text: Context around the citation
            confidence_score: Initial confidence score
            
        Returns:
            ValidationResult with validation outcome
        """
        try:
            validation_issues = []
            adjusted_confidence = confidence_score
            
            # Rule 1: Temporal consistency validation
            if self.validation_rules['temporal_consistency']:
                temporal_valid, temporal_issue = self._validate_temporal_consistency(
                    source_doc, target_doc
                )
                if not temporal_valid:
                    validation_issues.append(temporal_issue)
                    adjusted_confidence *= 0.7
            
            # Rule 2: Jurisdiction compatibility validation
            if self.validation_rules['jurisdiction_compatibility']:
                jurisdiction_valid, jurisdiction_issue = self._validate_jurisdiction_compatibility(
                    source_doc, target_doc
                )
                if not jurisdiction_valid:
                    validation_issues.append(jurisdiction_issue)
                    adjusted_confidence *= 0.8
            
            # Rule 3: Practice area relevance validation
            if self.validation_rules['practice_area_relevance']:
                practice_area_valid, practice_area_issue = self._validate_practice_area_relevance(
                    source_doc, target_doc
                )
                if not practice_area_valid:
                    validation_issues.append(practice_area_issue)
                    adjusted_confidence *= 0.9
            
            # Rule 4: Citation format validation
            if self.validation_rules['citation_format_validation']:
                format_valid, format_issue = self._validate_citation_format(
                    citation_text, target_doc
                )
                if not format_valid:
                    validation_issues.append(format_issue)
                    adjusted_confidence *= 0.8
            
            # Rule 5: Context validation
            if self.validation_rules['context_validation']:
                context_valid, context_issue = self._validate_citation_context(
                    context_text, relationship_type
                )
                if not context_valid:
                    validation_issues.append(context_issue)
                    adjusted_confidence *= 0.9
            
            # Determine overall validity
            is_valid = (
                adjusted_confidence >= self.validation_rules['minimum_confidence_threshold'] and
                len(validation_issues) <= 2  # Allow up to 2 minor issues
            )
            
            return ValidationResult(
                is_valid=is_valid,
                confidence_score=adjusted_confidence,
                validation_issues=validation_issues,
                validation_source='relationship_validator',
                validation_timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error validating relationship: {e}")
            return ValidationResult(
                is_valid=False,
                confidence_score=0.0,
                validation_issues=[f"Validation error: {str(e)}"],
                validation_source='relationship_validator',
                validation_timestamp=datetime.now().isoformat()
            )
    
    def validate_batch_relationships(
        self,
        relationships: List[Dict]
    ) -> List[ValidationResult]:
        """
        Validate a batch of relationships.
        
        Args:
            relationships: List of relationship dictionaries
            
        Returns:
            List of validation results
        """
        results = []
        
        for rel in relationships:
            try:
                result = self.validate_relationship(
                    source_doc=rel.get('source_doc', {}),
                    target_doc=rel.get('target_doc', {}),
                    relationship_type=rel.get('relationship_type', 'cites'),
                    citation_text=rel.get('citation_text', ''),
                    context_text=rel.get('context_text', ''),
                    confidence_score=rel.get('confidence_score', 0.0)
                )
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error validating relationship in batch: {e}")
                results.append(ValidationResult(
                    is_valid=False,
                    confidence_score=0.0,
                    validation_issues=[f"Batch validation error: {str(e)}"],
                    validation_source='relationship_validator',
                    validation_timestamp=datetime.now().isoformat()
                ))
        
        return results
    
    def _validate_temporal_consistency(
        self,
        source_doc: Dict,
        target_doc: Dict
    ) -> Tuple[bool, Optional[str]]:
        """Validate that citing document is dated after cited document."""
        try:
            source_date = self._extract_document_date(source_doc)
            target_date = self._extract_document_date(target_doc)
            
            if not source_date or not target_date:
                return True, None  # Can't validate without dates
            
            if source_date < target_date:
                return False, f"Temporal inconsistency: source document ({source_date}) predates target document ({target_date})"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating temporal consistency: {e}")
            return True, None  # Don't fail validation on error
    
    def _validate_jurisdiction_compatibility(
        self,
        source_doc: Dict,
        target_doc: Dict
    ) -> Tuple[bool, Optional[str]]:
        """Validate jurisdiction compatibility for citations."""
        try:
            source_jurisdiction = source_doc.get('jurisdiction', '').lower()
            target_jurisdiction = target_doc.get('jurisdiction', '').lower()
            
            if not source_jurisdiction or not target_jurisdiction:
                return True, None  # Can't validate without jurisdiction info
            
            # Same jurisdiction is always valid
            if source_jurisdiction == target_jurisdiction:
                return True, None
            
            # Federal law can be cited by any jurisdiction
            if target_jurisdiction == 'fed':
                return True, None
            
            # State courts can cite other state courts (with lower confidence)
            if source_jurisdiction != 'fed' and target_jurisdiction != 'fed':
                return True, None  # Valid but may reduce confidence elsewhere
            
            # Federal courts citing state law (less common but valid)
            if source_jurisdiction == 'fed' and target_jurisdiction != 'fed':
                return True, None
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating jurisdiction compatibility: {e}")
            return True, None
    
    def _validate_practice_area_relevance(
        self,
        source_doc: Dict,
        target_doc: Dict
    ) -> Tuple[bool, Optional[str]]:
        """Validate that practice areas are compatible."""
        try:
            source_practice_area = source_doc.get('primary_practice_area', '').lower()
            target_practice_area = target_doc.get('primary_practice_area', '').lower()
            
            if not source_practice_area or not target_practice_area:
                return True, None  # Can't validate without practice area info
            
            # Same practice area is always relevant
            if source_practice_area == target_practice_area:
                return True, None
            
            # Check compatibility matrix
            compatible_areas = self.practice_area_compatibility.get(source_practice_area, [])
            if target_practice_area in compatible_areas:
                return True, None
            
            # Constitutional law is relevant to most areas
            if target_practice_area == 'constitutional_law':
                return True, None
            
            # Cross-practice citations are valid but may be less relevant
            return True, f"Cross-practice area citation: {source_practice_area} citing {target_practice_area}"
            
        except Exception as e:
            logger.error(f"Error validating practice area relevance: {e}")
            return True, None
    
    def _validate_citation_format(
        self,
        citation_text: str,
        target_doc: Dict
    ) -> Tuple[bool, Optional[str]]:
        """Validate citation format against expected patterns."""
        try:
            if not citation_text or not citation_text.strip():
                return False, "Empty citation text"
            
            doc_type = target_doc.get('doc_type', '').lower()
            
            # Get appropriate patterns for document type
            if doc_type == 'case':
                patterns = self.valid_citation_patterns['case_citation']
            elif doc_type == 'statute':
                patterns = self.valid_citation_patterns['statute_citation']
            else:
                return True, None  # Unknown doc type, can't validate format
            
            # Check if citation matches any valid pattern
            for pattern in patterns:
                if re.search(pattern, citation_text, re.IGNORECASE):
                    return True, None
            
            return False, f"Citation format does not match expected patterns for {doc_type}"
            
        except Exception as e:
            logger.error(f"Error validating citation format: {e}")
            return True, None
    
    def _validate_citation_context(
        self,
        context_text: str,
        relationship_type: str
    ) -> Tuple[bool, Optional[str]]:
        """Validate that context supports the relationship type."""
        try:
            if not context_text or not context_text.strip():
                return True, None  # No context to validate
            
            context_lower = context_text.lower()
            
            # Check for positive context patterns
            positive_found = any(
                re.search(pattern, context_lower) 
                for pattern in self.positive_context_patterns
            )
            
            # Check for negative context patterns
            negative_found = any(
                re.search(pattern, context_lower) 
                for pattern in self.negative_context_patterns
            )
            
            # Validate context matches relationship type
            if relationship_type == 'cites' and negative_found and not positive_found:
                return False, "Context suggests negative relationship but type is 'cites'"
            
            if relationship_type == 'distinguishes' and positive_found and not negative_found:
                return False, "Context suggests positive relationship but type is 'distinguishes'"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating citation context: {e}")
            return True, None
    
    def _extract_document_date(self, document: Dict) -> Optional[str]:
        """Extract date from document for temporal validation."""
        # Try different date fields
        date_fields = ['date_filed', 'date_decided', 'effective_date', 'created_at', 'date']
        
        for field in date_fields:
            if field in document and document[field]:
                date_value = document[field]
                if isinstance(date_value, str):
                    # Extract year from date string
                    year_match = re.search(r'\b(19|20)\d{2}\b', date_value)
                    if year_match:
                        return year_match.group(0)
                return str(date_value)[:4]  # Take first 4 characters as year
        
        return None
    
    def get_validation_statistics(self, validation_results: List[ValidationResult]) -> Dict:
        """Get statistics from a batch of validation results."""
        if not validation_results:
            return {}
        
        total_count = len(validation_results)
        valid_count = sum(1 for result in validation_results if result.is_valid)
        
        avg_confidence = sum(result.confidence_score for result in validation_results) / total_count
        
        # Count common issues
        issue_counts = {}
        for result in validation_results:
            for issue in result.validation_issues:
                issue_type = issue.split(':')[0] if ':' in issue else issue
                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        
        return {
            'total_relationships': total_count,
            'valid_relationships': valid_count,
            'validation_rate': valid_count / total_count if total_count > 0 else 0,
            'average_confidence': avg_confidence,
            'common_issues': sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        }
    
    def update_validation_rules(self, new_rules: Dict) -> None:
        """Update validation rules configuration."""
        self.validation_rules.update(new_rules)
        logger.info(f"Updated validation rules: {new_rules}")
    
    def get_validation_rules(self) -> Dict:
        """Get current validation rules configuration."""
        return self.validation_rules.copy()

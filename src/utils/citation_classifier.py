#!/usr/bin/env python3
"""
Citation Classifier Module

This module provides functionality to classify legal citations into specific types
based on their patterns and context. It supports a wide variety of legal documents
including statutory codes, case law, regulations, and more.
"""

import re
import logging
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CitationType(Enum):
    """Enumeration of citation types"""
    INTERNAL_SECTION = "internal_section"  # References to sections within the same code
    LEGISLATIVE_HISTORY = "legislative_history"  # Legislative history citations (Acts, amendments)
    CASE_CITATION = "case_citation"  # References to case law
    EXTERNAL_STATUTE = "external_statute"  # References to other statutory codes
    PROCEDURAL_RULE = "procedural_rule"  # References to procedural rules
    CONSTITUTION = "constitution"  # References to constitutional provisions
    REGULATION = "regulation"  # References to administrative regulations
    LEGAL_DOCTRINE = "legal_doctrine"  # References to legal doctrines or principles
    UNKNOWN = "unknown"  # Unclassified citations

class CitationClassifier:
    """
    A class for classifying legal citations into specific types.
    Uses pattern recognition and contextual cues to determine citation types.
    """
    
    def __init__(self):
        """Initialize the citation classifier with pattern definitions"""
        # Compile regex patterns for different citation types
        self.patterns = {
            CitationType.INTERNAL_SECTION: [
                # Section references with various formats
                r"Section\s+\d+(\.\d+)?",
                r"Sec\.\s*\d+(\.\d+)?",
                r"§\s*\d+(\.\d+)?",
                r"Subchapter\s+[A-Z]",
                r"Chapter\s+\d+"
            ],
            
            CitationType.LEGISLATIVE_HISTORY: [
                # Various legislative history formats
                r"Acts\s+\d{4},\s+\d+[a-z]{2}\s+Leg",
                r"Acts\s+of\s+\d{4}",
                r"Acts\s+\d{4}.*[Cc]h\.\s+\d+",
                r"P\.L\.\s+\d+-\d+",
                r"S\.\s*\d+",
                r"H\.\s*[RB]\.\s*\d+"
            ],
            
            CitationType.CASE_CITATION: [
                # Standard case citation formats
                r"[A-Z][a-zA-Z\s\.']+\s+v\.\s+[A-Z][a-zA-Z\s\.']+",
                r"[A-Z][a-zA-Z\s\.']+\s+vs\.\s+[A-Z][a-zA-Z\s\.']+",
                r"[A-Z][a-zA-Z\s\.']+\s+ex\s+rel\.\s+[A-Z][a-zA-Z\s\.']+",
                r"\d+\s+[A-Za-z\.]+\s+\d+",  # 123 S.W.3d 456
                r"\d+\s+[A-Za-z\.]+\s+\d+,\s+\d+"  # 123 S.W.3d 456, 789
            ],
            
            CitationType.EXTERNAL_STATUTE: [
                # References to other codes
                r"[A-Z][a-z]+\s+Code\s+[§\s]\d+",
                r"[A-Z][a-z]+\s+Code\s+[Aa]nn\.\s+[§\s]\d+",
                r"[A-Z]\.[A-Z]\.[A-Z]\.\s+[§\s]\d+",
                r"U\.S\.C\.\s+[§\s]\d+",
                r"[A-Z][a-z]+\s+Statutes\s+[§\s]\d+"
            ],
            
            CitationType.PROCEDURAL_RULE: [
                # References to procedural rules
                r"Rule\s+\d+(\([a-z]\))?",
                r"Fed\.\s*R\.\s*Civ\.\s*P\.\s*\d+",
                r"Tex\.\s*R\.\s*Civ\.\s*P\.\s*\d+",
                r"Rules\s+of\s+[A-Za-z\s]+\s+Procedure",
                r"Rules\s+of\s+[A-Za-z\s]+\s+Evidence"
            ],
            
            CitationType.CONSTITUTION: [
                # Constitutional references
                r"[Aa]rt(\.|icle)\s+[IVXivx]+",
                r"[Aa]rt(\.|icle)\s+\d+,\s+[Ss]ec(\.|tion)\s+\d+",
                r"[Aa]mendment\s+[IVXivx]+",
                r"First Amendment",
                r"Second Amendment",
                r"Constitution\s+of\s+the\s+United\s+States",
                r"U\.S\.\s+Const\.",
                r"Tex\.\s+Const\."
            ],
            
            CitationType.REGULATION: [
                # Administrative regulations
                r"\d+\s+C\.F\.R\.\s+[§\s]\d+",
                r"\d+\s+Fed\.\s*Reg\.\s*\d+",
                r"[A-Z]+\s+Reg\.\s*\d+",
                r"[A-Z]+\s+Directive\s*\d+"
            ],
            
            CitationType.LEGAL_DOCTRINE: [
                # Common legal doctrines
                r"res\s+judicata",
                r"stare\s+decisis",
                r"respondeat\s+superior",
                r"estoppel",
                r"res\s+ipsa\s+loquitur",
                r"habeas\s+corpus",
                r"prima\s+facie",
                r"de\s+novo",
                r"en\s+banc",
                r"corpus\s+juris"
            ]
        }
        
        # Compile all regex patterns for efficiency
        self.compiled_patterns = {}
        for citation_type, patterns in self.patterns.items():
            self.compiled_patterns[citation_type] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
        # Keywords for context-based classification refinement
        self.context_keywords = {
            CitationType.INTERNAL_SECTION: ["this title", "this chapter", "this section", "hereunder"],
            CitationType.LEGISLATIVE_HISTORY: ["enacted", "amended", "effective", "repealed"],
            CitationType.CASE_CITATION: ["court", "held", "ruling", "judge", "opinion"],
            CitationType.EXTERNAL_STATUTE: ["title", "code", "act", "law"],
            CitationType.PROCEDURAL_RULE: ["procedure", "pleading", "motion", "rule", "evidence"],
            CitationType.CONSTITUTION: ["constitutional", "amendment", "founding", "framers"],
            CitationType.REGULATION: ["regulation", "administrative", "agency", "department"],
            CitationType.LEGAL_DOCTRINE: ["doctrine", "principle", "theory", "jurisprudence"]
        }
    
    def classify(self, citation_text: str, context: Optional[str] = None) -> Tuple[CitationType, float]:
        """
        Classify a citation into a specific type based on its text pattern and context.
        
        Args:
            citation_text: The text of the citation to classify
            context: Optional context around the citation (surrounding text)
            
        Returns:
            A tuple containing the citation type and a confidence score (0-1)
        """
        if not citation_text or citation_text.strip() == "":
            return CitationType.UNKNOWN, 0.0
        
        # Check against each pattern
        matches = {}
        for citation_type, patterns in self.compiled_patterns.items():
            for pattern in patterns:
                if pattern.search(citation_text):
                    matches[citation_type] = matches.get(citation_type, 0) + 1
        
        # If no pattern matches, return unknown
        if not matches:
            return CitationType.UNKNOWN, 0.0
        
        # Calculate confidence based on match count and pattern specificity
        confidence = {}
        for citation_type, match_count in matches.items():
            # Base confidence on number of patterns matched / total patterns for this type
            pattern_confidence = match_count / len(self.compiled_patterns[citation_type])
            confidence[citation_type] = pattern_confidence
        
        # Use context to refine classification if available
        if context:
            context = context.lower()
            for citation_type, keywords in self.context_keywords.items():
                if citation_type in confidence:
                    for keyword in keywords:
                        if keyword.lower() in context:
                            confidence[citation_type] += 0.1
                            
            # Normalize confidence scores
            max_conf = max(confidence.values())
            if max_conf > 1.0:
                for citation_type in confidence:
                    confidence[citation_type] /= max_conf
        
        # Get the type with highest confidence
        best_type = max(confidence.items(), key=lambda x: x[1])
        return best_type[0], min(best_type[1], 1.0)
    
    def get_classification_details(self, citation_text: str, context: Optional[str] = None) -> Dict[str, Any]:
        """
        Get detailed classification information about a citation.
        
        Args:
            citation_text: The text of the citation to classify
            context: Optional context around the citation
            
        Returns:
            A dictionary with classification details
        """
        citation_type, confidence = self.classify(citation_text, context)
        
        # Find which patterns matched
        matching_patterns = []
        if citation_type != CitationType.UNKNOWN:
            for pattern in self.patterns[citation_type]:
                if re.search(pattern, citation_text, re.IGNORECASE):
                    matching_patterns.append(pattern)
        
        # Extract citation components when possible
        components = self._extract_components(citation_text, citation_type)
        
        return {
            "citation_text": citation_text,
            "classification": {
                "type": citation_type.value,
                "confidence": confidence,
                "matching_patterns": matching_patterns
            },
            "components": components
        }
    
    def _extract_components(self, citation_text: str, citation_type: CitationType) -> Dict[str, str]:
        """
        Extract structured components from a citation based on its type.
        
        Args:
            citation_text: The citation text to analyze
            citation_type: The type of citation
            
        Returns:
            A dictionary of extracted components
        """
        components = {}
        
        if citation_type == CitationType.INTERNAL_SECTION:
            # Extract section/chapter numbers
            section_match = re.search(r"(?:Section|Sec\.|§)\s*(\d+(?:\.\d+)?)", citation_text, re.IGNORECASE)
            chapter_match = re.search(r"Chapter\s+(\d+)", citation_text, re.IGNORECASE)
            
            if section_match:
                components["section_number"] = section_match.group(1)
            if chapter_match:
                components["chapter_number"] = chapter_match.group(1)
        
        elif citation_type == CitationType.LEGISLATIVE_HISTORY:
            # Extract year, legislature, chapter, etc.
            year_match = re.search(r"(\d{4})", citation_text)
            leg_match = re.search(r"(\d+)[a-z]{2}\s+Leg", citation_text, re.IGNORECASE)
            ch_match = re.search(r"[Cc]h\.\s*(\d+)", citation_text)
            
            if year_match:
                components["year"] = year_match.group(1)
            if leg_match:
                components["legislature"] = leg_match.group(1)
            if ch_match:
                components["chapter"] = ch_match.group(1)
        
        elif citation_type == CitationType.CASE_CITATION:
            # Extract case name, reporter, etc.
            v_match = re.search(r"([A-Z][a-zA-Z\s\.']+)\s+v\.?\s+([A-Z][a-zA-Z\s\.']+)", citation_text)
            reporter_match = re.search(r"(\d+)\s+([A-Za-z\.]+)\s+(\d+)", citation_text)
            
            if v_match:
                components["plaintiff"] = v_match.group(1).strip()
                components["defendant"] = v_match.group(2).strip()
            if reporter_match:
                components["volume"] = reporter_match.group(1)
                components["reporter"] = reporter_match.group(2)
                components["page"] = reporter_match.group(3)
        
        elif citation_type == CitationType.EXTERNAL_STATUTE:
            # Extract code name, section, etc.
            code_match = re.search(r"([A-Z][a-z]+)\s+Code", citation_text)
            section_match = re.search(r"[§\s](\d+(?:\.\d+)?)", citation_text)
            
            if code_match:
                components["code_name"] = code_match.group(1)
            if section_match:
                components["section"] = section_match.group(1)
                
        elif citation_type == CitationType.CONSTITUTION:
            # Extract article, section, etc.
            article_match = re.search(r"[Aa]rt(?:icle)?\s+([IVXivx\d]+)", citation_text)
            section_match = re.search(r"[Ss]ec(?:tion)?\s+(\d+)", citation_text)
            
            if article_match:
                components["article"] = article_match.group(1)
            if section_match:
                components["section"] = section_match.group(1)
        
        return components

def classify_citation(citation_text: str, context: Optional[str] = None) -> Dict[str, Any]:
    """
    Convenience function to classify a citation without instantiating the class.
    
    Args:
        citation_text: The citation text to classify
        context: Optional surrounding context
        
    Returns:
        Classification details dictionary
    """
    classifier = CitationClassifier()
    return classifier.get_classification_details(citation_text, context)


if __name__ == "__main__":
    # Example usage
    classifier = CitationClassifier()
    
    test_citations = [
        "Section 11.20",
        "Acts 1985, 69th Leg., ch. 959, Sec. 1",
        "Brown v. Board of Education, 347 U.S. 483 (1954)",
        "Penal Code § 22.01",
        "Fed. R. Civ. P. 12(b)(6)",
        "U.S. Const. art. I, § 8",
        "42 C.F.R. § 423.10",
        "res judicata"
    ]
    
    for citation in test_citations:
        result = classifier.get_classification_details(citation)
        print(f"Citation: {citation}")
        print(f"Type: {result['classification']['type']}")
        print(f"Confidence: {result['classification']['confidence']:.2f}")
        print(f"Components: {result['components']}")
        print("-" * 50)

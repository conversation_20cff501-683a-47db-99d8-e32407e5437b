"""
Document Graph System for Legal Document Relationships
Uses Neo4j to create a graph database of document relationships
"""

import os
import re
import uuid
import json
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Import enhanced modules
from citation_extractor import HybridCitationExtractor
from schema_evolution import SchemaEvolutionManager

# Load environment variables
load_dotenv()

# Use Neo4j credentials from env or default to localhost
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

# LLM API credentials
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

class LegalDocumentGraph:
    """Build and maintain a Neo4j graph of legal document relationships
    Enhanced with LLM-driven schema evolution and hybrid citation extraction
    """
    
    def __init__(self, uri=NEO4J_URI, user=NEO4J_USER, password=NEO4J_PASSWORD, use_llm=True):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self.citation_extractor = HybridCitationExtractor()
        self.schema_manager = SchemaEvolutionManager(uri, user, password)
        self.use_llm = use_llm
        self._initialize_schema()
    
    def _initialize_schema(self):
        """Initialize Neo4j schema with constraints and indexes"""
        with self.driver.session() as session:
            # Create constraints
            session.run("CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.document_id IS UNIQUE")
            session.run("CREATE CONSTRAINT citation_text IF NOT EXISTS FOR (c:Citation) REQUIRE c.text IS UNIQUE")
            
            # Create indexes
            session.run("CREATE INDEX document_jurisdiction IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction)")
            session.run("CREATE INDEX document_type IF NOT EXISTS FOR (d:Document) ON (d.doc_type)")
    
    def create_document_node(self, document_id, metadata):
        """Create a node representing a legal document"""
        with self.driver.session() as session:
            # Build dynamic properties from metadata
            props = {
                "document_id": document_id,
                "title": metadata.get("document_title", ""),
                "doc_type": metadata.get("doc_type", ""),
                "jurisdiction": metadata.get("jurisdiction", ""),
                "created_at": datetime.now().isoformat()
            }
            
            # Add type-specific properties
            if metadata.get("doc_type") == "precedent_case":
                props.update({
                    "case_number": metadata.get("case_number", ""),
                    "court": metadata.get("court", ""),
                    "case_date": metadata.get("case_date", ""),
                    "case_citation": metadata.get("case_citation", "")
                })
            elif metadata.get("doc_type") == "law":
                props.update({
                    "statute_title": metadata.get("statute_title", ""),
                    "statute_chapter": metadata.get("statute_chapter", ""),
                    "statute_section": metadata.get("statute_section", "")
                })
            
            # Create or update document node
            result = session.run("""
            MERGE (d:Document {document_id: $document_id})
            SET d += $props
            RETURN d
            """, document_id=document_id, props=props)
            
            return result.single()
    
    def extract_citations(self, content, doc_type, metadata=None):
        """Extract citations from document content using hybrid approach
        Combines regex pattern matching with LLM-based extraction for improved accuracy
        """
        # Use the hybrid citation extractor
        return self.citation_extractor.extract_citations(
            content, 
            doc_type, 
            doc_metadata=metadata, 
            use_llm=self.use_llm
        )
    
    def link_document_citations(self, document_id, content, doc_type, metadata=None):
        """Extract citations from document content and create relationships
        Uses hybrid citation extraction (regex + LLM) for improved accuracy
        """
        citations = self.extract_citations(content, doc_type, metadata)
        
        with self.driver.session() as session:
            # Create citation nodes and relationships
            for citation in citations:
                result = session.run("""
                MATCH (source:Document {document_id: $source_id})
                MERGE (target:Citation {text: $citation_text})
                ON CREATE SET target.type = $citation_type, 
                               target.extraction_method = $extraction_method,
                               target.confidence = $confidence,
                               target.created_at = datetime()
                MERGE (source)-[r:CITES]->(target)
                SET r.context = $context
                RETURN r
                """, 
                source_id=document_id, 
                citation_text=citation["text"],
                citation_type=citation["type"], 
                extraction_method=citation.get("extraction_method", "regex"),
                confidence=citation.get("confidence", "high"),
                context=citation.get("context", ""))
            
            # Suggest schema enhancements based on document content if LLM is enabled
            if self.use_llm:
                self._suggest_schema_enhancements(document_id, content, doc_type, metadata)
        
        return citations
        
    def _suggest_schema_enhancements(self, document_id, content, doc_type, metadata=None):
        """Use LLM to suggest schema enhancements based on document content"""
        # Get schema enhancement suggestions
        suggestions = self.schema_manager.suggest_schema_enhancements(content, metadata)
        
        # Apply suggested schema enhancements
        if suggestions:
            for node_type in suggestions.get("node_types", []):
                self.schema_manager.add_node_type(node_type["name"], node_type.get("properties", []))
                
            for rel_type in suggestions.get("relationship_types", []):
                self.schema_manager.add_relationship_type(
                    rel_type["name"],
                    rel_type["source_node_type"],
                    rel_type["target_node_type"],
                    rel_type.get("properties", [])
                )
        
        return suggestions
    
    def resolve_citations(self, confidence_threshold="medium"):
        """Link citation nodes to actual document nodes when found
        Enhanced to handle different citation types and confidence levels
        """
        with self.driver.session() as session:
            # Get all registered node types from schema manager
            node_types = self.schema_manager.get_node_types()
            doc_types = [nt for nt in node_types if nt != "Citation"]
            
            # Build a dynamic query based on registered node types
            base_query = """
            MATCH (c:Citation)
            WHERE c.confidence IS NULL OR 
                  CASE c.confidence
                      WHEN 'high' THEN true
                      WHEN 'medium' THEN $threshold IN ['medium', 'low']
                      WHEN 'low' THEN $threshold = 'low'
                      ELSE true
                  END
            """
            
            # Default case for standard document types
            match_cases = """
            MATCH (d:Document)
            WHERE 
                CASE c.type
                    WHEN 'case_citation' THEN 
                        d.doc_type = 'precedent_case' AND
                        (c.text CONTAINS d.title OR d.case_citation CONTAINS c.text)
                    WHEN 'statute_citation' THEN
                        d.doc_type = 'law' AND
                        (c.text CONTAINS d.statute_section OR 
                         c.text CONTAINS d.statute_chapter)
            """
            
            # Add dynamic cases for any custom node types
            for doc_type in doc_types:
                if doc_type != "Document":
                    # For simplicity, let's just check against Document nodes with appropriate type
                    match_cases += f"""
                    WHEN '{doc_type.lower()}_citation' THEN 
                        d.doc_type = '{doc_type.lower()}' AND
                        ANY(prop IN KEYS(d) WHERE c.text CONTAINS toString(d[prop]))
                    """
            
            # Close the CASE statement
            match_cases += """
                    ELSE false
                END
            """
            
            # Complete the query
            query = base_query + match_cases + """
            MERGE (c)-[r:RESOLVES_TO]->(d)
            SET r.confidence = c.confidence,
                r.extraction_method = c.extraction_method,
                r.resolved_at = datetime()
            RETURN count(r) as resolved_count
            """
            
            result = session.run(query, threshold=confidence_threshold)
            
            return result.single()["resolved_count"]
    
    def build_related_documents(self, document_id, max_depth=2):
        """Find related documents based on citations and shared topics"""
        with self.driver.session() as session:
            # First, find directly cited documents
            result = session.run("""
            MATCH (d:Document {document_id: $document_id})-[:CITES]->
                  (c:Citation)-[:RESOLVES_TO]->(cited:Document)
            RETURN cited.document_id as related_id, 
                   cited.title as related_title,
                   'direct_citation' as relationship_type,
                   cited.doc_type as doc_type
            """, document_id=document_id)
            
            direct_citations = [dict(record) for record in result]
            
            # Next, find documents that cite the same sources (co-citation)
            result = session.run("""
            MATCH (d:Document {document_id: $document_id})-[:CITES]->(c:Citation)<-[:CITES]-(other:Document)
            WHERE other.document_id <> $document_id
            RETURN other.document_id as related_id, 
                   other.title as related_title,
                   'co_citation' as relationship_type,
                   other.doc_type as doc_type,
                   count(c) as strength
            ORDER BY strength DESC
            """, document_id=document_id)
            
            co_citations = [dict(record) for record in result]
            
            # Finally, find documents that are cited by the same documents (bibliographic coupling)
            result = session.run("""
            MATCH (citing:Document)-[:CITES]->(:Citation)-[:RESOLVES_TO]->(d:Document {document_id: $document_id})
            MATCH (citing)-[:CITES]->(:Citation)-[:RESOLVES_TO]->(other:Document)
            WHERE other.document_id <> $document_id
            RETURN other.document_id as related_id, 
                   other.title as related_title,
                   'bibliographic_coupling' as relationship_type,
                   other.doc_type as doc_type,
                   count(citing) as strength
            ORDER BY strength DESC
            """, document_id=document_id)
            
            bibliographic_coupling = [dict(record) for record in result]
            
            return {
                "direct_citations": direct_citations,
                "co_citations": co_citations,
                "bibliographic_coupling": bibliographic_coupling
            }
    
    def get_document_network(self, document_id, depth=2):
        """Retrieve the network of related documents as a graph"""
        with self.driver.session() as session:
            result = session.run("""
            MATCH path = (d:Document {document_id: $document_id})-[*1..%d]-(related)
            RETURN path
            LIMIT 100
            """ % depth, document_id=document_id)
            
            # Format results for visualization
            nodes = {}
            relationships = []
            
            for record in result:
                path = record["path"]
                for segment in path:
                    if hasattr(segment, "start") and hasattr(segment, "end"):
                        # This is a relationship
                        start_id = segment.start_node.id
                        end_id = segment.end_node.id
                        rel_type = segment.type
                        
                        relationships.append({
                            "source": start_id,
                            "target": end_id,
                            "type": rel_type
                        })
                    else:
                        # This is a node
                        node_id = segment.id
                        labels = list(segment.labels)
                        properties = dict(segment)
                        
                        nodes[node_id] = {
                            "id": node_id,
                            "labels": labels,
                            "properties": properties
                        }
            
            return {
                "nodes": list(nodes.values()),
                "relationships": relationships
            }
    
    def get_citation_stats(self, jurisdiction=None, doc_type=None):
        """Get statistics about citations in the document graph"""
        query = """
        MATCH (d:Document)-[r:CITES]->(c:Citation)
        """
        
        if jurisdiction or doc_type:
            query += "WHERE "
            conditions = []
            if jurisdiction:
                conditions.append("d.jurisdiction = $jurisdiction")
            if doc_type:
                conditions.append("d.doc_type = $doc_type")
            query += " AND ".join(conditions)
        
        query += """
        RETURN 
            d.jurisdiction as jurisdiction,
            d.doc_type as doc_type,
            count(r) as citation_count,
            count(DISTINCT d) as document_count,
            count(DISTINCT c) as unique_citations,
            1.0 * count(r) / count(DISTINCT d) as avg_citations_per_doc
        GROUP BY d.jurisdiction, d.doc_type
        ORDER BY citation_count DESC
        """
        
        with self.driver.session() as session:
            result = session.run(query, jurisdiction=jurisdiction, doc_type=doc_type)
            return [dict(record) for record in result]
    
    def close(self):
        """Close the Neo4j driver"""
        self.driver.close()
        
    def analyze_document_relationships(self, document_id=None, relationship_types=None):
        """Analyze document relationships using the enhanced schema
        
        Args:
            document_id: Optional ID of a specific document to analyze
            relationship_types: Optional list of relationship types to consider
            
        Returns:
            Dictionary containing relationship analysis results
        """
        with self.driver.session() as session:
            # Get all relationship types if not specified
            if not relationship_types:
                relationship_types = self.schema_manager.get_relationship_types()
                if "CITES" not in relationship_types:
                    relationship_types.append("CITES")
                if "RESOLVES_TO" not in relationship_types:
                    relationship_types.append("RESOLVES_TO")
            
            # Build dynamic query based on registered relationship types
            if document_id:
                # Analysis for a specific document
                query = """
                MATCH (d:Document {document_id: $document_id})
                """
                
                # Add path patterns for each relationship type
                # First relationship type needs to carry the d from the MATCH clause
                if relationship_types:
                    first_rel = relationship_types[0]
                    query += f"""
                    OPTIONAL MATCH path0 = (d)-[:{first_rel}*1..2]-(related0)
                    WITH d, collect(distinct related0) as related_via_{first_rel.lower()}
                    """
                    
                    # Subsequent relationship types need to carry all previous collections
                    for idx, rel_type in enumerate(relationship_types[1:], 1):
                        previous_withs = ", ".join([f"related_via_{rt.lower()}" for rt in relationship_types[:idx]])
                        query += f"""
                        OPTIONAL MATCH path{idx} = (d)-[:{rel_type}*1..2]-(related{idx})
                        WITH d, {previous_withs}, collect(distinct related{idx}) as related_via_{rel_type.lower()}
                        """
                
                # Complete the query
                query += """
                RETURN d.document_id as document_id,
                       d.title as document_title,
                       d.doc_type as document_type,
                """
                
                # Add return items for each relationship type
                for i, rel_type in enumerate(relationship_types):
                    query += f"""
                       size(related_via_{rel_type.lower()}) as {rel_type.lower()}_relationship_count
                    """
                    if i < len(relationship_types) - 1:
                        query += ","
            else:
                # Global analysis across all documents
                query = """
                MATCH (d:Document)
                """
                
                # Add optional matches for each relationship type
                for rel_type in relationship_types:
                    query += f"""
                    OPTIONAL MATCH (d)-[r{rel_type}:{rel_type}]-()
                    """
                
                # Complete the query
                query += """
                WITH count(d) as total_documents,
                """
                
                # Add counts for each relationship type
                for i, rel_type in enumerate(relationship_types):
                    query += f"""
                       count(r{rel_type}) as total_{rel_type.lower()}_relationships
                    """
                    if i < len(relationship_types) - 1:
                        query += ","
                
                # Add final return
                query += """
                RETURN total_documents,
                """
                
                for i, rel_type in enumerate(relationship_types):
                    query += f"""
                       total_{rel_type.lower()}_relationships,
                       total_{rel_type.lower()}_relationships * 1.0 / total_documents as avg_{rel_type.lower()}_per_document
                    """
                    if i < len(relationship_types) - 1:
                        query += ","
            
            # Execute the query
            if document_id:
                result = session.run(query, document_id=document_id)
            else:
                result = session.run(query)
            
            # Process and return results
            return result.single().data()


# Add new methods for schema evolution
    def suggest_content_based_relationships(self, document_id, min_similarity=0.6):
        """Identify potential new relationships between documents based on content similarity
        
        Uses the LLM to analyze document content and suggest new relationships that
        might not be explicitly cited. This helps discover implicit connections in the
        legal knowledge graph.
        
        Args:
            document_id: ID of the document to analyze
            min_similarity: Minimum similarity threshold (0.0-1.0)
            
        Returns:
            List of suggested relationships with similarity scores
        """
        with self.driver.session() as session:
            # Get the document content and metadata
            result = session.run("""
            MATCH (d:Document {document_id: $document_id})
            OPTIONAL MATCH (d)-[:HAS_CONTENT]->(c:Content)
            RETURN d.title as title, d.doc_type as doc_type, 
                   d.jurisdiction as jurisdiction, c.text as content
            """, document_id=document_id)
            
            doc_data = result.single()
            if not doc_data or not doc_data.get("content"):
                return {"error": "Document content not found"}
            
            # Get a sample of other documents (limit to 10 for performance)
            result = session.run("""
            MATCH (d:Document)
            WHERE d.document_id <> $document_id
            OPTIONAL MATCH (d)-[:HAS_CONTENT]->(c:Content)
            RETURN d.document_id as id, d.title as title, 
                   d.doc_type as doc_type, c.text as content
            LIMIT 10
            """, document_id=document_id)
            
            other_docs = list(result)
            if not other_docs:
                return {"error": "No other documents found for comparison"}
            
            # Prepare prompt for the LLM to analyze similarities
            prompt = f"""You are a legal knowledge graph expert. Analyze the similarity between 
            the source document and each target document. For each pair, determine:
            1. The similarity score (0.0-1.0)
            2. The type of relationship that might exist
            3. Key shared concepts or topics
            
            Source document: {doc_data['title']} (Type: {doc_data['doc_type']})\n
            First 500 chars of source content: {doc_data['content'][:500]}...\n\n"""
            
            for i, doc in enumerate(other_docs):
                if doc.get("content"):
                    prompt += f"Target {i+1}: {doc['title']} (Type: {doc['doc_type']})\n"
                    prompt += f"First 300 chars: {doc['content'][:300]}...\n\n"
            
            prompt += f"""For each target document with similarity score >= {min_similarity}, provide:
            {{
                "target_id": "<document_id>",
                "similarity_score": <0.0-1.0>,
                "suggested_relationship": "<RELATIONSHIP_TYPE>",
                "shared_concepts": ["<concept1>", "<concept2>", ...],
                "justification": "<brief explanation>"  
            }}"""  
            
            # Call LLM to analyze similarities
            try:
                response = self.schema_manager.call_gemini(prompt)
                suggestions = self.schema_manager.parse_gemini_relationship_suggestions(response)
                
                # Filter by minimum similarity
                filtered_suggestions = [s for s in suggestions if s.get("similarity_score", 0) >= min_similarity]
                
                # Apply suggested relationships to the graph
                for suggestion in filtered_suggestions:
                    target_id = suggestion.get("target_id")
                    relationship = suggestion.get("suggested_relationship", "RELATED_TO").upper()
                    properties = {
                        "similarity_score": suggestion.get("similarity_score", 0.0),
                        "shared_concepts": suggestion.get("shared_concepts", []),
                        "justification": suggestion.get("justification", ""),
                        "auto_generated": True,
                        "created_at": datetime.now().isoformat()
                    }
                    
                    # Register the relationship type if it doesn't exist
                    if relationship not in self.schema_manager.get_relationship_types():
                        self.schema_manager.add_relationship_type(
                            relationship,
                            "Document",
                            "Document",
                            ["similarity_score", "shared_concepts", "justification", "auto_generated"]
                        )
                    
                    # Create the relationship
                    self.apply_relationship_type(document_id, relationship, target_id, properties)
                
                return {
                    "document_id": document_id,
                    "suggestions_count": len(filtered_suggestions),
                    "suggestions": filtered_suggestions
                }
                
            except Exception as e:
                return {"error": str(e)}

    def _suggest_schema_enhancements(self, document_id, content, doc_type, metadata):
        """Suggest schema enhancements based on document content using LLM"""
        try:
            # Get document metadata from Neo4j
            with self.driver.session() as session:
                result = session.run("""
                MATCH (d:Document {document_id: $document_id})
                RETURN d
                """, document_id=document_id)
                
                doc_node = result.single()
                if not doc_node:
                    return
                
                doc_data = dict(doc_node["d"])
            
            # Suggest schema enhancements
            suggestions = self.schema_manager.suggest_schema_enhancements(
                content[:5000],  # Use first 5000 chars to avoid token limits
                doc_data
            )
            
            # Queue suggestions for review
            if suggestions and not "error" in suggestions:
                self.schema_manager.apply_schema_suggestions(suggestions, auto_apply=False)
                
                # Log the suggestions
                with self.driver.session() as session:
                    session.run("""
                    MATCH (d:Document {document_id: $document_id})
                    CREATE (s:SchemaEnhancementSuggestion {
                        id: $suggestion_id,
                        suggestions: $suggestions,
                        created_at: datetime()
                    })
                    CREATE (d)-[:HAS_SCHEMA_SUGGESTION]->(s)
                    """, 
                    document_id=document_id,
                    suggestion_id=str(uuid.uuid4()),
                    suggestions=json.dumps(suggestions))
        except Exception as e:
            print(f"Error suggesting schema enhancements: {str(e)}")
    
    def apply_relationship_type(self, source_id, relationship_type, target_id, properties=None):
        """Apply a relationship between two document nodes"""
        properties = properties or {}
        
        with self.driver.session() as session:
            # Check if relationship type exists in schema
            result = session.run("""
            MATCH (r:RelationshipType {name: $relationship_type})
            RETURN r
            """, relationship_type=relationship_type)
            
            if not result.single():
                # Register new relationship type
                self.schema_manager.add_relationship_type(
                    relationship_type,
                    "Document",  # Default source type
                    "Document",  # Default target type
                    f"Auto-generated relationship type for {relationship_type}"
                )
            
            # Create the relationship
            result = session.run("""
            MATCH (source:Document {document_id: $source_id})
            MATCH (target:Document {document_id: $target_id})
            CALL apoc.create.relationship(source, $relationship_type, $properties, target)
            YIELD rel
            RETURN rel
            """, 
            source_id=source_id,
            target_id=target_id,
            relationship_type=relationship_type,
            properties=properties)
            
            return bool(result.single())

# Helper function to set up Neo4j with the document
def setup_document_in_graph(document_id, pdf_path, content, metadata, use_llm=True, suggest_relationships=True):
    """Set up a document in the Neo4j graph database with enhanced schema evolution and citation extraction
    
    Args:
        document_id: Unique identifier for the document
        pdf_path: Path to the PDF file (optional)
        content: Text content of the document
        metadata: Document metadata dictionary
        use_llm: Whether to use LLM for enhanced citation extraction and schema evolution
        suggest_relationships: Whether to suggest content-based relationships
        
    Returns:
        Dictionary with processing results
    """
    graph = LegalDocumentGraph(use_llm=use_llm)
    
    try:
        # Create document node
        graph.create_document_node(document_id, metadata)
        
        # Store content as a separate node if not already present
        with graph.driver.session() as session:
            session.run("""
            MATCH (d:Document {document_id: $document_id})
            MERGE (c:Content {document_id: $document_id})
            SET c.text = $content,
                c.created_at = datetime()
            MERGE (d)-[:HAS_CONTENT]->(c)
            """, document_id=document_id, content=content)
        
        # Extract and link citations using hybrid approach
        doc_type = metadata.get("doc_type", "law")
        citations = graph.link_document_citations(document_id, content, doc_type, metadata)
        
        # Resolve citations to actual documents with confidence threshold
        resolved_count = graph.resolve_citations(confidence_threshold="medium")
        
        # Build related document relationships
        related_docs = graph.build_related_documents(document_id)
        
        # Analyze document relationships using enhanced schema
        relationship_analysis = graph.analyze_document_relationships(document_id)
        
        # Suggest content-based relationships if enabled
        content_relationships = None
        if use_llm and suggest_relationships:
            content_relationships = graph.suggest_content_based_relationships(document_id, min_similarity=0.7)
        
        return {
            "document_id": document_id,
            "citations": {
                "found": len(citations),
                "resolved": resolved_count
            },
            "relationships": {
                "explicit": relationship_analysis,
                "content_based": content_relationships
            },
            "llm_enhanced": use_llm,
            "processing_timestamp": datetime.now().isoformat()
        }
    finally:
        graph.close()


# Example usage
if __name__ == "__main__":
    # Test the enhanced document graph system
    test_content = """
    In Smith v. Jones, 123 F.3d 456 (5th Cir. 2020), the court held that pursuant to Section 123.45 
    of the Texas Civil Code, damages for personal injury claims are not capped in cases involving 
    gross negligence. This ruling overturned the previous precedent established in Johnson v. Medical Center,
    which had limited such damages under Chapter 41 of the Texas Civil Practice and Remedies Code.
    
    The court further ruled that according to Texas Transportation Code § 545.401, reckless driving
    constitutes a rebuttable presumption of gross negligence in personal injury cases.
    """
    
    test_metadata = {
        "document_title": "Smith v. Jones Analysis",
        "doc_type": "precedent_case",
        "jurisdiction": "Texas",
        "case_number": "2020-45678",
        "court": "Fifth Circuit Court of Appeals",
        "case_date": "2020-05-15"
    }
    
    # Generate a test document ID
    test_doc_id = str(uuid.uuid4())
    
    # Set up document in graph
    result = setup_document_in_graph(test_doc_id, None, test_content, test_metadata)
    
    print("\n===== DOCUMENT GRAPH TEST RESULTS =====")
    print(f"Document ID: {test_doc_id}")
    print(f"Citations found: {result['citations_found']}")
    print(f"Citations resolved: {result['citations_resolved']}")
    print(f"LLM enhanced: {result['llm_enhanced']}")

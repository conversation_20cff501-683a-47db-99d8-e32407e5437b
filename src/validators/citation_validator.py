#!/usr/bin/env python3
"""
Citation Validation Module
Provides Neo4j-based validation of legal citations to verify their accuracy
and detect unresolved or problematic citations.
"""

import os
import json
import re
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Import our OCR cleaner module
import sys
import os

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))) 

# Import from new module structure
from src.extractors.ocr_cleaner import clean_document_text, process_citations

# Load environment variables
load_dotenv()

# Neo4j database connection
NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

class CitationValidator:
    """
    Validates citations using Neo4j graph database to verify they
    resolve to actual documents and have correct bidirectional relationships.
    """
    
    def __init__(self):
        """Initialize the citation validator with Neo4j connection"""
        self.driver = GraphDatabase.driver(
            NEO4J_URI, 
            auth=(NEO4J_USER, NEO4J_PASSWORD)
        )
    
    def close(self):
        """Close the Neo4j driver connection"""
        self.driver.close()
    
    def validate_citations(self, document_id=None, min_confidence=None, limit=100):
        """
        Validate all citations in the database or for a specific document
        
        Parameters:
        - document_id: Optional document ID to validate citations for
        - min_confidence: Minimum confidence level to include in validation
        - limit: Maximum number of citations to validate
        
        Returns:
        Dictionary with validation results
        """
        confidence_clause = ""
        if min_confidence:
            confidence_levels = ["low", "medium", "high"]
            if min_confidence in confidence_levels:
                min_index = confidence_levels.index(min_confidence)
                valid_levels = confidence_levels[min_index:]
                confidence_clause = f" AND c.confidence IN {valid_levels}"
        
        document_clause = ""
        if document_id:
            document_clause = f" AND d.document_id = '{document_id}'"
            
        with self.driver.session() as session:
            # Get citations to validate
            query = f"""
            MATCH (d:Document)-[:CITES]->(c:Citation)
            WHERE c.text IS NOT NULL{document_clause}{confidence_clause}
            RETURN c, d
            LIMIT {limit}
            """
            
            result = session.run(query)
            citations = [{"citation": dict(record["c"]), "document": dict(record["d"])} for record in result]
            
            validation_results = {
                "timestamp": datetime.now().isoformat(),
                "total_citations": len(citations),
                "resolved_citations": 0,
                "unresolved_citations": 0,
                "resolution_rate": 0,
                "citations": []
            }
            
            # Validate each citation
            for item in citations:
                citation = item["citation"]
                document = item["document"]
                
                # Add document info to citation
                citation["document_id"] = document.get("document_id", "unknown")
                citation["document_title"] = document.get("title", "unknown")
                
                # Check if citation resolves to a document
                validation = self.validate_single_citation(citation)
                citation.update(validation)
                
                # Update counts
                if validation.get("resolves_to_document", False):
                    validation_results["resolved_citations"] += 1
                else:
                    validation_results["unresolved_citations"] += 1
                    
                validation_results["citations"].append(citation)
            
            # Calculate resolution rate
            if validation_results["total_citations"] > 0:
                validation_results["resolution_rate"] = (
                    validation_results["resolved_citations"] / 
                    validation_results["total_citations"]
                )
                
            return validation_results
    
    def validate_single_citation(self, citation):
        """
        Validate a single citation to check if it resolves to a document
        
        Parameters:
        - citation: Dictionary with citation data
        
        Returns:
        Dictionary with validation results for this citation
        """
        citation_text = citation.get("text", "")
        citation_id = citation.get("id", "unknown")
        
        validation_result = {
            "resolves_to_document": False,
            "resolved_document_count": 0,
            "bidirectional": False,
            "similar_citations": [],
            "validation_score": 0.0,
            "suggestions": []
        }
        
        if not citation_text:
            validation_result["suggestions"].append("Citation text is empty")
            return validation_result
        
        with self.driver.session() as session:
            # Check if citation resolves to any documents
            query = """
            MATCH (c:Citation {id: $citation_id})-[:RESOLVES_TO]->(d:Document)
            RETURN d.document_id AS document_id, d.title AS title, 
                   d.doc_type AS doc_type, d.path AS path
            """
            
            result = session.run(query, citation_id=citation_id)
            resolved_documents = [dict(record) for record in result]
            
            validation_result["resolved_document_count"] = len(resolved_documents)
            validation_result["resolves_to_document"] = len(resolved_documents) > 0
            
            if resolved_documents:
                validation_result["resolved_documents"] = resolved_documents
                
                # Check bidirectional relationships
                for doc in resolved_documents:
                    doc_id = doc.get("document_id")
                    if doc_id:
                        bid_query = """
                        MATCH (d:Document {document_id: $doc_id})-[:CITES]->
                              (c2:Citation)-[:RESOLVES_TO]->(d2:Document)
                        WHERE d2.document_id = $source_doc_id
                        RETURN count(c2) AS bidirectional_count
                        """
                        
                        bid_result = session.run(
                            bid_query, 
                            doc_id=doc_id, 
                            source_doc_id=citation.get("document_id")
                        )
                        
                        record = bid_result.single()
                        if record and record["bidirectional_count"] > 0:
                            validation_result["bidirectional"] = True
                            break
            
            # Find similar citations if this one doesn't resolve
            if not validation_result["resolves_to_document"]:
                # Look for citations with similar text
                similar_query = """
                MATCH (c:Citation)
                WHERE c.id <> $citation_id AND 
                      c.text IS NOT NULL AND
                      apoc.text.fuzzyMatch(c.text, $citation_text) > 0.7
                MATCH (c)-[:RESOLVES_TO]->(d:Document)
                RETURN c.id AS citation_id, c.text AS citation_text, 
                       d.document_id AS document_id, d.title AS title
                LIMIT 5
                """
                
                try:
                    similar_result = session.run(
                        similar_query, 
                        citation_id=citation_id,
                        citation_text=citation_text
                    )
                    
                    similar_citations = [dict(record) for record in similar_result]
                    validation_result["similar_citations"] = similar_citations
                    
                    if similar_citations:
                        validation_result["suggestions"].append(
                            "Consider connecting to same document as similar citations"
                        )
                except Exception as e:
                    # APOC might not be available
                    validation_result["similar_citations"] = []
                    validation_result["suggestions"].append(
                        "Enable APOC in Neo4j for better similarity detection"
                    )
            
            # Calculate validation score (0.0 - 1.0)
            score = 0.0
            
            # Points for resolving to a document
            if validation_result["resolves_to_document"]:
                score += 0.5
                
            # Points for bidirectional relationship
            if validation_result["bidirectional"]:
                score += 0.3
                
            # Points for multiple resolved documents
            if validation_result["resolved_document_count"] > 1:
                score += 0.1
                
            # Points based on confidence
            confidence = citation.get("confidence", "unknown")
            if confidence == "high":
                score += 0.1
            elif confidence == "medium":
                score += 0.05
                
            validation_result["validation_score"] = min(1.0, score)
            
            # Add suggestions based on score
            if validation_result["validation_score"] < 0.5:
                validation_result["suggestions"].append(
                    "Low validation score. Citation may be incorrect or incomplete."
                )
            
            return validation_result
    
    def find_unresolved_citations(self, min_confidence=None, limit=100):
        """
        Find citations that don't resolve to any document
        
        Parameters:
        - min_confidence: Minimum confidence level to include
        - limit: Maximum number of unresolved citations to return
        
        Returns:
        List of unresolved citations
        """
        confidence_clause = ""
        if min_confidence:
            confidence_levels = ["low", "medium", "high"]
            if min_confidence in confidence_levels:
                min_index = confidence_levels.index(min_confidence)
                valid_levels = confidence_levels[min_index:]
                confidence_clause = f" AND c.confidence IN {valid_levels}"
        
        with self.driver.session() as session:
            query = f"""
            MATCH (d:Document)-[:CITES]->(c:Citation)
            WHERE NOT EXISTS((c)-[:RESOLVES_TO]->(:Document)){confidence_clause}
            RETURN c.id AS citation_id, c.text AS citation_text, 
                   c.confidence AS confidence, d.document_id AS document_id,
                   d.title AS document_title
            LIMIT {limit}
            """
            
            result = session.run(query)
            unresolved = [dict(record) for record in result]
            
            return {
                "timestamp": datetime.now().isoformat(),
                "unresolved_count": len(unresolved),
                "unresolved_citations": unresolved
            }
    
    def find_citation_clusters(self, min_references=3):
        """
        Find clusters of citations that refer to the same document
        
        Parameters:
        - min_references: Minimum number of references to include in results
        
        Returns:
        List of document clusters with their referring citations
        """
        with self.driver.session() as session:
            query = f"""
            MATCH (d:Document)<-[r:RESOLVES_TO]-(c:Citation)
            WITH d, count(c) AS reference_count
            WHERE reference_count >= {min_references}
            MATCH (d)<-[:RESOLVES_TO]-(c:Citation)<-[:CITES]-(d2:Document)
            RETURN d.document_id AS cited_document_id, 
                   d.title AS cited_document_title,
                   d.doc_type AS cited_document_type,
                   count(DISTINCT c) AS citation_count,
                   count(DISTINCT d2) AS citing_document_count,
                   collect(DISTINCT {{citation_id: c.id, citation_text: c.text, 
                                     document_id: d2.document_id,
                                     document_title: d2.title}}) AS citations
            ORDER BY citation_count DESC
            LIMIT 20
            """
            
            result = session.run(query)
            clusters = [dict(record) for record in result]
            
            return {
                "timestamp": datetime.now().isoformat(),
                "cluster_count": len(clusters),
                "citation_clusters": clusters
            }
    
    def suggest_citation_connections(self, citation_text, document_id=None):
        """
        Suggest potential documents that a citation should connect to
        
        Parameters:
        - citation_text: Text of the citation to find connections for
        - document_id: Optional document ID where citation appears
        
        Returns:
        List of potential document connections
        """
        # Clean up the citation text
        cleaned_citation = clean_document_text(citation_text)
        
        # Extract key components from citation
        section_match = re.search(r'[Ss]ec\w*\.?\s*(\d+(\.\d+)?)', cleaned_citation)
        chapter_match = re.search(r'[Cc]h\w*\.?\s*(\d+)', cleaned_citation)
        code_match = re.search(r'([A-Z][a-z]+)\s+[Cc]ode', cleaned_citation)
        
        # Build query conditions based on extracted components
        conditions = []
        params = {"citation_text": citation_text}
        
        if section_match:
            conditions.append("d.title CONTAINS 'Section ' + $section_num")
            params["section_num"] = section_match.group(1)
            
        if chapter_match:
            conditions.append("d.title CONTAINS 'Chapter ' + $chapter_num")
            params["chapter_num"] = chapter_match.group(1)
            
        if code_match:
            conditions.append("d.title CONTAINS $code_type")
            params["code_type"] = code_match.group(1)
        
        # If no specific components found, use text similarity
        if not conditions:
            conditions.append("apoc.text.fuzzyMatch(d.title, $citation_text) > 0.6")
        
        # Add document clause if provided
        document_clause = ""
        if document_id:
            document_clause = " AND d.document_id <> $document_id"
            params["document_id"] = document_id
        
        with self.driver.session() as session:
            # Build the query
            condition_str = " OR ".join(conditions)
            query = f"""
            MATCH (d:Document)
            WHERE ({condition_str}){document_clause}
            RETURN d.document_id AS document_id, d.title AS title, 
                   d.doc_type AS doc_type,
                   apoc.text.fuzzyMatch(d.title, $citation_text) AS similarity
            ORDER BY similarity DESC
            LIMIT 5
            """
            
            try:
                result = session.run(query, **params)
                suggestions = [dict(record) for record in result]
            except Exception as e:
                # APOC might not be available
                query = f"""
                MATCH (d:Document)
                WHERE ({condition_str}){document_clause}
                RETURN d.document_id AS document_id, d.title AS title, 
                       d.doc_type AS doc_type, 0.0 AS similarity
                LIMIT 5
                """
                result = session.run(query, **params)
                suggestions = [dict(record) for record in result]
            
            return {
                "citation_text": citation_text,
                "cleaned_text": cleaned_citation,
                "suggestion_count": len(suggestions),
                "suggestions": suggestions
            }
    
    def resolve_unresolved_citations(self, min_confidence="medium", limit=50, dry_run=True):
        """
        Attempt to automatically resolve unresolved citations
        
        Parameters:
        - min_confidence: Minimum confidence level to process
        - limit: Maximum number of citations to process
        - dry_run: If True, only suggest changes without making them
        
        Returns:
        Report of resolution results
        """
        # Get unresolved citations
        unresolved = self.find_unresolved_citations(min_confidence, limit)
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "processed_count": 0,
            "resolved_count": 0,
            "resolution_details": []
        }
        
        for citation in unresolved.get("unresolved_citations", []):
            citation_id = citation.get("citation_id")
            citation_text = citation.get("citation_text", "")
            document_id = citation.get("document_id")
            
            # Get suggestions for this citation
            suggestions = self.suggest_citation_connections(citation_text, document_id)
            
            resolution = {
                "citation_id": citation_id,
                "citation_text": citation_text,
                "document_id": document_id,
                "suggestions": suggestions.get("suggestions", []),
                "resolved": False,
                "target_document_id": None
            }
            
            # Try to resolve if we have suggestions
            if suggestions.get("suggestions"):
                best_match = suggestions["suggestions"][0]
                resolution["target_document_id"] = best_match.get("document_id")
                
                # Create the relationship if not a dry run
                if not dry_run and resolution["target_document_id"]:
                    with self.driver.session() as session:
                        connect_query = """
                        MATCH (c:Citation {id: $citation_id})
                        MATCH (d:Document {document_id: $document_id})
                        MERGE (c)-[:RESOLVES_TO]->(d)
                        RETURN c.id AS citation_id, d.document_id AS document_id
                        """
                        
                        try:
                            result = session.run(
                                connect_query, 
                                citation_id=citation_id,
                                document_id=resolution["target_document_id"]
                            )
                            
                            if result.single():
                                resolution["resolved"] = True
                                results["resolved_count"] += 1
                        except Exception as e:
                            resolution["error"] = str(e)
            
            results["processed_count"] += 1
            results["resolution_details"].append(resolution)
        
        return results
    
    def generate_validation_report(self, document_id=None):
        """
        Generate a comprehensive validation report for citations
        
        Parameters:
        - document_id: Optional document ID to focus report on
        
        Returns:
        Comprehensive validation report
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "document_id": document_id,
            "validation_summary": None,
            "unresolved_citations": None,
            "citation_clusters": None,
            "suggestions": []
        }
        
        # Get validation results
        validation_results = self.validate_citations(document_id)
        report["validation_summary"] = {
            "total_citations": validation_results["total_citations"],
            "resolved_citations": validation_results["resolved_citations"],
            "unresolved_citations": validation_results["unresolved_citations"],
            "resolution_rate": validation_results["resolution_rate"]
        }
        
        # Get unresolved citations
        unresolved = self.find_unresolved_citations(min_confidence="medium")
        report["unresolved_citations"] = {
            "count": unresolved["unresolved_count"],
            "citations": unresolved["unresolved_citations"][:10]  # Limit to top 10
        }
        
        # Get citation clusters
        clusters = self.find_citation_clusters()
        report["citation_clusters"] = {
            "count": clusters["cluster_count"],
            "clusters": clusters["citation_clusters"][:5]  # Limit to top 5
        }
        
        # Generate suggestions
        if report["validation_summary"]["resolution_rate"] < 0.7:
            report["suggestions"].append(
                "Low citation resolution rate. Consider improving citation extraction quality."
            )
            
        if unresolved["unresolved_count"] > 0:
            report["suggestions"].append(
                f"Found {unresolved['unresolved_count']} unresolved citations. Run resolve_unresolved_citations() to fix."
            )
            
        return report

# Command-line interface
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate legal citations using Neo4j")
    parser.add_argument("--document", help="Document ID to validate citations for")
    parser.add_argument("--confidence", choices=["low", "medium", "high"], 
                        default="medium", help="Minimum confidence level")
    parser.add_argument("--limit", type=int, default=100, 
                        help="Maximum number of citations to process")
    parser.add_argument("--action", choices=[
                        "validate", "unresolved", "clusters", "resolve", "report"],
                        default="validate", help="Action to perform")
    parser.add_argument("--dry-run", action="store_true", 
                        help="Don't make changes, just show what would happen")
    parser.add_argument("--output", help="Output file for JSON results")
    
    args = parser.parse_args()
    
    validator = CitationValidator()
    
    try:
        # Perform the requested action
        if args.action == "validate":
            results = validator.validate_citations(args.document, args.confidence, args.limit)
            print(f"Validated {results['total_citations']} citations")
            print(f"Resolution rate: {results['resolution_rate']:.2f}")
            
        elif args.action == "unresolved":
            results = validator.find_unresolved_citations(args.confidence, args.limit)
            print(f"Found {results['unresolved_count']} unresolved citations")
            
        elif args.action == "clusters":
            results = validator.find_citation_clusters()
            print(f"Found {results['cluster_count']} citation clusters")
            
        elif args.action == "resolve":
            results = validator.resolve_unresolved_citations(
                args.confidence, args.limit, args.dry_run
            )
            print(f"Processed {results['processed_count']} citations")
            print(f"Resolved {results['resolved_count']} citations")
            if args.dry_run:
                print("Dry run: No changes were made")
                
        elif args.action == "report":
            results = validator.generate_validation_report(args.document)
            print(f"Resolution rate: {results['validation_summary']['resolution_rate']:.2f}")
            print(f"Unresolved citations: {results['unresolved_citations']['count']}")
            print(f"Citation clusters: {results['citation_clusters']['count']}")
            
        # Save results to file if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Results saved to {args.output}")
                
    finally:
        validator.close()

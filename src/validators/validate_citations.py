#!/usr/bin/env python3
"""
Citation Validation Demonstration Script
Shows how to use the CitationValidator class to analyze and improve citation quality
"""

import os
import json
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Import our validation module
from citation_validator import CitationValidator

# Load environment variables
load_dotenv()

def save_results(results, output_file=None):
    """Save validation results to a JSON file"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"validation_results_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"Results saved to: {output_file}")
    return output_file

def validate_all_citations(validator, confidence, limit, output_file=None):
    """Validate all citations in the database"""
    print(f"Validating all citations (min confidence: {confidence}, limit: {limit})...")
    results = validator.validate_citations(
        min_confidence=confidence,
        limit=limit
    )
    
    print("Validation Complete:")
    print(f"- Total citations analyzed: {results['total_citations']}")
    print(f"- Citations that resolve to documents: {results['resolved_citations']}")
    print(f"- Unresolved citations: {results['unresolved_citations']}")
    print(f"- Resolution rate: {results['resolution_rate']:.2f}")
    
    # Sample of unresolved citations
    if results['unresolved_citations'] > 0:
        print("\nSample of unresolved citations:")
        unresolved = [c for c in results['citations'] if not c.get('resolves_to_document', False)]
        for i, citation in enumerate(unresolved[:5]):
            print(f"  {i+1}. {citation.get('text', 'No text')} (ID: {citation.get('id', 'Unknown')})")
            if citation.get('similar_citations'):
                print(f"     Similar to: {citation['similar_citations'][0].get('citation_text', 'Unknown')}")
    
    if output_file:
        save_results(results, output_file)
    
    return results

def analyze_unresolved_citations(validator, confidence, limit, output_file=None):
    """Find all unresolved citations"""
    print(f"Finding unresolved citations (min confidence: {confidence}, limit: {limit})...")
    results = validator.find_unresolved_citations(
        min_confidence=confidence,
        limit=limit
    )
    
    print("Unresolved Citations Analysis:")
    print(f"- Total unresolved citations: {results['unresolved_count']}")
    
    if results['unresolved_count'] > 0:
        print("\nSample of unresolved citations:")
        for i, citation in enumerate(results['unresolved_citations'][:10]):
            print(f"  {i+1}. {citation.get('citation_text', 'No text')}")
            print(f"     From document: {citation.get('document_title', 'Unknown')}")
            
            # Get suggestions for this citation
            suggestions = validator.suggest_citation_connections(
                citation.get('citation_text', ''),
                citation.get('document_id')
            )
            
            if suggestions.get('suggestions'):
                print(f"     Possible match: {suggestions['suggestions'][0].get('title', 'No matches')}")
    
    if output_file:
        save_results(results, output_file)
    
    return results

def analyze_citation_clusters(validator, min_references=3, output_file=None):
    """Find clusters of citations referring to the same document"""
    print(f"Finding citation clusters (min references: {min_references})...")
    results = validator.find_citation_clusters(min_references)
    
    print("Citation Cluster Analysis:")
    print(f"- Total citation clusters: {results['cluster_count']}")
    
    if results['cluster_count'] > 0:
        print("\nTop citation clusters (most referenced documents):")
        for i, cluster in enumerate(results['citation_clusters'][:5]):
            print(f"  {i+1}. {cluster.get('cited_document_title', 'Unknown')} " + 
                  f"(ID: {cluster.get('cited_document_id', 'Unknown')})")
            print(f"     Referenced by {cluster.get('citation_count', 0)} citations " + 
                  f"from {cluster.get('citing_document_count', 0)} documents")
    
    if output_file:
        save_results(results, output_file)
    
    return results

def resolve_citations(validator, confidence, limit, dry_run=True, output_file=None):
    """Attempt to resolve unresolved citations automatically"""
    print(f"Resolving unresolved citations (min confidence: {confidence}, limit: {limit}, dry_run: {dry_run})...")
    results = validator.resolve_unresolved_citations(
        min_confidence=confidence,
        limit=limit,
        dry_run=dry_run
    )
    
    print("Citation Resolution Results:")
    print(f"- Processed {results['processed_count']} citations")
    print(f"- Successfully resolved: {results['resolved_count']}")
    
    if dry_run:
        print("- DRY RUN: No changes were made to the database")
        print("\nSample of resolutions that would be made:")
        for i, resolution in enumerate(results['resolution_details'][:5]):
            if resolution.get('target_document_id'):
                print(f"  {i+1}. Citation: {resolution.get('citation_text', 'No text')}")
                print(f"     Would connect to: {resolution.get('target_document_id', 'Unknown')}")
                if resolution.get('suggestions') and resolution['suggestions']:
                    suggestion = resolution['suggestions'][0]
                    print(f"     Document title: {suggestion.get('title', 'Unknown')}")
    else:
        print("\nSample of resolutions made:")
        resolved = [r for r in results['resolution_details'] if r.get('resolved', False)]
        for i, resolution in enumerate(resolved[:5]):
            print(f"  {i+1}. Citation: {resolution.get('citation_text', 'No text')}")
            print(f"     Connected to: {resolution.get('target_document_id', 'Unknown')}")
    
    if output_file:
        save_results(results, output_file)
    
    return results

def generate_validation_report(validator, document_id=None, output_file=None):
    """Generate a comprehensive validation report"""
    doc_str = f" for document {document_id}" if document_id else ""
    print(f"Generating comprehensive validation report{doc_str}...")
    results = validator.generate_validation_report(document_id)
    
    print("Citation Validation Report:")
    print(f"- Resolution rate: {results['validation_summary']['resolution_rate']:.2f}")
    print(f"- Total citations: {results['validation_summary']['total_citations']}")
    print(f"- Resolved citations: {results['validation_summary']['resolved_citations']}")
    print(f"- Unresolved citations: {results['validation_summary']['unresolved_citations']}")
    
    print("\nUnresolved Citations:")
    print(f"- {results['unresolved_citations']['count']} citations without connections")
    
    print("\nCitation Clusters:")
    print(f"- {results['citation_clusters']['count']} document clusters identified")
    
    if results.get('suggestions'):
        print("\nImprovement Suggestions:")
        for i, suggestion in enumerate(results['suggestions']):
            print(f"  {i+1}. {suggestion}")
    
    if output_file:
        save_results(results, output_file)
    
    return results

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Validate and analyze citations in the Neo4j database")
    parser.add_argument("action", choices=[
        "validate", "unresolved", "clusters", "resolve", "report", "full-analysis"
    ], help="Action to perform")
    
    parser.add_argument("--document", help="Specific document ID to analyze")
    parser.add_argument("--confidence", choices=["low", "medium", "high"], 
                        default="medium", help="Minimum confidence level")
    parser.add_argument("--limit", type=int, default=100, 
                        help="Maximum number of citations to process")
    parser.add_argument("--references", type=int, default=3, 
                        help="Minimum references for cluster analysis")
    parser.add_argument("--apply", action="store_true", 
                        help="Apply suggested changes (not just a dry run)")
    parser.add_argument("--output", help="Output file for results (JSON)")
    
    args = parser.parse_args()
    
    # Create validator instance
    validator = CitationValidator()
    
    try:
        # Perform requested action
        if args.action == "validate":
            validate_all_citations(validator, args.confidence, args.limit, args.output)
            
        elif args.action == "unresolved":
            analyze_unresolved_citations(validator, args.confidence, args.limit, args.output)
            
        elif args.action == "clusters":
            analyze_citation_clusters(validator, args.references, args.output)
            
        elif args.action == "resolve":
            resolve_citations(validator, args.confidence, args.limit, not args.apply, args.output)
            
        elif args.action == "report":
            generate_validation_report(validator, args.document, args.output)
            
        elif args.action == "full-analysis":
            print("="*80)
            print("COMPREHENSIVE CITATION VALIDATION")
            print("="*80)
            
            # Run all analyses
            report = generate_validation_report(validator, args.document)
            
            print("\n" + "="*80)
            print("UNRESOLVED CITATIONS ANALYSIS")
            print("="*80)
            unresolved = analyze_unresolved_citations(validator, args.confidence, args.limit)
            
            print("\n" + "="*80)
            print("CITATION CLUSTERS ANALYSIS")
            print("="*80)
            clusters = analyze_citation_clusters(validator, args.references)
            
            print("\n" + "="*80)
            print("SUGGESTED CITATION RESOLUTIONS")
            print("="*80)
            resolutions = resolve_citations(validator, args.confidence, args.limit, True)
            
            # Combine all results into a single report
            if args.output:
                full_results = {
                    "timestamp": datetime.now().isoformat(),
                    "document_id": args.document,
                    "validation_report": report,
                    "unresolved_analysis": unresolved,
                    "cluster_analysis": clusters,
                    "resolution_suggestions": resolutions
                }
                save_results(full_results, args.output)
            
    finally:
        validator.close()

if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
Test script to process a single case through the entire pipeline.
This helps diagnose issues with the various storage systems integration.
"""

import os
import sys
import json
import uuid
import logging
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
loaded = load_dotenv(dotenv_path=dotenv_path)
print(f"Loaded .env file: {loaded}")

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('case_pipeline_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Import our modules
from src.processing.case_law_processor_simple import CaseLawProcessor
from src.processing.providers.court_listener_api import get_cluster_by_id

def test_process_single_case(cluster_id: str):
    """
    Test processing a single case through the entire pipeline.
    
    Args:
        cluster_id: Court Listener cluster ID to process
    """
    logger.info(f"Starting test for cluster ID: {cluster_id}")
    
    # 1. Fetch case data from Court Listener API
    logger.info("Fetching case data from Court Listener API")
    raw_case_data = get_cluster_by_id(cluster_id)
    
    if not raw_case_data:
        logger.error("Failed to fetch case data. Aborting test.")
        return
    
    # Transform the data for processing
    from src.processing.providers.court_listener_api import transform_cluster_for_processing
    case_data = transform_cluster_for_processing(raw_case_data)
    
    logger.info(f"Successfully fetched case: {case_data.get('case_name')}")
    
    # Save the raw data and transformed data for reference
    with open(f"case_{cluster_id}_raw.json", "w") as f:
        json.dump(raw_case_data, f, indent=2)
    logger.info(f"Saved raw case data to case_{cluster_id}_raw.json")
    
    with open(f"case_{cluster_id}_transformed.json", "w") as f:
        json.dump(case_data, f, indent=2)
    logger.info(f"Saved transformed case data to case_{cluster_id}_transformed.json")
    
    # 2. Initialize the processor
    logger.info("Initializing CaseLawProcessor")
    processor = CaseLawProcessor(
        user_id="test_user",
        user_role="partner",
        tenant_id=None
    )
    
    # Set a specific batch ID for traceability
    processor.current_batch_id = str(uuid.uuid4())
    logger.info(f"Using batch ID: {processor.current_batch_id}")
    
    # 3. Process the case
    logger.info("Processing case through pipeline")
    # Use court_id from transformed data or fallback to default
    court_id = case_data.get('court_id')
    if not court_id:
        # Default to Texas Supreme Court for testing
        court_id = "tex"
        logger.warning(f"Using default court_id '{court_id}' because couldn't extract from case data")
    else:
        logger.info(f"Using court_id from transformed data: {court_id}")
        
    # Ensure the court_id is set in the case data
    case_data['court_id'] = court_id
    
    # Enhance case text by also fetching opinions
    logger.info("Fetching opinions to enhance case text")
    from src.processing.providers.court_listener_api import get_opinions_by_cluster
    opinions_data = get_opinions_by_cluster(cluster_id)
    opinions = opinions_data.get('results', [])
    
    if opinions:
        logger.info(f"Found {len(opinions)} opinions for cluster {cluster_id}")
        # Extract text from opinions to enhance case text
        opinion_texts = []
        for opinion in opinions:
            if opinion.get('plain_text'):
                opinion_texts.append(opinion.get('plain_text'))
        
        if opinion_texts:
            # Combine with existing case text
            case_data['case_text'] = case_data.get('case_text', '') + "\n\n" + "\n---\n".join(opinion_texts)
            logger.info(f"Enhanced case text with {len(opinion_texts)} opinions, total length: {len(case_data['case_text'])}")
    else:
        logger.info("No opinions found to enhance case text")
    
    # Map court_id to jurisdiction
    jurisdiction_map = {
        "tex": "tx",
        "texapp": "tx",
        "texcrimapp": "tx",
        "texjpml": "tx",
        "cal": "ca",
        "calctapp": "ca",
        "calag": "ca",
        "calworkcompapp": "ca",
    }
    jurisdiction = jurisdiction_map.get(court_id, "tx")  # Default to TX if unknown
    
    # Process the case
    result = processor.process_case(case_data, jurisdiction)
    
    # 4. Check and report results
    logger.info(f"Case processing result: {json.dumps(result, indent=2)}")
    
    # 5. Verify storage in all systems
    logger.info("Verifying storage in all systems...")
    
    # Check Supabase
    supabase_case = processor.supabase.get_case(cluster_id)
    if supabase_case:
        logger.info(f"✅ Case found in Supabase with original ID")
    else:
        # Try with cl_ prefix
        supabase_case = processor.supabase.get_case(f"cl_{cluster_id}")
        if supabase_case:
            logger.info(f"✅ Case found in Supabase with cl_ prefix")
        else:
            logger.error(f"❌ Case not found in Supabase")
    
    # Check GCS 
    gcs_path = None
    if supabase_case:
        gcs_path = supabase_case.get("gcs_path")
        if gcs_path:
            gcs_content = processor.gcs.get_text(gcs_path)
            if gcs_content:
                logger.info(f"✅ Case document found in GCS at {gcs_path}")
            else:
                logger.error(f"❌ Case document not found in GCS at {gcs_path}")
        else:
            logger.error("❌ No GCS path stored in Supabase record")
    
    # Check Pinecone
    pinecone_results = None
    for search_id in [cluster_id, f"cl_{cluster_id}"]:
        try:
            pinecone_results = processor.pinecone.query(
                query_text="",  # Dummy query
                top_k=1,
                filter={"case_id": search_id}
            )
            if pinecone_results and len(pinecone_results) > 0:
                logger.info(f"✅ Case embeddings found in Pinecone with ID {search_id}")
                break
        except Exception as e:
            logger.error(f"Error querying Pinecone for ID {search_id}: {str(e)}")
    
    if not pinecone_results or len(pinecone_results) == 0:
        logger.error("❌ Case embeddings not found in Pinecone")
    
    # Check Neo4j
    neo4j_case = None
    for search_id in [cluster_id, f"cl_{cluster_id}"]:
        try:
            neo4j_case = processor.neo4j.check_case_exists(search_id)
            if neo4j_case:
                logger.info(f"✅ Case node found in Neo4j with ID {search_id}")
                break
        except Exception as e:
            logger.error(f"Error checking Neo4j for ID {search_id}: {str(e)}")
    
    if not neo4j_case:
        logger.error("❌ Case node not found in Neo4j")
    
    # Summary
    logger.info("=" * 80)
    logger.info("CASE PROCESSING TEST SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Case ID: {cluster_id}")
    logger.info(f"Case Name: {case_data.get('case_name')}")
    logger.info(f"Batch ID: {processor.current_batch_id}")
    logger.info(f"Supabase Storage: {'✅ Success' if supabase_case else '❌ Failure'}")
    logger.info(f"GCS Storage: {'✅ Success' if gcs_path and processor.gcs.get_text(gcs_path) else '❌ Failure'}")
    logger.info(f"Pinecone Storage: {'✅ Success' if pinecone_results and len(pinecone_results) > 0 else '❌ Failure'}")
    logger.info(f"Neo4j Storage: {'✅ Success' if neo4j_case else '❌ Failure'}")
    logger.info("=" * 80)

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test single case processing")
    parser.add_argument("--cluster-id", required=True, help="Court Listener cluster ID to process")
    
    args = parser.parse_args()
    test_process_single_case(args.cluster_id)

if __name__ == "__main__":
    main()

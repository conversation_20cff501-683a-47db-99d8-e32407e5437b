"""
Tests for Enhanced Search API (v1) - Week 5 implementation.
Tests advanced filtering, RBAC, and enhanced functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from datetime import date, datetime

from src.api.main import app
from src.api.search.enhanced_search import EnhancedSearchEngine, EnhancedSearchRequest
from src.api.auth.rbac import rbac_manager


class TestEnhancedSearchEngine:
    """Test the enhanced search engine functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = EnhancedSearchEngine()
    
    def test_validate_request_valid(self):
        """Test request validation with valid parameters."""
        request = EnhancedSearchRequest(
            q="medical malpractice",
            jurisdiction="tx",
            practice_areas=["personal_injury", "medical_malpractice"],
            authority_min=0.5,
            date_range={"start": "2020-01-01", "end": "2023-12-31"},
            sort_by="relevance",
            limit=10,
            offset=0
        )
        
        # Should not raise any exception
        self.engine.validate_request(request)
    
    def test_validate_request_invalid_practice_areas(self):
        """Test request validation with invalid practice areas."""
        request = EnhancedSearchRequest(
            q="test query",
            practice_areas=["invalid_area", "another_invalid"]
        )
        
        with pytest.raises(ValueError, match="Invalid practice areas"):
            self.engine.validate_request(request)
    
    def test_validate_request_invalid_sort_option(self):
        """Test request validation with invalid sort option."""
        request = EnhancedSearchRequest(
            q="test query",
            sort_by="invalid_sort"
        )
        
        with pytest.raises(ValueError, match="Invalid sort option"):
            self.engine.validate_request(request)
    
    def test_validate_request_invalid_date_range(self):
        """Test request validation with invalid date range."""
        request = EnhancedSearchRequest(
            q="test query",
            date_range={"start": "invalid-date"}
        )
        
        with pytest.raises(ValueError, match="Invalid date range"):
            self.engine.validate_request(request)
    
    def test_parse_date_range(self):
        """Test date range parsing."""
        date_range_dict = {
            "start": "2020-01-01",
            "end": "2023-12-31"
        }
        
        date_range = self.engine._parse_date_range(date_range_dict)
        
        assert date_range.start == date(2020, 1, 1)
        assert date_range.end == date(2023, 12, 31)
    
    def test_build_pinecone_filter(self):
        """Test Pinecone filter building."""
        request = EnhancedSearchRequest(
            q="test query",
            jurisdiction="tx",
            practice_areas=["personal_injury", "medical_malpractice"]
        )
        
        pinecone_filter = self.engine._build_pinecone_filter(request)
        
        expected = {
            "filter": {
                "jurisdiction": {"$eq": "tx"},
                "practice_area": {"$in": ["personal_injury", "medical_malpractice"]}
            }
        }
        
        assert pinecone_filter == expected
    
    def test_build_supabase_filter(self):
        """Test Supabase filter building."""
        request = EnhancedSearchRequest(
            q="test query",
            authority_min=0.5,
            date_range={"start": "2020-01-01", "end": "2023-12-31"},
            jurisdiction="tx"
        )
        
        supabase_filter = self.engine._build_supabase_filter(request)
        
        assert "authority_score >= :authority_min" in supabase_filter["conditions"]
        assert "document_date >= :start_date" in supabase_filter["conditions"]
        assert "document_date <= :end_date" in supabase_filter["conditions"]
        assert "jurisdiction = :jurisdiction" in supabase_filter["conditions"]
        
        assert supabase_filter["params"]["authority_min"] == 0.5
        assert supabase_filter["params"]["jurisdiction"] == "tx"
    
    def test_apply_advanced_sorting_relevance(self):
        """Test advanced sorting by relevance."""
        from src.api.search.hybrid_search import SearchResult
        
        results = [
            SearchResult(
                id="1", title="Test 1", type="case", jurisdiction="tx",
                authority_score=0.8, relevance_score=0.6, snippet="", metadata={}
            ),
            SearchResult(
                id="2", title="Test 2", type="case", jurisdiction="tx",
                authority_score=0.5, relevance_score=0.9, snippet="", metadata={}
            )
        ]
        
        sorted_results = self.engine._apply_advanced_sorting(results, "relevance")
        
        # Should be sorted by hybrid score (0.7 * relevance + 0.3 * authority)
        # Result 2: 0.7 * 0.9 + 0.3 * 0.5 = 0.78
        # Result 1: 0.7 * 0.6 + 0.3 * 0.8 = 0.66
        assert sorted_results[0].id == "2"
        assert sorted_results[1].id == "1"
    
    def test_apply_advanced_sorting_authority(self):
        """Test advanced sorting by authority."""
        from src.api.search.hybrid_search import SearchResult
        
        results = [
            SearchResult(
                id="1", title="Test 1", type="case", jurisdiction="tx",
                authority_score=0.5, relevance_score=0.9, snippet="", metadata={}
            ),
            SearchResult(
                id="2", title="Test 2", type="case", jurisdiction="tx",
                authority_score=0.8, relevance_score=0.6, snippet="", metadata={}
            )
        ]
        
        sorted_results = self.engine._apply_advanced_sorting(results, "authority")
        
        # Should be sorted by authority score
        assert sorted_results[0].id == "2"  # Higher authority
        assert sorted_results[1].id == "1"


class TestRBACIntegration:
    """Test RBAC integration with search endpoints."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
    
    def test_get_role_from_payload(self):
        """Test role extraction from JWT payload."""
        # Test direct role field
        payload = {"role": "attorney", "sub": "user123"}
        role = rbac_manager.get_role(payload)
        assert role == "attorney"
        
        # Test app_metadata.role
        payload = {"app_metadata": {"role": "partner"}, "sub": "user123"}
        role = rbac_manager.get_role(payload)
        assert role == "partner"
        
        # Test default to client
        payload = {"sub": "user123"}
        role = rbac_manager.get_role(payload)
        assert role == "client"
    
    def test_has_permission(self):
        """Test permission checking."""
        # Partner should have access to attorney functions
        assert rbac_manager.has_permission("partner", "attorney") == True
        
        # Attorney should not have access to partner functions
        assert rbac_manager.has_permission("attorney", "partner") == False
        
        # Same role should have access
        assert rbac_manager.has_permission("paralegal", "paralegal") == True
    
    def test_can_access_jurisdiction(self):
        """Test jurisdiction access checking."""
        # No restrictions - should allow all
        assert rbac_manager.can_access_jurisdiction([], "tx") == True
        
        # Has access to requested jurisdiction
        assert rbac_manager.can_access_jurisdiction(["tx", "ny"], "tx") == True
        
        # No access to requested jurisdiction
        assert rbac_manager.can_access_jurisdiction(["ny", "fl"], "tx") == False
        
        # No specific jurisdiction requested
        assert rbac_manager.can_access_jurisdiction(["tx"], None) == True
    
    def test_validate_access(self):
        """Test comprehensive access validation."""
        user_info = {
            "user_id": "user123",
            "role": "attorney",
            "jurisdictions": ["tx", "ny"],
            "tenant_id": "tenant1"
        }
        
        # Valid access
        result = rbac_manager.validate_access(
            user_info, 
            required_role="paralegal", 
            requested_jurisdiction="tx"
        )
        assert result["access_granted"] == True
        
        # Invalid role
        result = rbac_manager.validate_access(
            user_info, 
            required_role="partner", 
            requested_jurisdiction="tx"
        )
        assert result["access_granted"] == False
        assert "Insufficient role" in result["reasons"][0]
        
        # Invalid jurisdiction
        result = rbac_manager.validate_access(
            user_info, 
            required_role="paralegal", 
            requested_jurisdiction="ca"
        )
        assert result["access_granted"] == False
        assert "No access to jurisdiction" in result["reasons"][0]


class TestSearchAPIEndpoints:
    """Test the search API endpoints with authentication and RBAC."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
    
    @patch('src.api.auth.jwt_middleware.JWTAuthMiddleware.verify_token')
    def test_v1_search_with_valid_user(self, mock_verify):
        """Test v1 search endpoint with valid authenticated user."""
        # Mock JWT verification
        mock_verify.return_value = {
            "user_id": "user123",
            "role": "attorney",
            "jurisdictions": ["tx"],
            "tenant_id": "tenant1",
            "permissions_hash": "hash123"
        }
        
        # Mock the enhanced search engine
        with patch('src.api.search.enhanced_search.EnhancedSearchEngine.search') as mock_search:
            mock_search.return_value = AsyncMock()
            mock_search.return_value.results = []
            mock_search.return_value.total = 0
            mock_search.return_value.query_time_ms = 100
            mock_search.return_value.query = "test query"
            mock_search.return_value.filters_applied = {}
            
            response = self.client.get(
                "/v1/search",
                params={
                    "q": "medical malpractice",
                    "jurisdiction": "tx",
                    "practice_areas": ["personal_injury"],
                    "authority_min": 0.5,
                    "sort_by": "relevance"
                },
                headers={"Authorization": "Bearer valid_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "results" in data
            assert "total_hits" in data
            assert "next_offset" in data
    
    @patch('src.api.auth.jwt_middleware.JWTAuthMiddleware.verify_token')
    def test_v1_search_jurisdiction_access_denied(self, mock_verify):
        """Test v1 search endpoint with jurisdiction access denied."""
        # Mock JWT verification - user only has access to 'ny'
        mock_verify.return_value = {
            "user_id": "user123",
            "role": "attorney",
            "jurisdictions": ["ny"],
            "tenant_id": "tenant1",
            "permissions_hash": "hash123"
        }
        
        response = self.client.get(
            "/v1/search",
            params={
                "q": "medical malpractice",
                "jurisdiction": "tx"  # User doesn't have access to TX
            },
            headers={"Authorization": "Bearer valid_token"}
        )
        
        assert response.status_code == 403
        assert "Access denied to jurisdiction" in response.json()["detail"]
    
    def test_v1_search_parameter_validation(self):
        """Test v1 search endpoint parameter validation."""
        with patch('src.api.auth.jwt_middleware.JWTAuthMiddleware.verify_token') as mock_verify:
            mock_verify.return_value = {
                "user_id": "user123",
                "role": "attorney",
                "jurisdictions": ["tx"],
                "tenant_id": "tenant1",
                "permissions_hash": "hash123"
            }
            
            # Test invalid authority_min
            response = self.client.get(
                "/v1/search",
                params={
                    "q": "test query",
                    "authority_min": 1.5  # Invalid - should be <= 1.0
                },
                headers={"Authorization": "Bearer valid_token"}
            )
            
            assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
class TestRateLimiting:
    """Test rate limiting functionality."""
    
    async def test_memory_rate_limiter(self):
        """Test memory-based rate limiter."""
        from src.api.auth.rate_limiter import MemoryRateLimitBackend
        
        backend = MemoryRateLimitBackend()
        
        # First request should be allowed
        allowed, remaining, reset_time = await backend.check_limit("user1", 5, 60)
        assert allowed == True
        assert remaining == 4
        
        # Fill up the limit
        for i in range(4):
            await backend.check_limit("user1", 5, 60)
        
        # Next request should be denied
        allowed, remaining, reset_time = await backend.check_limit("user1", 5, 60)
        assert allowed == False
        assert remaining == 0
    
    async def test_enhanced_rate_limiter(self):
        """Test enhanced rate limiter with user and tenant limits."""
        from src.api.auth.rate_limiter import EnhancedRateLimiter
        
        # Mock the backend to use memory for testing
        with patch.dict('os.environ', {'RATE_LIMIT_BACKEND': 'memory', 'RATE_LIMIT_PER_USER': '3', 'RATE_LIMIT_PER_TENANT': '5'}):
            limiter = EnhancedRateLimiter()
            
            # Test user limit
            result = await limiter.check_limit("user1", "tenant1")
            assert result["allowed"] == True
            assert result["user_remaining"] == 2
            assert result["tenant_remaining"] == 4
            
            # Fill user limit
            await limiter.check_limit("user1", "tenant1")
            await limiter.check_limit("user1", "tenant1")
            
            # User limit exceeded
            result = await limiter.check_limit("user1", "tenant1")
            assert result["allowed"] == False


if __name__ == "__main__":
    pytest.main([__file__])

#!/usr/bin/env python3
"""
Citation Extraction Benchmark Tool
Compares performance of different models and OCR cleanup approaches
"""

import os
import json
import time
import argparse
from datetime import datetime
import glob
from dotenv import load_dotenv

# Import our extraction and processing modules
from citation_extractor import HybridCitationExtractor
from ocr_cleaner import clean_document_text, process_citations

# Load environment variables
load_dotenv()

def read_document(file_path):
    """Read the content of a document from a file (PDF or TXT)"""
    # Check file extension
    if file_path.lower().endswith('.pdf'):
        import PyPDF2
        # Extract text from PDF
        try:
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ''
                for page in reader.pages:
                    text += page.extract_text() + '\n'
                return text
        except Exception as e:
            print(f"Error reading PDF {file_path}: {str(e)}")
            return ""
    else:
        # Regular text file
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with different encoding if UTF-8 fails
            with open(file_path, 'r', encoding='latin-1') as file:
                return file.read()

def save_benchmark_results(results, output_file=None):
    """Save benchmark results to a JSON file"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"benchmark_results_{timestamp}.json"
    
    output_path = os.path.join(os.path.dirname(__file__), output_file)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"Benchmark results saved to: {output_path}")
    return output_path

def run_benchmark(docs_folder, models, test_count=5, enable_ocr_cleanup=None):
    """
    Run benchmark tests with different models and configurations
    
    Parameters:
    - docs_folder: Path to folder containing test documents
    - models: List of Gemini models to test ['gemini-2.5-pro-preview-03-25', 'gemini-2.0-flash-lite']
    - test_count: Number of documents to process for each configuration
    - enable_ocr_cleanup: Test with OCR cleanup (True/False/None). If None, test both.
    
    Returns dictionary of benchmark results
    """
    results = {
        "metadata": {
            "timestamp": datetime.now().isoformat(),
            "test_count": test_count,
            "models_tested": models,
            "ocr_cleanup_tested": [True, False] if enable_ocr_cleanup is None else [enable_ocr_cleanup]
        },
        "configurations": []
    }
    
    # Get test documents - look for both PDFs and text files
    pdf_files = glob.glob(os.path.join(docs_folder, "*.pdf"))
    txt_files = glob.glob(os.path.join(docs_folder, "*.txt"))
    document_files = pdf_files + txt_files
    
    if not document_files:
        print(f"No PDF or text documents found in {docs_folder}")
        return results
    
    # Limit to test_count
    document_files = document_files[:test_count]
    
    # Load documents
    documents = []
    for file_path in document_files:
        doc_id = os.path.basename(file_path)
        content = read_document(file_path)
        documents.append({
            "id": doc_id,
            "content": content,
            "file_path": file_path
        })
    
    print(f"Running benchmarks on {len(documents)} documents")
    
    # Setup test configurations
    test_configs = []
    for model in models:
        if enable_ocr_cleanup is None:
            # Test both with and without OCR cleanup
            test_configs.append({"model": model, "ocr_cleanup": True})
            test_configs.append({"model": model, "ocr_cleanup": False})
        else:
            # Test only with specified OCR cleanup setting
            test_configs.append({"model": model, "ocr_cleanup": enable_ocr_cleanup})
    
    # Add regex-only configuration
    test_configs.append({"model": "regex-only", "ocr_cleanup": True})
    test_configs.append({"model": "regex-only", "ocr_cleanup": False})
    
    # Run benchmarks for each configuration
    for config in test_configs:
        model = config["model"]
        ocr_cleanup = config["ocr_cleanup"]
        
        print(f"\nBenchmarking configuration: Model={model}, OCR Cleanup={ocr_cleanup}")
        
        # Set environment variables for this configuration
        os.environ["GEMINI_MODEL"] = model if model != "regex-only" else "gemini-2.5-pro-preview-03-25"
        os.environ["ENABLE_OCR_CLEANUP"] = str(ocr_cleanup).lower()
        
        # Create extractor
        extractor = HybridCitationExtractor()
        use_llm = model != "regex-only"
        
        # Run tests
        config_results = {
            "model": model,
            "ocr_cleanup": ocr_cleanup,
            "overall_stats": {
                "total_documents": len(documents),
                "total_citations": 0,
                "total_processing_time": 0,
                "avg_citations_per_doc": 0,
                "avg_time_per_doc": 0,
                "confidence_levels": {"high": 0, "medium": 0, "low": 0, "unknown": 0}
            },
            "document_results": []
        }
        
        for doc in documents:
            print(f"  Processing document: {doc['id']}")
            start_time = time.time()
            
            # Extract citations
            citations = extractor.extract_citations(
                doc["content"], 
                "legal", 
                {"document_id": doc["id"]},
                use_llm=use_llm
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Count confidence levels
            confidence_counts = {"high": 0, "medium": 0, "low": 0, "unknown": 0}
            for citation in citations:
                confidence = citation.get("confidence", "unknown").lower()
                confidence_counts[confidence] += 1
            
            # Record document result
            doc_result = {
                "document_id": doc["id"],
                "citation_count": len(citations),
                "processing_time": processing_time,
                "confidence_counts": confidence_counts
            }
            config_results["document_results"].append(doc_result)
            
            # Update overall stats
            config_results["overall_stats"]["total_citations"] += len(citations)
            config_results["overall_stats"]["total_processing_time"] += processing_time
            for level, count in confidence_counts.items():
                config_results["overall_stats"]["confidence_levels"][level] += count
        
        # Calculate averages
        doc_count = len(documents)
        if doc_count > 0:
            config_results["overall_stats"]["avg_citations_per_doc"] = config_results["overall_stats"]["total_citations"] / doc_count
            config_results["overall_stats"]["avg_time_per_doc"] = config_results["overall_stats"]["total_processing_time"] / doc_count
        
        # Record configuration results
        results["configurations"].append(config_results)
        
        print(f"  Completed: {len(documents)} documents processed in {config_results['overall_stats']['total_processing_time']:.2f} seconds")
        print(f"  Average processing time: {config_results['overall_stats']['avg_time_per_doc']:.2f} seconds per document")
        print(f"  Total citations: {config_results['overall_stats']['total_citations']} ({config_results['overall_stats']['avg_citations_per_doc']:.2f} per document)")
        print(f"  Confidence distribution: {config_results['overall_stats']['confidence_levels']}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Benchmark citation extraction with different models and OCR cleanup")
    parser.add_argument("--docs", help="Path to folder containing test documents")
    parser.add_argument("--count", type=int, default=5, help="Number of documents to process for each configuration")
    parser.add_argument("--models", default="gemini-2.5-pro-preview-03-25,gemini-2.0-flash-lite", help="Comma-separated list of models to test")
    parser.add_argument("--ocr", choices=["true", "false", "both"], default="both", help="Test with OCR cleanup")
    parser.add_argument("--output", help="Output file for benchmark results (default: timestamped filename)")
    
    args = parser.parse_args()
    
    # Use PDF_FOLDER_PATH_SUCCESS from .env if docs not specified
    docs_folder = args.docs or os.getenv("PDF_FOLDER_PATH_SUCCESS")
    if not docs_folder:
        print("Error: No documents folder specified. Use --docs or set PDF_FOLDER_PATH_SUCCESS in .env")
        return
    
    # Parse models
    models = [model.strip() for model in args.models.split(",")]
    
    # Parse OCR cleanup flag
    if args.ocr == "both":
        ocr_cleanup = None  # Test both
    else:
        ocr_cleanup = args.ocr.lower() == "true"
    
    # Run benchmark
    results = run_benchmark(docs_folder, models, args.count, ocr_cleanup)
    
    # Save results
    save_benchmark_results(results, args.output)

if __name__ == "__main__":
    main()

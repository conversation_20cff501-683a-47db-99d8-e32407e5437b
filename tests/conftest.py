"""
Pytest configuration and fixtures for tests.
"""

import os
import pytest
from unittest.mock import MagicMock, patch
from typing import Dict, List, Optional

# Mock environment variables
@pytest.fixture(autouse=True)
def mock_env_vars():
    """Set up mock environment variables for testing."""
    with patch.dict(os.environ, {
        "SUPABASE_URL": "https://test-supabase.co",
        "SUPABASE_KEY": "test-key",
        "GCS_BUCKET_NAME": "test-bucket",
        "PINECONE_INDEX_NAME": "test-index",
        "NEO4J_URI": "bolt://localhost:7687",
        "COURT_LISTENER_API_KEY": "test-api-key",
        "FINDLAW_API_KEY": "test-findlaw-key",
        "ALLOWED_JURISDICTIONS": "tx,ca,ny,fed"
    }):
        yield

# Mock Supabase connector
@pytest.fixture
def mock_supabase():
    """Create a mock Supabase connector."""
    mock = MagicMock()
    
    # Set up tenant jurisdictions
    def get_tenant_jurisdictions(tenant_id):
        tenant_map = {
            "tenant1": ["tx", "ca"],
            "tenant2": ["ny", "fed"],
            "tenant3": ["tx"]
        }
        return tenant_map.get(tenant_id, [])
    
    # Set up client case jurisdictions
    def get_client_case_jurisdictions(user_id):
        client_map = {
            "client1": ["tx"],
            "client2": ["ca"],
            "client3": []
        }
        return client_map.get(user_id, [])
    
    # Set up user role lookup
    def get_user_role(user_id):
        role_map = {
            "user1": "partner",
            "user2": "attorney",
            "user3": "paralegal",
            "user4": "staff",
            "client1": "client",
            "client2": "client"
        }
        return role_map.get(user_id)
    
    # Set up user tenant lookup
    def get_user_tenant(user_id):
        tenant_map = {
            "user1": "tenant1",
            "user2": "tenant1",
            "user3": "tenant2",
            "user4": "tenant3",
            "client1": "tenant1",
            "client2": "tenant2"
        }
        return tenant_map.get(user_id)
    
    # Mock get_case to return proper quality metrics
    def get_case(case_id):
        if case_id == "test-case-id":
            return {
                "id": "test-case-id",
                "jurisdiction": "tx",
                "completeness_score": 0.5,  # Below the 0.7 threshold for most tests
                "opinion_count": 1,
                "citation_count": 2
            }
        return None
    
    # Mock methods
    mock.get_tenant_jurisdictions.side_effect = get_tenant_jurisdictions
    mock.get_client_case_jurisdictions.side_effect = get_client_case_jurisdictions
    mock.get_user_role.side_effect = get_user_role
    mock.get_user_tenant.side_effect = get_user_tenant
    mock.get_case.side_effect = get_case
    mock.store_case.return_value = {"id": "test-case-id", "jurisdiction": "tx"}
    mock.create_processing_history.return_value = {"id": "test-history-id"}
    
    # Mock citation storage
    mock.store_citation.return_value = {"id": "test-citation-id"}
    
    return mock

# Mock GCS connector
@pytest.fixture
def mock_gcs():
    """Create a mock GCS connector."""
    mock = MagicMock()
    mock.store_text.return_value = "gs://test-bucket/test-path"
    return mock

# Mock Pinecone connector
@pytest.fixture
def mock_pinecone():
    """Create a mock Pinecone connector."""
    mock = MagicMock()
    mock.store_embedding.return_value = "test-vector-id"
    return mock

# Mock Neo4j connector
@pytest.fixture
def mock_neo4j():
    """Create a mock Neo4j connector."""
    mock = MagicMock()
    mock.add_citation.return_value = True
    return mock

# Mock Court Listener connector
@pytest.fixture
def mock_court_listener():
    """Create a mock Court Listener connector."""
    mock = MagicMock()
    
    # Sample case data
    def search_cases(jurisdiction, query, count):
        return {
            "count": 1,
            "results": [
                {
                    "id": "test-case-id",
                    "cluster_id": "test-cluster-id",
                    "case_name": "Test Case v. Example",
                    "jurisdiction": jurisdiction,
                    "opinions": [
                        {
                            "id": "test-opinion-id",
                            "text": "This is a test opinion text with a citation to Smith v. Jones.",
                            "opinions_cited": [
                                {
                                    "id": "cited-opinion-id",
                                    "cluster_id": "cited-cluster-id",
                                    "citation": "123 F.3d 456"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    
    mock.search_cases.side_effect = search_cases
    return mock

# Mock FindLaw connector
@pytest.fixture
def mock_findlaw():
    """Create a mock FindLaw connector."""
    mock = MagicMock()
    
    # Sample opinion data
    def search_opinions(query, jurisdiction, page, per_page):
        return {
            "count": 1,
            "results": [
                {
                    "id": "test-findlaw-id",
                    "source_id": "fl-123456",
                    "case_name": "FindLaw Test Case",
                    "jurisdiction": jurisdiction,
                    "opinions": [
                        {
                            "id": "fl-opinion-id",
                            "text": "This is a test FindLaw opinion with citation to Brown v. Board."
                        }
                    ]
                }
            ]
        }
    
    mock.search_opinions.side_effect = search_opinions
    return mock

# Mock Citation Extractor
@pytest.fixture
def mock_citation_extractor():
    """Create a mock Citation Extractor."""
    mock = MagicMock()
    
    def extract_cited_cases(case_id, text, jurisdiction):
        return [
            {
                "id": "citation-1",
                "cited_id": "cited-case-1",
                "text": "Smith v. Jones, 123 F.3d 456",
                "confidence": 0.95
            },
            {
                "id": "citation-2",
                "cited_id": "cited-case-2",
                "text": "Brown v. Board, 347 U.S. 483",
                "confidence": 0.98
            }
        ]
    
    mock.extract_cited_cases.side_effect = extract_cited_cases
    return mock

# Sample test data
@pytest.fixture
def sample_case_data():
    """Sample case data for testing."""
    return {
        "id": "test-case-id",
        "cluster_id": "test-cluster-id",
        "case_name": "Test Case v. Example",
        "jurisdiction": "tx",
        "opinions": [
            {
                "id": "test-opinion-id",
                "text": "This is a test opinion text.",
                "opinions_cited": [
                    {
                        "id": "cited-opinion-id",
                        "cluster_id": "cited-cluster-id",
                        "citation": "123 F.3d 456"
                    }
                ]
            }
        ]
    }

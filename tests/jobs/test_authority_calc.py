"""
Tests for Authority Calculator (Week 6).
Tests PageRank calculation, recency boost, and dry-run mode.
"""

import pytest
import math
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import networkx as nx

from src.jobs.authority_calculator import AuthorityCalculator, run_authority_calculation


class TestAuthorityCalculator:
    """Test suite for Authority Calculator."""
    
    def setup_method(self):
        """Setup test calculator with mocked dependencies."""
        with patch('src.jobs.authority_calculator.Neo4jConnector'), \
             patch('src.jobs.authority_calculator.SupabaseConnector'):
            self.calculator = AuthorityCalculator()
    
    def test_pagerank_parameters(self):
        """Test that PageRank uses correct parameters."""
        # Verify parameters are set correctly
        assert self.calculator.damping == 0.85
        assert self.calculator.max_iter == 100
        assert self.calculator.tol == 1e-6
        
        # Verify recency boost parameters
        assert self.calculator.pagerank_weight == 0.8
        assert self.calculator.recency_weight == 0.2
        assert self.calculator.recency_decay == 0.1
    
    def test_build_citation_network(self):
        """Test citation network building from Neo4j data."""
        # Mock Neo4j session and results
        mock_session = MagicMock()
        mock_result = [
            {
                "source_id": "doc1",
                "target_id": "doc2", 
                "weight": 5,
                "source_date": "2023-01-01",
                "target_date": "2022-01-01"
            },
            {
                "source_id": "doc2",
                "target_id": "doc3",
                "weight": 3,
                "source_date": "2022-01-01", 
                "target_date": "2021-01-01"
            }
        ]
        
        mock_isolated_result = [
            {"document_id": "doc4", "date": "2020-01-01"}
        ]
        
        mock_session.run.side_effect = [
            mock_result,  # First query (citations)
            mock_isolated_result  # Second query (isolated nodes)
        ]
        
        self.calculator.neo4j.driver.session.return_value.__enter__.return_value = mock_session
        
        # Test network building
        graph = self.calculator._build_citation_network()
        
        # Verify graph structure
        assert isinstance(graph, nx.DiGraph)
        assert len(graph.nodes()) == 4  # doc1, doc2, doc3, doc4
        assert len(graph.edges()) == 2  # doc1->doc2, doc2->doc3
        
        # Verify edge weights
        assert graph["doc1"]["doc2"]["weight"] == 5
        assert graph["doc2"]["doc3"]["weight"] == 3
        
        # Verify isolated node is included
        assert "doc4" in graph.nodes()
        assert graph.degree("doc4") == 0
    
    def test_calculate_pagerank(self):
        """Test PageRank calculation with specified parameters."""
        # Create test graph
        graph = nx.DiGraph()
        graph.add_edge("doc1", "doc2", weight=2)
        graph.add_edge("doc2", "doc3", weight=1)
        graph.add_edge("doc3", "doc1", weight=3)
        
        # Calculate PageRank
        pagerank_scores = self.calculator._calculate_pagerank(graph)
        
        # Verify results
        assert isinstance(pagerank_scores, dict)
        assert len(pagerank_scores) == 3
        assert all(0 <= score <= 1 for score in pagerank_scores.values())
        
        # Verify sum approximately equals 1 (PageRank property)
        total_score = sum(pagerank_scores.values())
        assert abs(total_score - 1.0) < 0.01
    
    def test_apply_recency_boost(self):
        """Test recency boost formula application."""
        # Mock PageRank scores
        pagerank_scores = {
            "doc1": 0.4,
            "doc2": 0.3,
            "doc3": 0.3
        }
        
        # Mock document dates
        current_date = datetime(2024, 1, 1)
        mock_dates = [
            {"document_id": "doc1", "date": "2023-01-01"},  # 1 year old
            {"document_id": "doc2", "date": "2022-01-01"},  # 2 years old
            {"document_id": "doc3", "date": "2021-01-01"}   # 3 years old
        ]
        
        mock_session = MagicMock()
        mock_session.run.return_value = mock_dates
        self.calculator.neo4j.driver.session.return_value.__enter__.return_value = mock_session
        
        with patch('src.jobs.authority_calculator.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_date
            mock_datetime.fromisoformat = datetime.fromisoformat
            
            # Apply recency boost
            authority_scores = self.calculator._apply_recency_boost(pagerank_scores)
        
        # Verify formula: authority = 0.8 * pagerank + 0.2 * e^(-0.1*age_years)
        for doc_id, authority in authority_scores.items():
            pagerank = pagerank_scores[doc_id]
            
            if doc_id == "doc1":  # 1 year old
                expected_recency = math.exp(-0.1 * 1)
            elif doc_id == "doc2":  # 2 years old
                expected_recency = math.exp(-0.1 * 2)
            else:  # doc3, 3 years old
                expected_recency = math.exp(-0.1 * 3)
            
            expected_authority = 0.8 * pagerank + 0.2 * expected_recency
            assert abs(authority - expected_authority) < 0.001
    
    def test_normalize_scores(self):
        """Test score normalization to 0-1 range."""
        # Test scores with different ranges
        scores = {
            "doc1": 0.5,
            "doc2": 1.0,
            "doc3": 0.2,
            "doc4": 0.8
        }
        
        normalized = self.calculator._normalize_scores(scores)
        
        # Verify normalization
        assert min(normalized.values()) == 0.0
        assert max(normalized.values()) == 1.0
        
        # Verify relative ordering is preserved
        score_order = sorted(scores.items(), key=lambda x: x[1])
        normalized_order = sorted(normalized.items(), key=lambda x: x[1])
        
        assert [item[0] for item in score_order] == [item[0] for item in normalized_order]
    
    def test_normalize_scores_edge_cases(self):
        """Test score normalization edge cases."""
        # Test empty scores
        assert self.calculator._normalize_scores({}) == {}
        
        # Test identical scores
        identical_scores = {"doc1": 0.5, "doc2": 0.5, "doc3": 0.5}
        normalized = self.calculator._normalize_scores(identical_scores)
        
        # All should be 0.5 when identical
        assert all(score == 0.5 for score in normalized.values())
    
    def test_update_authority_scores(self):
        """Test authority score updates in both databases."""
        authority_scores = {
            "doc1": 0.8,
            "doc2": 0.6,
            "doc3": 0.4
        }
        
        # Mock Neo4j session
        mock_neo4j_session = MagicMock()
        self.calculator.neo4j.driver.session.return_value.__enter__.return_value = mock_neo4j_session
        
        # Mock Supabase table operations
        mock_table = MagicMock()
        mock_update = MagicMock()
        mock_table.update.return_value = mock_update
        mock_update.eq.return_value = mock_update
        mock_update.execute.return_value = None
        self.calculator.supabase.table.return_value = mock_table
        
        # Update scores
        self.calculator._update_authority_scores(authority_scores)
        
        # Verify Neo4j update was called
        mock_neo4j_session.run.assert_called_once()
        call_args = mock_neo4j_session.run.call_args
        assert "UNWIND $scores" in call_args[0][0]
        assert len(call_args[1]["scores"]) == 3
        
        # Verify Supabase updates were called
        assert mock_table.update.call_count == 3
        assert mock_update.eq.call_count == 3
        assert mock_update.execute.call_count == 3
    
    @patch('src.jobs.authority_calculator.authority_job_counter')
    @patch('src.jobs.authority_calculator.authority_age_gauge')
    def test_calculate_authority_scores_success(self, mock_gauge, mock_counter):
        """Test successful authority score calculation."""
        # Mock all the methods
        with patch.object(self.calculator, '_build_citation_network') as mock_build, \
             patch.object(self.calculator, '_calculate_pagerank') as mock_pagerank, \
             patch.object(self.calculator, '_apply_recency_boost') as mock_recency, \
             patch.object(self.calculator, '_normalize_scores') as mock_normalize, \
             patch.object(self.calculator, '_update_authority_scores') as mock_update:
            
            # Setup mock returns
            mock_graph = nx.DiGraph()
            mock_graph.add_node("doc1")
            mock_build.return_value = mock_graph
            
            mock_pagerank.return_value = {"doc1": 0.5}
            mock_recency.return_value = {"doc1": 0.6}
            mock_normalize.return_value = {"doc1": 0.7}
            
            # Run calculation
            result = self.calculator.calculate_authority_scores()
            
            # Verify all steps were called
            mock_build.assert_called_once()
            mock_pagerank.assert_called_once_with(mock_graph)
            mock_recency.assert_called_once_with({"doc1": 0.5})
            mock_normalize.assert_called_once_with({"doc1": 0.6})
            mock_update.assert_called_once_with({"doc1": 0.7})
            
            # Verify metrics
            mock_counter.labels.assert_called_with(status='success')
            mock_gauge.set.assert_called_with(0)
            
            # Verify result
            assert result == {"doc1": 0.7}
    
    @patch('src.jobs.authority_calculator.authority_job_counter')
    def test_calculate_authority_scores_empty_graph(self, mock_counter):
        """Test authority calculation with empty citation network."""
        with patch.object(self.calculator, '_build_citation_network') as mock_build:
            # Return empty graph
            mock_build.return_value = nx.DiGraph()
            
            result = self.calculator.calculate_authority_scores()
            
            # Verify empty result
            assert result == {}
            
            # Verify empty status metric
            mock_counter.labels.assert_called_with(status='empty')
    
    @patch('src.jobs.authority_calculator.authority_job_counter')
    def test_calculate_authority_scores_error(self, mock_counter):
        """Test authority calculation error handling."""
        with patch.object(self.calculator, '_build_citation_network') as mock_build:
            # Raise exception
            mock_build.side_effect = Exception("Test error")
            
            with pytest.raises(Exception):
                self.calculator.calculate_authority_scores()
            
            # Verify error metric
            mock_counter.labels.assert_called_with(status='error')
    
    def test_dry_run_mode(self):
        """Test dry-run mode functionality."""
        # This would be implemented in the manual script
        # For now, verify the calculator can be instantiated without errors
        assert self.calculator is not None
        assert hasattr(self.calculator, 'calculate_authority_scores')


class TestAuthorityCalculationRunner:
    """Test the main authority calculation runner function."""
    
    @patch('src.jobs.authority_calculator.AuthorityCalculator')
    def test_run_authority_calculation_success(self, mock_calculator_class):
        """Test successful authority calculation run."""
        mock_calculator = Mock()
        mock_calculator.calculate_authority_scores.return_value = {"doc1": 0.5}
        mock_calculator_class.return_value = mock_calculator
        
        result = run_authority_calculation()
        
        assert result is True
        mock_calculator.calculate_authority_scores.assert_called_once()
    
    @patch('src.jobs.authority_calculator.AuthorityCalculator')
    def test_run_authority_calculation_failure(self, mock_calculator_class):
        """Test authority calculation run with failure."""
        mock_calculator = Mock()
        mock_calculator.calculate_authority_scores.side_effect = Exception("Test error")
        mock_calculator_class.return_value = mock_calculator
        
        result = run_authority_calculation()
        
        assert result is False

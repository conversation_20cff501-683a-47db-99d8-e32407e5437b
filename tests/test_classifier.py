"""
Test Script for Document Classification System
Tests the Gemini-powered document classifier on sample PDFs
"""

import os
import sys
import json
from dotenv import load_dotenv
from document_classifier import DocumentClassifier, test_classifier

# Load environment variables
load_dotenv()

def main():
    """Run the document classifier test on a PDF file or directory"""
    if len(sys.argv) < 2:
        print("Usage: python test_classifier.py <pdf_path_or_directory>")
        return
    
    target_path = sys.argv[1]
    
    if os.path.isfile(target_path) and target_path.lower().endswith('.pdf'):
        # Test a single PDF file
        print(f"Testing classification on file: {target_path}")
        result, metadata = test_classifier(target_path)
        
        # Save results to JSON file for inspection
        output_file = f"classification_result_{os.path.basename(target_path)}.json"
        with open(output_file, 'w') as f:
            json.dump({
                "classification": result,
                "metadata": metadata
            }, f, indent=2)
            
        print(f"Classification results saved to {output_file}")
        
    elif os.path.isdir(target_path):
        # Test all PDFs in a directory
        pdf_files = [
            os.path.join(target_path, f) for f in os.listdir(target_path)
            if f.lower().endswith('.pdf')
        ]
        
        if not pdf_files:
            print(f"No PDF files found in {target_path}")
            return
            
        print(f"Testing classification on {len(pdf_files)} PDF files...")
        
        classifier = DocumentClassifier()
        results = []
        
        for pdf_file in pdf_files:
            try:
                print(f"Processing {os.path.basename(pdf_file)}...")
                classification = classifier.classify_document(pdf_file)
                metadata = classifier.extract_metadata_preview(pdf_file, classification)
                
                results.append({
                    "filename": os.path.basename(pdf_file),
                    "classification": classification,
                    "metadata": metadata
                })
            except Exception as e:
                print(f"Error processing {pdf_file}: {str(e)}")
        
        # Save all results to a single JSON file
        output_file = "classification_batch_results.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        print(f"Batch classification results saved to {output_file}")
        
        # Generate summary statistics
        doc_types = {}
        jurisdictions = {}
        confidence_levels = {"high": 0, "medium": 0, "low": 0}
        
        for result in results:
            classification = result["classification"]
            doc_type = classification.get("doc_type")
            jurisdiction = classification.get("jurisdiction")
            confidence = classification.get("confidence")
            
            if doc_type:
                doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
            if jurisdiction:
                jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1
            if confidence:
                confidence_levels[confidence] = confidence_levels.get(confidence, 0) + 1
        
        print("\n===== CLASSIFICATION SUMMARY =====")
        print(f"Total documents processed: {len(results)}")
        
        print("\nDocument Types:")
        for doc_type, count in doc_types.items():
            print(f"  {doc_type}: {count} ({count/len(results)*100:.1f}%)")
            
        print("\nJurisdictions:")
        for jurisdiction, count in jurisdictions.items():
            print(f"  {jurisdiction}: {count} ({count/len(results)*100:.1f}%)")
            
        print("\nConfidence Levels:")
        for confidence, count in confidence_levels.items():
            print(f"  {confidence}: {count} ({count/len(results)*100:.1f}%)")
            
    else:
        print(f"Error: {target_path} is not a valid PDF file or directory")

if __name__ == "__main__":
    main()

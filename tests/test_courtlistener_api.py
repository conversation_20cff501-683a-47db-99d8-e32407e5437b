"""
Integration Tests for Court Listener API
Run this to verify the Court Listener API integration is working properly
"""

import os
import sys
import unittest
from dotenv import load_dotenv

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.client import CourtListenerClient
from src.api.courtlistener.exceptions import CourtListenerAPIError
from src.processing.courtlistener_processor import CourtListenerProcessor


class TestCourtListenerAPI(unittest.TestCase):
    """Test the Court Listener API integration"""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures"""
        load_dotenv()
        cls.client = CourtListenerClient()
        cls.processor = CourtListenerProcessor()
        
    def test_api_key(self):
        """Test that the API key is set and valid"""
        self.assertIsNotNone(os.getenv("COURTLISTENER_API_KEY"), "COURTLISTENER_API_KEY not found in environment")
        
    def test_citation_search(self):
        """Test searching for cases by citation"""
        citation = "410 U.S. 113"  # <PERSON> v. <PERSON>
        cases = self.client.get_cases_by_citation(citation)
        
        self.assertGreater(len(cases), 0, f"No cases found for citation '{citation}'")
        
        # Check case properties
        case = cases[0]
        self.assertIsNotNone(case.name, "Case name is None")
        self.assertIsNotNone(case.court, "Case court is None")
        self.assertTrue(hasattr(case, 'date_filed'), "Case has no date_filed attribute")
        
    def test_texas_search(self):
        """Test searching for Texas cases"""
        results = self.client.get_texas_cases("premises liability", page_size=5)
        
        self.assertIn("count", results, "No count field in results")
        self.assertGreater(results.get("count", 0), 0, "No Texas cases found for 'premises liability'")
        self.assertIn("results", results, "No results field in results")
        self.assertGreater(len(results.get("results", [])), 0, "No results returned")
        
    def test_opinion_retrieval(self):
        """Test retrieving opinions for a case"""
        # First find a case
        cases = self.client.get_cases_by_citation("410 U.S. 113")
        self.assertGreater(len(cases), 0, "No cases found to test opinion retrieval")
        
        # Then get opinions
        case_id = cases[0].id
        opinions = self.client.get_opinions_by_case(case_id)
        
        # There should be at least one opinion
        self.assertGreater(len(opinions), 0, f"No opinions found for case ID {case_id}")
        
    def test_court_listing(self):
        """Test listing courts"""
        all_courts = self.client.get_courts()
        texas_courts = self.client.get_courts(jurisdiction="tex")
        
        self.assertGreater(len(all_courts), 0, "No courts found")
        self.assertGreater(len(texas_courts), 0, "No Texas courts found")
        
    def test_citation_extraction(self):
        """Test extracting citations from text"""
        text = "The Supreme Court's decision in Roe v. Wade, 410 U.S. 113 (1973), established the right to abortion."
        citations = self.processor.extract_and_fetch_citations(text)
        
        self.assertGreater(len(citations), 0, "No citations extracted from text")


if __name__ == "__main__":
    print("Testing Court Listener API Integration...")
    print("This test will verify your API key and the core functionality.")
    print("Each test will make real API calls to Court Listener.")
    print("\nRunning tests...")
    unittest.main(verbosity=2)

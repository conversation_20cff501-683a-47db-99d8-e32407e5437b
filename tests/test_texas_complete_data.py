"""
Integration Tests for Texas Case Law Multi-Stage Data Retrieval
Tests the enhanced data retrieval functionality for Texas cases
"""

import os
import sys
import unittest
from pprint import pprint
from dotenv import load_dotenv

# Add project root to path to enable imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.api.courtlistener.texas import TexasCaseClient, CourtLevel
from src.api.courtlistener.exceptions import CourtListenerAPIError


class TestTexasCompleteDataRetrieval(unittest.TestCase):
    """Test the enhanced data retrieval for Texas cases"""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures"""
        load_dotenv()
        cls.client = TexasCaseClient()
        
    def test_get_case_by_id_complete(self):
        """Test retrieving a complete case by ID"""
        # This is a well-known case ID for a Texas case
        # You may need to update this to a valid case ID if this one expires
        case_id = "4928505"  # Replace with a known valid case ID
        
        # Get the case with complete data
        complete_case = self.client.get_case_by_id(case_id, get_complete_data=True)
        
        # Get the same case without complete data
        basic_case = self.client.get_case_by_id(case_id, get_complete_data=False)
        
        # Verify we got richer data with complete mode
        self.assertIsNotNone(complete_case, "Complete case data is None")
        self.assertIsNotNone(basic_case, "Basic case data is None")
        
        # Complete data should have cluster, opinions, and docket
        self.assertIn("cluster", complete_case, "No cluster data in complete case")
        self.assertIn("opinions", complete_case, "No opinions in complete case")
        self.assertIn("docket", complete_case, "No docket in complete case")
        
        print("\nComplete Case Keys:")
        print(list(complete_case.keys()))
        print("\nBasic Case Type:")
        print(type(basic_case))
        
    def _debug_opinion_structure(self, enhanced_results):
        """Debug helper to print opinion structure"""
        print("\n=== DEBUG: Opinion Structure Analysis ===")
        found_opinion_with_text = False
        
        for result in enhanced_results.get("results", []):
            if "_complete_data" in result and result["_complete_data"] and "opinions" in result:
                print(f"Found enhanced case: {result.get('caseName') or result.get('case_name')}")
                opinions = result.get("opinions", [])
                print(f"Number of opinions: {len(opinions)}")
                
                if opinions:
                    # Examine first opinion structure
                    print("\nOpinion Fields:")
                    first_opinion = opinions[0]
                    if isinstance(first_opinion, dict):
                        print(f"Opinion keys: {list(first_opinion.keys())}")
                        
                        # Check for common text field names
                        for field in ['text', 'plain_text', 'html_text', 'html', 'plain_text', 'html_with_citations']:
                            if field in first_opinion:
                                print(f"Found text field: '{field}' with length: {len(str(first_opinion[field]))}")
                                if len(str(first_opinion[field])) > 100:
                                    found_opinion_with_text = True
                            else:
                                print(f"Text field '{field}' not found")
                                
                        # Look for any field with potentially large string content
                        for key, value in first_opinion.items():
                            if isinstance(value, str) and len(value) > 1000:
                                print(f"Large text found in field: '{key}' with length: {len(value)}")
                                found_opinion_with_text = True
                    else:
                        print(f"Opinion is not a dictionary: {type(first_opinion)}")
                                
                    print(f"Has substantial text content: {found_opinion_with_text}")
                    break  # Only analyze the first case with opinions
        
        print("=== End of Opinion Structure Analysis ===\n")
        return found_opinion_with_text
    
    def test_personal_injury_cases_enhanced(self):
        """Test retrieving personal injury cases with enhanced data"""
        # Get cases with basic data
        basic_results = self.client.get_personal_injury_cases(
            page=1, 
            page_size=10, 
            court_level=CourtLevel.ALL,
            get_complete_data=False
        )
        
        # Print debug information about the basic results
        print("\nBasic Results Structure:")
        print(f"Keys: {list(basic_results.keys())}")
        print(f"Result count: {len(basic_results.get('results', []))}")
        
        # Check if results have IDs or resource_uris
        if 'results' in basic_results and basic_results['results']:
            print("\nSample Result Fields:")
            sample_result = basic_results['results'][0]
            print(f"Keys: {list(sample_result.keys())}")
            print(f"Has ID: {'id' in sample_result}")
            print(f"ID: {sample_result.get('id')}")
            print(f"Has resource_uri: {'resource_uri' in sample_result}")
            print(f"Resource URI: {sample_result.get('resource_uri')}")
            
        # Get cases with enhanced data
        enhanced_results = self.client.get_personal_injury_cases(
            page=1, 
            page_size=10, 
            court_level=CourtLevel.ALL,
            get_complete_data=True,
            max_complete_cases=3  # Limit to 3 enhanced cases to be efficient
        )
        
        # Debug the opinion structure
        self._debug_opinion_structure(enhanced_results)
        
        self.assertIsNotNone(basic_results, "Basic results are None")
        self.assertIsNotNone(enhanced_results, "Enhanced results are None")
        
        # Enhanced results should have the enhanced_count field
        self.assertIn("enhanced_count", enhanced_results, "No enhanced_count field in enhanced results")
        
        # Check if we have cases with complete data
        enhanced_count = 0
        for result in enhanced_results.get("results", []):
            if result.get("_complete_data"):
                enhanced_count += 1
                # Complete data should have opinions and docket
                self.assertIn("opinions", result, "No opinions in enhanced case")
                self.assertIn("docket", result, "No docket in enhanced case")
        
        print(f"\nEnhanced case count: {enhanced_count}")
        print(f"Reported enhanced count: {enhanced_results.get('enhanced_count', 0)}")
        
        # Display detailed completeness metrics
        basic_validation = basic_results.get("validation", {})
        enhanced_validation = enhanced_results.get("validation", {})
        
        print("\n=== Basic Results Validation Metrics ===")
        if "quality_assessment" in basic_validation:
            quality = basic_validation["quality_assessment"]
            print(f"Document Quality: {quality.get('document_quality', 'N/A')}")
            print(f"Metadata Quality: {quality.get('metadata_quality', 'N/A')}")
            print(f"Full Document Completeness: {quality.get('full_document_completeness', '0%')}")
            print(f"Enhanced Data Completeness: {quality.get('enhanced_data_completeness', '0%')}")
            print(f"Basic Metadata Completeness: {quality.get('basic_metadata_completeness', '0%')}")
            print(f"Full Document Count: {quality.get('full_document_count', 0)}")
            print(f"Cases suitable for GCS: {quality.get('cases_suitable_for_storage', {}).get('gcs', 0)}")
            print(f"Cases suitable for Pinecone: {quality.get('cases_suitable_for_storage', {}).get('pinecone', 0)}")
            print(f"Cases suitable for Neo4j: {quality.get('cases_suitable_for_storage', {}).get('neo4j', 0)}")
        else:
            print("No quality assessment available")
        
        print("\n=== Enhanced Results Validation Metrics ===")
        if "quality_assessment" in enhanced_validation:
            quality = enhanced_validation["quality_assessment"]
            print(f"Document Quality: {quality.get('document_quality', 'N/A')}")
            print(f"Metadata Quality: {quality.get('metadata_quality', 'N/A')}")
            print(f"Full Document Completeness: {quality.get('full_document_completeness', '0%')}")
            print(f"Enhanced Data Completeness: {quality.get('enhanced_data_completeness', '0%')}")
            print(f"Basic Metadata Completeness: {quality.get('basic_metadata_completeness', '0%')}")
            print(f"Full Document Count: {quality.get('full_document_count', 0)}")
            print(f"Cases suitable for GCS: {quality.get('cases_suitable_for_storage', {}).get('gcs', 0)}")
            print(f"Cases suitable for Pinecone: {quality.get('cases_suitable_for_storage', {}).get('pinecone', 0)}")
            print(f"Cases suitable for Neo4j: {quality.get('cases_suitable_for_storage', {}).get('neo4j', 0)}")
        else:
            print("No quality assessment available")
        
    def test_criminal_defense_cases_enhanced(self):
        """Test retrieving criminal defense cases with enhanced data"""
        # Get cases with basic data
        basic_results = self.client.get_criminal_defense_cases(
            page=1, 
            page_size=10,
            get_complete_data=False
        )
        
        # Get cases with enhanced data
        enhanced_results = self.client.get_criminal_defense_cases(
            page=1, 
            page_size=10,
            get_complete_data=True,
            max_complete_cases=2  # Limit to 2 enhanced cases to be efficient
        )
        
        self.assertIsNotNone(basic_results, "Basic results are None")
        self.assertIsNotNone(enhanced_results, "Enhanced results are None")
        
        # Check if we have enhanced data field
        self.assertIn("enhanced_count", enhanced_results, "No enhanced_count field in enhanced results")
        
        # Validate at least some cases have enhanced data
        enhanced_count = sum(1 for result in enhanced_results.get("results", []) 
                           if result.get("_complete_data"))
                           
        print(f"\nCriminal defense enhanced case count: {enhanced_count}")
        
    def test_family_law_cases_enhanced(self):
        """Test retrieving family law cases with enhanced data"""
        # Get cases with enhanced data
        enhanced_results = self.client.get_family_law_cases(
            page=1, 
            page_size=10,
            get_complete_data=True,
            max_complete_cases=2  # Limit to 2 enhanced cases to be efficient
        )
        
        self.assertIsNotNone(enhanced_results, "Enhanced results are None")
        
        # Check for enhanced cases
        enhanced_count = sum(1 for result in enhanced_results.get("results", []) 
                           if result.get("_complete_data"))
        
        print(f"\nFamily law enhanced case count: {enhanced_count}")
        
        # Get a sample case with enhanced data
        for result in enhanced_results.get("results", []):
            if result.get("_complete_data"):
                print("\nSample Family Law Enhanced Case:")
                print(f"Name: {result.get('caseName') or result.get('case_name')}")
                
                # Handle court data which might be a string or a dict
                court_info = result.get('court', '')
                if isinstance(court_info, dict):
                    court_name = court_info.get('full_name', 'Unknown Court')
                else:
                    court_name = court_info
                print(f"Court: {court_name}")
                
                print(f"Opinion count: {len(result.get('opinions', []))}")
                print(f"Docket entries: {len(result.get('docket', {}).get('docket_entries', []))}")
                break


if __name__ == "__main__":
    print("Testing Enhanced Texas Case Law Data Retrieval...")
    print("This test will verify the multi-stage data retrieval functionality.")
    print("Each test will make real API calls to Court Listener.")
    print("\nRunning tests...")
    unittest.main(verbosity=2)

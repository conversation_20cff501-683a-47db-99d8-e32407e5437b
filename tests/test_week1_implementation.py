#!/usr/bin/env python3
"""
Week 1 Implementation Tests
Tests the jurisdiction tagging, document type classification, and storage organization features.

This test suite validates:
1. Enhanced jurisdiction configuration loading
2. Document type taxonomy classification
3. Storage connector enhancements (GCS, Pinecone)
4. Database schema updates
5. End-to-end document processing with new features
"""

import os
import sys
import unittest
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.extractors.enhanced_document_classifier import EnhancedDocumentClassifier
from src.processing.storage.gcs_connector import GCSConnector, load_jurisdiction_config, load_document_taxonomy
from src.processing.storage.pinecone_connector import PineconeConnector

class TestJurisdictionConfiguration(unittest.TestCase):
    """Test jurisdiction configuration loading and validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            "jurisdictions": {
                "tx": {
                    "code": "tx",
                    "name": "Texas",
                    "level": "state",
                    "document_types": ["statute", "case", "regulation"],
                    "storage_config": {
                        "gcs_prefix": "legal/tx",
                        "pinecone_namespace": "tx"
                    }
                },
                "fed": {
                    "code": "fed",
                    "name": "Federal",
                    "level": "federal",
                    "document_types": ["statute", "case", "regulation", "constitution"],
                    "storage_config": {
                        "gcs_prefix": "legal/fed",
                        "pinecone_namespace": "fed"
                    }
                }
            }
        }
    
    def test_jurisdiction_config_loading(self):
        """Test that jurisdiction configuration loads correctly."""
        # Create a temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            temp_config_path = f.name
        
        try:
            # Mock the config path
            with patch('src.processing.storage.gcs_connector.os.path.join', return_value=temp_config_path):
                config = load_jurisdiction_config()
                
                self.assertIn('jurisdictions', config)
                self.assertIn('tx', config['jurisdictions'])
                self.assertIn('fed', config['jurisdictions'])
                
                tx_config = config['jurisdictions']['tx']
                self.assertEqual(tx_config['name'], 'Texas')
                self.assertEqual(tx_config['level'], 'state')
                self.assertIn('statute', tx_config['document_types'])
        finally:
            os.unlink(temp_config_path)
    
    def test_document_taxonomy_loading(self):
        """Test that document taxonomy loads correctly."""
        test_taxonomy = {
            "document_types": {
                "statute": {
                    "name": "Statute",
                    "subtypes": {
                        "civil_code": {"name": "Civil Code"}
                    }
                },
                "case": {
                    "name": "Case Law",
                    "subtypes": {
                        "supreme_court": {"name": "Supreme Court Opinion"}
                    }
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_taxonomy, f)
            temp_taxonomy_path = f.name
        
        try:
            with patch('src.processing.storage.gcs_connector.os.path.join', return_value=temp_taxonomy_path):
                taxonomy = load_document_taxonomy()
                
                self.assertIn('document_types', taxonomy)
                self.assertIn('statute', taxonomy['document_types'])
                self.assertIn('case', taxonomy['document_types'])
        finally:
            os.unlink(temp_taxonomy_path)

class TestEnhancedDocumentClassifier(unittest.TestCase):
    """Test the enhanced document classifier."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock the configuration loading
        self.mock_jurisdiction_config = {
            "jurisdictions": {
                "tx": {
                    "name": "Texas",
                    "document_types": ["statute", "case"],
                    "citation_formats": {
                        "statute": ["Tex\\. [A-Z][a-z]+ Code § \\d+\\.\\d+"],
                        "case": {"tex_supreme": ["\\d+ S\\.W\\.\\d+d \\d+"]}
                    }
                }
            },
            "global_settings": {"default_jurisdiction": "tx"}
        }
        
        self.mock_document_taxonomy = {
            "document_types": {
                "statute": {
                    "name": "Statute",
                    "subtypes": {
                        "civil_code": {
                            "name": "Civil Code",
                            "patterns": ["civil", "procedure", "tort"]
                        }
                    },
                    "hierarchy_levels": ["title", "chapter", "section"]
                },
                "case": {
                    "name": "Case Law",
                    "subtypes": {
                        "supreme_court": {
                            "name": "Supreme Court Opinion",
                            "patterns": ["supreme court"]
                        }
                    }
                }
            },
            "practice_areas": {
                "personal_injury": {
                    "keywords": ["tort", "negligence", "liability", "damages"]
                },
                "immigration_law": {
                    "keywords": ["immigration", "visa", "deportation", "asylum"]
                }
            }
        }
    
    @patch('src.extractors.enhanced_document_classifier.load_jurisdiction_config')
    @patch('src.extractors.enhanced_document_classifier.load_document_taxonomy')
    def test_classifier_initialization(self, mock_taxonomy, mock_jurisdiction):
        """Test classifier initialization with mocked configs."""
        mock_jurisdiction.return_value = self.mock_jurisdiction_config
        mock_taxonomy.return_value = self.mock_document_taxonomy
        
        classifier = EnhancedDocumentClassifier()
        
        self.assertEqual(classifier.supported_jurisdictions, ['tx'])
        self.assertEqual(set(classifier.supported_doc_types), {'statute', 'case'})
    
    @patch('src.extractors.enhanced_document_classifier.load_jurisdiction_config')
    @patch('src.extractors.enhanced_document_classifier.load_document_taxonomy')
    def test_statute_classification(self, mock_taxonomy, mock_jurisdiction):
        """Test classification of a statute document."""
        mock_jurisdiction.return_value = self.mock_jurisdiction_config
        mock_taxonomy.return_value = self.mock_document_taxonomy
        
        classifier = EnhancedDocumentClassifier()
        
        statute_text = """
        Texas Civil Practice and Remedies Code
        Title 4. Liability in Tort
        Chapter 33. Proportionate Responsibility
        Section 33.001. Definitions
        
        In this chapter:
        (1) "Claimant" means a party seeking recovery of damages.
        (2) "Defendant" means a party from whom damages are sought.
        """
        
        result = classifier.classify_document(statute_text, "texas_civil_code.txt")
        
        self.assertEqual(result['jurisdiction'], 'tx')
        self.assertEqual(result['doc_type'], 'statute')
        self.assertEqual(result['doc_subtype'], 'civil_code')
        self.assertIn('personal_injury', result['practice_areas'])
        self.assertTrue(result['is_valid'])
    
    @patch('src.extractors.enhanced_document_classifier.load_jurisdiction_config')
    @patch('src.extractors.enhanced_document_classifier.load_document_taxonomy')
    def test_case_classification(self, mock_taxonomy, mock_jurisdiction):
        """Test classification of a case document."""
        mock_jurisdiction.return_value = self.mock_jurisdiction_config
        mock_taxonomy.return_value = self.mock_document_taxonomy
        
        classifier = EnhancedDocumentClassifier()
        
        case_text = """
        Supreme Court of Texas
        
        SMITH v. JONES
        
        No. 12-0345
        
        Decided: March 15, 2023
        
        This case involves a negligence claim arising from a motor vehicle accident.
        The plaintiff seeks damages for personal injury and property damage.
        """
        
        result = classifier.classify_document(case_text, "smith_v_jones.txt")
        
        self.assertEqual(result['jurisdiction'], 'tx')
        self.assertEqual(result['doc_type'], 'case')
        self.assertEqual(result['doc_subtype'], 'supreme_court')
        self.assertIn('personal_injury', result['practice_areas'])
        self.assertTrue(result['is_valid'])

class TestGCSConnectorEnhancements(unittest.TestCase):
    """Test GCS connector enhancements."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock GCS client
        self.mock_client = Mock()
        self.mock_bucket = Mock()
        self.mock_client.bucket.return_value = self.mock_bucket
    
    @patch('src.processing.storage.gcs_connector.storage.Client')
    @patch('src.processing.storage.gcs_connector.load_jurisdiction_config')
    @patch('src.processing.storage.gcs_connector.load_document_taxonomy')
    def test_enhanced_path_generation(self, mock_taxonomy, mock_jurisdiction, mock_storage_client):
        """Test enhanced jurisdiction-based path generation."""
        mock_storage_client.from_service_account_json.return_value = self.mock_client
        
        mock_jurisdiction.return_value = {
            "jurisdictions": {
                "tx": {
                    "storage_config": {
                        "gcs_prefix": "legal/tx"
                    }
                }
            }
        }
        mock_taxonomy.return_value = {"document_types": {}}
        
        with patch.dict(os.environ, {'GCS_SERVICE_ACCOUNT_FILE': '/fake/path.json'}):
            connector = GCSConnector()
            
            # Test case document path
            case_path = connector.get_jurisdiction_path(
                jurisdiction="tx",
                document_id="case_123",
                doc_type="case",
                year="2023",
                filename="full_text.txt"
            )
            
            expected_path = "legal/tx/cases/2023/case_123/full_text.txt"
            self.assertEqual(case_path, expected_path)
            
            # Test statute document path
            statute_path = connector.get_jurisdiction_path(
                jurisdiction="tx",
                document_id="statute_456",
                doc_type="statute",
                doc_subtype="civil_code",
                filename="content.txt"
            )
            
            expected_statute_path = "legal/tx/statutes/civil_code/statute_456/content.txt"
            self.assertEqual(statute_path, expected_statute_path)
    
    @patch('src.processing.storage.gcs_connector.storage.Client')
    @patch('src.processing.storage.gcs_connector.load_jurisdiction_config')
    @patch('src.processing.storage.gcs_connector.load_document_taxonomy')
    def test_jurisdiction_validation(self, mock_taxonomy, mock_jurisdiction, mock_storage_client):
        """Test jurisdiction and document type validation."""
        mock_storage_client.from_service_account_json.return_value = self.mock_client
        
        mock_jurisdiction.return_value = {
            "jurisdictions": {
                "tx": {
                    "document_types": ["statute", "case"]
                }
            }
        }
        mock_taxonomy.return_value = {"document_types": {}}
        
        with patch.dict(os.environ, {'GCS_SERVICE_ACCOUNT_FILE': '/fake/path.json'}):
            connector = GCSConnector()
            
            # Valid combination
            self.assertTrue(connector.validate_jurisdiction_doc_type("tx", "statute"))
            self.assertTrue(connector.validate_jurisdiction_doc_type("tx", "case"))
            
            # Invalid combination
            self.assertFalse(connector.validate_jurisdiction_doc_type("tx", "regulation"))

class TestPineconeConnectorEnhancements(unittest.TestCase):
    """Test Pinecone connector enhancements."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_pc = Mock()
        self.mock_index = Mock()
        self.mock_pc.Index.return_value = self.mock_index
    
    @patch('src.processing.storage.pinecone_connector.Pinecone')
    @patch('src.processing.storage.pinecone_connector.load_jurisdiction_config')
    @patch('src.processing.storage.pinecone_connector.load_document_taxonomy')
    def test_enhanced_namespace_generation(self, mock_taxonomy, mock_jurisdiction, mock_pinecone):
        """Test enhanced namespace generation."""
        mock_pinecone.return_value = self.mock_pc
        self.mock_pc.list_indexes.return_value.names.return_value = ['legal-documents']
        
        mock_jurisdiction.return_value = {
            "jurisdictions": {
                "tx": {
                    "storage_config": {
                        "pinecone_namespace": "tx"
                    }
                }
            }
        }
        mock_taxonomy.return_value = {"document_types": {}}
        
        with patch.dict(os.environ, {'PINECONE_API_KEY': 'fake_key'}):
            connector = PineconeConnector()
            
            namespace = connector.get_namespace("tx", "case")
            self.assertEqual(namespace, "tx_case")
    
    @patch('src.processing.storage.pinecone_connector.Pinecone')
    @patch('src.processing.storage.pinecone_connector.load_jurisdiction_config')
    @patch('src.processing.storage.pinecone_connector.load_document_taxonomy')
    def test_enhanced_metadata_storage(self, mock_taxonomy, mock_jurisdiction, mock_pinecone):
        """Test enhanced metadata storage with jurisdiction information."""
        mock_pinecone.return_value = self.mock_pc
        self.mock_pc.list_indexes.return_value.names.return_value = ['legal-documents']
        
        mock_jurisdiction.return_value = {
            "jurisdictions": {
                "tx": {
                    "name": "Texas",
                    "level": "state",
                    "document_types": ["case"]
                }
            }
        }
        mock_taxonomy.return_value = {
            "document_types": {
                "case": {
                    "name": "Case Law",
                    "description": "Court decisions and opinions"
                }
            }
        }
        
        with patch.dict(os.environ, {'PINECONE_API_KEY': 'fake_key'}):
            connector = PineconeConnector()
            
            test_vector = [0.1] * 1024
            test_metadata = {"title": "Test Case"}
            
            result = connector.store_embedding(
                vector=test_vector,
                id="test_case_123",
                metadata=test_metadata,
                jurisdiction="tx",
                doc_type="case"
            )
            
            self.assertTrue(result)
            
            # Verify that upsert was called with enhanced metadata
            self.mock_index.upsert.assert_called_once()
            call_args = self.mock_index.upsert.call_args
            
            # Check that the metadata was enhanced
            stored_metadata = call_args[1]['vectors'][0][2]  # Third element is metadata
            self.assertEqual(stored_metadata['jurisdiction'], 'tx')
            self.assertEqual(stored_metadata['doc_type'], 'case')
            self.assertEqual(stored_metadata['jurisdiction_name'], 'Texas')
            self.assertEqual(stored_metadata['doc_type_name'], 'Case Law')

class TestEndToEndIntegration(unittest.TestCase):
    """Test end-to-end integration of Week 1 features."""
    
    @patch('src.extractors.enhanced_document_classifier.load_jurisdiction_config')
    @patch('src.extractors.enhanced_document_classifier.load_document_taxonomy')
    def test_document_processing_pipeline(self, mock_taxonomy, mock_jurisdiction):
        """Test the complete document processing pipeline with Week 1 enhancements."""
        # Mock configurations
        mock_jurisdiction.return_value = {
            "jurisdictions": {
                "tx": {
                    "name": "Texas",
                    "document_types": ["statute"],
                    "citation_formats": {
                        "statute": ["Tex\\. [A-Z][a-z]+ Code § \\d+\\.\\d+"]
                    }
                }
            },
            "global_settings": {"default_jurisdiction": "tx"}
        }
        
        mock_taxonomy.return_value = {
            "document_types": {
                "statute": {
                    "name": "Statute",
                    "subtypes": {
                        "civil_code": {
                            "name": "Civil Code",
                            "patterns": ["civil", "procedure"]
                        }
                    }
                }
            },
            "practice_areas": {
                "personal_injury": {
                    "keywords": ["tort", "liability"]
                }
            }
        }
        
        classifier = EnhancedDocumentClassifier()
        
        test_document = """
        Texas Civil Practice and Remedies Code
        Chapter 33. Proportionate Responsibility
        Section 33.001. Definitions
        
        This chapter establishes rules for tort liability and damages.
        """
        
        classification = classifier.classify_document(test_document, "tx_civil_code.txt")
        
        # Verify comprehensive classification
        self.assertEqual(classification['jurisdiction'], 'tx')
        self.assertEqual(classification['doc_type'], 'statute')
        self.assertEqual(classification['doc_subtype'], 'civil_code')
        self.assertIn('personal_injury', classification['practice_areas'])
        self.assertTrue(classification['is_valid'])
        
        # Verify metadata extraction
        metadata = classification['metadata']
        self.assertIn('classification_timestamp', metadata)
        self.assertEqual(metadata['classifier_version'], '2.0_week1')

if __name__ == '__main__':
    unittest.main()

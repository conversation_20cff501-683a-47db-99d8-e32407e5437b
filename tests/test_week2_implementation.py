"""
Week 2 Implementation Tests

Test suite for automated case harvesting and enhanced processing capabilities.
"""

import pytest
import json
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.harvesting.harvesting_config import (
    HarvestingConfigManager, 
    HarvestingConfig, 
    JurisdictionConfig
)
from src.harvesting.jurisdiction_harvester import JurisdictionHarvester, HarvestResult
from src.harvesting.automated_harvester import AutomatedHarvester
from src.processing.enhanced_opinion_processor import (
    EnhancedOpinionProcessor, 
    OpinionType, 
    OpinionQuality
)
from src.processing.metadata_enhancer import CaseMetadataEnhancer, EnhancedCaseMetadata


class TestHarvestingConfig:
    """Test harvesting configuration management"""
    
    def test_load_valid_config(self):
        """Test loading a valid configuration file"""
        config_data = {
            "harvesting": {
                "enabled": True,
                "default_schedule": "0 2 * * *",
                "max_concurrent_jobs": 3,
                "retry_attempts": 3,
                "retry_delay_seconds": 300,
                "batch_size": 50,
                "rate_limit_delay": 0.5
            },
            "jurisdictions": {
                "tx": {
                    "name": "Texas",
                    "enabled": True,
                    "priority": "high",
                    "schedule": "0 1 * * *",
                    "courts": ["tex", "texapp1st"],
                    "practice_areas": ["personal_injury"],
                    "search_queries": {
                        "personal_injury": ["negligence", "tort"]
                    },
                    "date_range": {
                        "start_date": "2020-01-01",
                        "incremental_days": 30
                    },
                    "max_cases_per_run": 100
                }
            },
            "quality_thresholds": {
                "min_opinion_length": 100,
                "min_citation_count": 1,
                "required_fields": ["case_name", "court", "date_filed"]
            },
            "storage": {
                "gcs_bucket": "legal-documents"
            },
            "monitoring": {
                "log_level": "INFO"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name
        
        try:
            manager = HarvestingConfigManager(config_path)
            config = manager.load_config()
            
            assert config.enabled is True
            assert config.max_concurrent_jobs == 3
            assert len(config.jurisdictions) == 1
            assert "tx" in config.jurisdictions
            
            tx_config = config.get_jurisdiction_config("tx")
            assert tx_config is not None
            assert tx_config.name == "Texas"
            assert tx_config.enabled is True
            assert len(tx_config.courts) == 2
            
        finally:
            Path(config_path).unlink()
    
    def test_jurisdiction_config_search_terms(self):
        """Test jurisdiction configuration search term extraction"""
        jurisdiction_config = JurisdictionConfig(
            name="Texas",
            enabled=True,
            priority="high",
            schedule="0 1 * * *",
            courts=["tex"],
            practice_areas=["personal_injury", "criminal_law"],
            search_queries={
                "personal_injury": ["negligence", "tort"],
                "criminal_law": ["criminal", "prosecution"]
            },
            date_range={"start_date": "2020-01-01"},
            max_cases_per_run=100
        )
        
        # Test specific practice area
        pi_terms = jurisdiction_config.get_search_terms("personal_injury")
        assert "negligence" in pi_terms
        assert "tort" in pi_terms
        assert len(pi_terms) == 2
        
        # Test all terms
        all_terms = jurisdiction_config.get_search_terms()
        assert "negligence" in all_terms
        assert "criminal" in all_terms
        assert len(all_terms) == 4
    
    def test_config_validation(self):
        """Test configuration validation"""
        manager = HarvestingConfigManager()
        
        # Valid config
        valid_config = HarvestingConfig(
            enabled=True,
            default_schedule="0 2 * * *",
            max_concurrent_jobs=3,
            retry_attempts=3,
            retry_delay_seconds=300,
            batch_size=50,
            rate_limit_delay=0.5,
            jurisdictions={
                "tx": JurisdictionConfig(
                    name="Texas",
                    enabled=True,
                    priority="high",
                    schedule="0 1 * * *",
                    courts=["tex"],
                    practice_areas=["personal_injury"],
                    search_queries={"personal_injury": ["negligence"]},
                    date_range={"start_date": "2020-01-01"},
                    max_cases_per_run=100
                )
            }
        )
        
        issues = manager.validate_config(valid_config)
        assert len(issues) == 0
        
        # Invalid config - no enabled jurisdictions
        invalid_config = HarvestingConfig(
            enabled=True,
            default_schedule="0 2 * * *",
            max_concurrent_jobs=3,
            retry_attempts=3,
            retry_delay_seconds=300,
            batch_size=50,
            rate_limit_delay=0.5,
            jurisdictions={}
        )
        
        issues = manager.validate_config(invalid_config)
        assert len(issues) > 0
        assert any("No jurisdictions are enabled" in issue for issue in issues)


class TestJurisdictionHarvester:
    """Test jurisdiction-specific harvesting"""
    
    @patch('src.harvesting.jurisdiction_harvester.CourtListenerClient')
    @patch('src.harvesting.jurisdiction_harvester.CaseLawProcessor')
    @patch('src.harvesting.jurisdiction_harvester.SupabaseClient')
    def test_harvest_cases(self, mock_supabase, mock_processor, mock_client):
        """Test case harvesting for a jurisdiction"""
        # Setup mocks
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        mock_processor_instance = Mock()
        mock_processor.return_value = mock_processor_instance
        
        # Mock search results
        mock_search_results = {
            "results": [
                {
                    "id": "12345",
                    "case_name": "Test Case v. Example",
                    "court": "tex",
                    "date_filed": "2023-01-01",
                    "citation_count": 5
                }
            ]
        }
        mock_client_instance.search_opinions.return_value = mock_search_results
        
        # Mock processing result
        mock_processor_instance.process_case.return_value = {"status": "success"}
        
        # Create jurisdiction config
        config = JurisdictionConfig(
            name="Texas",
            enabled=True,
            priority="high",
            schedule="0 1 * * *",
            courts=["tex"],
            practice_areas=["personal_injury"],
            search_queries={"personal_injury": ["negligence"]},
            date_range={"start_date": "2020-01-01", "incremental_days": 30},
            max_cases_per_run=10
        )
        
        harvester = JurisdictionHarvester("tx", config)
        
        # Test harvest
        result = harvester.harvest_cases(
            practice_area="personal_injury",
            max_cases=5
        )
        
        assert isinstance(result, HarvestResult)
        assert result.jurisdiction == "tx"
        assert result.total_found >= 0
        assert result.end_time is not None
    
    def test_quality_threshold_filtering(self):
        """Test quality threshold filtering"""
        config = JurisdictionConfig(
            name="Texas",
            enabled=True,
            priority="high",
            schedule="0 1 * * *",
            courts=["tex"],
            practice_areas=["personal_injury"],
            search_queries={"personal_injury": ["negligence"]},
            date_range={"start_date": "2020-01-01"},
            max_cases_per_run=10
        )
        
        harvester = JurisdictionHarvester("tx", config)
        
        # Test case that meets threshold
        good_case = {
            "case_name": "Test Case",
            "court": "tex",
            "date_filed": "2023-01-01",
            "citation_count": 5
        }
        assert harvester._meets_quality_threshold(good_case) is True
        
        # Test case that doesn't meet threshold (missing required field)
        bad_case = {
            "court": "tex",
            "date_filed": "2023-01-01",
            "citation_count": 5
        }
        assert harvester._meets_quality_threshold(bad_case) is False


class TestEnhancedOpinionProcessor:
    """Test enhanced opinion processing"""
    
    def test_opinion_type_classification(self):
        """Test opinion type classification"""
        processor = EnhancedOpinionProcessor()
        
        # Test majority opinion
        majority_opinion = {
            "id": "12345",
            "cluster": "67890",
            "type": "lead",
            "plain_text": "The Court holds that the defendant is liable for negligence."
        }
        
        result = processor.process_opinion(majority_opinion)
        assert result["metadata"]["opinion_type"] == OpinionType.MAJORITY.value
        
        # Test dissenting opinion
        dissenting_opinion = {
            "id": "12346",
            "cluster": "67890",
            "plain_text": "I respectfully dissent from the majority opinion."
        }
        
        result = processor.process_opinion(dissenting_opinion)
        assert result["metadata"]["opinion_type"] == OpinionType.DISSENTING.value
    
    def test_citation_extraction(self):
        """Test legal citation extraction"""
        processor = EnhancedOpinionProcessor()
        
        opinion_text = """
        This case follows the precedent set in Brown v. Board, 347 U.S. 483 (1954).
        We also consider the holding in Miranda v. Arizona, 384 U.S. 436 (1966).
        The Texas Supreme Court ruled in Smith v. Jones, 123 S.W.3d 456 (Tex. 2003).
        """
        
        citations = processor._extract_citations(opinion_text)
        
        assert "347 U.S. 483" in citations
        assert "384 U.S. 436" in citations
        assert "123 S.W.3d 456" in citations
        assert len(citations) >= 3
    
    def test_quality_assessment(self):
        """Test opinion quality assessment"""
        processor = EnhancedOpinionProcessor()
        
        # High quality opinion
        high_quality_text = "A" * 3000 + " Therefore, we conclude that the defendant's actions constitute negligence. However, the plaintiff also bears some responsibility. Moreover, the evidence clearly shows..."
        quality = processor._assess_opinion_quality(high_quality_text, 3000, 15)
        assert quality == OpinionQuality.HIGH
        
        # Low quality opinion
        low_quality_text = "Short opinion."
        quality = processor._assess_opinion_quality(low_quality_text, 10, 0)
        assert quality == OpinionQuality.POOR
    
    def test_judge_extraction(self):
        """Test judge information extraction"""
        processor = EnhancedOpinionProcessor()
        
        opinion_data = {
            "id": "12345",
            "cluster": "67890",
            "author_str": "Justice Smith"
        }
        
        opinion_text = "Justice Johnson joins this opinion. Justice Brown also joins."
        
        author, joining_judges = processor._extract_judges(opinion_data, opinion_text)
        
        assert author == "Justice Smith"
        assert "Johnson" in joining_judges or "Brown" in joining_judges


class TestCaseMetadataEnhancer:
    """Test case metadata enhancement"""
    
    def test_party_extraction(self):
        """Test party extraction from case names"""
        enhancer = CaseMetadataEnhancer()
        
        # Test standard case name
        parties = enhancer._extract_parties("Smith v. Jones Corporation")
        assert len(parties) == 2
        assert parties[0].name == "Smith"
        assert parties[0].party_type == "plaintiff"
        assert parties[1].name == "Jones Corporation"
        assert parties[1].party_type == "defendant"
        assert parties[1].is_organization is True
    
    def test_organization_detection(self):
        """Test organization detection"""
        enhancer = CaseMetadataEnhancer()
        
        assert enhancer._is_organization("ABC Corporation") is True
        assert enhancer._is_organization("State of Texas") is True
        assert enhancer._is_organization("John Smith") is False
        
        assert enhancer._get_organization_type("ABC Corporation") == "corporation"
        assert enhancer._get_organization_type("State of Texas") == "government"
    
    def test_case_type_classification(self):
        """Test case type classification"""
        enhancer = CaseMetadataEnhancer()
        
        civil_case = {
            "case_name": "Smith v. Jones - Negligence Action",
            "summary": "This is a civil tort case involving negligence claims."
        }
        
        case_type = enhancer._classify_case_type(civil_case, civil_case["case_name"])
        assert case_type == "civil"
    
    def test_completeness_score_calculation(self):
        """Test completeness score calculation"""
        metadata = EnhancedCaseMetadata(
            case_id="12345",
            case_name="Test Case",
            jurisdiction="tx",
            court="Texas Supreme Court",
            date_filed=datetime.now(),
            docket_number="12-345"
        )
        
        # Add some enhanced fields
        metadata.case_type = "civil"
        metadata.subject_matter = ["tort"]
        metadata.outcome = "affirmed"
        
        score = metadata.calculate_completeness_score()
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should have decent completeness with the fields we set


class TestAutomatedHarvester:
    """Test automated harvesting system"""
    
    @patch('src.harvesting.automated_harvester.SCHEDULER_AVAILABLE', True)
    @patch('src.harvesting.automated_harvester.BackgroundScheduler')
    def test_harvester_initialization(self, mock_scheduler_class):
        """Test harvester initialization"""
        mock_scheduler = Mock()
        mock_scheduler_class.return_value = mock_scheduler
        
        harvester = AutomatedHarvester()
        
        assert harvester.config is not None
        assert harvester.scheduler is not None
        assert harvester.is_running is False
    
    @patch('src.harvesting.automated_harvester.JurisdictionHarvester')
    def test_manual_harvest(self, mock_harvester_class):
        """Test manual harvest trigger"""
        # Setup mock
        mock_harvester = Mock()
        mock_harvester_class.return_value = mock_harvester
        
        mock_result = HarvestResult(
            jurisdiction="tx",
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_found=10,
            total_processed=10,
            total_success=8,
            total_failed=2
        )
        mock_harvester.harvest_cases.return_value = mock_result
        
        harvester = AutomatedHarvester()
        result = harvester.harvest_jurisdiction_now("tx")
        
        assert isinstance(result, HarvestResult)
        assert result.jurisdiction == "tx"
        assert result.total_success == 8
    
    def test_status_reporting(self):
        """Test status reporting"""
        harvester = AutomatedHarvester()
        status = harvester.get_status()
        
        assert "is_running" in status
        assert "stats" in status
        assert "jurisdictions" in status
        assert isinstance(status["stats"]["success_rate"], float)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

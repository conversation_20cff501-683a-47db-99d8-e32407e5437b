"""
Core Week 4 functionality tests.
Tests data structures, cache functionality, and basic API components.
"""

import pytest
import json
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from fastapi.testclient import TestClient
    from fastapi import FastAPI
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    TestClient = None
    FastAPI = None


class TestCacheAbstraction:
    """Test the cache abstraction layer."""
    
    def test_cache_key_generation(self):
        """Test cache key generation functions."""
        # Mock the cache module functions
        import hashlib
        
        def mock_search_cache_key(query: str, filters: dict, user_permissions_hash: str) -> str:
            query_data = {
                'query': query,
                'filters': filters,
                'permissions': user_permissions_hash
            }
            query_hash = hashlib.md5(json.dumps(query_data, sort_keys=True).encode()).hexdigest()
            return f"search:{query_hash}"
        
        # Test search cache key
        key = mock_search_cache_key(
            "medical malpractice",
            {"jurisdiction": "tx", "doc_type": "case"},
            "user_hash_123"
        )
        
        assert key.startswith("search:")
        assert len(key) > 10  # Should have hash component
    
    def test_cache_ttl_constants(self):
        """Test cache TTL constants are reasonable."""
        SEARCH_TTL = 900      # 15 minutes
        GRAPH_TTL = 900       # 15 minutes
        RECOMMEND_TTL = 1800  # 30 minutes
        JWKS_TTL = 86400      # 24 hours
        
        assert SEARCH_TTL == 15 * 60
        assert GRAPH_TTL == 15 * 60
        assert RECOMMEND_TTL == 30 * 60
        assert JWKS_TTL == 24 * 60 * 60


class TestDataStructures:
    """Test Week 4 data structures and models."""
    
    def test_search_result_structure(self):
        """Test search result data structure."""
        result = {
            "id": "C-2023-TX-123",
            "title": "Smith v. Jones",
            "type": "case",
            "jurisdiction": "tx",
            "authority_score": 0.87,
            "relevance_score": 0.92,
            "snippet": "Test snippet...",
            "metadata": {"court": "Texas Supreme Court"}
        }
        
        # Verify required fields
        required_fields = ["id", "title", "type", "jurisdiction", "authority_score", "relevance_score", "snippet", "metadata"]
        for field in required_fields:
            assert field in result
        
        # Verify data types
        assert isinstance(result["authority_score"], float)
        assert isinstance(result["relevance_score"], float)
        assert isinstance(result["metadata"], dict)
    
    def test_recommendation_structure(self):
        """Test recommendation data structure with score_explainer."""
        recommendation = {
            "id": "C-2019-TX-456",
            "title": "Johnson v. Memorial Hospital",
            "type": "case",
            "jurisdiction": "tx",
            "relationship": "cites",
            "strength": 0.85,
            "reason": "Cited by this document",
            "score_explainer": "Authority score: 0.72, cited 12 times. This document is directly referenced, indicating its relevance to the legal principles discussed.",
            "authority_score": 0.72,
            "metadata": {"citation_count": 12}
        }
        
        # Verify required fields including new score_explainer
        required_fields = ["id", "title", "type", "jurisdiction", "relationship", "strength", "reason", "score_explainer", "authority_score", "metadata"]
        for field in required_fields:
            assert field in recommendation
        
        # Verify score_explainer provides meaningful explanation
        assert "Authority score" in recommendation["score_explainer"]
        assert "cited" in recommendation["score_explainer"]
        assert len(recommendation["score_explainer"]) > 20  # Should be descriptive
    
    def test_graph_node_structure(self):
        """Test React-Flow compatible graph node structure."""
        node = {
            "id": "C-2023-TX-123",
            "label": "Smith v. Jones",
            "type": "case",
            "authority": 0.87,
            "data": {
                "jurisdiction": "tx",
                "court": "Texas Supreme Court",
                "date": "2023-03-15",
                "practice_areas": ["personal_injury"]
            }
        }
        
        # Verify React-Flow required fields
        react_flow_fields = ["id", "label", "type", "data"]
        for field in react_flow_fields:
            assert field in node
        
        # Verify authority field for our use case
        assert "authority" in node
        assert isinstance(node["authority"], float)
        assert 0.0 <= node["authority"] <= 1.0
    
    def test_graph_edge_structure(self):
        """Test React-Flow compatible graph edge structure."""
        edge = {
            "id": "edge-1",
            "source": "C-2023-TX-123",
            "target": "C-2019-TX-456",
            "type": "cites",
            "data": {
                "weight": 34,
                "relationship_type": "supportive",
                "citation_frequency": 12
            }
        }
        
        # Verify React-Flow required fields
        react_flow_fields = ["id", "source", "target", "type", "data"]
        for field in react_flow_fields:
            assert field in edge
        
        # Verify data structure
        assert isinstance(edge["data"], dict)
        assert "weight" in edge["data"]


class TestGraphTruncation:
    """Test graph truncation logic."""
    
    def test_truncation_metadata(self):
        """Test truncation metadata structure."""
        metadata = {
            "center_node": "dense_node_123",
            "returned_nodes": 500,  # Hit hard cap
            "returned_edges": 1500,  # Hit hard cap
            "total_nodes": 600,
            "total_edges": 1600,
            "truncated": True,
            "query_time_ms": 450,
            "depth_used": 3,
            "direction_used": "both",
            "max_nodes_requested": 100,
            "max_edges_requested": 300
        }
        
        # Verify truncation detection
        assert metadata["truncated"] == True
        assert metadata["returned_nodes"] < metadata["total_nodes"]
        assert metadata["returned_edges"] < metadata["total_edges"]
        
        # Verify hard caps were applied
        assert metadata["returned_nodes"] == 500  # Hard cap
        assert metadata["returned_edges"] == 1500  # Hard cap
    
    def test_graph_limits(self):
        """Test graph size limits are reasonable."""
        DEFAULT_MAX_NODES = 50
        DEFAULT_MAX_EDGES = 150
        HARD_CAP_NODES = 500
        HARD_CAP_EDGES = 1500
        
        # Verify limits are reasonable for React-Flow
        assert DEFAULT_MAX_NODES <= HARD_CAP_NODES
        assert DEFAULT_MAX_EDGES <= HARD_CAP_EDGES
        assert HARD_CAP_NODES <= 1000  # Should render on mid-range laptops
        assert HARD_CAP_EDGES <= 2000  # Should keep payload < 200 kB


class TestAuthenticationStructure:
    """Test authentication and JWT structure."""
    
    def test_user_info_structure(self):
        """Test user information structure from JWT."""
        user_info = {
            "user_id": "test_user_123",
            "email": "<EMAIL>",
            "role": "attorney",
            "aud": "authenticated",
            "permissions": ["search", "recommend"],
            "tenant_id": "test_tenant",
            "jurisdictions": ["tx", "oh"],
            "permissions_hash": "hash_123"
        }
        
        # Verify required fields
        required_fields = ["user_id", "email", "role", "aud", "permissions", "jurisdictions", "permissions_hash"]
        for field in required_fields:
            assert field in user_info
        
        # Verify data types
        assert isinstance(user_info["permissions"], list)
        assert isinstance(user_info["jurisdictions"], list)
        assert user_info["aud"] == "authenticated"
    
    def test_permissions_hash_generation(self):
        """Test permissions hash generation for cache keys."""
        import hashlib
        
        def mock_get_user_permissions_hash(user_info: dict) -> str:
            permissions_data = {
                "user_id": user_info.get("user_id"),
                "role": user_info.get("role"),
                "permissions": sorted(user_info.get("permissions", [])),
                "tenant_id": user_info.get("tenant_id"),
                "jurisdictions": sorted(user_info.get("jurisdictions", []))
            }
            permissions_json = json.dumps(permissions_data, sort_keys=True)
            return hashlib.md5(permissions_json.encode()).hexdigest()
        
        user_info = {
            "user_id": "test_user",
            "role": "attorney",
            "permissions": ["search", "recommend"],
            "tenant_id": "tenant_1",
            "jurisdictions": ["tx", "oh"]
        }
        
        hash1 = mock_get_user_permissions_hash(user_info)
        hash2 = mock_get_user_permissions_hash(user_info)
        
        # Same input should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 32  # MD5 hash length


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    def test_rate_limit_headers(self):
        """Test rate limit response headers."""
        headers = {
            "X-RateLimit-Limit": "100",
            "X-RateLimit-Remaining": "95",
            "X-RateLimit-Reset": "1642678800",
            "Retry-After": "60"
        }
        
        # Verify required headers
        required_headers = ["X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset", "Retry-After"]
        for header in required_headers:
            assert header in headers
        
        # Verify values are reasonable
        assert int(headers["X-RateLimit-Limit"]) == 100
        assert int(headers["X-RateLimit-Remaining"]) <= 100
        assert int(headers["Retry-After"]) == 60


@pytest.mark.skipif(not FASTAPI_AVAILABLE, reason="FastAPI not available")
class TestBasicAPI:
    """Test basic API functionality when FastAPI is available."""
    
    def test_health_endpoint(self):
        """Test basic health endpoint."""
        app = FastAPI()
        
        @app.get("/health")
        def health():
            return {"status": "healthy"}
        
        client = TestClient(app)
        response = client.get("/health")
        
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_graph_sample_structure(self):
        """Test graph sample endpoint structure."""
        # Load and validate sample data structure
        sample_data = {
            "nodes": [
                {
                    "id": "sample-case-1",
                    "label": "Sample Case v. Example",
                    "type": "case",
                    "authority": 0.75,
                    "data": {
                        "jurisdiction": "tx",
                        "court": "Sample Court",
                        "date": "2023-01-01"
                    }
                }
            ],
            "edges": [],
            "metadata": {
                "center_node": "sample-case-1",
                "total_nodes": 1,
                "total_edges": 0,
                "truncated": False,
                "query_time_ms": 1,
                "sample": True
            }
        }
        
        # Verify structure matches React-Flow requirements
        assert "nodes" in sample_data
        assert "edges" in sample_data
        assert "metadata" in sample_data
        
        if sample_data["nodes"]:
            node = sample_data["nodes"][0]
            required_keys = ["id", "label", "type", "authority", "data"]
            for key in required_keys:
                assert key in node


class TestAuthorityCalculation:
    """Test authority calculation parameters."""
    
    def test_pagerank_parameters(self):
        """Test PageRank parameters match specification."""
        DAMPING = 0.85
        MAX_ITER = 100
        TOL = 1e-6
        
        assert DAMPING == 0.85
        assert MAX_ITER == 100
        assert TOL == 0.000001
    
    def test_recency_boost_formula(self):
        """Test recency boost formula components."""
        PAGERANK_WEIGHT = 0.8
        RECENCY_WEIGHT = 0.2
        RECENCY_DECAY = 0.1  # per year
        
        assert PAGERANK_WEIGHT + RECENCY_WEIGHT == 1.0
        assert RECENCY_DECAY == 0.1
        
        # Test formula: authority = 0.8 * pagerank + 0.2 * e^(-0.1*age_years)
        import math
        
        pagerank_score = 0.5
        age_years = 2.0
        
        recency_boost = math.exp(-RECENCY_DECAY * age_years)
        authority_score = PAGERANK_WEIGHT * pagerank_score + RECENCY_WEIGHT * recency_boost
        
        assert 0.0 <= authority_score <= 1.0
        assert authority_score > 0  # Should always be positive


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
